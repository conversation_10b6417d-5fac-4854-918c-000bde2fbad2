
import { useState, useCallback, useRef } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { AdvancedGeolocationService, GeolocationResult } from '@/lib/services/location/AdvancedGeolocationService';

interface UseAdvancedGeolocationOptions {
  targetAccuracy?: number;
  maxAttempts?: number;
  enableHybrid?: boolean;
  autoRefine?: boolean;
}

interface GeolocationState {
  isLoading: boolean;
  result: GeolocationResult | null;
  error: string | null;
  attempts: number;
  isRefining: boolean;
  qualityIndicator: 'excellent' | 'good' | 'fair' | 'poor' | null;
}

export function useAdvancedGeolocation(options: UseAdvancedGeolocationOptions = {}) {
  const { toast } = useToast();
  const serviceRef = useRef(AdvancedGeolocationService.getInstance());
  const previousLocationRef = useRef<GeolocationResult | null>(null);
  
  const [state, setState] = useState<GeolocationState>({
    isLoading: false,
    result: null,
    error: null,
    attempts: 0,
    isRefining: false,
    qualityIndicator: null
  });

  const getQualityIndicator = useCallback((result: GeolocationResult): 'excellent' | 'good' | 'fair' | 'poor' => {
    if (result.quality_score >= 80) return 'excellent';
    if (result.quality_score >= 60) return 'good';
    if (result.quality_score >= 40) return 'fair';
    return 'poor';
  }, []);

  const getQualityMessage = useCallback((result: GeolocationResult): string => {
    const messages = {
      gps_high: `GPS de alta precisão (±${Math.round(result.accuracy)}m)`,
      gps_medium: `GPS médio (±${Math.round(result.accuracy)}m)`,
      gps_low: `GPS de baixa precisão (±${Math.round(result.accuracy)}m)`,
      ip_premium: `Localização por IP premium (±${Math.round(result.accuracy)}m)`,
      ip_fallback: `Localização aproximada por IP (±${Math.round(result.accuracy)}m)`,
      hybrid: `Localização híbrida GPS+IP (±${Math.round(result.accuracy)}m)`
    };
    
    return messages[result.source] || `Localização obtida (±${Math.round(result.accuracy)}m)`;
  }, []);

  const getCurrentLocation = useCallback(async (forceRefresh: boolean = false) => {
    if (state.isLoading) return null;

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      attempts: 0,
      isRefining: false
    }));

    try {
      console.log('[useAdvancedGeolocation] Iniciando obtenção de localização avançada');
      
      const result = await serviceRef.current.getHighPrecisionLocation({
        targetAccuracy: options.targetAccuracy || 15,
        maxAttempts: options.maxAttempts || 3,
        enableHybrid: options.enableHybrid ?? true,
        maxWatchTime: options.autoRefine ? 45000 : 30000
      });

      // Validar a localização
      const validation = serviceRef.current.validateLocation(result, previousLocationRef.current);
      
      if (validation.issues.length > 0) {
        console.warn('[useAdvancedGeolocation] Problemas detectados:', validation.issues);
        
        // Mostrar aviso se há problemas, mas não bloquear
        if (validation.issues.some(issue => issue.includes('muito rápido'))) {
          toast({
            title: 'Localização suspeita',
            description: 'Movimento muito rápido detectado. Verifique se a localização está correta.',
            variant: 'default'
          });
        }
      }

      const finalResult = validation.correctedResult || result;
      const qualityIndicator = getQualityIndicator(finalResult);
      
      setState({
        isLoading: false,
        result: finalResult,
        error: null,
        attempts: 1,
        isRefining: false,
        qualityIndicator
      });

      // Mostrar toast com qualidade
      const qualityMessage = getQualityMessage(finalResult);
      const toastVariant = qualityIndicator === 'excellent' || qualityIndicator === 'good' ? 'default' : 'default';
      
      toast({
        title: 'Localização obtida',
        description: `${qualityMessage}${finalResult.address ? ` - ${finalResult.address}` : ''}`,
        variant: toastVariant,
        duration: qualityIndicator === 'poor' ? 6000 : 4000
      });

      // Atualizar referência para próxima validação
      previousLocationRef.current = finalResult;
      
      return finalResult;

    } catch (error: any) {
      console.error('[useAdvancedGeolocation] Erro:', error);
      
      const errorMessage = error.message || 'Não foi possível obter sua localização';
      
      setState({
        isLoading: false,
        result: null,
        error: errorMessage,
        attempts: 0,
        isRefining: false,
        qualityIndicator: null
      });

      toast({
        title: 'Erro de localização',
        description: errorMessage,
        variant: 'destructive'
      });

      return null;
    }
  }, [options, state.isLoading, toast, getQualityIndicator, getQualityMessage]);

  const improveAccuracy = useCallback(async () => {
    if (!state.result || state.isRefining) return;

    setState(prev => ({ ...prev, isRefining: true }));

    try {
      console.log('[useAdvancedGeolocation] Melhorando precisão...');
      
      const improvedResult = await serviceRef.current.getHighPrecisionLocation({
        targetAccuracy: 5, // Precisão mais alta
        maxAttempts: 5,
        maxWatchTime: 60000, // Mais tempo para refinamento
        enableHybrid: true
      });

      const qualityIndicator = getQualityIndicator(improvedResult);
      
      setState(prev => ({
        ...prev,
        result: improvedResult,
        isRefining: false,
        qualityIndicator,
        attempts: prev.attempts + 1
      }));

      toast({
        title: 'Precisão melhorada',
        description: getQualityMessage(improvedResult),
        variant: qualityIndicator === 'excellent' ? 'default' : 'default'
      });

      return improvedResult;

    } catch (error) {
      setState(prev => ({ ...prev, isRefining: false }));
      
      toast({
        title: 'Não foi possível melhorar',
        description: 'Tente novamente ou mova-se para uma área aberta',
        variant: 'destructive'
      });
      
      return state.result;
    }
  }, [state.result, state.isRefining, toast, getQualityIndicator, getQualityMessage]);

  const reset = useCallback(() => {
    setState({
      isLoading: false,
      result: null,
      error: null,
      attempts: 0,
      isRefining: false,
      qualityIndicator: null
    });
    serviceRef.current.cleanup();
  }, []);

  return {
    // Estado
    isLoading: state.isLoading,
    isRefining: state.isRefining,
    result: state.result,
    error: state.error,
    attempts: state.attempts,
    qualityIndicator: state.qualityIndicator,
    
    // Ações
    getCurrentLocation,
    improveAccuracy,
    reset,
    
    // Utilitários
    getQualityMessage: state.result ? () => getQualityMessage(state.result!) : () => '',
    canImprove: state.result && state.qualityIndicator !== 'excellent' && !state.isRefining,
    
    // Métricas
    accuracy: state.result?.accuracy || null,
    confidence: state.result?.confidence || null,
    source: state.result?.source || null,
    address: state.result?.address || null
  };
}
