
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Users, Calendar, Cake } from 'lucide-react';
import { useProfile } from '@/hooks/useProfile';

interface Student {
  id: string;
  full_name: string;
  email: string;
  birth_date?: string;
  user_type: string;
}

interface StudentManagerProps {
  students: Student[];
  loading: boolean;
}

const StudentManager: React.FC<StudentManagerProps> = ({ students, loading }) => {
  const { profile } = useProfile();

  // Calcular idade
  const calculateAge = (birthDateString: string): number | null => {
    if (!birthDateString) return null;
    
    const birth = new Date(birthDateString);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Gerenciar Estudantes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Carregando estudantes...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!students || students.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Gerenciar Estudantes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Nenhum estudante encontrado
            </h3>
            <p className="text-sm text-gray-600">
              Você ainda não tem estudantes vinculados à sua conta.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Gerenciar Estudantes ({students.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {students.map((student) => {
            const age = student.birth_date ? calculateAge(student.birth_date) : null;
            const isMinor = age !== null && age < 18;

            return (
              <div 
                key={student.id} 
                className="p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">
                      {student.full_name || student.email}
                    </h4>
                    <p className="text-sm text-gray-600">{student.email}</p>
                    
                    {student.birth_date && age !== null && (
                      <div className="flex items-center gap-2 mt-2">
                        <Calendar className="h-4 w-4 text-blue-600" />
                        <span className="text-sm text-blue-800">
                          {age} anos
                        </span>
                        {isMinor && (
                          <span className="px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded-full">
                            Menor de idade
                          </span>
                        )}
                      </div>
                    )}
                    
                    {!student.birth_date && (
                      <div className="flex items-center gap-2 mt-2">
                        <Cake className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-500">
                          Data de nascimento não informada
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default StudentManager;
