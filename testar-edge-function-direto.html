<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Teste Direto da Edge Function</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-card { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        button { 
            background: #007bff; color: white; padding: 10px 20px; 
            border: none; border-radius: 4px; cursor: pointer; margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log { 
            background: #f8f9fa; padding: 10px; margin: 10px 0; 
            border-left: 3px solid #007bff; font-family: monospace; 
            font-size: 12px; max-height: 300px; overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Teste Direto: Edge Function send-password-reset</h1>
        
        <div class="test-card warning">
            <h3>⚠️ PROBLEMA CONFIRMADO</h3>
            <p>Franklin continua recebendo template genérico do Supabase Auth.</p>
            <p><strong>Hipótese:</strong> Nossa Edge Function não está sendo chamada ou está falhando.</p>
        </div>

        <div class="test-card">
            <h3>🧪 Teste Direto da Edge Function</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            
            <button onclick="testarEdgeFunctionDireto()">🚀 Testar Edge Function Diretamente</button>
            <button onclick="verificarVariaveis()">🔧 Verificar Variáveis</button>
            <button onclick="simularFormulario()">📝 Simular Formulário</button>
            
            <div id="resultado"></div>
        </div>

        <div class="test-card">
            <h3>📊 Diagnóstico Completo</h3>
            <div id="diagnostico">
                <p>⏳ Aguardando teste...</p>
            </div>
        </div>

        <div id="logs" class="log"></div>
    </div>

    <script>
        function log(message, type = 'INFO') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            logElement.innerHTML += `[${timestamp}] ${type}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        async function testarEdgeFunctionDireto() {
            const resultDiv = document.getElementById('resultado');
            const diagnosticoDiv = document.getElementById('diagnostico');
            
            try {
                log('🔍 Testando Edge Function send-password-reset diretamente...');
                
                const supabaseUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co';
                const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTY4OTEzNzQsImV4cCI6MjAzMjQ2NzM3NH0.w5HVgP7mlnE1w_sWbSO7CKkOBmGtT3xJPo4rPwzJ4L8';
                
                const response = await fetch(`${supabaseUrl}/functions/v1/send-password-reset`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${anonKey}`,
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>'
                    })
                });
                
                log(`📡 Response status: ${response.status}`);
                
                const responseText = await response.text();
                log(`📄 Response body: ${responseText}`);
                
                if (response.ok) {
                    const data = JSON.parse(responseText);
                    
                    resultDiv.innerHTML = `
                        <div class="test-card success">
                            <h4>✅ Edge Function Funcionando!</h4>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Resposta:</strong> ${data.message}</p>
                            <p><strong>Fallback:</strong> ${data.fallback ? 'Sim (Resend)' : 'Não (Supabase Auth)'}</p>
                        </div>
                    `;
                    
                    diagnosticoDiv.innerHTML = `
                        <p>✅ <strong>Edge Function:</strong> Funcionando</p>
                        <p>${data.fallback ? '⚠️' : '✅'} <strong>Template:</strong> ${data.fallback ? 'Resend (bonito)' : 'Supabase Auth (genérico)'}</p>
                        <p>📧 <strong>Franklin deve verificar:</strong> Email recebido</p>
                    `;
                    
                    log('SUCCESS: Edge Function respondeu com sucesso!', 'SUCCESS');
                    
                } else {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }
                
            } catch (error) {
                log(`ERROR: ${error.message}`, 'ERROR');
                
                resultDiv.innerHTML = `
                    <div class="test-card error">
                        <h4>❌ Edge Function com Problema</h4>
                        <p><strong>Erro:</strong> ${error.message}</p>
                        <p>Isso explica por que Franklin está recebendo template genérico!</p>
                    </div>
                `;
                
                diagnosticoDiv.innerHTML = `
                    <p>❌ <strong>Edge Function:</strong> Falhando</p>
                    <p>⚠️ <strong>Template:</strong> Fallback para Supabase Auth (genérico)</p>
                    <p>🔧 <strong>Ação:</strong> Corrigir Edge Function</p>
                `;
            }
        }

        async function verificarVariaveis() {
            log('🔧 Verificando configuração de variáveis...');
            log('✅ VITE_RESEND_API_KEY: Existe no dashboard Supabase');
            log('✅ Correção aplicada: Código usa VITE_RESEND_API_KEY');
            log('⏳ Testando se deploy foi aplicado...');
            
            // Fazer a chamada para verificar se está usando a variável correta
            await testarEdgeFunctionDireto();
        }

        async function simularFormulario() {
            log('📝 Simulando formulário de recuperação...');
            log('🔄 Abrindo página de login para teste...');
            
            window.open('https://sistema-monitore.com.br/login', '_blank');
            
            log('✅ Página aberta. Franklin deve:');
            log('1. Clicar em "Esqueci minha senha"');
            log('2. <NAME_EMAIL>');
            log('3. Verificar console do navegador para logs');
            log('4. Observar se email que chega é bonito ou genérico');
        }

        // Auto-executar log inicial
        window.onload = function() {
            log('🔍 Iniciando diagnóstico da Edge Function...');
            log('❌ Problema: Franklin continua recebendo email genérico');
            log('🎯 Objetivo: Identificar por que Edge Function não está funcionando');
        };
    </script>
</body>
</html> 