
/**
 * Tipos de usuário suportados pela aplicação
 */
export type UserType = 'student' | 'parent' | 'developer' | 'admin';

/**
 * Verifica se um tipo de usuário é válido
 * @param type Tipo a ser verificado
 * @returns True se for um tipo válido
 */
export function isValidUserType(type: string): type is UserType {
  return ['student', 'parent', 'developer', 'admin'].includes(type);
}

/**
 * Obtém o tipo de usuário a partir dos metadados
 * @param userMetadata Metadados do usuário
 * @returns Tipo do usuário ou 'student' como padrão
 */
export function getUserTypeFromMetadata(userMetadata?: Record<string, any>): UserType {
  const userType = userMetadata?.user_type;
  return isValidUserType(userType) ? userType : 'student';
}

/**
 * Mapeamento de rotas padrão por tipo de usuário
 */
export const DEFAULT_ROUTES: Record<UserType, string> = {
  'student': '/student-dashboard',
  'parent': '/parent-dashboard',
  'developer': '/dev-dashboard',
  'admin': '/admin-dashboard'
};

/**
 * Obtém a rota padrão para um tipo de usuário
 * @param userType Tipo do usuário
 * @returns Rota padrão
 */
export function getDefaultRouteForUserType(userType: UserType): string {
  return DEFAULT_ROUTES[userType] || '/dashboard';
}

/**
 * Verifica se um usuário tem permissões administrativas
 * @param userType Tipo do usuário
 * @returns True se tiver permissões administrativas
 */
export function hasAdminPermissions(userType: UserType): boolean {
  return userType === 'admin' || userType === 'developer';
}

/**
 * Obtém o label amigável para o tipo de usuário
 * @param userType Tipo do usuário
 * @returns Label em português
 */
export function getUserTypeLabel(userType: UserType): string {
  const labels: Record<UserType, string> = {
    'student': 'Estudante',
    'parent': 'Responsável',
    'developer': 'Desenvolvedor',
    'admin': 'Administrador'
  };
  return labels[userType] || 'Usuário';
}
