
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Trash2, AlertTriangle } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface DeleteAccountCardProps {
  onDeleteAccount: () => void;
  isDeleting: boolean;
}

const DeleteAccountCard: React.FC<DeleteAccountCardProps> = ({ onDeleteAccount, isDeleting }) => {
  const { t } = useTranslation();
  return (
    <Card data-cy="delete-account-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <Trash2 className="h-5 w-5" />
          {t('accountDeletion.title')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-foreground dark:text-white mb-4">
          {t('accountDeletion.intro')}
        </p>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" className="w-full dark:bg-red-700 dark:text-white" data-cy="delete-account-button">
              {t('accountDeletion.button')}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                {t('accountDeletion.confirmTitle')}
              </AlertDialogTitle>
              <div className="space-y-2 text-sm text-muted-foreground">
                <AlertDialogDescription>
                  {t('accountDeletion.intro')}
                </AlertDialogDescription>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  {(t('accountDeletion.confirmList', { returnObjects: true }) as string[]).map((item: string) => (
                    <li key={item}>{item}</li>
                  ))}
                </ul>
                <strong className="block mt-3">{t('accountDeletion.intro')}</strong>
              </div>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t('accountDeletion.cancel')}</AlertDialogCancel>
              <AlertDialogAction
                data-cy="confirm-delete-account-button"
                onClick={onDeleteAccount}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isDeleting ? t('accountDeletion.processing') : t('accountDeletion.confirmButton')}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
};

export default DeleteAccountCard;
