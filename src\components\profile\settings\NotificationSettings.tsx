
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Bell, MapPin, Wifi, Battery } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export const NotificationSettings: React.FC = () => {
  const { t } = useTranslation();
  const [settings, setSettings] = useState({
    locationAlerts: true,
    networkStatus: true,
    batteryWarnings: true,
    emailNotifications: true,
    pushNotifications: true
  });

  const updateSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          {t('settings.notifications.title', 'Notifications')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MapPin className="h-4 w-4 text-blue-500" />
            <div>
              <Label>{t('settings.notifications.locationAlerts.label', 'Location Alerts')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.notifications.locationAlerts.description', 'Notifications about location sharing')}
              </p>
            </div>
          </div>
          <Switch
            checked={settings.locationAlerts}
            onCheckedChange={() => updateSetting('locationAlerts')}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Wifi className="h-4 w-4 text-green-500" />
            <div>
              <Label>{t('settings.notifications.networkStatus.label', 'Network Status')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.notifications.networkStatus.description', 'Alerts about offline/online connection')}
              </p>
            </div>
          </div>
          <Switch
            checked={settings.networkStatus}
            onCheckedChange={() => updateSetting('networkStatus')}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Battery className="h-4 w-4 text-orange-500" />
            <div>
              <Label>{t('settings.notifications.batteryWarnings.label', 'Battery Warnings')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.notifications.batteryWarnings.description', 'Alerts about low device battery')}
              </p>
            </div>
          </div>
          <Switch
            checked={settings.batteryWarnings}
            onCheckedChange={() => updateSetting('batteryWarnings')}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Bell className="h-4 w-4 text-purple-500" />
            <div>
              <Label>{t('settings.notifications.pushNotifications.label', 'Push Notifications')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.notifications.pushNotifications.description', 'Browser notifications')}
              </p>
            </div>
          </div>
          <Switch
            checked={settings.pushNotifications}
            onCheckedChange={() => updateSetting('pushNotifications')}
          />
        </div>
      </CardContent>
    </Card>
  );
};
