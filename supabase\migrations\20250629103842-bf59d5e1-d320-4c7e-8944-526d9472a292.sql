
-- CORREÇÃO CRÍTICA: Schema Auth para resolver erro de login (CORRIGIDA)
-- Corrige o problema "sql: Scan error on column index 24, name 'phone_change_token': converting NULL to string is unsupported"

-- 1. VERIFICAR E CORRIGIR COLUNAS PROBLEMÁTICAS NA TABELA auth.users
-- Atualizar colunas que podem estar causando problemas de conversão NULL
UPDATE auth.users 
SET phone_change_token = '' 
WHERE phone_change_token IS NULL;

UPDATE auth.users 
SET email_change_token_new = '' 
WHERE email_change_token_new IS NULL;

UPDATE auth.users 
SET email_change_token_current = '' 
WHERE email_change_token_current IS NULL;

-- 2. ALTERAR COLUNAS PARA TER VALORES PADRÃO ADEQUADOS
-- Isso previne problemas futuros com conversões NULL
ALTER TABLE auth.users 
ALTER COLUMN phone_change_token SET DEFAULT '';

ALTER TABLE auth.users 
ALTER COLUMN email_change_token_new SET DEFAULT '';

ALTER TABLE auth.users 
ALTER COLUMN email_change_token_current SET DEFAULT '';

-- 3. VERIFICAR E CORRIGIR OUTROS CAMPOS PROBLEMÁTICOS
UPDATE auth.users 
SET phone_change = '' 
WHERE phone_change IS NULL;

UPDATE auth.users 
SET email_change = '' 
WHERE email_change IS NULL;

ALTER TABLE auth.users 
ALTER COLUMN phone_change SET DEFAULT '';

ALTER TABLE auth.users 
ALTER COLUMN email_change SET DEFAULT '';

-- 4. VERIFICAR INTEGRIDADE DOS METADADOS (usando apenas colunas que existem)
-- Ensure all users have proper metadata structure
UPDATE auth.users 
SET raw_user_meta_data = COALESCE(raw_user_meta_data, '{}'::jsonb)
WHERE raw_user_meta_data IS NULL;

-- 5. LIMPAR SESSÕES PROBLEMÁTICAS SE EXISTIREM
-- Remove sessões que podem estar corrompidas
DELETE FROM auth.sessions 
WHERE updated_at < NOW() - INTERVAL '24 hours';

-- 6. REGISTRAR A CORREÇÃO NOS LOGS
INSERT INTO public.auth_logs (
    event_type,
    metadata,
    occurred_at
) VALUES (
    'auth_schema_fix_applied_corrected',
    jsonb_build_object(
        'description', 'Correção do schema auth.users para resolver problema de login - versão corrigida',
        'timestamp', NOW(),
        'issue', 'phone_change_token NULL conversion error',
        'solution', 'Updated NULL values to empty strings and set defaults'
    ),
    NOW()
);
