import React from 'react'
import { Wifi, WifiOff, Alert<PERSON>riangle } from 'lucide-react'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'

export function NetworkStatus() {
  const { isOnline, isSlowConnection } = useNetworkStatus()

  return (
    <div className="flex items-center gap-1 text-xs">
      {isOnline ? (
        isSlowConnection ? (
          <AlertTriangle className="w-3 h-3 text-yellow-500" />
        ) : (
          <Wifi className="w-3 h-3 text-green-600" />
        )
      ) : (
        <WifiOff className="w-3 h-3 text-red-600" />
      )}
      <span className="hidden sm:inline">
        {isOnline ? (isSlowConnection ? 'Conexão lenta' : 'Online') : 'Offline'}
      </span>
    </div>
  )
}
