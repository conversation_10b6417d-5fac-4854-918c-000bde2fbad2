# 🚨 Guia de Resolução: Problema do Expo Mobile

## 🔍 Análise do Problema

### Erro Principal:
```
Unable to resolve "../../App" from "node_modules\expo\AppEntry.js"
Unable to resolve module ../src/App from mobile/App.tsx
```

### Causa Raiz:
O Expo está tentando importar arquivos que não existem ou estão no local errado devido a configurações conflitantes.

## 📋 Diagnóstico Completo

### 1. **Problema de Configuração do Expo**
- O `AppEntry.js` do Expo está tentando importar `../../App` (que seria `src/App.tsx`)
- O `mobile/App.tsx` está tentando importar `../src/App` (que não existe para mobile)

### 2. **Estrutura do Monorepo**
```
locate-family-connect/
├── src/           ← App web (React + Vite)
├── mobile/        ← App mobile (React Native + Expo)
├── web/           ← App web alternativo
└── shared/        ← Código compartilhado
```

### 3. **Conflitos Identificados**
- Arquivo `App.tsx` na raiz (já removido)
- Import incorreto no `mobile/App.tsx`
- Configuração do Expo tentando acessar arquivos web

## 🛠️ Solução Passo a Passo

### Passo 1: Verificar mobile/App.tsx
```typescript
// ❌ ERRADO (atual)
import App from '../src/App'
export default App

// ✅ CORRETO (deve ser)
// O arquivo deve conter o código React Native completo
```

### Passo 2: Corrigir mobile/App.tsx
O arquivo `mobile/App.tsx` deve conter o código React Native completo, não apenas um import do web app.

### Passo 3: Verificar Configurações
1. **app.config.js**: `entryPoint: "./App.tsx"` ✅
2. **metro.config.js**: Configuração de resolução de módulos
3. **babel.config.js**: Configuração de aliases

### Passo 4: Limpar Cache e Reinstalar
```bash
# Parar todos os processos
taskkill /F /IM node.exe

# Limpar cache do Expo
cd mobile
npx expo start --clear --reset-cache

# OU usar workspace (recomendado)
cd ..
npm run dev:mobile
```

## 🎯 Solução Recomendada

### Opção 1: Usar Workspace (RECOMENDADO)
```bash
# Da pasta raiz
npm run dev:mobile
```

### Opção 2: Executar da Pasta Mobile
```bash
# Da pasta mobile
cd mobile
npx expo start --clear
```

## 🔧 Correções Necessárias

### 1. Corrigir mobile/App.tsx
O arquivo deve conter código React Native, não import do web app.

### 2. Verificar Dependências
```bash
# Na pasta mobile
npm install
```

### 3. Verificar Configurações
- `app.config.js`: entryPoint correto
- `metro.config.js`: resolução de módulos
- `babel.config.js`: aliases

## 📋 Checklist de Resolução

- [ ] **Parar todos os processos** node.exe
- [ ] **Verificar mobile/App.tsx** - deve conter código React Native
- [ ] **Limpar cache** do Expo
- [ ] **Reinstalar dependências** se necessário
- [ ] **Executar via workspace** ou da pasta mobile
- [ ] **Testar** se o app carrega corretamente

## 🚨 Comandos de Emergência

### Parar Processos:
```bash
taskkill /F /IM node.exe
```

### Limpar Cache:
```bash
cd mobile
npx expo start --clear --reset-cache
```

### Reinstalar Dependências:
```bash
cd mobile
rm -rf node_modules
npm install
```

## 📚 Referências

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Metro Config](https://docs.expo.dev/guides/customizing-metro/)
- [Monorepo Workspaces](https://docs.npmjs.com/cli/v7/using-npm/workspaces)

## 🎯 Resultado Esperado

Após as correções:
1. ✅ Expo inicia sem erros
2. ✅ QR Code aparece
3. ✅ App carrega no dispositivo
4. ✅ Login funciona
5. ✅ Navegação funciona

---

**Status**: Aguardando implementação das correções
**Prioridade**: CRÍTICA
**Complexidade**: MÉDIA
**Tempo Estimado**: 30-60 minutos 