import { BaseService } from '../base/BaseService';
import { UserProfile } from '@/types/database';

/**
 * Service responsible for managing student profile data
 */
export class StudentProfileService extends BaseService {
  /**
   * Get student profile by user ID
   */
  async getStudentProfile(userId: string): Promise<UserProfile | null> {
    try {
      // Check auth
      await this.getCurrentUser();
      
      const { data, error } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (error) {
        console.error('[StudentProfileService] Error fetching profile:', error);
        this.showError('Não foi possível buscar o perfil do estudante');
        return null;
      }
      
      // Convert database profile to UserProfile type
      return {
        id: data.id.toString(), // Convert number to string
        user_id: data.user_id || '',
        full_name: data.full_name || '',
        email: data.email || '',
        phone: data.phone || undefined,
        user_type: data.user_type || '',
        created_at: data.created_at || '',
        updated_at: data.updated_at || ''
      } as UserProfile;
    } catch (error: any) {
      console.error('[StudentProfileService] Error in getStudentProfile:', error);
      this.showError('Erro ao acessar perfil do estudante');
      return null;
    }
  }
  
  /**
   * Update student profile
   */
  async updateStudentProfile(userId: string, updates: Partial<UserProfile>): Promise<boolean> {
    try {
      // Check auth
      await this.getCurrentUser();
      
      // Convert UserProfile updates to database format
      const dbUpdates: any = { ...updates };
      if (dbUpdates.id && typeof dbUpdates.id === 'string') {
        dbUpdates.id = parseInt(dbUpdates.id, 10);
      }
      
      const { error } = await this.supabase
        .from('profiles')
        .update(dbUpdates)
        .eq('user_id', userId);
      
      if (error) {
        console.error('[StudentProfileService] Error updating profile:', error);
        this.showError('Não foi possível atualizar o perfil');
        return false;
      }
      
      this.showSuccess('Perfil atualizado com sucesso');
      return true;
    } catch (error: any) {
      console.error('[StudentProfileService] Error in updateStudentProfile:', error);
      this.showError('Erro ao atualizar perfil');
      return false;
    }
  }

  /**
   * Fetch students for the current guardian com timeout e fallbacks
   */
  async getStudentsForGuardian(): Promise<any[]> {
    try {
      console.log('[StudentProfileService] Starting student fetch for guardian');
      
      // Get current user
      const user = await this.getCurrentUser();
      console.log('[StudentProfileService] Authenticated user:', user.id, user.email);
      
      // Timeout curto para evitar loading infinito
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Student fetch timeout')), 2000);
      });

      // Tentar método direto primeiro (mais confiável)
      try {
        console.log('[StudentProfileService] Using direct query method');
        
        // 1º passo: buscar IDs na tabela de relacionamento
        const relPromise = this.supabase
          .from('student_guardian_relationships')
          .select('student_id')
          .eq('guardian_id', user.id);

        const { data: relationshipsData, error: relationshipsError } = await Promise.race([
          relPromise,
          timeoutPromise
        ]) as any;

        if (relationshipsError) {
          console.warn('[StudentProfileService] Relationship query error:', relationshipsError);
          throw relationshipsError;
        }

        if (relationshipsData && relationshipsData.length > 0) {
          const studentIds = relationshipsData.map((r: any) => r.student_id);

          // 2º passo: buscar perfis filtrando por user_id
          const { data: profiles, error: profileError } = await this.supabase
            .from('profiles')
            .select('user_id, full_name, email, phone, created_at')
            .in('user_id', studentIds)
            .eq('user_type', 'student');

          if (profileError) {
            console.warn('[StudentProfileService] Profile query error:', profileError);
            throw profileError;
          }

          const students = profiles?.map((profile: any) => ({
            id: profile.user_id,
            name: profile.full_name || profile.email || 'Estudante',
            email: profile.email || '',
            created_at: profile.created_at || '',
            status: 'active',
            avatar_url: null,
            phone: profile.phone || null,
          })) || [];

          console.log('[StudentProfileService] Found students via profiles query:', students.length);
          return students;
        }
      } catch (directError) {
        console.warn('[StudentProfileService] Direct method failed, trying RPC:', directError);
        
        // Fallback: Tentar RPC (pode falhar por auth.email())
        try {
          const rpcPromise = this.supabase.rpc('get_guardian_students');
          const { data, error } = await Promise.race([rpcPromise, timeoutPromise]) as any;
          
          if (!error && data && data.length > 0) {
            console.log('[StudentProfileService] Found students via RPC:', data.length);
            const students = data.map((item: any) => ({
              id: item.student_id,
              name: item.student_name || item.student_email || '',
              email: item.student_email || '',
              created_at: '',
              status: 'active',
              avatar_url: null,
              phone: null,
            }));
            return students;
          }
        } catch (rpcError) {
          console.warn('[StudentProfileService] RPC also failed:', rpcError);
        }
      }
      
      // Se chegou aqui, sem estudantes encontrados
      console.log('[StudentProfileService] No students found, returning empty array');
      return [];
      
    } catch (error: any) {
      console.error('[StudentProfileService] Error fetching students:', error);
      // Sempre retornar array vazio para não quebrar a UI
      return [];
    }
  }

  /**
   * Alias for getStudentsForGuardian to support hooks
   */
  async getStudentsByParent(): Promise<any[]> {
    return this.getStudentsForGuardian();
  }
}

export const studentProfileService = new StudentProfileService();
