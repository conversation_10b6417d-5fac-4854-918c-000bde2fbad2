
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User, Phone, Mail, FileText, Loader2 } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { useProfileQuery } from '@/hooks/queries/useProfileQuery';
import { useUser } from '@/contexts/UnifiedAuthContext';
import ErrorFallback from '@/components/common/ErrorFallback';
import { useTranslation } from 'react-i18next';

const ProfileEditFormOptimized: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useUser();
  const { profile, isLoading, updateProfile, isUpdating, error, refetch } = useProfileQuery();
  const [isEditing, setIsEditing] = useState(false);
  
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    cpf: '',
    user_type: ''
  });

  useEffect(() => {
    console.log('[ProfileEditFormOptimized] Effect triggered with:', { profile, user, isLoading });
    
    if (profile) {
      console.log('[ProfileEditFormOptimized] Profile loaded:', profile);
      setFormData({
        full_name: profile.full_name || '',
        email: profile.email || user?.email || '',
        phone: profile.phone || '',
        cpf: profile.cpf || '',
        user_type: profile.user_type || ''
      });
    } else if (user && !isLoading) {
      // Fallback to user metadata if profile not loaded yet
      console.log('[ProfileEditFormOptimized] Using user metadata as fallback');
      setFormData({
        full_name: user.user_metadata?.full_name || '',
        email: user.email || '',
        phone: user.user_metadata?.phone || '',
        cpf: user.user_metadata?.cpf || '',
        user_type: user.user_metadata?.user_type || ''
      });
    }
  }, [profile, user, isLoading]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    if (!formData.full_name || !formData.email || !formData.user_type) {
      console.warn('[ProfileEditFormOptimized] Missing required fields');
      return;
    }

    console.log('[ProfileEditFormOptimized] Saving profile data:', formData);
    updateProfile(formData, {
      onSuccess: () => {
        setIsEditing(false);
        console.log('[ProfileEditFormOptimized] Profile saved successfully');
      }
    });
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        email: profile.email || user?.email || '',
        phone: profile.phone || '',
        cpf: profile.cpf || '',
        user_type: profile.user_type || ''
      });
    }
    setIsEditing(false);
  };

  // Mostrar erro se houver
  if (error && !profile && !isLoading) {
    return (
      <ErrorFallback
        title={t('profile.errorLoading')}
        message={t('profile.errorLoadingMessage')}
        onRetry={refetch}
      />
    );
  }

  // Loading skeleton
  if (isLoading && !profile) {
    return (
      <Card className="bg-transparent border-0 shadow-none">
        <CardHeader className="bg-transparent border-0 shadow-none">
          <CardTitle className="flex items-center gap-2 bg-transparent">
            <User className="h-5 w-5" />
            {t('profile.personalInfo')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 bg-transparent border-0 shadow-none">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-transparent border-0 shadow-none">
      <CardHeader className="bg-transparent border-0 shadow-none">
        <CardTitle className="flex items-center justify-between bg-transparent">
          <span className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('profile.personalInfo')}
          </span>
          {!isEditing && (
            <Button variant="outline" onClick={() => setIsEditing(true)}>{t('profile.edit')}</Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 bg-transparent border-0 shadow-none">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="full_name">{t('profile.fullName')} *</Label>
            <Input
              id="full_name"
              value={formData.full_name}
              onChange={(e) => handleInputChange('full_name', e.target.value)}
              disabled={!isEditing}
              placeholder={t('profile.fullNamePlaceholder')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">{t('profile.email')} *</Label>
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-500" />
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={!isEditing}
                placeholder={t('profile.emailPlaceholder')}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">{t('profile.phone')}</Label>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-gray-500" />
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                disabled={!isEditing}
                placeholder={t('profile.phonePlaceholder')}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="cpf">{t('profile.cpf')}</Label>
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-500" />
              <Input
                id="cpf"
                value={formData.cpf}
                onChange={(e) => handleInputChange('cpf', e.target.value)}
                disabled={!isEditing}
                placeholder={t('profile.cpfPlaceholder')}
              />
            </div>
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="user_type">{t('profile.userType')} *</Label>
            <Select
              value={formData.user_type}
              onValueChange={(value) => handleInputChange('user_type', value)}
              disabled={!isEditing}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('profile.userTypePlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="student">{t('profile.student')}</SelectItem>
                <SelectItem value="parent">{t('profile.parent')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {isEditing && (
          <div className="flex gap-2 pt-4">
            <Button
              onClick={handleSave}
              disabled={isUpdating || !formData.full_name || !formData.email || !formData.user_type}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t('profile.saving')}
                </>
              ) : (
                t('profile.saveChanges')
              )}
            </Button>
            <Button variant="outline" onClick={handleCancel} disabled={isUpdating}>
              {t('profile.cancel')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProfileEditFormOptimized;
