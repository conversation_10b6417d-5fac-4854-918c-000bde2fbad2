-- CORREÇÃO EMERGENCIAL: Remover TODAS as referências à tabela guardians

-- 1. Remover função problemática
DROP FUNCTION IF EXISTS save_student_location(double precision, double precision, boolean);

-- 2. Desabilitar temporariamente RLS
ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;

-- 3. Remover TODAS as policies da tabela locations
DO \$\$ 
DECLARE
    pol_record RECORD;
BEGIN
    FOR pol_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'locations' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(pol_record.policyname) || ' ON public.locations';
    END LOOP;
END \$\$;

-- 4. Remover TODOS os triggers da tabela locations
DO \$\$ 
DECLARE
    trig_record RECORD;
BEGIN
    FOR trig_record IN 
        SELECT trigger_name 
        FROM information_schema.triggers 
        WHERE event_object_table = 'locations' AND event_object_schema = 'public'
    LOOP
        EXECUTE 'DROP TRIGGER IF EXISTS ' || quote_ident(trig_record.trigger_name) || ' ON public.locations';
    END LOOP;
END \$\$;

-- 5. Criar função completamente limpa
CREATE OR REPLACE FUNCTION save_student_location(
  p_latitude double precision,
  p_longitude double precision,
  p_shared_with_guardians boolean DEFAULT true
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS \$\$
DECLARE
  current_user_id uuid;
  new_location_id uuid;
BEGIN
  current_user_id := auth.uid();
  
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;
  
  INSERT INTO public.locations (
    user_id,
    latitude,
    longitude,
    shared_with_guardians
  ) VALUES (
    current_user_id,
    p_latitude,
    p_longitude,
    p_shared_with_guardians
  )
  RETURNING id INTO new_location_id;
  
  RETURN new_location_id;
END;
\$\$;

-- 6. Permissões amplas temporariamente
GRANT ALL ON TABLE public.locations TO authenticated;
GRANT EXECUTE ON FUNCTION save_student_location TO authenticated;

-- 7. Reabilitar RLS com policy simples
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- 8. Policy super simples
CREATE POLICY \
Allow
all
for
authenticated
users\ ON public.locations
  FOR ALL USING (auth.uid() IS NOT NULL);

-- 9. Verificação final
SELECT 'CORREÇÃO EMERGENCIAL APLICADA COM SUCESSO!' as status;
