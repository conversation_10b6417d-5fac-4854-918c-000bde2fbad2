import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent } from '../components/ui/card';
import StudentsListContainer from '../components/student/StudentsListContainer';
import { StudentMapTabs } from '../components/dashboard/StudentMapTabs';
import GuardianInfoPanel from '../components/guardian/GuardianInfoPanel';
import { useParentDashboard } from '../hooks/useParentDashboard';
import { LoadingSpinner } from '../components/ui/loading-spinner';
import { Alert, AlertDescription } from '../components/ui/alert';
import { AlertCircle } from 'lucide-react';

function ParentDashboard() {
  const {
    selectedStudent,
    locations,
    locationError,
    isLoadingLocations,
    selectedLocation,
    activeTab,
    user,
    signOut,
    handleGoToProfile,
    handleSelectStudent,
    handleLocationSelect,
    handleClearSelection,
    setActiveTab,
    handleStudentAdded,
    isLoadingStudents,
    studentsError
  } = useParentDashboard();

  const { t } = useTranslation();

  const userDisplayName = React.useMemo(() => {
    return (user as any)?.user_metadata?.full_name || user?.email || t('dashboard.parent.defaultName');
  }, [user, t]);

  if (isLoadingStudents) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (studentsError) {
    return (
      <div className="w-full max-w-screen-xl mx-auto px-4 py-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t('dashboard.parent.errorLoading')}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="content-overlay rounded-xl bg-transparent p-4 md:p-8 w-full max-w-screen-xl mx-auto">
        {/* Header Section with Guardian Info */}
        <div className="mb-6">
          <Card className="glass-card">
            <CardContent className="p-6">
              <GuardianInfoPanel
                userFullName={userDisplayName}
                email={user?.email || null}
                onStudentAdded={handleStudentAdded}
              />
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Students List - Left Column */}
          <div className="lg:col-span-1">
            <Card className="glass-card h-fit">
              <CardContent className="p-6">
                <StudentsListContainer
                  onSelectStudent={handleSelectStudent}
                  selectedStudent={selectedStudent}
                />
              </CardContent>
            </Card>
          </div>

          {/* Map and History - Right Column (spans 2 columns) */}
          <div className="lg:col-span-2">
            <Card className="glass-card">
              <CardContent className="p-6">
                <StudentMapTabs
                  selectedStudent={selectedStudent}
                  locations={locations}
                  isLoadingLocations={isLoadingLocations}
                  locationError={locationError}
                  selectedLocation={selectedLocation}
                  activeTab={activeTab}
                  userName={userDisplayName}
                  onTabChange={setActiveTab}
                  onLocationSelect={handleLocationSelect}
                  onClearSelection={handleClearSelection}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ParentDashboard;
