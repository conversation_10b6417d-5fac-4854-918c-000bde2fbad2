import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslation } from 'react-i18next';
import { MapStyle, useMapStyle } from '@/hooks/useMapStyle';
import { cn } from '@/lib/utils';

interface MapStyleSelectorProps {
  className?: string;
}

const options: { value: MapStyle; label: string }[] = [
  { value: 'streets-v12', label: 'Ruas' },
  { value: 'satellite-streets-v12', label: 'Satélite' },
  { value: 'light-v11', label: 'Claro' },
  { value: 'dark-v11', label: 'Escuro' },
  { value: 'outdoors-v12', label: 'Ar Livre' },
];

export const MapStyleSelector: React.FC<MapStyleSelectorProps> = ({ className }) => {
  const { t } = useTranslation();
  const [style, setStyle] = useMapStyle();

  return (
    <div className={cn('w-36 text-sm', className)}>
      <Select value={style} onValueChange={(value) => setStyle(value as any)}>
        <SelectTrigger>
          <SelectValue placeholder={t('map.style.label', 'Map Style')} />
        </SelectTrigger>
        <SelectContent>
          {options.map((opt) => (
            <SelectItem key={opt.value} value={opt.value}>
              {t(`map.style.${opt.value}`, opt.label)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default MapStyleSelector;
