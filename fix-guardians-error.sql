-- CORREÇÃO URGENTE: Função save_student_location sem referências a guardians
DROP FUNCTION IF EXISTS save_student_location(double precision, double precision, boolean);

CREATE OR REPLACE FUNCTION save_student_location(
  p_latitude double precision,
  p_longitude double precision,
  p_shared_with_guardians boolean DEFAULT true
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $\$
DECLARE
  current_user_id uuid;
  new_location_id uuid;
BEGIN
  current_user_id := auth.uid();
  
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;
  
  INSERT INTO public.locations (
    user_id,
    latitude,
    longitude,
    shared_with_guardians
  ) VALUES (
    current_user_id,
    p_latitude,
    p_longitude,
    p_shared_with_guardians
  )
  RETURNING id INTO new_location_id;
  
  RETURN new_location_id;
END;
$\$;

GRANT EXECUTE ON FUNCTION save_student_location TO authenticated;
