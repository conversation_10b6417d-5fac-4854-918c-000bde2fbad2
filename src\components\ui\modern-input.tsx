import React from 'react';
import { cn } from '@/lib/utils';

export interface ModernInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: 'default' | 'refined' | 'glass';
  icon?: React.ReactNode;
}

const ModernInput = React.forwardRef<HTMLInputElement, ModernInputProps>(
  ({ className, type, variant = 'default', icon, ...props }, ref) => {
    const variants = {
      default: 'border-input bg-background',
      refined: 'refined-input',
      glass: 'bg-white/10 border-white/20 backdrop-blur-md text-foreground placeholder:text-muted-foreground'
    };

    const inputElement = (
      <input
        type={type}
        className={cn(
          'flex h-11 w-full rounded-xl px-4 py-3 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300',
          variants[variant],
          icon ? 'pl-12' : '',
          className
        )}
        ref={ref}
        {...props}
      />
    );

    if (icon) {
      return (
        <div className="relative">
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground">
            {icon}
          </div>
          {inputElement}
        </div>
      );
    }

    return inputElement;
  }
);

ModernInput.displayName = 'ModernInput';

export { ModernInput };