
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Home, User, LogOut, LifeBuoy } from "lucide-react";
import { useUser } from "@/contexts/UnifiedAuthContext";
import { useResponsiveLayout } from "@/hooks/useResponsiveLayout";
import { cn } from "@/lib/utils";

interface MobileNavigationProps {
  userType: string;
  dashboardLink: string;
}

export const MobileNavigation = ({ userType, dashboardLink }: MobileNavigationProps) => {
  const location = useLocation();
  const { signOut } = useUser();
  const { device, textSizes, buttonStyles } = useResponsiveLayout();
  
  // Only show on mobile and tablet
  if (device.type !== 'mobile' && device.type !== 'tablet') return null;
  
  // Check if current route is active
  const isActive = (path: string) => {
    return location.pathname.startsWith(path);
  };

  const getHelpLink = () => {
    switch (userType) {
      case 'parent':
        return '/ajuda/responsavel';
      case 'student':
        return '/ajuda/estudante';
      default:
        return '/ajuda/estudante';
    }
  };

  const helpLink = getHelpLink();
  
  // Navigation height based on device and orientation
  const getNavHeight = () => {
    if (device.orientation === 'portrait') {
      switch (device.size) {
        case 'xxs':
          return 'h-12';
        case 'xs':
          return 'h-14';
        case 'sm':
          return 'h-16';
        default:
          return 'h-16';
      }
    } else {
      // Landscape - more compact
      switch (device.size) {
        case 'xxs':
        case 'xs':
          return 'h-10';
        case 'sm':
          return 'h-12';
        default:
          return 'h-12';
      }
    }
  };
  
  // Icon and text sizing
  const getIconSize = () => {
    if (device.orientation === 'portrait') {
      switch (device.size) {
        case 'xxs':
          return 'h-4 w-4';
        case 'xs':
          return 'h-5 w-5';
        default:
          return 'h-5 w-5';
      }
    } else {
      return device.size === 'xxs' || device.size === 'xs' ? 'h-3.5 w-3.5' : 'h-4 w-4';
    }
  };
  
  const getTextSize = () => {
    if (device.orientation === 'portrait') {
      switch (device.size) {
        case 'xxs':
          return 'text-[0.6rem]';
        case 'xs':
          return 'text-xs';
        default:
          return 'text-xs';
      }
    } else {
      return device.size === 'xxs' || device.size === 'xs' ? 'text-[0.55rem]' : 'text-[0.65rem]';
    }
  };
  
  // Layout mode for very wide screens
  const getLayoutMode = () => {
    if (device.orientation === 'landscape' && device.aspectRatio > 2) {
      return 'flex-row items-center justify-center gap-1';
    }
    return 'flex-col items-center justify-center';
  };
  
  // Padding and spacing
  const getItemPadding = () => {
    if (device.orientation === 'portrait') {
      return 'p-1';
    } else {
      return device.size === 'xxs' || device.size === 'xs' ? 'p-0.5' : 'p-1';
    }
  };
  
  // Safe area support
  const getSafeAreaClasses = () => {
    return device.hasNotch ? 'pb-safe-area-bottom' : '';
  };
  
  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Erro ao fazer logout:", error);
    }
  };

  return (
    <nav className={cn(
      'md:hidden fixed bottom-0 left-0 right-0 bg-white border-t z-40 flex justify-around items-center shadow-lg',
      getNavHeight(),
      getSafeAreaClasses(),
      'px-1'
    )}>
      {/* Home */}
      <Link
        to={dashboardLink}
        className={cn(
          'flex',
          getLayoutMode(),
          getItemPadding(),
          getTextSize(),
          isActive(dashboardLink) ? "text-blue-600 font-semibold" : "text-gray-600",
          'transition-colors'
        )}
      >
        <Home className={cn(
          getIconSize(),
          isActive(dashboardLink) ? "text-blue-600" : ""
        )} />
        <span className={cn(
          device.orientation === 'landscape' && device.aspectRatio > 2 ? "" : "mt-0.5"
        )}>
          Home
        </span>
      </Link>

      {/* Help */}
      <Link
        to={helpLink}
        className={cn(
          'flex',
          getLayoutMode(),
          getItemPadding(),
          getTextSize(),
          isActive(helpLink) ? 'text-blue-600 font-semibold' : 'text-gray-600',
          'transition-colors'
        )}
      >
        <LifeBuoy className={cn(
          getIconSize(),
          isActive(helpLink) ? 'text-blue-600' : ''
        )} />
        <span className={cn(
          device.orientation === 'landscape' && device.aspectRatio > 2 ? '' : 'mt-0.5'
        )}>
          Ajuda
        </span>
      </Link>

      {/* Profile */}
      <Link
        to="/profile"
        className={cn(
          'flex',
          getLayoutMode(),
          getItemPadding(),
          getTextSize(),
          isActive("/profile") ? "text-blue-600 font-semibold" : "text-gray-600",
          'transition-colors'
        )}
      >
        <User className={cn(
          getIconSize(),
          isActive("/profile") ? "text-blue-600" : ""
        )} />
        <span className={cn(
          device.orientation === 'landscape' && device.aspectRatio > 2 ? "" : "mt-0.5"
        )}>
          Perfil
        </span>
      </Link>
      
      {/* Logout */}
      <button
        onClick={handleLogout}
        className={cn(
          'flex',
          getLayoutMode(),
          getItemPadding(),
          getTextSize(),
          'text-red-600 transition-colors hover:text-red-700'
        )}
      >
        <LogOut className={getIconSize()} />
        <span className={cn(
          device.orientation === 'landscape' && device.aspectRatio > 2 ? "" : "mt-0.5"
        )}>
          Sair
        </span>
      </button>
    </nav>
  );
};
