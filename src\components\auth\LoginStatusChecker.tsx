
import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';

interface LoginStatusCheckerProps {
  email?: string;
  onStatusFixed?: () => void;
}

const LoginStatusChecker: React.FC<LoginStatusCheckerProps> = ({ 
  email, 
  onStatusFixed 
}) => {
  const [checking, setChecking] = useState(false);
  const [statusIssue, setStatusIssue] = useState<string | null>(null);
  const [fixing, setFixing] = useState(false);
  const [isFixed, setIsFixed] = useState(false);

  const checkUserStatus = async (userEmail: string) => {
    setChecking(true);
    setStatusIssue(null);
    setIsFixed(false);

    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('registration_status, status, email, full_name, user_id')
        .eq('email', userEmail)
        .single();

      if (error) {
        console.error('[STATUS_CHECKER] Profile check error:', error);
        if (error.code === 'PGRST116') {
          setStatusIssue('Perfil não encontrado. Pode ser necessário criar o perfil do usuário.');
        } else {
          setStatusIssue('Não foi possível verificar o status da conta');
        }
        return;
      }

      if (profile.registration_status === 'pending') {
        setStatusIssue('Sua conta está com status pendente. Isso pode impedir o login.');
      } else if (profile.status === 'inactive') {
        setStatusIssue('Sua conta está inativa. Entre em contato com o suporte.');
      } else {
        setStatusIssue(null);
        setIsFixed(true);
      }
    } catch (error) {
      console.error('[STATUS_CHECKER] Status check failed:', error);
      setStatusIssue('Erro ao verificar status da conta');
    } finally {
      setChecking(false);
    }
  };

  const fixUserStatus = async () => {
    if (!email) return;

    setFixing(true);

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ 
          registration_status: 'active',
          status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq('email', email);

      if (error) {
        throw error;
      }

      // Log the fix safely with improved error handling
      try {
        await supabase.from('auth_logs').insert({
          event_type: 'status_auto_fix',
          metadata: {
            email,
            action: 'status_updated_to_active',
            timestamp: new Date().toISOString()
          }
        });
      } catch (logError) {
        // Don't fail the fix if logging fails
        console.warn('[STATUS_CHECKER] Failed to log fix:', logError);
      }

      setStatusIssue(null);
      setIsFixed(true);
      onStatusFixed?.();

    } catch (error) {
      console.error('[STATUS_CHECKER] Failed to fix status:', error);
      setStatusIssue('Não foi possível corrigir automaticamente. Entre em contato com o suporte.');
    } finally {
      setFixing(false);
    }
  };

  useEffect(() => {
    if (email) {
      checkUserStatus(email);
    }
  }, [email]);

  if (!email || checking) {
    return null;
  }

  if (isFixed && !statusIssue) {
    return (
      <Alert className="mb-4 border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          Status da conta verificado e corrigido com sucesso!
        </AlertDescription>
      </Alert>
    );
  }

  if (!statusIssue) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <span>{statusIssue}</span>
        {statusIssue.includes('pendente') && (
          <Button
            onClick={fixUserStatus}
            disabled={fixing}
            size="sm"
            variant="outline"
            className="ml-2"
          >
            {fixing ? (
              <>
                <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                Corrigindo...
              </>
            ) : (
              'Corrigir'
            )}
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
};

export default LoginStatusChecker;

