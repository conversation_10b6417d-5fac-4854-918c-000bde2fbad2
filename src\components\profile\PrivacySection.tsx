
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useLGPDActions } from '@/hooks/lgpd/useLGPDActions';
import DataSummaryCard from '@/components/lgpd/DataSummaryCard';
import ExportDataCard from '@/components/lgpd/ExportDataCard';
import DeleteAccountCard from '@/components/lgpd/DeleteAccountCard';
import LegalInformationCard from '@/components/lgpd/LegalInformationCard';

const PrivacySection: React.FC = React.memo(() => {
  const { user } = useUser();
  const { t } = useTranslation();
  const {
    loading, 
    exportUserData, 
    requestAccountDeletion 
  } = useLGPDActions();

  const userType = user?.user_metadata?.user_type || 'unknown';
  const userName = user?.user_metadata?.full_name || user?.email || t('common.user');

  const dataSummary = React.useMemo(() => ({
    personal: {
      name: userName,
      email: user?.email || '',
      phone: user?.user_metadata?.phone || t('common.notProvided'),
      type:
        userType === 'student'
          ? t('auth.userTypes.student')
          : userType === 'parent'
            ? t('auth.userTypes.parent')
            : t('common.undefined')
    }
  }), [userName, user?.email, user?.user_metadata?.phone, userType, t]);

  return (
    <div className="space-y-6 bg-transparent">
      <DataSummaryCard 
        dataSummary={dataSummary} 
        userType={userType} 
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ExportDataCard
          onExportData={exportUserData}
          isExporting={loading}
        />

        {userType === 'student' && (
          <DeleteAccountCard
            onDeleteAccount={requestAccountDeletion}
            isDeleting={loading}
          />
        )}
      </div>

      <LegalInformationCard />
    </div>
  );
});

PrivacySection.displayName = 'PrivacySection';

export default PrivacySection;
