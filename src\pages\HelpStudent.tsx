import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PublicPageContainer from '@/components/PublicPageContainer';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { useTranslation } from 'react-i18next';

const HelpStudent: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleGoBack = () => navigate(-1);
  const handleProceed = () => navigate('/login');

  return (
    <PublicPageContainer
      className="space-y-8 text-base leading-relaxed text-gray-900 dark:text-gray-100"
      withPattern={false}
    >
      <h1 className="text-3xl font-bold">{t('help.student.title')}</h1>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.student.dashboard.title')}</h2>
        <p className="font-semibold">{t('help.student.dashboard.description')}</p>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.student.sharing.title')}</h2>
        <ul className="list-disc pl-6 space-y-2 font-semibold">
          <li>{t('help.student.sharing.sendButton')}</li>
          <li>{t('help.student.sharing.individual')}</li>
        </ul>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.student.guardians.title')}</h2>
        <p className="font-semibold">{t('help.student.guardians.description')}</p>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.student.history.title')}</h2>
        <p className="font-semibold">{t('help.student.history.description')}</p>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.student.profile.title')}</h2>
        <p className="font-semibold">{t('help.student.profile.description')}</p>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.student.faq.title')}</h2>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="q1">
            <AccordionTrigger className="font-semibold text-xl">{t('help.student.faq.q1')}</AccordionTrigger>
            <AccordionContent className="font-semibold">{t('help.student.faq.a1')}</AccordionContent>
          </AccordionItem>
          <AccordionItem value="q2">
            <AccordionTrigger className="font-semibold text-xl">{t('help.student.faq.q2')}</AccordionTrigger>
            <AccordionContent className="font-semibold">{t('help.student.faq.a2')}</AccordionContent>
          </AccordionItem>
          <AccordionItem value="q3">
            <AccordionTrigger className="font-semibold text-xl">{t('help.student.faq.q3')}</AccordionTrigger>
            <AccordionContent className="font-semibold">{t('help.student.faq.a3')}</AccordionContent>
          </AccordionItem>
        </Accordion>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.student.support.title')}</h2>
        <p className="font-semibold">
          {t('help.student.support.description')}{' '}
          <a className="text-blue-600 underline" href="mailto:<EMAIL>">
            <EMAIL>
          </a>.
        </p>
      </section>

      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={handleGoBack} className="flex items-center gap-2 text-lg font-semibold">
          <ArrowLeft className="h-5 w-5" />
          {t('help.buttons.back')}
        </Button>
        <Button onClick={handleProceed} className="flex items-center gap-2 text-lg font-semibold">
          {t('help.buttons.proceed')}
          <ArrowRight className="h-5 w-5" />
        </Button>
      </div>
    </PublicPageContainer>
  );
};

export default HelpStudent;
