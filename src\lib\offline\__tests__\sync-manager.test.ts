import { offlineDexie } from '@/lib/offline/dexie-db'
import { syncManager } from '@/lib/offline/sync-manager'
import { jest } from '@jest/globals'

// Mock Dexie to avoid ESM issues in Jest
jest.mock('dexie', () => {
  class Table {
    data: any[] = []
    async put(item: any) { this.data.push(item) }
    async toArray() { return this.data }
    async delete(id: string) { this.data = this.data.filter(i => i.id !== id) }
    async clear() { this.data = [] }
  }
  class DexieMock {
    sync_queue = new Table()
    version() { return { stores: () => {} } }
  }
  return { __esModule: true, default: DexieMock, Table }
}, { virtual: true })

// Mock supabase client
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: () => ({
      insert: jest.fn(() => Promise.resolve({})) as any,
      update: jest.fn(() => Promise.resolve({})) as any,
      delete: jest.fn(() => Promise.resolve({})) as any
    }),
    rpc: jest.fn(() => Promise.resolve({})) as any
  }
}))

describe('sync manager', () => {
  beforeEach(async () => {
    await offlineDexie.sync_queue.clear()
  })

  test('queued actions persist and are cleared after sync', async () => {
    await syncManager.addAction('add_location', { id: '1', latitude: 1, longitude: 2 })
    await syncManager.addAction('add_location', { id: '2', latitude: 3, longitude: 4 })

    let items = await offlineDexie.sync_queue.toArray()
    expect(items).toHaveLength(2)

    await syncManager.processQueue()

    items = await offlineDexie.sync_queue.toArray()
    expect(items).toHaveLength(0)
  })
})
