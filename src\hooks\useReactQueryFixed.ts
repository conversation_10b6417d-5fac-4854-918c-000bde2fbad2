
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// User profile queries
export const useUserProfile = (userId?: string) => {
  return useQuery({
    queryKey: ['userProfile', userId],
    queryFn: async () => {
      if (!userId) return null;
      
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Location sharing mutations
export const useShareLocation = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ latitude, longitude, studentId }: {
      latitude: number;
      longitude: number;
      studentId: string;
    }) => {
      const response = await fetch('/api/share-location', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          latitude,
          longitude,
          student_id: studentId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to share location');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Localização compartilhada",
        description: "Sua localização foi compartilhada com sucesso.",
      });
      queryClient.invalidateQueries({ queryKey: ['locations'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Erro ao compartilhar localização",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Guardian list queries using proper relationships
export const useGuardianList = (studentId?: string) => {
  return useQuery({
    queryKey: ['guardians', studentId],
    queryFn: async () => {
      if (!studentId) return [];
      
      // Use the RPC function instead of direct table access
      const { data, error } = await supabase.rpc(
        'get_student_guardians_from_relationships',
        { p_student_id: studentId }
      );
      
      if (error) throw error;
      return data || [];
    },
    enabled: !!studentId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Recent locations query - simplified to avoid type issues
export const useRecentLocations = (studentId?: string) => {
  return useQuery({
    queryKey: ['locations', studentId],
    queryFn: async () => {
      if (!studentId) return [];
      
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .eq('user_id', studentId)
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (error) throw error;
      return data || [];
    },
    enabled: !!studentId,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

