import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-site-url',
}

serve(async (req) => {
  console.log('🚀 Edge Function invite-student iniciada')
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('📝 Processando request...')
    
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')
    
    console.log('🔑 Environment check:', {
      hasUrl: !!supabaseUrl,
      hasServiceKey: !!supabaseServiceKey,
      hasAnonKey: !!supabaseAnonKey
    })

    if (!supabaseUrl || !supabaseServiceKey || !supabaseAnonKey) {
      console.error('❌ Missing environment variables')
      return new Response(
        JSON.stringify({ error: 'Configuração do servidor incompleta' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const body = await req.json()
    console.log('📋 Request body:', body)
    
    const { name, email, cpf, phone } = body

    if (!name || !cpf) {
      console.error('❌ Missing required fields')
      return new Response(
        JSON.stringify({ error: 'Nome e CPF são obrigatórios' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get guardian info (using anon key for auth check)
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      console.error('❌ Missing authorization header')
      return new Response(
        JSON.stringify({ error: 'Token de autorização necessário' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey)
    const token = authHeader.replace('Bearer ', '')
    supabaseAuth.auth.setAuth(token)

    console.log('🔍 Verificando usuário autenticado...')
    const { data: { user }, error: userError } = await supabaseAuth.auth.getUser()

    if (userError || !user) {
      console.error('❌ Authentication error:', userError)
      return new Response(
        JSON.stringify({ error: 'Usuário não autenticado' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('✅ Guardian autenticado:', user.id)

    // Clean CPF
    const cleanCpf = cpf.replace(/\D/g, '')
    console.log('🧹 CPF limpo:', cleanCpf)

    // Generate temp password
    const tempPassword = `Temp${Math.random().toString(36).slice(2, 8)}!`
    console.log('🔐 Senha temporária gerada')

    // Create student user (using service role)
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)
    
    console.log('👤 Criando usuário estudante...')
    const { data: newUser, error: createUserError } = await supabaseAdmin.auth.admin.createUser({
      email: email,
      password: tempPassword,
      user_metadata: {
        full_name: name,
        cpf: cleanCpf,
        phone: phone || null,
        user_type: 'student'
      },
      email_confirm: true
    })

    if (createUserError) {
      console.error('❌ Erro ao criar usuário:', createUserError)
      return new Response(
        JSON.stringify({ error: 'Erro ao criar usuário estudante', details: createUserError.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('✅ Usuário estudante criado:', newUser.user?.id)

    // Create profile
    console.log('📝 Criando perfil...')
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        user_id: newUser.user!.id,
        full_name: name,
        email: email,
        cpf: cleanCpf,
        phone: phone || null,
        user_type: 'student',
        status: 'pending'
      })

    if (profileError) {
      console.error('❌ Erro ao criar perfil:', profileError)
      return new Response(
        JSON.stringify({ error: 'Erro ao criar perfil', details: profileError.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('✅ Perfil criado com sucesso')

    // Create guardian-student relationship
    console.log('🔗 Criando relacionamento guardian-student...')
    const { error: relationshipError } = await supabaseAdmin
      .from('student_guardian_relationships')
      .insert({
        student_id: newUser.user!.id,
        guardian_id: user.id,
        relationship_type: 'parent',
        is_primary: true
      })

    if (relationshipError) {
      console.error('❌ Erro ao criar relacionamento:', relationshipError)
      return new Response(
        JSON.stringify({ error: 'Erro ao criar relacionamento', details: relationshipError.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('✅ Relacionamento criado com sucesso')

    // Success response
    console.log('🎉 Processo concluído com sucesso!')
    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'Estudante adicionado com sucesso',
        student_id: newUser.user!.id,
        temp_password: tempPassword,
        student_email: email
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('💥 Erro geral:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Erro interno do servidor',
        details: error.message 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}) 