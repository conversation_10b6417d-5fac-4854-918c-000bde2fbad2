import { env } from '@/env';

// Interface para configuração Redis
interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
  maxRetriesPerRequest?: number;
}

// Interface para dados de localização
interface LocationData {
  id: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: string;
  studentId: string;
  address?: string;
}

// Interface para sessão de usuário
interface UserSession {
  userId: string;
  email: string;
  role: string;
  lastActivity: string;
  metadata?: Record<string, any>;
}

class RedisManager {
  private redis: any = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 5;

  constructor(private config: RedisConfig) {
    this.initializeConnection();
  }

  private async initializeConnection() {
    if (typeof window !== 'undefined') {
      console.warn('[Redis] Tentativa de conexão no cliente - usando localStorage como fallback');
      return;
    }

    try {
      // Tentativa de importação dinâmica do ioredis
      const Redis = await import('ioredis').then(mod => mod.default);
      
      this.redis = new Redis({
        host: this.config.host,
        port: this.config.port,
        password: this.config.password,
        db: this.config.db || 0,
        maxRetriesPerRequest: 3,
        
        // Configurações de reconexão
        lazyConnect: true,
        enableOfflineQueue: false,
        
        // Event handlers
        retryStrategy: (times) => {
          const delay = Math.min(times * 50, 2000);
          return delay;
        }
      });

      this.setupEventHandlers();
      await this.redis.connect();
      this.isConnected = true;
      console.log('[Redis] Conexão estabelecida com sucesso');
      
    } catch (error) {
      console.error('[Redis] Erro na inicialização:', error);
      this.handleConnectionError();
    }
  }

  private setupEventHandlers() {
    if (!this.redis) return;

    this.redis.on('connect', () => {
      console.log('[Redis] Conectado');
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.redis.on('error', (error: Error) => {
      console.error('[Redis] Erro de conexão:', error);
      this.isConnected = false;
      this.handleConnectionError();
    });

    this.redis.on('close', () => {
      console.warn('[Redis] Conexão fechada');
      this.isConnected = false;
    });
  }

  private handleConnectionError() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`[Redis] Tentativa de reconexão ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
      setTimeout(() => this.initializeConnection(), 5000 * this.reconnectAttempts);
    } else {
      console.error('[Redis] Máximo de tentativas de reconexão atingido');
    }
  }

  // Fallback para localStorage no cliente
  private fallbackGet(key: string): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(`redis:${key}`);
    }
    return null;
  }

  private fallbackSet(key: string, value: string, ttlSeconds?: number): void {
    if (typeof window !== 'undefined') {
      const data = {
        value,
        expires: ttlSeconds ? Date.now() + (ttlSeconds * 1000) : null
      };
      localStorage.setItem(`redis:${key}`, JSON.stringify(data));
    }
  }

  private fallbackDel(key: string): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`redis:${key}`);
    }
  }

  // Métodos principais de cache
  async get(key: string): Promise<string | null> {
    if (!this.isConnected || !this.redis) {
      const fallback = this.fallbackGet(key);
      if (fallback) {
        try {
          const data = JSON.parse(fallback);
          if (data.expires && Date.now() > data.expires) {
            this.fallbackDel(key);
            return null;
          }
          return data.value;
        } catch {
          return fallback;
        }
      }
      return null;
    }

    try {
      return await this.redis.get(key);
    } catch (error) {
      console.error(`[Redis] Erro ao obter ${key}:`, error);
      return this.fallbackGet(key);
    }
  }

  async set(key: string, value: string, ttlSeconds?: number): Promise<boolean> {
    if (!this.isConnected || !this.redis) {
      this.fallbackSet(key, value, ttlSeconds);
      return true;
    }

    try {
      if (ttlSeconds) {
        await this.redis.setex(key, ttlSeconds, value);
      } else {
        await this.redis.set(key, value);
      }
      return true;
    } catch (error) {
      console.error(`[Redis] Erro ao definir ${key}:`, error);
      this.fallbackSet(key, value, ttlSeconds);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    if (!this.isConnected || !this.redis) {
      this.fallbackDel(key);
      return true;
    }

    try {
      await this.redis.del(key);
      return true;
    } catch (error) {
      console.error(`[Redis] Erro ao deletar ${key}:`, error);
      this.fallbackDel(key);
      return false;
    }
  }

  // Métodos específicos para localizações
  async cacheLocation(studentId: string, location: LocationData): Promise<boolean> {
    const key = `location:${studentId}:latest`;
    const locationWithTTL = {
      ...location,
      cached_at: new Date().toISOString()
    };
    
    return this.set(key, JSON.stringify(locationWithTTL), 3600); // 1 hora TTL
  }

  async getLatestLocation(studentId: string): Promise<LocationData | null> {
    const key = `location:${studentId}:latest`;
    const cached = await this.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch (error) {
      console.error('[Redis] Erro ao parsear localização:', error);
      return null;
    }
  }

  async cacheUserSession(userId: string, session: UserSession): Promise<boolean> {
    const key = `session:${userId}`;
    return this.set(key, JSON.stringify(session), 86400); // 24 horas TTL
  }

  async getUserSession(userId: string): Promise<UserSession | null> {
    const key = `session:${userId}`;
    const cached = await this.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch (error) {
      console.error('[Redis] Erro ao parsear sessão:', error);
      return null;
    }
  }

  async invalidateUserSession(userId: string): Promise<boolean> {
    const key = `session:${userId}`;
    return this.del(key);
  }

  // Cache de estudantes por responsável
  async cacheStudentsList(guardianId: string, students: any[]): Promise<boolean> {
    const key = `students:${guardianId}`;
    return this.set(key, JSON.stringify(students), 1800); // 30 minutos TTL
  }

  async getStudentsList(guardianId: string): Promise<any[] | null> {
    const key = `students:${guardianId}`;
    const cached = await this.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch (error) {
      console.error('[Redis] Erro ao parsear lista de estudantes:', error);
      return null;
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    if (!this.isConnected || !this.redis) {
      return false;
    }

    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('[Redis] Health check falhou:', error);
      return false;
    }
  }

  // Cleanup
  async disconnect(): Promise<void> {
    if (this.redis) {
      await this.redis.quit();
      this.redis = null;
      this.isConnected = false;
    }
  }
}

// Configuração do Redis a partir de variáveis de ambiente
const redisConfig: RedisConfig = {
  host: env.REDIS_HOST,
  port: parseInt(env.REDIS_PORT),
  password: env.REDIS_PASSWORD || undefined,
  db: parseInt(env.REDIS_DB)
};

// Singleton instance
export const redisManager = new RedisManager(redisConfig);

export default redisManager; 