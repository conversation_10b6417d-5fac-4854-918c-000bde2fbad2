
import { useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { MapViewport } from './useMapInitialization';
import axios from 'axios';
import { apiCache } from '@/utils/apiAvailabilityCache';

interface UseMapLocationProps {
  selectedUserId?: string;
  onViewportChange?: (viewport: MapViewport) => void;
  userType?: string;
  studentDetails?: {
    name: string;
    email: string;
  } | null;
  senderName?: string;
}

export function useMapLocation({ 
  selectedUserId, 
  onViewportChange,
  userType,
  studentDetails,
  senderName
}: UseMapLocationProps = {}) {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const updateLocation = async (mapInstance?: mapboxgl.Map | null) => {
    if (!navigator.geolocation) {
      toast({
        title: "Erro",
        description: "Seu navegador não suporta geolocalização",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);

    try {
      // Get current position
      let position: GeolocationPosition | null = null;
      let coords: { latitude: number; longitude: number; accuracy?: number; source: string } | null = null;
      try {
        position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
          });
        });
        const { latitude, longitude, accuracy } = position.coords;
        coords = { latitude, longitude, accuracy, source: 'gps' };
      } catch (err) {
        // Falha ao obter via navegador
        coords = null;
      }

      // Se precisão ruim (>200m) ou falhou, usar fallback AWS
      if (!coords || (coords.accuracy && coords.accuracy > 200)) {
        const endpoint = '/api/resolve-location';

        // Check if API is available before making the call
        const { shouldCall, reason } = apiCache.shouldAttemptCall(endpoint);
        if (!shouldCall) {
          console.log(`[useMapLocation] Skipping API call: ${reason}`);
          // Use GPS coordinates or fallback immediately
          if (coords) {
            toast({
              title: 'Localização obtida via GPS',
              description: 'Usando coordenadas GPS. Precisão pode ser limitada.',
              variant: 'default',
            });
          } else {
            coords = { latitude: -23.5489, longitude: -46.6388, source: 'default' };
            toast({
              title: 'Localização padrão',
              description: 'Usando localização padrão. Ative o GPS para maior precisão.',
              variant: 'default',
            });
          }
        } else {
          try {
            const res = await axios.post(endpoint, coords ? { latitude: coords.latitude, longitude: coords.longitude } : {});
            if (res.data?.result?.Place?.Geometry?.Point) {
              const [lng, lat] = res.data.result.Place.Geometry.Point;
              coords = { latitude: lat, longitude: lng, source: 'aws' };
              apiCache.markApiSuccess(endpoint);
              toast({
                title: 'Localização aproximada',
                description: 'Sua localização foi obtida via AWS Location Service. Precisão pode ser limitada.',
                variant: 'default',
              });
            } else {
              apiCache.markApiFailure(endpoint);
              throw new Error('Não foi possível obter localização via AWS');
            }
          } catch (apiError: any) {
            // Handle API server not available (404 error)
            if (apiError.response?.status === 404 || apiError.code === 'ECONNREFUSED') {
              apiCache.markApiFailure(endpoint, apiError.response?.status || 404);
              console.warn('[useMapLocation] API server not available, using GPS coordinates or fallback');
              if (coords) {
                // Use the GPS coordinates we have, even if accuracy is poor
                toast({
                  title: 'Localização obtida via GPS',
                  description: 'Usando coordenadas GPS. Precisão pode ser limitada.',
                  variant: 'default',
                });
              } else {
                // Use default São Paulo coordinates
                coords = { latitude: -23.5489, longitude: -46.6388, source: 'default' };
                toast({
                  title: 'Localização padrão',
                  description: 'Usando localização padrão. Ative o GPS para maior precisão.',
                  variant: 'default',
                });
              }
            } else {
              // Handle other API errors
              apiCache.markApiFailure(endpoint, apiError.response?.status);
              console.error('[useMapLocation] API error:', apiError);
              toast({
                title: 'Erro',
                description: 'Não foi possível obter localização nem via GPS nem AWS',
                variant: 'destructive',
              });
              return null;
            }
          }
        }
      } else {
        toast({
          title: 'Localização precisa',
          description: `Sua localização foi obtida via GPS. Precisão: ${coords.accuracy ? Math.round(coords.accuracy) + 'm' : 'desconhecida'}.`,
          variant: 'default',
        });
      }

      // Atualizar mapa e salvar se necessário
      const { latitude, longitude } = coords;
      if (mapInstance) {
        mapInstance.flyTo({
          center: [longitude, latitude],
          zoom: 15,
          essential: true
        });
        if (onViewportChange) {
          onViewportChange({ latitude, longitude, zoom: 15 });
        }
      }
      if (selectedUserId) {
        const { error } = await supabase
          .from('locations')
          .insert([{ user_id: selectedUserId, latitude, longitude, shared_with_guardians: true }]);
        if (error) throw error;
      }
      return { latitude, longitude };
    } catch (error: any) {
      console.error('Erro ao atualizar localização:', error);
      toast({
        title: "Erro",
        description: error.message || "Não foi possível atualizar sua localização",
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { loading, updateLocation };
}

