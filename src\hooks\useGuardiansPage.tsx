
import { useState, useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useUser } from '@/contexts/UnifiedAuthContext';

interface Guardian {
  id: string;
  email: string;
  full_name: string | null;
  phone: string | null;
  is_active: boolean;
  created_at: string;
}

export function useGuardiansPage() {
  const [guardians, setGuardians] = useState<Guardian[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddGuardian, setShowAddGuardian] = useState(false);
  const { toast } = useToast();
  const { user } = useUser();

  // Fetch guardians for the current student using RPC
  const fetchGuardians = async () => {
    if (!user?.id) {
      setError("User not authenticated");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log('[useGuardiansPage] Fetching guardians via RPC...');
      
      // Use the RPC function to get guardians from relationships
      const { data: guardianData, error } = await supabase.rpc(
        'get_student_guardians_from_relationships',
        { p_student_id: user.id }
      );

      if (error) {
        console.error("[useGuardiansPage] Erro RPC:", error);
        setError("Unable to load your guardians");
        return;
      }

      // Format data to match Guardian interface
      const formattedGuardians: Guardian[] = [];
      
      if (Array.isArray(guardianData)) {
        guardianData.forEach((item: any, index: number) => {
          if (item && typeof item === 'object') {
            formattedGuardians.push({
              id: item.id || `guardian-${index}`,
              email: item.email || '',
              full_name: item.full_name || null,
              phone: item.phone || null,
              is_active: true,
              created_at: item.created_at || new Date().toISOString()
            });
          }
        });
      }

      console.log('[useGuardiansPage] Guardians found:', formattedGuardians.length);
      setGuardians(formattedGuardians);
    } catch (err) {
      console.error("[useGuardiansPage] Erro ao processar dados:", err);
      setError("An error occurred while processing the guardian data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchGuardians();
    }
  }, [user?.id]);

  // Add a new guardian using the family invitation system
  const addGuardian = async (
    email: string, 
    fullName: string, 
    phone: string
  ) => {
    if (!user?.id) {
      throw new Error("User not authenticated");
    }

    // Validate email
    if (!email || !email.includes("@")) {
      throw new Error("Guardian email is required and must be valid");
    }

    try {
      // Use family invitation system
      const { error } = await supabase.rpc('send_family_invitation', {
        p_guardian_email: email
      });

      if (error) {
        console.error("Error sending invitation:", error);
        throw new Error(error.message || "Failed to send invitation");
      }

      // Close the modal and refresh the list
      setShowAddGuardian(false);
      fetchGuardians();
      
      toast({
        title: "Invitation sent",
        description: "The guardian will receive an invitation via email",
      });
    } catch (error: any) {
      console.error("Invitation process error:", error);
      throw error;
    }
  };

  // Remove a guardian using relationship table
  const removeGuardian = async (guardianId: string) => {
    if (!user?.id) {
      return;
    }

    try {
      // Use student_guardian_relationships table
      const { error } = await supabase
        .from("student_guardian_relationships")
        .delete()
        .eq("id", guardianId);

      if (error) {
        console.warn("[useGuardiansPage] Erro ao remover relacionamento:", error);
      }

      fetchGuardians();
      
      toast({
        title: "Guardian removed",
        description: "The guardian was unlinked from your account",
      });
    } catch (err: any) {
      console.error("Error removing guardian:", err);
      toast({
        title: "Error removing guardian",
        description: err.message || "An error occurred while removing the guardian",
        variant: "destructive",
      });
    }
  };

  // Send invite email to guardian
  const sendInviteEmail = async (guardianEmail: string, guardianName: string | null) => {
    if (!user?.id) {
      return;
    }

    try {
      const recipientName = guardianName || "Guardian";
      const studentName = user.user_metadata?.full_name || "Student";
      
      // Use family invitation system
      const { error } = await supabase.rpc('send_family_invitation', {
        p_guardian_email: guardianEmail
      });

      if (error) {
        console.error("Error sending invitation:", error);
        toast({
          title: "Error sending invitation",
          description: "Unable to send the invitation email",
          variant: "destructive",
        });
        return;
      }
      
      toast({
        title: "Invitation sent",
          description: `An invitation email was sent to ${guardianEmail}`,
      });
    } catch (err: any) {
      console.error("Error sending invitation:", err);
      toast({
        title: "Error sending invitation",
        description: err.message || "An error occurred while sending the invitation",
        variant: "destructive",
      });
    }
  };

  return {
    user,
    guardians,
    loading,
    error,
    showAddGuardian,
    setShowAddGuardian,
    fetchGuardians,
    addGuardian,
    removeGuardian,
    sendInviteEmail,
  };
}

