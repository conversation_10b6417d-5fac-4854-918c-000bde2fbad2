# Contexto do Projeto Locate-Family-Connect para Codex CLI

## 1. Visão Geral do Projeto

**Propósito**: Sistema seguro de compartilhamento de localização entre estudantes e responsáveis

**Arquitetura**:
- **Frontend**: React + TypeScript + Vite
- **UI**: Radix UI + TailwindCSS
- **Backend**: Supabase (Auth, PostgreSQL, Edge Functions)
- **Maps**: MapBox
- **Email**: Resend API

## 2. Componentes por Nível de Sensibilidade (Break-Safe Philosophy)

### Nível 1: Crítico - Exige Máxima Cautela

- **Autenticação**:
  - `src/components/auth/` - Componentes de autenticação (AuthActionLinks, PasswordField)
  - `src/contexts/UnifiedAuthContext.tsx` - Contexto de autenticação centralizado
  
- **Localização**:
  - `src/components/location/` - Funcionalidades de compartilhamento (LocationDisplay, LocationControls)
  - `src/components/map/` - Visualização de mapas e marcadores (MapView, MapMarker)
  - `supabase/functions/share-location/` - Edge Function crítica
  
- **Dados Sensíveis**:
  - `src/components/student/form/StudentForm.tsx` - Formulários de estudantes
  - `src/components/guardian/AddGuardianForm.tsx` - Manipulação de responsáveis
  - `src/components/lgpd/` - Componentes relacionados à LGPD (DeleteAccountCard, etc.)

### Nível 2: Moderado - Cautela Necessária

- **Componentes de Estado**:
  - `src/hooks/useInviteStudent.ts` - Hooks personalizados
  - `src/components/offline/` - Gestão de estado offline (NetworkStatusIndicator)
  - `src/components/student/LocationHistoryList.tsx` - Histórico de localização

- **Tipos e Definições**:
  - `src/components/student/types/` - Tipos de estudantes (student-form.types.ts)
  - `src/types/` - Definições de tipos globais

### Nível 3: Superficial - Mais Seguro para Testes Iniciais

- **Componentes UI**:
  - `src/components/ui/` - Componentes reutilizáveis (button, card, etc.)
  - `src/components/Logo.tsx` - Elementos visuais simples
  - `src/components/dashboard/DashboardHeader.tsx` - Elementos de navegação
  
- **Utilidades**:
  - `src/lib/utils/email-validator.ts` - Validação de email
  - `src/lib/utils/cache-manager.ts` - Gerenciamento de cache

## 3. Recomendações para Testes Codex (Ordem de Segurança)

### Primeiros Testes (Começar Aqui)
1. Documentação JSDoc para componentes UI: `src/components/ui/button.tsx`
   ```powershell
   C:\Users\<USER>\AppData\Roaming\npm\codex.ps1 --model gpt-4o-mini-2024-07-18 "gerar documentação JSDoc para src/components/ui/button.tsx"
   ```

2. Análise de acessibilidade: `src/components/ui/card.tsx`
   ```powershell
   C:\Users\<USER>\AppData\Roaming\npm\codex.ps1 --model gpt-4o-mini-2024-07-18 "analisar e sugerir melhorias de acessibilidade em src/components/ui/card.tsx"
   ```

3. Melhorias de estilo: `src/components/Logo.tsx`
   ```powershell
   C:\Users\<USER>\AppData\Roaming\npm\codex.ps1 --model gpt-4o-mini-2024-07-18 "analisar e sugerir melhorias de estilo em src/components/Logo.tsx sem alterar funcionalidades"
   ```

### Testes Intermediários (Após validação inicial)
1. Documentação para utilidades: `src/lib/utils/email-validator.ts`
   ```powershell
   C:\Users\<USER>\AppData\Roaming\npm\codex.ps1 --model gpt-4o-mini-2024-07-18 "gerar documentação JSDoc para src/lib/utils/email-validator.ts"
   ```

2. Melhorias de UI para feedback: `src/components/offline/NetworkStatusIndicator.tsx`
   ```powershell
   C:\Users\<USER>\AppData\Roaming\npm\codex.ps1 --model gpt-4o-mini-2024-07-18 "sugerir melhorias visuais para src/components/offline/NetworkStatusIndicator.tsx"
   ```

3. Otimizações de performance: `src/components/dashboard/DashboardHeader.tsx`
   ```powershell
   C:\Users\<USER>\AppData\Roaming\npm\codex.ps1 --model gpt-4o-mini-2024-07-18 "analisar e sugerir otimizações em src/components/dashboard/DashboardHeader.tsx"
   ```

### Testes Avançados (Somente após confiança estabelecida)
1. Geração de testes: `src/lib/utils/email-validator.ts`
   ```powershell
   C:\Users\<USER>\AppData\Roaming\npm\codex.ps1 --model gpt-4o-mini-2024-07-18 "gerar testes unitários para src/lib/utils/email-validator.ts"
   ```

2. Sugestões de refatoração: `src/components/student/StudentsList.tsx`
   ```powershell
   C:\Users\<USER>\AppData\Roaming\npm\codex.ps1 --model gpt-4o-mini-2024-07-18 "sugerir refatorações para src/components/student/StudentsList.tsx para melhorar manutenção"
   ```

## 4. Protocolo de Avaliação (Break-Safe Philosophy)

### Checklist Pré-Teste
- [ ] Sistema funciona? (validar com `npm run dev`)
- [ ] Funcionalidade já existe?
- [ ] Backup criado?
- [ ] Teste cobre a lógica afetada?
- [ ] Plano de rollback pronto?

### Avaliação de Resultados
- [ ] Verificar preservação de funcionalidade
- [ ] Testar em diferentes navegadores
- [ ] Confirmar compatibilidade com componentes relacionados
- [ ] Documentar mudanças e justificativas

### Mantras de Segurança
- "Prove it before fixing it"
- "Rollback > repair"
- "Compatibility > perfection"

## 5. Ferramentas de Verificação

```bash
# Verificar funcionalidades
npm run dev
npm run build

# Buscar código existente
find src/ -name "*.tsx" | grep -i [functionality]
grep -r "similar_function" src/
```

## 6. Lista Completa de Arquivos

A lista completa dos arquivos do projeto está disponível em:
`lista-arquivos.txt`

---

Este documento foi criado para garantir que os testes do Codex CLI sigam rigorosamente o protocolo Break-Safe Philosophy, priorizando a segurança e estabilidade do projeto Locate-Family-Connect.