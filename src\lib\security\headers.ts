// Headers de segurança para produção
export const securityHeaders = {
  // Content Security Policy
  'Content-Security-Policy': 
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
    "img-src 'self' data: https://*.mapbox.com; " +
    "connect-src 'self' https://rsvjnndhbyyxktbczlnk.supabase.co https://*.sentry.io; " +
    "font-src 'self' https://fonts.gstatic.com;",
  
  // HTTPS Strict Transport Security
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  
  // Proteção contra clickjacking
  'X-Frame-Options': 'DENY',
  
  // Proteção XSS
  'X-XSS-Protection': '1; mode=block',
  
  // Não inferir MIME types
  'X-Content-Type-Options': 'nosniff',
  
  // Referrer policy
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Permissions policy
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self)',
};