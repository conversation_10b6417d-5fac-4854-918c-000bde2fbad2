# Relat\u00f3rio de Erro: AuthApiError durante Login

## 1. Contexto
Ao tentar acessar [https://www.sistema-monitore.com.br/login](https://www.sistema-monitore.com.br/login), um erro de autentica\u00e7\u00e3o ocorreu e o login falhou.

## 2. Log Observado
```text
[LOGIN_FORM] Attempting login with email: <EMAIL>
AuthApiError: Database error querying schema
```

## 3. Poss\u00edvel Causa
Esse erro indica falha na camada de banco de dados (Supabase) utilizada pela aplica\u00e7\u00e3o. Pode estar relacionada a migr\u00e7\u00f5es pendentes ou instabilidade do servi\u00e7o.

## 4. A\u00e7\u00f5es Recomendadas
1. Verificar os logs do projeto no painel do Supabase para detalhes do erro.
2. Conferir se h\u00e1 atualiza\u00e7\u00f5es ou migra\u00e7\u00f5es n\u00e3o aplicadas.
3. Garantir que a conex\u00e3o com o banco esteja funcionando e, se necess\u00e1rio, reiniciar os servi\u00e7os.
4. Caso persista, acionar o suporte t\u00e9cnico do Supabase.

## 5. Poss\u00edvel Interfer\u00eancia de Triggers
Relat\u00f3rios mais recentes indicam que os triggers `validate_cpf_before_insert_update`
e `validate_phone_profiles` podem bloquear a autentica\u00e7\u00e3o de alguns usu\u00e1rios.
Se os passos anteriores n\u00e3o resolverem, teste desabilitar temporariamente estes
triggers:

```sql
ALTER TABLE profiles DISABLE TRIGGER validate_cpf_before_insert_update;
ALTER TABLE profiles DISABLE TRIGGER validate_phone_profiles;
```

Reabilite-os ap\u00f3s a verifica\u00e7\u00e3o, caso n\u00e3o sejam a causa do erro.

## 6. Status Atual
Erro em produ\u00e7\u00e3o e investiga\u00e7\u00e3o em andamento.

## 7. Orienta\u00e7\u00f5es Complementares
- Revise eventuais triggers na tabela `auth.users` e em esquemas relacionados. Se identificados como causa, remova-os e recrie utilizando `SECURITY DEFINER`.
- Confirme que a migra\u00e7\u00e3o `database_schema_fix_applied_v2` est\u00e1 aplicada e que as pol\u00edticas RLS foram recriadas.
- Acesse a p\u00e1gina **Reports** do Supabase para verificar poss\u00edvel strain no banco e otimize queries ou aumente recursos se necess\u00e1rio.
- Mantenha RLS ativo e revisado ap\u00f3s qualquer ajuste no esquema.
