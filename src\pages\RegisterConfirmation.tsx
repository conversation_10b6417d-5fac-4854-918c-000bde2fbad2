
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Mail, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { EmailConfirmationService } from '@/lib/services/EmailConfirmationService';
import { useToast } from '@/components/ui/use-toast';

const RegisterConfirmation: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(true);
  const [showInstructions, setShowInstructions] = React.useState(false);

  React.useEffect(() => {
    // Simulate loading for 2 seconds then show instructions
    const timer = setTimeout(() => {
      setIsLoading(false);
      setShowInstructions(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleLogin = () => {
    navigate('/login');
  };

  const { toast } = useToast();

  const handleResendEmail = async () => {
    try {
      const { data } = await supabase.auth.getUser();
      if (!data.user) {
        toast({
          title: 'Erro ao reenviar',
          description: 'Usuário não encontrado. Faça login novamente.',
          variant: 'destructive'
        });
        return;
      }

      const emailConfirmationService = EmailConfirmationService.getInstance();

      const result = await emailConfirmationService.sendConfirmationEmail({
        email: data.user.email ?? '',
        fullName: data.user.user_metadata?.full_name || '',
        userType: data.user.user_metadata?.user_type || 'student',
        confirmationToken: data.user.id
      });

      if (result.success) {
        toast({
          title: 'E-mail reenviado',
          description: 'Confira sua caixa de entrada para o novo link.'
        });
      } else {
        toast({
          title: 'Erro ao reenviar',
          description: result.message,
          variant: 'destructive'
        });
      }
    } catch (err: any) {
      console.error('Resend email error:', err);
      toast({
        title: 'Erro inesperado',
        description: err.message || 'Não foi possível reenviar o e-mail.',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center p-4 bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            {isLoading ? (
              <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
            ) : (
              <Mail className="h-12 w-12 text-blue-500" />
            )}
          </div>
          <CardTitle className="text-xl">
            {isLoading ? 'Processando cadastro...' : 'Verifique seu e-mail'}
          </CardTitle>
          <CardDescription>
            {isLoading 
              ? 'Finalizando sua conta...' 
              : 'Confirme sua conta antes de fazer login'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              <div className="text-center">
                <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                <p className="text-sm text-gray-600">Preparando sua conta...</p>
              </div>
            </div>
          ) : (
            <>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-blue-900">Cadastro realizado com sucesso!</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Enviamos um e-mail de confirmação para você. Verifique sua caixa de entrada (e spam) para ativar sua conta.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-amber-900">Importante</h4>
                    <p className="text-sm text-amber-700 mt-1">
                      <strong>Não tente fazer login</strong> até confirmar seu e-mail. Isso evitará erros de autenticação.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Próximos passos:</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium text-xs">1</div>
                    <p>Abra seu e-mail e procure pela mensagem de confirmação</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium text-xs">2</div>
                    <p>Clique no link de confirmação no e-mail</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium text-xs">3</div>
                    <p>Retorne aqui e faça login com suas credenciais</p>
                  </div>
                </div>
              </div>

              <div className="pt-4 space-y-3">
                <Button onClick={handleLogin} className="w-full" variant="outline">
                  <Clock className="w-4 h-4 mr-2" />
                  Ir para página de login
                </Button>
                
                <button
                  onClick={handleResendEmail}
                  className="w-full text-sm text-blue-600 hover:text-blue-800 underline"
                >
                  Não recebeu o e-mail? Reenviar
                </button>
              </div>

              <div className="text-xs text-gray-500 text-center mt-4">
                <p>Problemas com o e-mail? Verifique sua pasta de spam ou entre em contato conosco.</p>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default RegisterConfirmation;
