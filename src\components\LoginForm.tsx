
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useToast } from "@/components/ui/use-toast";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { UserType } from '@/lib/auth-redirects';
import PasswordField from '@/components/auth/PasswordField';
import AuthActionLinks from '@/components/auth/AuthActionLinks';
import LoginStatusChecker from '@/components/auth/LoginStatusChecker';
import AuthErrorHandler from '@/components/auth/AuthErrorHandler';

export interface LoginFormProps {
  userType: UserType;
  onRegisterClick: () => void;
  onForgotPasswordClick: () => void;
  variant?: 'login' | 'register';
}

const LoginForm: React.FC<LoginFormProps> = ({
  userType,
  onRegisterClick,
  onForgotPasswordClick,
}) => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showStatusChecker, setShowStatusChecker] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const { toast } = useToast();

  // Enhanced error logging function with safe error handling
  const logLoginError = async (email: string, error: any, step: string) => {
    try {
      await supabase.from('auth_logs').insert({
        event_type: 'login_error_detailed_v2',
        metadata: {
          email,
          error_message: error.message,
          error_code: error.code,
          step,
          retry_count: retryCount,
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
          url: window.location.href
        }
      });
    } catch (logError) {
      console.warn('[LOGIN_FORM] Failed to log error:', logError);
    }
  };

  const clearError = () => {
    setError('');
    setShowStatusChecker(false);
  };

  const retryLogin = () => {
    setRetryCount(prev => prev + 1);
    clearError();
    handleSubmit(new Event('submit') as any);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setShowStatusChecker(false);
    
    // Basic validation
    if (!email || !password) {
      toast({
        title: "Campos obrigatórios",
        description: "Por favor, preencha todos os campos.",
        variant: "destructive",
      });
      setLoading(false);
      return;
    }

    try {
      console.log(`[LOGIN_FORM] Attempting login with email: ${email} (attempt ${retryCount + 1})`);

      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim().toLowerCase(),
        password
      });

      if (error) {
        await logLoginError(email, error, 'supabase_auth');
        
        // Show status checker for database errors
        if (error.message?.includes('Database error')) {
          setShowStatusChecker(true);
        }
        
        throw error;
      }

      const authUser = data.user;
      if (!authUser) {
        await logLoginError(email, { message: 'No user returned from auth' }, 'user_check');
        throw new Error('Usuário não encontrado');
      }

      console.log('[LOGIN_FORM] Login successful, user:', authUser);
      
      // Update last login time safely
      try {
        const { data: profileData } = await supabase
          .from('profiles')
          .select('login_count')
          .eq('user_id', authUser.id)
          .maybeSingle();

        const currentCount = profileData?.login_count || 0;

        if (profileData) {
          await supabase
            .from('profiles')
            .update({ 
              last_login_at: new Date().toISOString(),
              login_count: currentCount + 1
            })
            .eq('user_id', authUser.id);
        } else {
          // cria perfil mínimo se não existir
          await supabase
            .from('profiles')
            .insert({
              user_id: authUser.id,
              full_name: authUser.user_metadata?.full_name || authUser.email,
              email: authUser.email,
              user_type: 'student',
              login_count: 1,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
        }
      } catch (updateError) {
        console.warn('[LOGIN_FORM] Failed to update login time:', updateError);
      }

      // Log successful login
      try {
        await supabase.from('auth_logs').insert({
          event_type: 'login_success_v2',
          user_id: authUser.id,
          metadata: {
            email,
            login_method: 'password',
            retry_count: retryCount,
            timestamp: new Date().toISOString()
          }
        });
      } catch (logError) {
        console.warn('[LOGIN_FORM] Failed to log successful login:', logError);
      }
      
      toast({
        title: "Login bem-sucedido",
        description: `Bem-vindo de volta!`,
      });

      // Reset retry count on success
      setRetryCount(0);
      
    } catch (error: any) {
      console.error('[LOGIN_FORM] Login error:', error);
      
      let errorMessage = 'Ocorreu um erro ao realizar o login.';
      
      if (error.message?.includes('Invalid login credentials')) {
        errorMessage = 'Email ou senha incorretos. Por favor, verifique suas credenciais.';
      } else if (error.message?.includes('Email not confirmed')) {
        errorMessage = 'Email não confirmado. Por favor, verifique sua caixa de entrada.';
      } else if (error.message?.includes('Database error')) {
        errorMessage = 'Erro temporário no sistema. Nossa equipe técnica foi notificada e está trabalhando na correção.';
      } else if (error.message?.includes('Too many requests')) {
        errorMessage = 'Muitas tentativas de login. Aguarde alguns minutos antes de tentar novamente.';
      }
      
      setError(errorMessage);
      
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6" data-cy="login-form">
      {error && (
        <AuthErrorHandler 
          error={error}
          onRetry={retryLogin}
          onClearError={clearError}
        />
      )}

      {showStatusChecker && email && (
        <LoginStatusChecker 
          email={email} 
          onStatusFixed={() => {
            setShowStatusChecker(false);
            setError('');
            toast({
              title: "Status corrigido",
              description: "Tente fazer login novamente.",
            });
          }}
        />
      )}

      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm sm:text-base">
          {t('auth.login.email')}
        </Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full"
          placeholder="<EMAIL>"
          disabled={loading}
          autoComplete="username"
          aria-label="Email"
          data-cy="email-input"
        />
      </div>

      <PasswordField
        value={password}
        onChange={setPassword}
        showPassword={showPassword}
        onToggleVisibility={() => setShowPassword(!showPassword)}
        disabled={loading}
      />

      <Button
        type="submit"
        size="default"
        className="w-full"
        disabled={loading}
        data-cy="submit-button"
      >
        {loading ? t('common.loading') : t('auth.login.signIn')}
      </Button>

      <AuthActionLinks
        onForgotPasswordClick={onForgotPasswordClick}
        onRegisterClick={onRegisterClick}
      />
    </form>
  );
};

export default LoginForm;

