#!/bin/bash
# Script para corrigir problemas de rede no WSL2 para o Supabase
# Este script ajusta as configurações de rede para permitir que containers Docker se comuniquem

# Obtém o IP do host WSL2
WSL_HOST_IP=$(ip route | grep default | awk '{print $3}')
echo "IP do host WSL2: $WSL_HOST_IP"

# Adiciona uma entrada para docker.internal
echo "$WSL_HOST_IP docker.internal" | sudo tee -a /etc/hosts

# Verifica se consegue fazer ping no host
echo "Testando conexão com docker.internal:"
ping -c 3 docker.internal

echo "Configuração de rede WSL2 ajustada para funcionar com Supabase"
