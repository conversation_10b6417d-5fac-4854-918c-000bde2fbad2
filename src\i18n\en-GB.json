{"common": {"loading": "Loading...", "processing": "Processing...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "clear": "Clear", "apply": "Apply", "refresh": "Refresh", "attention": "Attention", "actions": "Actions", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "settings": "Settings", "help": "Help", "dashboard": "Dashboard", "home": "Home", "today": "Today", "notProvided": "Not provided", "user": "User", "undefined": "Undefined"}, "landing": {"title": "Monitor your loved ones' location", "tagline": "Track your children's location in real time with consent, safety and privacy.", "features": {"liveMap": "Live map view (Mapbox)", "quickInvite": "Quick invitation between students and guardians", "security": "Security with PKCE, RLS and GDPR", "noHistory": "No history is shared without permission"}, "login": "Sign In", "createAccount": "Create Account", "studentHelp": "Student Help", "guardianHelp": "Guardian Help"}, "navigation": {"applicationMonitor": "Monitor App", "monitoring": "Monitoring", "familyLocationSafety": "Family Location Safety", "myMap": "My Map", "myGuardians": "My Guardians", "studentMap": "Student Map", "apiDocs": "API Docs"}, "auth": {"login": {"title": "Sign In", "subtitle": "Access your account", "email": "Email", "password": "Password", "forgotPassword": "Forgot password?", "signIn": "Sign In", "noAccount": "Don't have an account?", "createAccount": "Create account"}, "forgotPassword": {"title": "Recover Password"}, "selectProfile": "Choose your Profile", "changeAccountType": "Change account type", "userTypes": {"student": "Student", "parent": "Guardian", "guardian": "Guardian", "admin": "Administrator", "developer": "Developer"}, "userTypeSelector": {"title": "What is your profile?", "subtitle": "Choose carefully - this will define all features available to you", "selected": "Selected", "featuresLabel": "Features:", "examplesLabel": "Use cases:", "continueAs": "Continue as {{type}}", "student": {"title": "I am a Student", "description": "I share my location with my guardians", "features": {"shareLocation": "Share your location", "privacyControl": "Privacy controlled by you", "receiveNotifications": "Receive notifications from guardians", "locationWhenNeeded": "Location only when necessary"}, "examples": {"school": "You are at school/university", "parents": "Your parents want to know if you arrived safely", "control": "You control when to share", "age": "You are between 6-25 years old"}, "warning": "Choose this option only if you are the STUDENT who will share location"}, "parent": {"title": "I am a Guardian", "description": "I monitor my students' location", "features": {"manageStudents": "Manage multiple students", "viewLocation": "View students' location", "sendNotifications": "Send notifications and alerts", "responsibleSecurity": "Responsible for security"}, "examples": {"parentRole": "You are a parent or guardian", "schoolArrival": "Want to know if child arrived at school", "mapView": "View on map where they are", "inviteStudents": "Invite students to connect"}, "warning": "Choose this option only if you are a PARENT/GUARDIAN"}, "dialog": {"title": "Confirm Account Type", "creatingAs": "You are creating an account as {{type}}.", "studentWillDo": "As a student, you will:", "parentWillDo": "As a guardian, you will:", "studentFeatures": {"shareLocation": "Share your location with guardians", "receiveFamilyRequests": "Receive family link requests", "controlSharing": "Control when to share your location"}, "parentFeatures": {"viewStudentLocation": "View location of linked students", "requestLinks": "Request links with students", "manageFamilyConnections": "Manage multiple family connections"}, "warning": "ATTENTION: This choice cannot be changed later!", "confirmAs": "Yes, confirm as {{type}}"}}, "tabs": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password"}, "password": {"show": "Show password", "hide": "Hide password"}, "register": {"title": "Create Account", "subtitle": "Create your new account", "fullName": "Full Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "userType": "User Type", "student": "Student", "parent": "Guardian", "country": "Country / Language", "countryBR": "Brazil / Portuguese", "countryUK": "United Kingdom / English", "nationalId": "National ID", "phone": "Phone", "cpf": "CPF", "createAccount": "Create Account", "haveAccount": "Already have an account?", "signIn": "Sign in", "cpfHint": "The CPF will be used to prevent duplicate accounts and facilitate connection between guardians and students.", "nationalIdHint": "Your national ID helps prevent duplicate accounts."}}, "dashboard": {"tabs": {"map": "Map", "history": "History"}, "parent": {"title": "Guardian Dashboard", "addStudent": "Add Student", "selectStudentPrompt": "Select a student", "selectStudentInstruction": "Click on a student in the list to view their location.", "viewAllLocations": "View all locations", "showing": "Showing", "studentLocationTitle": "Location of {{name}}", "selectStudentPromptDetailed": "Select a student to see their location"}, "student": {"title": "Student Dashboard", "welcome": "Welcome", "shareLocation": "Share Location", "locationHistory": "Location History", "guardians": "Guardians", "privacy": "Privacy"}}, "student": {"manageStudentsTitle": "Manage Students", "addStudent": "Add New Student", "cpfHint": "The CPF will be used to identify the student in the system.", "studentName": "Student Name", "studentEmail": "Student Email", "studentCPF": "Student CPF", "studentPhone": "Student Phone", "inviteStudent": "Invite Student", "inviteButton": "Send In<PERSON>te to Student", "sendingInvite": "Sending Invite...", "inviteEmailHint": "An invitation will be sent to this email for the student to accept.", "studentAdded": "Student added successfully", "addStudentDescription": "Enter the student information you want to monitor.", "addError": "Error adding student", "studentExists": "Student already exists", "invalidData": "Invalid data", "createAccount": "Create Account", "accountCreated": "Account created successfully"}, "location": {"shareLocation": "Share Location", "locationShared": "Location shared", "locationHistory": "Location History", "currentLocation": "Current Location", "lastUpdate": "Last Update", "map": "Map", "history": "History", "noLocations": "No locations available", "accuracy": "Accuracy", "timestamp": "Date/Time"}, "profile": {"title": "Profile", "greeting": "Hello, {{name}}", "subtitle": "Manage your personal information, privacy and settings.", "personalInfo": "Personal Information", "editProfile": "Edit Profile", "edit": "Edit", "saveChanges": "Save Changes", "saving": "Saving...", "cancel": "Cancel", "fullName": "Full Name", "fullNamePlaceholder": "Your full name", "email": "Email", "emailPlaceholder": "<EMAIL>", "phone": "Phone", "phonePlaceholder": "+55 (11) 99999-9999", "cpf": "CPF", "cpfPlaceholder": "000.000.000-00", "userType": "User Type", "userTypePlaceholder": "Select user type", "student": "Student", "parent": "Guardian", "birthDateTitle": "Birth Date", "birthDateLabel": "Birth Date", "age": "Age: {{count}} years", "minor": "Minor", "saveBirthDate": "Save Birth Date", "errorLoading": "Error loading profile", "errorLoadingMessage": "Could not load your profile information.", "tabs": {"profile": "Profile", "privacy": "Privacy", "relationships": "Links", "settings": "Config"}, "accountSettings": "Account <PERSON><PERSON>", "changePassword": "Change Password", "twoFactorAuth": "Two-Factor Authentication", "manageSessions": "Manage Sessions", "accountInfo": "Account Information", "lastProfileUpdate": "Last profile update", "sessionStartedAt": "Session started at", "connectedDevices": "Connected devices", "updateSuccessTitle": "Profile updated", "updateSuccessDescription": "Your information was saved successfully.", "updateErrorTitle": "Error saving", "updateErrorDescription": "Could not save changes", "notLoggedIn": "You are not logged in. Please sign in to access your profile.", "name": "Name"}, "messages": {"welcome": "Welcome to {{brandName}}", "accountCreated": "Account created successfully! Check your email to activate.", "locationShared": "Location shared successfully", "errorOccurred": "An error occurred. Please try again.", "sessionExpired": "Session expired. Please login again.", "unauthorized": "Unauthorized access", "dataNotFound": "Data not found", "invalidInput": "Invalid data provided", "operationSuccess": "Operation completed successfully", "operationFailed": "Operation failed", "inDevelopment": "Feature under development"}, "errors": {"required": "This field is required", "invalidEmail": "Invalid email", "invalidCPF": "Invalid CPF", "invalidPhone": "Invalid phone", "passwordTooShort": "Password too short", "passwordMismatch": "Passwords don't match", "networkError": "Connection error", "serverError": "Internal server error", "authenticationFailed": "Authentication failed", "accessDenied": "Access denied"}, "studentDashboard": {"title": "Student Dashboard", "profile": "Profile", "personalInfo": "Personal Information", "name": "Name", "email": "Email", "phone": "Phone", "actions": "Actions", "myGuardians": "My Guardians", "map": "Map", "history": "History", "quickActions": "Quick Actions", "updateLocationAria": "Update your current location", "updateLocation": "Update Location", "sendToGuardiansAria": "Share location with your guardians", "sendToGuardians": "SEND TO {{count}} GUARDIANS", "readyToShare": "Ready to share with {{count}} guardian(s)", "addGuardian": "Add Guardian", "addGuardianAria": "Open dialog to add a new guardian", "active": "Active", "addedOn": "Added on: {{date}}", "connected": "Connected", "remove": "Remove", "guardianRequests": "Guardian Requests", "guardianRequestsDesc": "Requests from guardians who want to connect to your account", "noPendingRequests": "You have no pending guardian requests at the moment.", "obtaining": "Getting...", "sending": "Sending...", "locationSentSuccess": "Location sent successfully!", "locationSendError": "Error sending location", "addGuardiansToShare": "Add guardians to share your location", "getLocationFirst": "Get your current location first", "locationRecorded": "Location successfully recorded!", "locationUpdated": "Location updated!", "shareSuccess": "Location shared successfully!", "shareComplete": "Sharing complete!", "duplicateLocationTitle": "Location already sent", "duplicateLocationDesc": "You have already sent this location recently. Please wait before trying again."}, "studentPendingRequests": {"title": "Guardian Requests", "titleWithCount": "Guardian Requests ({{count}})", "description": "Requests from guardians who want to connect to your account", "loading": "Loading requests...", "noPending": "You have no pending guardian requests at the moment.", "error": "❌ Error", "loadError": "Could not load pending requests.", "requestAccepted": "✅ Request accepted!", "guardianCanTrack": "The guardian can now track your location.", "acceptError": "❌ Error accepting", "acceptRetry": "Could not accept the request. Try again.", "requestRejected": "🚫 Request rejected", "guardianNotified": "The guardian has been notified of the refusal.", "rejectError": "❌ Error rejecting", "rejectFailed": "Could not reject the request.", "receivedAt": "Request received on: {{date}}", "expiresAt": "Expires on: {{date}}", "wantsToConnect": "This person wants to connect as your guardian to track your location.", "accept": "Accept", "reject": "Reject", "howItWorks": "How it works?", "acceptDescription": "The guardian will be linked to your account and can track your location", "rejectDescription": "The request will be removed and the guardian will not have access", "autoExpire": "Requests automatically expire in 7 days"}, "guardianManager": {"title": "My Guardians", "addGuardian": "Add Guardian", "noGuardians": "No guardians linked yet.", "addFirstGuardian": "Add your first guardian to start sharing your location.", "email": "Email", "emailPlaceholder": "<EMAIL>", "name": "Name", "fullNamePlaceholder": "Full name", "phone": "Phone", "phonePlaceholder": "+XX (XX) XXXXX-XXXX", "cancel": "Cancel", "add": "Add", "active": "Active", "pending": "Pending", "addedAt": "Added on", "connected": "Connected", "invite": "Invite", "remove": "Remove"}, "locale": "en-GB", "validation": {"registrationError": "Registration error", "userAlreadyExists": "This email is already registered. Try logging in or recovering your password.", "userAlreadyRegistered": "User already registered", "redirectingToLogin": "Redirecting to login page...", "databaseError": "Server error during registration. Contact support and provide the code:", "serverError": "Internal server error", "serverErrorDescription": "We are experiencing a technical problem. Please try again later or contact support.", "passwordRequirements": "Password does not meet minimum requirements. Must have at least 8 characters.", "invalidCpf": "Invalid or already registered National ID.", "consentRequired": "Consent required", "consentDescription": "You must agree to the processing of personal data to proceed.", "passwordMismatchError": "Password confirmation error", "passwordsDoNotMatch": "Passwords do not match.", "invalidEmail": "Invalid email", "cpfRequired": "National ID is required", "cpfInvalid": "Invalid National ID. Check the format and verification digits.", "missingFields": "Required fields are missing.", "passwordGuidance": {"title": "💡 Tips for creating a cool and secure password:", "tip1": "Use your favourite pet's name", "tip2": "Add special numbers for you (like your age)", "tip3": "Include fun symbols (!@#$)", "example": "Example: \"Rex2024!@\""}, "accountCreated": "Registration successful!", "verifyEmail": "Check your email to confirm your account before logging in.", "registrationComplete": "Registration complete", "accountCreatedConfirmed": "{{userType}} account created and confirmed successfully!", "studentAccount": "Student", "parentAccount": "Guardian"}, "communication": {"title": "Communication Preferences", "email": {"label": "Email", "description": "Always on for important notifications"}, "sms": {"label": "SMS", "description": "Text messages to your phone"}, "whatsapp": {"label": "WhatsApp", "description": "Messages via WhatsApp Business"}, "phone": {"label": "Phone Number", "placeholder": "+55 (11) 99999-9999", "formatHelp": "Format: +55 (DDD) 9XXXX-XXXX"}, "preferredMethod": {"label": "Preferred Method", "description": "Channel used first when you share location"}, "phoneRequired": {"title": "Phone number required", "description": "Provide a phone number to enable SMS or WhatsApp."}, "saveSuccess": {"title": "Preferences saved", "description": "Your communication preferences have been updated."}, "saveError": {"title": "Save error", "description": "Could not save your preferences. Please try again."}, "saving": "Saving...", "saveButton": "Save Preferences"}, "theme": {"title": "Interface Theme", "light": "Light", "dark": "Dark", "system": "Automatic (System)", "systemDescription": "The automatic theme follows your device settings."}, "map": {"style": {"label": "Map Style", "streets-v12": "Streets", "satellite-streets-v12": "Satellite", "light-v11": "Light", "dark-v11": "Dark", "outdoors-v12": "Outdoors"}}, "notifications": {"title": "Notifications", "locationAlerts": {"label": "Location Alerts", "description": "Notifications about location sharing"}, "networkStatus": {"label": "Network Status", "description": "Alerts about offline/online connection"}, "batteryWarnings": {"label": "Battery Warnings", "description": "Alerts about low device battery"}, "pushNotifications": {"label": "Push Notifications", "description": "Browser notifications"}, "privacy": {"title": "Privacy Settings", "profileVisibility": {"label": "Profile Visibility", "description": "Allow other users to see your profile"}, "autoSharing": {"label": "Automatic Sharing", "description": "Share location automatically"}, "dataRetention": {"label": "Data Retention", "description": "Time to keep location history", "options": {"7days": "7 days", "30days": "30 days", "90days": "90 days", "1year": "1 year", "forever": "Forever"}}}}, "legal": {"purpose": "Purpose", "purposeDescription": "We collect your data to {{purpose}}", "dataCollected": "Data collected", "legalBasis": "Legal basis", "retention": "Retention", "yourRights": "Your rights", "consentText": "I agree with the processing of my personal data as described and authorize location sharing when necessary.", "viewFullPolicy": "View full policy", "dataCollection": {"student": {"purpose": "allow secure sharing of your location with your guardians"}, "parent": {"purpose": "allow you to monitor the location of students under your responsibility"}, "data": {"fullName": "Full name", "email": "Email", "phone": "Phone (optional)", "gpsLocation": "GPS location data", "studentLinks": "Student linking data"}, "retention": "24 months after last account activity"}, "rights": {"access": "Access data", "export": "Export data", "delete": "Delete data"}, "lgpd": {"title": "Personal Data Protection (LGPD)", "legalBasis": "Consent (Art. 7, I of LGPD)"}, "uk_gdpr": {"title": "Personal Data Protection (UK GDPR)", "legalBasis": "Consent (Art. 6(1)(a) of UK GDPR)"}, "privacyPolicy": {"title": "Privacy Policy - EduConnect", "description": "Detailed information about personal data processing", "controller": {"title": "1. Data Controller Identification", "name": "EduConnect - Family Location Platform", "contact": "Privacy contact: <PERSON><PERSON><PERSON>@gmail.com"}, "dataCollected": {"title": "2. <PERSON> Collected", "intro": "We collect only the data necessary for platform operation:", "identification": "Identification data: name, email", "contact": "Contact data: phone (optional)", "location": "Location data: GPS coordinates when shared", "navigation": "Navigation data: access logs for security"}, "purposes": {"title": "3. Processing Purposes", "authentication": "Authentication and access control", "locationSharing": "Secure location sharing", "communication": "Communication about security and updates", "improvement": "User experience improvement"}, "sharing": {"title": "4. Data Sharing", "intro": "Your data is shared only:", "authorized": "With authorized guardians/students", "legal": "To comply with legal obligations", "noSale": "We never sell your data to third parties"}, "rights": {"title": "5. Your Rights", "intro": "You can at any time:", "access": "Access your personal data", "correct": "Correct incorrect information", "delete": "Request account deletion", "export": "Export your data", "revoke": "Revoke consent"}, "security": {"title": "6. Security", "description": "We implement technical and organizational measures to protect your data against unauthorized access, loss or leakage."}, "contact": {"title": "7. <PERSON>", "intro": "To exercise your rights or clarify privacy questions:", "email": "Email: educate<PERSON><PERSON>@gmail.com", "response": "Response within 15 business days"}}}, "lgpdSettingsPage": {"mainTitle": "Privacy Settings", "mainDescription": "Manage your personal data according to the applicable data protection regulations.", "exportTitle": "Export My Data", "exportDescription": "Download a copy of all your personal data stored in our system.", "exportButton": "Export Data", "exportingButton": "Exporting...", "deleteAccountTitle": "Request Account Deletion", "deleteAccountDescription": "This action will request the permanent deletion of all your data. Your guardians will receive a notification to approve this action.", "deleteReasonLabel": "Reason for request (optional)", "deleteReasonPlaceholder": "Please tell us the reason for your deletion request...", "deleteButton": "Request Deletion", "deletingButton": "Processing...", "yourRightsTitle": "Your Rights"}, "interface": {"title": "Interface & Accessibility", "fontSize": {"label": "Font Size", "options": {"small": "Small", "medium": "Medium", "large": "Large", "extraLarge": "Extra Large"}}, "layoutStyle": {"label": "Layout Style", "options": {"default": "<PERSON><PERSON><PERSON>", "compact": "Compact", "spacious": "Spacious"}}, "highContrast": {"label": "High Contrast", "description": "Increase contrast for better visibility"}, "reducedMotion": {"label": "Reduce Motion", "description": "Minimize animations and transitions"}}, "help": {"buttons": {"back": "Back", "proceed": "Proceed"}, "student": {"title": "Student Help", "dashboard": {"title": "Student Dashboard", "description": "Overview of the main available functions."}, "sharing": {"title": "Location Sharing", "sendButton": "Use the \"Send\" button to share your location.", "individual": "Individual sharing is also available."}, "guardians": {"title": "Manage Guardians", "description": "Add or remove trusted guardians."}, "history": {"title": "History", "description": "View your recent location records."}, "profile": {"title": "Profile", "description": "Update your personal information."}, "faq": {"title": "Frequently Asked Questions", "q1": "How do I stop sharing?", "a1": "You can disable sharing anytime on the dashboard.", "q2": "I forgot my password", "a2": "Use the password recovery option on the login page.", "q3": "How do I delete my account?", "a3": "Go to privacy settings and request deletion."}, "support": {"title": "Support", "description": "If you have questions, contact support at"}}, "parent": {"title": "Guardian Help", "dashboard": {"title": "Guardian Dashboard", "description": "Main tools to monitor students."}, "managing": {"title": "Managing Students", "invite": "Invite new students using their email", "pending": "Approve or reject pending requests", "remove": "Remove a student from your list"}, "monitoring": {"title": "Monitoring", "description": "Follow real-time location when authorized."}, "profile": {"title": "Profile", "description": "Update your guardian profile information."}, "faq": {"title": "Frequently Asked Questions", "q1": "How to invite a student?", "a1": "Use the invite form on your dashboard.", "q2": "Can I have multiple guardians?", "a2": "Yes, students can link multiple guardians.", "q3": "How do I change my email?", "a3": "Edit your profile information."}, "support": {"title": "Support", "description": "For assistance contact"}, "removeDuplicates": {"title": "Remove Duplicate Locations", "step1": "Select a student in the guardian dashboard.", "step2": "Click the red \"Remove duplicates from database\" button at the top of the location history.", "step3": "Configure the removal criteria (radius, time window, accuracy) as needed.", "step4": "Type CONFIRM (or CONFIRMAR) to enable deletion and click \"Remove duplicates\".", "step5": "Wait for confirmation. Duplicate locations will be permanently removed from the database.", "warning": "Warning: This action is irreversible. Please review the criteria before confirming."}}}, "studentsList": {"title": "Linked Students", "none": "No students linked yet.", "helpTitle": "How to add students", "helpBullet1": "Fill out the form above with the student details", "helpBullet2": "Credentials are automatically emailed", "helpBullet3": "Existing students will be linked to your account", "helpBullet4": "You will receive confirmation when processed", "helpFooter": "System fully operational with multiple reliability levels"}, "studentInvitationsStatus": {"title": "Invitation Status", "pending": "Pending", "accepted": "Accepted", "rejected": "Rejected", "expired": "Expired", "unknown": "Unknown", "sent": "<PERSON><PERSON>", "acceptedAt": "Accepted", "expires": "Expires", "expiredAt": "Expired", "error": "Error loading invitations"}, "locationHistory": {"loadingAddresses": "Loading addresses...", "loadingHistory": "Loading history...", "errorTitle": "Error loading history", "noneFound": "No location history found", "noLocationsForStudent": "{{name}} has not shared any locations yet", "noLocations": "No locations registered yet", "requestShare": "Request that {{name}} shares their current location.", "title": "Location History", "oneLocation": "location", "manyLocations": "locations", "clickToView": "Click any location to view it on the map", "student": "Student", "mostRecent": "Most recent", "selected": "Selected", "accuracy": "Accuracy", "loadingAddress": "Loading address...", "addressUnavailable": "Address not available", "sharedWithGuardians": "Shared with guardians", "clickToSeeOnMap": "Click to view on map", "sendByEmail": "Send by <PERSON><PERSON>", "importantInfo": "Important Information", "automaticSharing": "Locations are shared automatically when {{name}} allows. Address information is obtained automatically based on GPS coordinates.", "clickToHighlight": "Click any location in the history to highlight it on the map."}, "lgpd": {"dataSummary": {"title": "Your Data Summary", "name": "Name", "email": "Email", "userType": "User Type", "locationTitle": "Location data", "locationStatusActive": "Sharing active"}, "export": {"title": "Export Data", "description": "Download a copy of all your personal data in JSON format.", "button": "Download My Data", "exporting": "Exporting..."}, "legalInfo": {"title": "Data Processing Information", "legalBasis": "Legal Basis:", "consent": "Consent (Art. 7, I of LGPD) for collection and processing of personal data.", "purposesTitle": "Processing Purposes:", "purposes": {"auth": "Authentication and access control", "location": "Location sharing", "security": "Security communication", "experience": "Experience improvement"}, "rightsTitle": "Your Rights:", "rights": {"access": "Access your data", "correct": "Correct information", "delete": "Request deletion", "portability": "Data portability", "revoke": "Revoke consent"}, "privacyQuestions": "Questions about privacy?", "contact": "Contact: educate<PERSON><PERSON>@gmail.com", "responseTime": "Response time: up to 15 business days"}, "accountDeletion": {"title": "Delete Account", "intro": "Permanently remove all your data. This action cannot be undone.", "button": "Delete My Account", "confirmTitle": "Confirm Account Deletion", "confirmList": ["All your personal data", "Location history", "Relationships with guardians/students", "Access to the platform"], "confirmButton": "Yes, Delete Permanently", "cancel": "Cancel", "processing": "Deleting..."}, "deletionRequests": {"title": "Account Deletion Requests", "actionRequired": "Action required", "studentRequested": "A student requested account deletion.", "studentsRequested": "{{count}} students requested account deletion.", "reviewPrompt": "Please review and make a decision.", "pending": "Pending", "requestedAt": "Requested at {{date}}", "reason": "Reason for request", "approve": "Approve Deletion", "reject": "Reject"}}, "relationships": {"supervisedTitle": "Supervised Students", "supervisedDescription": "Students you monitor and can supervise.", "studentViewTitle": "My Guardians", "studentViewDescription": "Guardians who can track your location and activities.", "noGuardians": "No guardians linked yet.", "contactGuardian": "Contact a guardian to create a link.", "loadingGuardians": "Loading guardians...", "guardianActive": "Active", "guardianInactive": "Inactive", "noStudents": "No students linked yet.", "inviteStudents": "Invite students to create family links.", "addStudentButton": "<PERSON> New Student"}}