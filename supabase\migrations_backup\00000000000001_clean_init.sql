-- =====================================================
-- MIGRAÇÃO INICIAL LIMPA - AMBIENTE LOCAL
-- Baseada no banco remoto funcionando (21 tabelas ativas)
-- =====================================================

-- <PERSON><PERSON>õ<PERSON> necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. PROFILES TABLE (Base do sistema)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.profiles (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  full_name TEXT NOT NULL,
  phone VARCHAR CHECK (phone ~ '^\\+[1-9][0-9]{7,14}$' OR phone IS NULL),
  user_type TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  email TEXT NOT NULL DEFAULT '',
  cpf VARCHAR NOT NULL UNIQUE,
  birth_date DATE,
  parent_email TEXT,
  parent_cpf TEXT,
  registration_status TEXT DEFAULT 'active',
  requires_parent_confirmation BOOLEAN DEFAULT false,
  status TEXT DEFAULT 'active',
  last_login_at TIMESTAMPTZ,
  login_count INTEGER DEFAULT 0
);

-- =====================================================
-- 2. LOCATIONS TABLE (Rastreamento de localização)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.locations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID DEFAULT auth.uid() NOT NULL REFERENCES auth.users(id),
  latitude DOUBLE PRECISION NOT NULL,
  longitude DOUBLE PRECISION NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT now() NOT NULL,
  address TEXT,
  shared_with_guardians BOOLEAN DEFAULT false,
  accuracy NUMERIC,
  speed NUMERIC,
  bearing NUMERIC,
  battery_level NUMERIC,
  is_mocked BOOLEAN DEFAULT false,
  source TEXT DEFAULT 'manual',
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. STUDENTS TABLE (Estudantes cadastrados)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.students (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  school_id TEXT NOT NULL,
  school_name TEXT NOT NULL,
  grade TEXT NOT NULL,
  class TEXT NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  guardian_id UUID NOT NULL REFERENCES auth.users(id),
  location_sharing BOOLEAN DEFAULT false NOT NULL,
  last_location JSONB,
  created_at TIMESTAMPTZ DEFAULT timezone('utc', now()),
  updated_at TIMESTAMPTZ DEFAULT timezone('utc', now())
);

-- =====================================================
-- 4. RELACIONAMENTOS ESTUDANTE-RESPONSÁVEL
-- =====================================================
CREATE TABLE IF NOT EXISTS public.student_guardian_relationships (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES auth.users(id),
  guardian_id UUID NOT NULL REFERENCES auth.users(id),
  relationship_type TEXT NOT NULL,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 5. SISTEMA DE REMOÇÃO SEGURA
-- =====================================================
CREATE TABLE IF NOT EXISTS public.removal_requests (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES auth.users(id),
  guardian_id UUID NOT NULL REFERENCES auth.users(id),
  student_name TEXT NOT NULL,
  guardian_name TEXT NOT NULL,
  guardian_email TEXT NOT NULL,
  reason TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
  request_token UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  expires_at TIMESTAMPTZ DEFAULT (now() + interval '7 days'),
  processed_at TIMESTAMPTZ,
  processed_by UUID REFERENCES auth.users(id)
);

-- =====================================================
-- 6. PERFIS DE RESPONSÁVEIS
-- =====================================================
CREATE TABLE IF NOT EXISTS public.guardian_profiles (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  full_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT,
  cpf TEXT,
  birth_date DATE,
  status TEXT DEFAULT 'active',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 7. CONVITES FAMILIARES
-- =====================================================
CREATE TABLE IF NOT EXISTS public.family_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES auth.users(id),
  guardian_email TEXT NOT NULL,
  student_name TEXT NOT NULL,
  student_email TEXT NOT NULL,
  invitation_token TEXT NOT NULL UNIQUE,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'expired')),
  expires_at TIMESTAMPTZ DEFAULT (now() + interval '7 days') NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  accepted_at TIMESTAMPTZ,
  accepted_by_guardian_id UUID REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 8. LOGS DE AUTENTICAÇÃO
-- =====================================================
CREATE TABLE IF NOT EXISTS public.auth_logs (
  id SERIAL PRIMARY KEY,
  event_type TEXT,
  user_id UUID,
  metadata JSONB,
  occurred_at TIMESTAMPTZ
);

-- =====================================================
-- 9. NOTIFICAÇÕES DE LOCALIZAÇÃO
-- =====================================================
CREATE TABLE IF NOT EXISTS public.location_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  location_id UUID REFERENCES public.locations(id),
  guardian_id UUID,
  guardian_email TEXT NOT NULL,
  student_id UUID NOT NULL,
  status TEXT DEFAULT 'unread' NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  viewed_at TIMESTAMPTZ
);

-- =====================================================
-- 10. HISTORICO DE LOCALIZAÇÕES
-- =====================================================
CREATE TABLE IF NOT EXISTS public.location_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  location_id UUID REFERENCES public.locations(id),
  user_id UUID REFERENCES auth.users(id),
  latitude NUMERIC NOT NULL,
  longitude NUMERIC NOT NULL,
  accuracy NUMERIC,
  speed NUMERIC,
  bearing NUMERIC,
  battery_level NUMERIC,
  is_mocked BOOLEAN,
  source TEXT,
  created_at TIMESTAMPTZ DEFAULT timezone('utc', now())
);

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Habilitar RLS em todas as tabelas
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.student_guardian_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.removal_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guardian_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.auth_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.location_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.location_history ENABLE ROW LEVEL SECURITY;

-- Políticas básicas para profiles
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can insert profiles" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Políticas para locations
CREATE POLICY "Users can view own locations" ON public.locations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own locations" ON public.locations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- =====================================================
-- FUNÇÕES RPC ESSENCIAIS
-- =====================================================

-- Função para buscar localizações do estudante
CREATE OR REPLACE FUNCTION get_student_locations_with_names(
  p_student_id UUID, 
  p_time_filter TEXT DEFAULT 'all'
)
RETURNS TABLE (
  id UUID,
  latitude DOUBLE PRECISION,
  longitude DOUBLE PRECISION,
  timestamp TIMESTAMPTZ,
  address TEXT,
  shared_with_guardians BOOLEAN,
  accuracy NUMERIC,
  speed NUMERIC,
  bearing NUMERIC,
  battery_level NUMERIC,
  is_mocked BOOLEAN,
  source TEXT,
  created_at TIMESTAMPTZ,
  student_name TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT 
    l.id,
    l.latitude,
    l.longitude,
    l.timestamp,
    l.address,
    l.shared_with_guardians,
    l.accuracy,
    l.speed,
    l.bearing,
    l.battery_level,
    l.is_mocked,
    l.source,
    l.created_at,
    COALESCE(p.full_name, 'Usuário') as student_name
  FROM public.locations l
  LEFT JOIN public.profiles p ON l.user_id = p.user_id
  WHERE l.user_id = p_student_id
  ORDER BY l.timestamp DESC;
END;
$$;

-- Função para criar solicitação de remoção
CREATE OR REPLACE FUNCTION create_removal_request(
  p_student_id UUID,
  p_guardian_id UUID,
  p_reason TEXT DEFAULT NULL
)
RETURNS JSON LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  student_profile public.profiles%ROWTYPE;
  guardian_profile public.profiles%ROWTYPE;
  new_request_id UUID;
BEGIN
  -- Buscar dados do estudante
  SELECT * INTO student_profile FROM public.profiles WHERE user_id = p_student_id;
  
  -- Buscar dados do responsável
  SELECT * INTO guardian_profile FROM public.profiles WHERE user_id = p_guardian_id;
  
  -- Verificar se os perfis existem
  IF student_profile.user_id IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'Estudante não encontrado');
  END IF;
  
  IF guardian_profile.user_id IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'Responsável não encontrado');
  END IF;
  
  -- Criar solicitação
  INSERT INTO public.removal_requests (
    student_id,
    guardian_id,
    student_name,
    guardian_name,
    guardian_email,
    reason,
    status
  ) VALUES (
    p_student_id,
    p_guardian_id,
    student_profile.full_name,
    guardian_profile.full_name,
    guardian_profile.email,
    p_reason,
    'pending'
  ) RETURNING id INTO new_request_id;
  
  RETURN json_build_object(
    'success', true,
    'request_id', new_request_id,
    'message', 'Solicitação criada com sucesso'
  );
END;
$$;

-- Função para buscar solicitações do responsável
CREATE OR REPLACE FUNCTION get_guardian_removal_requests(p_guardian_id UUID)
RETURNS TABLE (
  id UUID,
  student_name TEXT,
  reason TEXT,
  status TEXT,
  request_token UUID,
  created_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT 
    rr.id,
    rr.student_name,
    rr.reason,
    rr.status,
    rr.request_token,
    rr.created_at,
    rr.expires_at
  FROM public.removal_requests rr
  WHERE rr.guardian_id = p_guardian_id
    AND rr.status = 'pending'
    AND rr.expires_at > now()
  ORDER BY rr.created_at DESC;
END;
$$;

-- =====================================================
-- TRIGGERS PARA ATUALIZAÇÃO AUTOMÁTICA
-- =====================================================

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar triggers onde necessário
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON public.students
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_guardian_profiles_updated_at BEFORE UPDATE ON public.guardian_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- DADOS DE SEED BÁSICOS (OPCIONAL)
-- =====================================================

-- Inserir dados básicos para teste (se necessário)
-- Este bloco pode ser comentado em produção

/*
-- Exemplo de usuário de teste (descomente se precisar)
INSERT INTO auth.users (id, email, raw_app_meta_data, raw_user_meta_data, is_super_admin, role, aud, confirmation_token, recovery_token, email_change_token_new, email_change_new, created_at, updated_at, last_sign_in_at, email_confirmed_at)
VALUES ('11111111-1111-1111-1111-111111111111', '<EMAIL>', '{}', '{}', false, 'authenticated', 'authenticated', '', '', '', '', now(), now(), now(), now())
ON CONFLICT (id) DO NOTHING;

-- Perfil de teste
INSERT INTO public.profiles (user_id, full_name, email, cpf, user_type)
VALUES ('11111111-1111-1111-1111-111111111111', 'Usuário Teste', '<EMAIL>', '12345678901', 'student')
ON CONFLICT (cpf) DO NOTHING;
*/ 