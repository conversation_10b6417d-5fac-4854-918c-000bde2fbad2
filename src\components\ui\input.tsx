
import * as React from "react"
import { cn } from "@/lib/utils"
import { useDevice } from "@/hooks/useDevice"

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    const device = useDevice();
    
    // Get responsive input styles
    const getInputStyles = () => {
      if (device.orientation === 'portrait') {
        switch (device.size) {
          case 'xxs':
            return 'h-10 px-3 py-2 text-sm';
          case 'xs':
            return 'h-11 px-3.5 py-2.5 text-base';
          case 'sm':
            return 'h-12 px-4 py-3 text-base';
          default:
            return 'h-12 px-4 py-3 text-base md:h-10 md:text-sm';
        }
      } else {
        // Landscape - more compact
        switch (device.size) {
          case 'xxs':
          case 'xs':
            return 'h-9 px-3 py-2 text-sm';
          case 'sm':
            return 'h-10 px-3.5 py-2.5 text-base';
          default:
            return 'h-10 px-4 py-2.5 text-base md:h-9 md:text-sm';
        }
      }
    };
    
    // iOS specific styles to prevent zoom
    const getIOSStyles = () => {
      if (device.isIOS) {
        return 'text-base'; // Prevents zoom on iOS
      }
      return '';
    };
    
    // Touch-friendly styles
    const getTouchStyles = () => {
      if (device.isTouch) {
        return 'touch-manipulation'; // Improves touch responsiveness
      }
      return '';
    };
    
    return (
      <input
        type={type}
        className={cn(
          "flex w-full rounded-md border border-input bg-background ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors dark:bg-zinc-800 dark:text-white dark:placeholder:text-gray-400",
          getInputStyles(),
          getIOSStyles(),
          getTouchStyles(),
          // Better focus styles for mobile
          device.isTouch && 'focus:border-primary focus:ring-primary/20',
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
