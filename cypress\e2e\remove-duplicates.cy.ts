/// <reference types="cypress" />

describe('Remoção de localizações duplicadas', () => {
  before(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.window().then((win) => {
      win.sessionStorage.clear();
      win.localStorage.removeItem('supabase.auth.token');
    });
    cy.visit('/login');
    cy.get('[data-cy="email-input"]', { timeout: 10000 }).should('be.visible').type('<EMAIL>');
    cy.get('[data-cy="password-input"]').type('Escola2025!');
    cy.get('button[type="submit"]').click();
    cy.get('[data-cy="student-item"]', { timeout: 10000 }).should('exist');
  });

  it('Remove duplicatas de localizações de um estudante', () => {
    // Seleciona o primeiro estudante
    cy.get('[data-cy="student-item"]').first().click();
    cy.contains(/Histórico|History/).click();
    cy.get('[data-testid="location-count"]', { timeout: 20000 }).should('be.visible');

    // Conta localizações antes
    cy.get('[data-testid="location-count"]').invoke('text').then((countBefore) => {
      const before = parseInt(countBefore, 10);

      // Aciona deduplicação
      cy.contains(/Remover duplicatas do banco|Remove duplicates from database/).click();
      // Aguarda o modal abrir
      cy.contains(/Para confirmar a exclusão|To confirm deletion/).should('be.visible');
      // Aguarda o campo de confirmação (input ou textarea, PT/EN)
      cy.get('input[placeholder*="CONFIRM"], textarea[placeholder*="CONFIRM"]', { timeout: 10000 })
        .should('be.visible')
        .type('CONFIRMAR');
      // Confirma remoção
      cy.contains(/Remover duplicatas|Remove duplicates/).click();
      // Aguarda o modal fechar (botão sumir ou modal sumir)
      cy.contains(/Remover duplicatas|Remove duplicates/).should('not.exist');
      // Aguarda atualização
      cy.get('[data-testid="location-count"]', { timeout: 20000 }).should('be.visible');
      cy.get('[data-testid="location-count"]').invoke('text').then((countAfter) => {
        const after = parseInt(countAfter, 10);
        expect(after).to.be.lessThan(before);
      });
    });
  });
}); 