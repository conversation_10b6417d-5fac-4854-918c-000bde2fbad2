
import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface AuthMonitoringOptions {
  enableRealTimeAlerts?: boolean;
  logLevel?: 'basic' | 'detailed';
}

export const useAuthMonitoring = (options: AuthMonitoringOptions = {}) => {
  const { enableRealTimeAlerts = false, logLevel = 'basic' } = options;

  useEffect(() => {
    if (!enableRealTimeAlerts) return;

    // Monitor auth state changes with improved error handling
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        try {
          if (logLevel === 'detailed') {
            await supabase.from('auth_logs').insert({
              event_type: `auth_state_${event}`,
              user_id: session?.user?.id || null,
              metadata: {
                event,
                timestamp: new Date().toISOString(),
                session_exists: !!session,
                user_email: session?.user?.email
              }
            });
          }

          // Log critical events
          if (event === 'SIGNED_IN') {
            console.log('[AUTH_MONITOR] User signed in successfully');
          } else if (event === 'SIGNED_OUT') {
            console.log('[AUTH_MONITOR] User signed out');
          } else if (event === 'TOKEN_REFRESHED') {
            console.log('[AUTH_MONITOR] Token refreshed');
          }
        } catch (error) {
          console.error('[AUTH_MONITOR] Failed to log auth event:', error);
          // Don't propagate logging errors
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [enableRealTimeAlerts, logLevel]);

  // Utility function to log custom auth events with safe error handling
  const logAuthEvent = async (eventType: string, metadata: any = {}) => {
    // Skip logging during logout to prevent interference
    if (eventType.includes('logout')) {
      return;
    }
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      await supabase.from('auth_logs').insert({
        event_type: eventType,
        user_id: user?.id || null,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('[AUTH_MONITOR] Failed to log custom event:', error);
      // Silently handle logging errors to prevent blocking main functionality
    }
  };

  return { logAuthEvent };
};
