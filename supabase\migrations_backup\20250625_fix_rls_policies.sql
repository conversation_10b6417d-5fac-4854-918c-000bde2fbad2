-- Enable RLS on student_guardian_relationships table
ALTER TABLE public.student_guardian_relationships ENABLE ROW LEVEL SECURITY;

-- Remove any existing policies to avoid conflicts
DROP POLICY IF EXISTS "Guardians can view their relationships" ON public.student_guardian_relationships;
DROP POLICY IF EXISTS "Students can view their relationships" ON public.student_guardian_relationships;
DROP POLICY IF EXISTS "Users can view relationships they participate in" ON public.student_guardian_relationships;
DROP POLICY IF EXISTS "Guardians can create relationships" ON public.student_guardian_relationships;

-- Policy to allow guardians to view their relationships
CREATE POLICY "Guardians can view their relationships"
ON public.student_guardian_relationships
FOR SELECT
TO authenticated
USING (auth.uid() = guardian_id);

-- Policy to allow students to view their relationships  
CREATE POLICY "Students can view their relationships"
ON public.student_guardian_relationships
FOR SELECT
TO authenticated
USING (auth.uid() = student_id);

-- Policy to allow users to view relationships they participate in
CREATE POLICY "Users can view relationships they participate in"
ON public.student_guardian_relationships
FOR SELECT
TO authenticated
USING (auth.uid() = guardian_id OR auth.uid() = student_id);

-- Policy to allow guardians to create relationships
CREATE POLICY "Guardians can create relationships"
ON public.student_guardian_relationships
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = guardian_id);

-- Grant necessary permissions
GRANT SELECT ON public.student_guardian_relationships TO authenticated;
GRANT INSERT ON public.student_guardian_relationships TO authenticated; 