
import React, { Suspense, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ProfileTabsOptimized from '@/components/profile/ProfileTabsOptimized';
import ErrorBoundary from '@/components/ErrorBoundary';
import ProfileStatusBadge from '@/components/profile/ProfileStatusBadge';
import LastLoginInfo from '@/components/profile/LastLoginInfo';

const ProfilePageSkeleton = React.memo(() => (
  <div className="container mx-auto p-6 max-w-6xl">
    <div className="mb-8">
      <Skeleton className="h-8 w-48 mb-2" />
      <Skeleton className="h-4 w-96 mb-1" />
      <Skeleton className="h-3 w-80" />
    </div>
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          <Skeleton className="h-10 w-full" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
));

ProfilePageSkeleton.displayName = 'ProfilePageSkeleton';

const ProfilePage: React.FC = React.memo(() => {
  const { user, isLoading } = useUser();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const userName = useMemo(
    () => user?.full_name || (user as any)?.email || t('common.user'),
    [user?.full_name, (user as any)?.email, t]
  );

  const userType = useMemo(() => 
    user?.user_type, 
    [user?.user_type]
  );

  const userTypeDisplay = useMemo(() => {
    if (!userType) return t('common.user');
    return userType === 'student'
      ? t('auth.userTypes.student')
      : userType === 'parent'
        ? t('auth.userTypes.parent')
        : t('common.user');
  }, [userType, t]);

  console.log('[ProfilePage] Rendering with user:', !!user, 'loading:', isLoading);

  if (isLoading) {
    console.log('[ProfilePage] User loading, showing skeleton');
    return <ProfilePageSkeleton />;
  }

  if (!user) {
    console.log('[ProfilePage] No user found, showing login message');
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <p className="text-lg">{t('profile.notLoggedIn')}</p>
        </div>
      </div>
    );
  }

  console.log('[ProfilePage] Rendering profile for user:', userName, 'type:', userType);

  return (
    <ErrorBoundary>
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="content-overlay rounded-xl bg-transparent p-4 md:p-8 w-full max-w-6xl mx-auto">
          {/* Conteúdo original do ProfilePage */}
          <div className="mb-8">
            <Button 
              variant="ghost" 
              onClick={() => navigate(-1)}
              className="mb-4 p-2 hover:bg-gray-100"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common.back')}
            </Button>
            <h1 className="text-3xl font-bold">{t('profile.title')}</h1>
            <p className="text-muted-foreground mt-2">
              {t('profile.greeting', { name: userName })}
              {userType && (
                <span className="ml-1">({userTypeDisplay})</span>
              )}
              {user?.status && (
                <span className="ml-2"><ProfileStatusBadge status={user.status} /></span>
              )}
            </p>
            {user?.last_login_at && (
              <p className="text-sm text-muted-foreground">
                <LastLoginInfo lastLoginDate={user.last_login_at} />
              </p>
            )}
            <p className="text-sm text-muted-foreground mt-1">
              {t('profile.subtitle')}
            </p>
          </div>

          <Suspense fallback={<ProfilePageSkeleton />}>
            <ProfileTabsOptimized />
          </Suspense>
        </div>
      </div>
    </ErrorBoundary>
  );
});

ProfilePage.displayName = 'ProfilePage';

export default ProfilePage;
