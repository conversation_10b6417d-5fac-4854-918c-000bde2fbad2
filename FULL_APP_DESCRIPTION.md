# Localize-Family-Connect - Visão Geral Completa

## Introdução
O Localize-Family-Connect é um sistema de monitoramento de localização que conecta estudantes e seus responsáveis. O projeto utiliza React, TypeScript e Supabase para fornecer uma experiência segura e responsiva tanto para quem compartilha quanto para quem acompanha as localizações.

## Tipos de Usuário
- **Estudante** – Perfil focado em enviar sua localização e gerenciar responsáveis vinculados.
- **Responsável** – Perfil para acompanhar a localização dos estudantes autorizados e receber notificações.
- **Administrador/Desenvolvedor** – Perfis com acesso a ferramentas de gerenciamento e rotas especiais, como `/dev-dashboard`.

## Rotas Principais
As rotas são declaradas em `src/App.tsx` e cobrem páginas públicas de autenticação, páginas protegidas e rotas de ajuda.

| Rota | Descrição |
|------|-----------|
| `/` | Landing page com seleção de idioma |
| `/login` | Tela de login |
| `/register` | Cadastro de usuário |
| `/reset-password` | Recuperação de senha |
| `/accept-invitation` | Aceitar convite de vínculo |
| `/activate-account` | Ativar conta via link de email |
| `/dashboard` | Dashboard geral após login |
| `/parent-dashboard` | Painel do responsável |
| `/student-dashboard` | Painel do estudante |
| `/profile` | Configurações de perfil |
| `/guardians` | Gerenciar responsáveis |
| `/add-student` | Adicionar estudante via email/CPF |
| `/student-map/:id?` | Visualizar localizações em mapa |
| `/student-dashboard/location` | Atalho do estudante para o mapa |
| `/system-test` | Página de testes internos |
| `/ajuda/responsavel` | Ajuda dedicada aos responsáveis |
| `/ajuda/estudante` | Ajuda para estudantes |
| `/dev-dashboard` | Painel especial para desenvolvedores |
| `/dev/cypress` | Execução de testes e2e |
| `/dev/api-docs` | Documentação interativa da API |
| `/dev/database` | Explorador de banco de dados |

## Funcionalidades
- **Autenticação com Supabase** usando fluxo PKCE.
- **Compartilhamento de Localização** com envio para responsáveis via email (Resend).
- **Visualização em Mapa** com MapBox e histórico de coordenadas.
- **Vínculo Responsável–Estudante** bidirecional, permitindo convites e aceites.
- **Painéis Específicos**: Student Dashboard e Parent Dashboard com acesso aos dados relevantes de cada perfil.
- **Gerenciamento de Perfis** com atualização de dados pessoais e permissões.
- **Notificações e Alertas** por email e feedback visual em tempo real.
- **Admin/Developer Tools** com rotas protegidas para monitoramento e automação de testes.

## Estrutura Básica do Projeto
```
src/
├── components/         # Componentes reutilizáveis e específicos por perfil
├── contexts/           # Contextos de autenticação e dados globais
├── hooks/              # Lógica de negócios em hooks customizados
├── lib/                # Serviços, integrações e configuração do Supabase
├── pages/              # Páginas mapeadas nas rotas
└── supabase/           # Funções Edge e migrações de banco
```

## Fluxos de Usuário
1. **Registro e Onboarding** – O usuário cria sua conta, seleciona o tipo (estudante ou responsável) e confirma o email.
2. **Vinculação** – Estudantes convidam responsáveis ou vice‑versa, criando a relação no banco.
3. **Compartilhamento de Localização** – Estudante envia sua localização via mapa e os responsáveis recebem por email e no dashboard.
4. **Consulta e Histórico** – Responsáveis acessam o mapa e histórico de localizações para cada estudante vinculado.

## Considerações de Segurança
- Uso intensivo de RLS para isolar dados por usuário.
- Comunicação sempre via HTTPS com tokens JWT.
- Acesso a rotas sensíveis somente com autenticação válida e perfil apropriado.

Esta descrição resume o aplicativo e serve como ponto de partida para novos desenvolvedores compreenderem o escopo atual do Localize-Family-Connect.
