import React from 'react';
import StudentInfoPanel from '@/components/StudentInfoPanel';
import LocationActions from '@/components/location/LocationActions';
import { cn } from '@/lib/utils';

interface StudentInfoSectionProps {
  userFullName: string;
  email: string | null;
  phone: string;
  onGetLocation: () => void;
  isGettingLocation: boolean;
  onShareAll?: () => Promise<void>;
  guardianCount: number;
  isSendingAll: boolean;
  sharingStatus: 'idle' | 'loading' | 'success' | 'error';
  hasLocations: boolean;
  className?: string;
}

const StudentInfoSection: React.FC<StudentInfoSectionProps> = ({
  userFullName,
  email,
  phone,
  onGetLocation,
  isGettingLocation,
  onShareAll,
  guardianCount,
  isSendingAll,
  sharingStatus,
  hasLocations,
  className
}) => {
  const cardClasses =
    'bg-white/60 backdrop-blur shadow-lg border border-white/30 text-foreground p-4 rounded-2xl min-w-[280px] w-full';

  return (
    <StudentInfoPanel
      className={cn(cardClasses, className)}
      userFullName={userFullName}
      email={email}
      phone={phone}
    />
  );
};

export default StudentInfoSection;
