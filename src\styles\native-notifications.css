/* Native Notifications Styling */

/* Notification badges */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* iOS specific notification styling */
.ios-notification {
  --notification-background: rgba(250, 250, 250, 0.95);
  --notification-text: #000;
  --notification-border: rgba(0, 0, 0, 0.1);
  --notification-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --notification-border-radius: 13px;
}

/* Android specific notification styling */
.android-notification {
  --notification-background: #fff;
  --notification-text: #000;
  --notification-border: rgba(0, 0, 0, 0.05);
  --notification-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  --notification-border-radius: 8px;
}

/* In-app notification toast */
.native-notification-toast {
  position: fixed;
  top: env(safe-area-inset-top, 20px);
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 32px);
  max-width: 400px;
  padding: 12px 16px;
  background: var(--notification-background);
  color: var(--notification-text);
  border: 1px solid var(--notification-border);
  border-radius: var(--notification-border-radius);
  box-shadow: var(--notification-shadow);
  z-index: 9999;
  display: flex;
  align-items: flex-start;
  animation: notification-slide-in 0.3s ease-out forwards;
}

.native-notification-toast.ios-notification {
  padding: 12px 16px;
}

.native-notification-toast.android-notification {
  padding: 14px 16px;
}

.notification-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 14px;
}

.notification-body {
  font-size: 13px;
  opacity: 0.9;
}

.notification-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
}

/* Animation for notification appearance */
@keyframes notification-slide-in {
  0% {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

/* Animation for notification disappearance */
@keyframes notification-slide-out {
  0% {
    transform: translate(-50%, 0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
}

.notification-exit {
  animation: notification-slide-out 0.3s ease-in forwards;
}

/* Notification action buttons */
.notification-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.notification-action-button {
  background: transparent;
  border: none;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
}

.ios-notification .notification-action-button {
  color: #007aff;
}

.android-notification .notification-action-button {
  color: #1a73e8;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.notification-action-button.destructive {
  color: #ff3b30;
}

/* Notification counter */
.notification-counter {
  position: relative;
}

.notification-counter-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Offline notification indicator */
.offline-notification-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 16px;
  font-size: 12px;
  margin-top: 8px;
}

.offline-notification-indicator svg {
  margin-right: 4px;
}