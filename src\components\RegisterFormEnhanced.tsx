import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import PasswordField from '@/components/auth/PasswordField';
import { validateCPF } from '@/lib/utils/cpf-validator';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Calendar as CalendarIcon } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { addYears, isBefore } from 'date-fns';

const PHONE_REGEX = /^\(\d{2}\) \d{4,5}-\d{4}$/;

// Schema de validação
const registerSchema = z.object({
  name: z
    .string()
    .min(3, { message: 'Nome deve ter pelo menos 3 caracteres' })
    .max(100, { message: 'Nome muito longo' }),
  cpf: z
    .string()
    .min(11, { message: 'CPF inválido' })
    .max(14, { message: 'CPF inválido' })
    .refine(validateCPF, { message: 'CPF inválido' }),
  birthDate: z.date({
    required_error: "Data de nascimento é obrigatória",
  }).refine(
    (date) => isBefore(date, new Date()),
    {
      message: "A data de nascimento não pode ser no futuro",
    }
  ),
  phone: z
    .string()
    .regex(PHONE_REGEX, { message: 'Formato: (99) 99999-9999' })
    .optional(),
  email: z
    .string()
    .min(1, { message: 'Email é obrigatório' })
    .email({ message: 'Email inválido' }),
  password: z
    .string()
    .min(8, { message: 'Senha deve ter pelo menos 8 caracteres' })
    .max(100, { message: 'Senha muito longa' })
    .regex(/[A-Z]/, { message: 'Senha deve ter pelo menos uma letra maiúscula' })
    .regex(/[a-z]/, { message: 'Senha deve ter pelo menos uma letra minúscula' })
    .regex(/[0-9]/, { message: 'Senha deve ter pelo menos um número' })
    .regex(/[^A-Za-z0-9]/, {
      message: 'Senha deve ter pelo menos um caractere especial',
    }),
  confirmPassword: z.string(),
  userTypeConfirm: z.boolean().refine(value => value === true, {
    message: "Você precisa confirmar seu tipo de usuário",
  }),
})
.refine((data) => data.password === data.confirmPassword, {
  message: 'As senhas não coincidem',
  path: ['confirmPassword'],
});

type RegisterFormData = z.infer<typeof registerSchema>;

export interface RegisterFormProps {
  userType: 'student' | 'parent';
  onLoginClick?: () => void;
  variant?: 'login' | 'register';
}

interface PasswordRequirement {
  regex: RegExp;
  text: string;
}

const passwordRequirements: PasswordRequirement[] = [
  { regex: /.{8,}/, text: 'Pelo menos 8 caracteres' },
  { regex: /[A-Z]/, text: 'Pelo menos uma letra maiúscula' },
  { regex: /[a-z]/, text: 'Pelo menos uma letra minúscula' },
  { regex: /[0-9]/, text: 'Pelo menos um número' },
  { regex: /[^A-Za-z0-9]/, text: 'Pelo menos um caractere especial' },
];

const RegisterFormEnhanced: React.FC<RegisterFormProps> = ({
  userType,
  onLoginClick,
  variant = 'register',
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  const {
    register,
    handleSubmit,
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: '',
      cpf: '',
      phone: '',
      email: '',
      password: '',
      confirmPassword: '',
      userTypeConfirm: false,
    },
  });

  const password = watch('password', '');
  const confirmPassword = watch('confirmPassword', '');

  // Função para calcular força da senha
  React.useEffect(() => {
    if (password) {
      let strength = 0;
      passwordRequirements.forEach((requirement) => {
        if (requirement.regex.test(password)) {
          strength += 20;
        }
      });
      setPasswordStrength(strength);
    } else {
      setPasswordStrength(0);
    }
  }, [password]);

  // Função para obter a cor da barra de força
  const getStrengthColor = (strength: number) => {
    if (strength < 40) return 'bg-red-500';
    if (strength < 80) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // Função para verificar cada requisito da senha
  const checkPasswordRequirement = (regex: RegExp) => {
    return regex.test(password);
  };

  const onSubmit = async (data: RegisterFormData) => {
    try {
      setLoading(true);

      // Prepare os dados do usuário
      const userPayload = {
        email: data.email,
        password: data.password,
        options: {
          data: {
            name: data.name,
            cpf: data.cpf,
            phone: data.phone || '',
            birth_date: format(data.birthDate, 'yyyy-MM-dd'),
            user_type: userType,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            consent_lgpd: true,
            consent_terms: true,
          },
        },
      };

      // Fazer o registro no Supabase
      const { error } = await supabase.auth.signUp(userPayload);

      if (error) {
        throw error;
      }

      // Resetar o formulário e mostrar mensagem de sucesso
      reset();
      toast({
        title: 'Conta criada com sucesso!',
        description: 'Verifique seu email para confirmar seu cadastro.',
      });
    } catch (error: any) {
      console.error('Registration error:', error);
      
      // Tratar erros específicos
      if (error.message.includes('already exists')) {
        toast({
          title: 'Erro no registro',
          description: 'Este email já está em uso.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Erro no registro',
          description:
            error.message || 'Ocorreu um erro durante o registro. Tente novamente.',
          variant: 'destructive',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
        <div className="bg-yellow-100 p-3 rounded-md text-sm border border-yellow-300 mb-4">
          <p className="font-semibold">Formulário Melhorado</p>
          <p className="text-xs">Este é o formulário com todas as melhorias implementadas.</p>
        </div>
        
        <div className="space-y-2">
          <Label>Nome Completo</Label>
          <Input
            {...register('name')}
            type="text"
            placeholder="Nome completo"
            className="w-full"
            data-cy="name-input"
          />
          {errors.name && (
            <p className="text-sm text-red-500">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>CPF</Label>
          <Input
            {...register('cpf')}
            type="text"
            placeholder="000.000.000-00"
            className="w-full"
            data-cy="cpf-input"
          />
          {errors.cpf && (
            <p className="text-sm text-red-500">{errors.cpf.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Data de Nascimento</Label>
          <Controller
            control={control}
            name="birthDate"
            render={({ field }) => (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !field.value && "text-gray-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {field.value ? (
                      format(field.value, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })
                    ) : (
                      <span>Selecione sua data de nascimento</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => {
                      // Não permitir datas futuras
                      return date > new Date() || 
                        // Para guardiões, exigir idade mínima de 18 anos
                        (userType === 'parent' && date > addYears(new Date(), -18));
                    }}
                    initialFocus
                    locale={ptBR}
                  />
                </PopoverContent>
              </Popover>
            )}
          />
          {errors.birthDate && (
            <p className="text-sm text-red-500">{String(errors.birthDate.message)}</p>
          )}
          {userType === 'parent' && (
            <p className="text-xs text-gray-500">Guardiões precisam ter pelo menos 18 anos.</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Telefone (opcional)</Label>
          <Input
            {...register('phone')}
            type="text"
            placeholder="(00) 00000-0000"
            className="w-full"
            data-cy="phone-input"
          />
          {errors.phone && (
            <p className="text-sm text-red-500">{errors.phone.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Email</Label>
          <Input
            {...register('email')}
            type="email"
            placeholder="<EMAIL>"
            className="w-full"
            data-cy="email-input"
          />
          {errors.email && (
            <p className="text-sm text-red-500">{errors.email.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Senha</Label>
          <PasswordField
            value={password}
            onChange={(value) => {
              // Atualiza o valor no hook form
              const event = { target: { value } } as React.ChangeEvent<HTMLInputElement>;
              register('password').onChange(event);
            }}
            showPassword={showPassword}
            onToggleVisibility={() => setShowPassword(!showPassword)}
            placeholder="Crie uma senha forte"
            data-cy="password-input"
          />
          {password && (
            <div className="space-y-2 mt-2">
              <div className="h-1 w-full bg-gray-200 rounded-full">
                <div
                  className={`h-1 rounded-full ${getStrengthColor(passwordStrength)}`}
                  style={{ width: `${passwordStrength}%` }}
                ></div>
              </div>
              <ul className="text-xs space-y-1 mt-2">
                {passwordRequirements.map((req, index) => (
                  <li
                    key={index}
                    className={`flex items-center ${
                      checkPasswordRequirement(req.regex)
                        ? 'text-green-600'
                        : 'text-gray-500'
                    }`}
                  >
                    <span className="mr-1">
                      {checkPasswordRequirement(req.regex) ? '✓' : '○'}
                    </span>
                    {req.text}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {errors.password && (
            <p className="text-sm text-red-500">{errors.password.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Confirme sua senha</Label>
          <PasswordField
            value={confirmPassword}
            onChange={(value) => {
              // Atualiza o valor no hook form
              const event = { target: { value } } as React.ChangeEvent<HTMLInputElement>;
              register('confirmPassword').onChange(event);
            }}
            showPassword={showConfirmPassword}
            onToggleVisibility={() => setShowConfirmPassword(!showConfirmPassword)}
            placeholder="Repita sua senha"
            data-cy="confirm-password-input"
          />
          {confirmPassword && password !== confirmPassword && (
            <p className="text-sm text-amber-500">As senhas não coincidem</p>
          )}
          {errors.confirmPassword && (
            <p className="text-sm text-red-500">{String(errors.confirmPassword.message)}</p>
          )}
        </div>

        <div className="flex items-start space-x-2 mt-4">
          <input
            type="checkbox"
            id="userTypeConfirm"
            className="mt-1"
            {...register('userTypeConfirm')}
          />
          <label htmlFor="userTypeConfirm" className="text-sm">
            Confirmo que estou me registrando como{' '}
            <strong>
              {userType === 'student' ? 'Estudante' : 'Guardião/Responsável'}
            </strong>
          </label>
        </div>
        {errors.userTypeConfirm && (
          <p className="text-sm text-red-500">{errors.userTypeConfirm.message}</p>
        )}

        <div className="bg-blue-50 p-3 rounded-md text-sm border border-blue-100 mt-4">
          <p className="text-blue-800">
            Após o registro, enviaremos um email de confirmação para verificar seu endereço.
          </p>
        </div>

        <Button
          type="submit"
          className={cn(
            'w-full',
            userType === 'parent' ? 'bg-emerald-500 hover:bg-emerald-600' : ''
          )}
          disabled={loading}
          data-cy="register-submit"
        >
          {loading ? 'Registrando...' : 'Criar conta'}
        </Button>

        {onLoginClick && (
          <p className="text-sm text-center mt-4">
            Já tem uma conta?{' '}
            <button
              type="button"
              onClick={onLoginClick}
              className="text-primary hover:underline"
            >
              Faça login
            </button>
          </p>
        )}
      </form>
    </div>
  );
};

export default RegisterFormEnhanced;

