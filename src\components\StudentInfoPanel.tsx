import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface StudentInfoPanelProps {
  userFullName: string;
  email: string | null;
  phone: string;
  className?: string;
}

const StudentInfoPanel: React.FC<StudentInfoPanelProps> = ({
  userFullName,
  email,
  phone,
  className
}) => {
  const { t } = useTranslation();

  return (
    <Card className={cn('h-full flex-1 min-w-[220px] max-w-md bg-white/60 backdrop-blur shadow-lg border border-white/30 text-foreground', className)}>
      <CardHeader>
        <CardTitle className="text-foreground dark:text-white">{t('profile.personalInfo')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <p className="text-foreground dark:text-white"><strong className="text-foreground dark:text-white">{t('profile.name', 'Name')}:</strong> {userFullName}</p>
            <p className="text-foreground dark:text-white"><strong className="text-foreground dark:text-white">{t('profile.email', 'Email')}:</strong> {email}</p>
            <p className="text-foreground dark:text-white"><strong className="text-foreground dark:text-white">{t('profile.phone', 'Phone')}:</strong> {phone}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StudentInfoPanel;
