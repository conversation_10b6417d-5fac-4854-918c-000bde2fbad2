
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mail, Check, Trash2 } from 'lucide-react';
import ProfileStatusBadge from '@/components/profile/ProfileStatusBadge';
import { cn } from '@/lib/utils';

interface GuardianCardProps {
  id: string;
  name: string;
  email: string;
  phone: string;
  cpf?: string;
  birthDate?: string;
  status?: string;
  isActive: boolean;
  createdAt: string;
  onRemove: (id: string) => Promise<void>;
  onSendInvite: (email: string, name: string) => Promise<void>;
  className?: string;
}

export const GuardianCard: React.FC<GuardianCardProps> = ({
  id,
  name,
  email,
  phone,
  cpf,
  birthDate,
  status,
  isActive,
  createdAt,
  onRemove,
  onSendInvite,
  className
}) => {
  const { t } = useTranslation();
  return (
    <Card className={cn("break-words", className)}>
      <CardHeader>
        <CardTitle>{name}</CardTitle>
        <div className="text-sm text-muted-foreground flex items-center gap-2">
          {email}
          {status && <ProfileStatusBadge status={status} />}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground">
          {phone && <p>{t('guardianManager.phone', 'Phone')}: {phone}</p>}
          {cpf && <p>CPF: {cpf.slice(0,3)}.***.***-{cpf.slice(-2)}</p>}
          {birthDate && <p>Nascimento: {new Date(birthDate).toLocaleDateString()}</p>}
          <p>{t('guardianManager.addedAt', 'Added on')}: {new Date(createdAt).toLocaleDateString()}</p>
        </div>
      </CardContent>
      <CardFooter className="flex flex-wrap justify-between items-center gap-2">
        <Button 
          variant={isActive ? "secondary" : "outline"}
          size="sm"
          disabled={isActive}
          onClick={() => onSendInvite(email, name)}
        >
          {isActive ? (
            <>
              <Check className="mr-2 h-4 w-4" /> {t('guardianManager.connected', 'Connected')}
            </>
          ) : (
            <>
              <Mail className="mr-2 h-4 w-4" /> {t('guardianManager.invite', 'Invite')}
            </>
          )}
        </Button>
        <Button 
          variant="destructive" 
          size="sm"
          onClick={() => onRemove(id)}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          {t('guardianManager.remove', 'Remove')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default GuardianCard;
