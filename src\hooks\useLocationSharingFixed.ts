import { useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { supabase } from '@/integrations/supabase/client';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { useSyncQueue } from '@/hooks/useSyncQueue';

export const useLocationSharingFixed = () => {
  const [isSharing, setIsSharing] = useState(false);
  const { toast } = useToast();
  const { isOnline } = useNetworkStatus();
  const { enqueueAction } = useSyncQueue();

  const shareLocation = async (
    latitude: number,
    longitude: number,
    guardianEmail: string,
    senderName?: string
  ): Promise<boolean> => {
    setIsSharing(true);

    try {
      if (!isOnline) {
        await enqueueAction('location_share', {
          latitude,
          longitude,
          guardianEmail,
          senderName: senderName || 'Estudante'
        });

        toast({
          title: '📡 Offline',
          description: 'Localização salva para envio quando online',
          variant: 'default'
        });

        return true;
      }

      console.log('🚀 [useLocationSharingFixed] Compartilhando localização:', {
        email: guardianEmail,
        latitude,
        longitude,
        senderName: senderName || 'Estudante'
      });

      const { data, error } = await supabase.functions.invoke('share-location', {
        body: {
          email: guardianEmail,
          latitude,
          longitude,
          senderName: senderName || 'Estudante',
          isRequest: false
        }
      });

      if (error) {
        console.error('❌ [useLocationSharingFixed] Erro da Edge Function:', error);
        throw error;
      }

      console.log('✅ [useLocationSharingFixed] Resposta da Edge Function:', data);

      toast({
        title: "✅ Sucesso!",
        description: `Localização compartilhada com ${guardianEmail}`,
        variant: "default"
      });

      return true;
    } catch (error: any) {
      console.error('❌ [useLocationSharingFixed] Erro ao compartilhar localização:', error);

      const errorMessage = error?.message || error?.details || 'Erro desconhecido';

      // Tentar enfileirar ação se possível
      try {
        await enqueueAction('location_share', {
          latitude,
          longitude,
          guardianEmail,
          senderName: senderName || 'Estudante'
        });
        toast({
          title: '📥 Enfileirado',
          description: 'Será reenviado automaticamente quando online',
          variant: 'default'
        });
      } catch (queueErr) {
        toast({
          title: "❌ Erro",
          description: `Não foi possível compartilhar: ${errorMessage}`,
          variant: "destructive"
        });
        return false;
      }

      return true;
    } finally {
      setIsSharing(false);
    }
  };

  const shareLocationWithAll = async (
    latitude: number,
    longitude: number,
    guardianEmails: string[],
    senderName?: string
  ): Promise<{ success: number; total: number; errors: string[] }> => {
    console.log('🚀 [useLocationSharingFixed] Compartilhando com todos os responsáveis:', guardianEmails);
    
    setIsSharing(true);
    const results = { success: 0, total: guardianEmails.length, errors: [] as string[] };
    
    try {
      // Compartilhar com todos os responsáveis em paralelo
      const promises = guardianEmails.map(async (email) => {
        try {
          const success = await shareLocation(latitude, longitude, email, senderName);
          if (success) {
            results.success++;
          } else {
            results.errors.push(`Falha ao enviar para ${email}`);
          }
        } catch (error: any) {
          results.errors.push(`Erro ao enviar para ${email}: ${error.message}`);
        }
      });
      
      await Promise.all(promises);
      
      console.log('📊 [useLocationSharingFixed] Resultado final:', results);
      
      return results;
    } finally {
      setIsSharing(false);
    }
  };

  return {
    shareLocation,
    shareLocationWithAll,
    isSharing
  };
};