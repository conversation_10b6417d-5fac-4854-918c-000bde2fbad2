# 📋 QUESTIONÁRIO COMPLETO - SISTEMA EDUCONNECT

## 📍 **CONTEXTO ATUAL DO PROJETO**

O EduConnect é um sistema React/Vite + Supabase para monitoramento consensual de localização entre estudantes e responsáveis, com as seguintes características principais:

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Edge Functions)
- **Mapas**: MapBox para visualização de localização
- **Email**: Resend API para notificações
- **Arquitetura**: SPA responsivo com RLS (Row Level Security)

---

## **1. ARQUITETURA E INFRAESTRUTURA** 

**1.1** O projeto usa **Supabase** como backend principal. Você confirma que todas as configurações do Supabase estão funcionais (autenticação, banco de dados, edge functions)?

**1.2** O sistema de **emails** usa Resend API. A chave atual está funcionando? Você possui acesso ao painel do Resend?

**1.3** **MapBox** é usado para mapas. O token atual está ativo e funcionando corretamente?

**1.4** Qual é o **domínio de produção** atual? (`sistema-monitore.com.br` é válido?)

---

## **2. TIPOS DE USUÁRIO E AUTENTICAÇÃO**

**2.1** Confirme os **3 tipos** de usuário:
- ✅ **Estudante** (compartilha localização)
- ✅ **Responsável/Guardian** (monitora estudantes)  
- ✅ **Developer** (administração)

**2.2** Como funciona o **registro inicial**? Estudantes se registram primeiro ou responsáveis podem criar convites?

**2.3** O **login** deve ser único para todos os tipos ou separado por tipo (como `/login/responsavel`)?

**2.4** A **recuperação de senha** deve funcionar igual para todos os tipos de usuário?

---

## **3. FLUXO DE CONVITES E VÍNCULOS**

**3.1** **CENÁRIO PRINCIPAL**: Uma mãe quer se vincular ao filho já cadastrado. Qual é o fluxo correto:
- A) Mãe procura filho por email/CPF e envia convite
- B) Filho adiciona mãe e ela confirma
- C) Ambas opções disponíveis

**3.2** Os **convites** devem expirar? Em quanto tempo (7 dias está ok)?

**3.3** Deve ter **notificação por email** quando:
- Convite é enviado?
- Convite é aceito?
- Vínculo é criado?

**3.4** Um estudante pode ter **múltiplos responsáveis** (pai, mãe, avó, etc.)?

---

## **4. COMPARTILHAMENTO DE LOCALIZAÇÃO**

**4.1** O compartilhamento é:
- A) **Manual** (estudante clica para enviar)
- B) **Automático** (localização em tempo real)
- C) **Ambos** (estudante escolhe)

**4.2** **Histórico** de localizações deve ser mantido por quanto tempo?

**4.3** **Responsáveis** podem:
- Apenas ver localização atual?
- Ver histórico completo?
- Solicitar localização específica?

**4.4** Deve ter **alertas** por área (geofencing)?

---

## **5. INTERFACE E EXPERIÊNCIA**

**5.1** **Dashboard do Responsável** deve mostrar:
- Lista de estudantes vinculados?
- Mapa com localizações atuais?
- Histórico de movimentação?
- Configurações de notificação?

**5.2** **Dashboard do Estudante** deve ter:
- Lista de responsáveis vinculados?
- Controles de privacidade?
- Botão de compartilhar localização?
- Histórico do que foi compartilhado?

**5.3** O sistema deve funcionar bem em **mobile**? É prioritário mobile ou desktop?

---

## **6. CONFIGURAÇÕES E PRIVACIDADE**

**6.1** **Estudante** pode:
- Remover responsáveis?
- Pausar compartilhamento temporariamente?
- Ver o que foi compartilhado?

**6.2** **Responsável** pode:
- Configurar frequência de notificações?
- Definir alertas por horário?
- Exportar dados de localização?

**6.3** **LGPD/Privacidade**: O sistema precisa de:
- Termo de consentimento específico?
- Opção de exclusão completa dos dados?
- Relatório de dados coletados?

---

## **7. PROBLEMA ATUAL - FORMULÁRIO "ADICIONAR ESTUDANTE"**

**7.1** Você está testando no **Parent Dashboard** → "Adicionar Estudante". Os dados de teste são:
- CPF: 717.102.482-20
- Nome: Fabio Leda Cunha
- Email: <EMAIL>
- Telefone: +5592994897102

**7.2** O erro era **500** na Edge Function `invite-student`. Agora deveria estar **funcionando** após as correções das políticas RLS. Você pode testar novamente?

**7.3** Se ainda der erro, você pode **verificar os logs** no console do navegador e me enviar?

---

## **8. NEXT STEPS - APÓS CORRIGIR O CONVITE**

**8.1** Depois que o convite funcionar, qual é a **prioridade**:
- A) Melhorar interface do dashboard
- B) Implementar notificações por email  
- C) Adicionar mais funcionalidades de mapa
- D) Testes e correções de bugs

**8.2** Você tem **usuários reais** testando ou é apenas desenvolvimento?

**8.3** Qual é o **cronograma** desejado para ter uma versão funcional completa?

---

## **9. RECURSOS E EQUIPE**

**9.1** Você está desenvolvendo **sozinho** ou tem uma equipe?

**9.2** Qual é seu **nível de experiência** com:
- React/TypeScript
- Supabase
- Configuração de email/DNS
- Deploy e DevOps

**9.3** Você tem **orçamento** para:
- Serviços externos (Supabase, Resend, MapBox)
- Domínio e hospedagem
- Ferramentas de desenvolvimento

---

## **10. VISÃO DE FUTURO**

**10.1** **Público-alvo** principal:
- Famílias em geral?
- Escolas e instituições?
- Empresas com funcionários?

**10.2** **Monetização** (se aplicável):
- Gratuito sempre?
- Freemium (recursos limitados)?
- Assinatura mensal?

**10.3** **Escalabilidade**: Espera quantos usuários simultâneos inicialmente?

---

## **11. FUNCIONALIDADES IMPLEMENTADAS (BASEADO NA DOCUMENTAÇÃO)**

### ✅ **IMPLEMENTADO**
- Autenticação completa com Supabase
- Sistema de perfis por tipo de usuário
- Dashboard do Estudante com compartilhamento manual
- Dashboard do Responsável com visualização
- Sistema de vínculos familiares (`family_invitations`)
- Edge Functions para convites
- Mapas MapBox integrados
- Sistema de emails via Resend
- RLS (Row Level Security) implementado
- Interface responsiva

### 🚧 **EM DESENVOLVIMENTO/CORREÇÃO**
- Formulário "Adicionar Estudante" (problema nas políticas RLS - CORRIGIDO)
- Recuperação de senha
- Notificações automáticas por email

### 📋 **FUNCIONALIDADES DOCUMENTADAS MAS NÃO CONFIRMADAS**
- Geofencing (alertas por área)
- Histórico de localizações com filtros
- Múltiplos responsáveis por estudante
- Sistema de permissões granulares
- Modo offline
- Aplicativo mobile nativo

---

## **12. ARQUITETURA TÉCNICA ATUAL**

### **Database Schema (Supabase)**
- `profiles` - Perfis de usuário
- `locations` - Histórico de localizações  
- `guardians` / `student_guardian_relationships` - Vínculos
- `family_invitations` - Sistema de convites
- `auth.users` - Usuários do Supabase Auth

### **Edge Functions**
- `invite-student` - Criar convites para estudantes
- `share-location` - Compartilhar localização por email
- `email-service` - Serviço de emails
- `test-email` - Teste de configuração de email

### **Frontend Structure**
- `src/components/` - Componentes por domínio (student/, guardian/, map/)
- `src/contexts/` - Contextos de autenticação
- `src/lib/services/` - Serviços de negócio
- `src/pages/` - Páginas da aplicação
- `src/hooks/` - Hooks customizados

---

**📝 INSTRUÇÃO PARA PREENCHIMENTO:**
Responda **quantas perguntas conseguir** - isso me ajudará a entender exatamente o que você precisa e priorizar as correções e melhorias certas! 🎯

**🔗 PRÓXIMO PASSO IMEDIATO:**
Teste o formulário "Adicionar Estudante" em http://localhost:4000/parent-dashboard com os dados fornecidos e reporte se ainda há erro.
