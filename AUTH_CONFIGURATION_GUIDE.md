# 🔐 GUIA DE CONFIGURAÇÃO DE AUTENTICAÇÃO SUPABASE

**Data:** 31/01/2025  
**Projeto:** locate-family-connect  
**Ambiente:** Supabase Authentication

---

## 🎯 CONFIGURAÇÃO RECOMENDADA

### **1. Site URL (Produção)**
```
https://monitore-mvp.lovable.app
```
**Justificativa:** URL principal da aplicação em produção

### **2. Redirect URLs Organizadas**

#### **🏠 DESENVOLVIMENTO**
```
http://localhost:4000/**
http://localhost:3000/**
http://127.0.0.1:4000/**
```

#### **🧪 STAGING/PREVIEW**
```
https://preview--monitore-mvp.lovable.app/**
https://6c8163f7-b023-44b2-bbbd-e884e83007bf.lovableproject.com/**
https://id-preview--6c8163f7-b023-44b2-bbbd-e884e83007bf.lovable.app/**
```

#### **🚀 PRODUÇÃO**
```
https://monitore-mvp.lovable.app/**
https://sistema-monitore.com.br/**
https://www.sistema-monitore.com.br/**
```

#### **🔄 ALTERNATIVAS/BACKUP**
```
https://monitore-s1pv.onrender.com/**
```

---

## 🚨 PROBLEMAS ATUAIS IDENTIFICADOS

### **❌ Problema 1: Site URL Incorreta**
- **Atual:** `https://sistema-monitore.com.br/login`
- **Problema:** Aponta para rota específica `/login` em vez da raiz
- **Impacto:** Redirecionamentos podem falhar

### **❌ Problema 2: URLs Locais Ausentes**  
- **Ausente:** `http://localhost:4000/**`
- **Impacto:** Desenvolvimento local com problemas de auth
- **Erro típico:** "Invalid redirect URL" em login local

### **❌ Problema 3: URLs Desorganizadas**
- **Problema:** Mistura de staging/prod sem hierarquia clara
- **Impacto:** Dificuldade de manutenção e debug

---

## 🔧 INSTRUÇÕES DE CORREÇÃO

### **PASSO 1: Corrigir Site URL**
1. Acesse [Auth URL Configuration](https://supabase.com/dashboard/project/rsvjnndhbyyxktbczlnk/auth/url-configuration)
2. Altere Site URL de:
   ```
   https://sistema-monitore.com.br/login
   ```
   Para:
   ```
   https://monitore-mvp.lovable.app
   ```
3. Clique em "Save changes"

### **PASSO 2: Adicionar URLs de Desenvolvimento**
1. Clique em "Add URL"
2. Adicione uma por vez:
   ```
   http://localhost:4000/**
   http://localhost:3000/**
   http://127.0.0.1:4000/**
   ```

### **PASSO 3: Verificar URLs Existentes**
Manter as URLs essenciais:
- ✅ `https://monitore-mvp.lovable.app/**`
- ✅ `https://preview--monitore-mvp.lovable.app/**` 
- ✅ `https://sistema-monitore.com.br/**`
- ✅ `https://www.sistema-monitore.com.br/**`

### **PASSO 4: Remover URLs Desnecessárias (Opcional)**
URLs que podem ser removidas se não estão em uso:
- `https://6c8163f7-b023-44b2-bbbd-e884e83007bf.lovableproject.com/**`
- `https://id-preview--6c8163f7-b023-44b2-bbbd-e884e83007bf.lovable.app/**`
- `https://monitore-s1pv.onrender.com` (se não for mais usado)

---

## 🧪 TESTES APÓS CORREÇÃO

### **Desenvolvimento Local**
```bash
# 1. Iniciar servidor local
npm run dev

# 2. Acessar aplicação
http://localhost:4000

# 3. Testar login
- Fazer login via email/senha
- Verificar se não há erro "Invalid redirect URL"
- Confirmar redirecionamento para dashboard
```

### **Produção**
```bash
# 1. Acessar aplicação em produção
https://monitore-mvp.lovable.app

# 2. Testar fluxo completo
- Login → Dashboard → Logout → Login novamente
- Verificar se redirecionamentos funcionam
- Testar em diferentes browsers
```

---

## 📋 CONFIGURAÇÃO FINAL RECOMENDADA

### **Site URL**
```
https://monitore-mvp.lovable.app
```

### **Redirect URLs (Total: 8-10)**
```
http://localhost:4000/**
http://localhost:3000/**
https://monitore-mvp.lovable.app/**
https://preview--monitore-mvp.lovable.app/**
https://sistema-monitore.com.br/**
https://www.sistema-monitore.com.br/**
```

---

## 🔍 TROUBLESHOOTING

### **Erro: "Invalid redirect URL"**
- **Causa:** URL não está na lista de Redirect URLs
- **Solução:** Adicionar a URL na configuração do Supabase

### **Erro: Login redireciona para página errada**
- **Causa:** Site URL incorreta
- **Solução:** Verificar e corrigir Site URL

### **Erro: Auth não funciona em localhost**
- **Causa:** URLs locais não configuradas
- **Solução:** Adicionar `http://localhost:4000/**`

---

## 📞 APLICAÇÃO IMEDIATA

Para resolver os problemas atuais **imediatamente**, execute estas correções:

1. **Site URL:** `https://monitore-mvp.lovable.app`
2. **Adicionar:** `http://localhost:4000/**`
3. **Verificar:** URLs de produção estão corretas

---

**Impacto esperado:** Resolução de problemas de autenticação em desenvolvimento e melhoria da estabilidade geral do sistema.

---

## 📅 MANUTENÇÃO FUTURA

- **Revisar URLs** a cada deploy de novo ambiente
- **Remover URLs** de ambientes descontinuados  
- **Monitorar logs** de auth para URLs rejeitadas
- **Documentar** novas URLs quando criadas

---

**FIM DO GUIA**

*Documento criado em 31/01/2025 para otimização da configuração de autenticação.* 