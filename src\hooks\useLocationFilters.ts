import { useState, useMemo } from 'react';
import { LocationData } from '@/types/database';
import { LocationFilters } from '@/components/student/LocationFilters';

// Tipo para localizações enriquecidas com endereço
type EnrichedLocationData = LocationData & { enrichedAddress?: string };

// Função para calcular distância entre dois pontos (fórmula de Haversine)
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371000; // Raio da Terra em metros
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// Função para determinar a precisão com base nos dados disponíveis
function getAccuracyLevel(location: LocationData): 'high' | 'medium' | 'low' {
  // Se tem endereço detalhado, assumir precisão alta
  if (location.address && location.address.trim() && location.address.length > 20) {
    return 'high';
  }
  
  // Se tem endereço básico, assumir precisão média
  if (location.address && location.address.trim()) {
    return 'medium';
  }
  
  // Caso contrário, precisão baixa (apenas coordenadas)
  return 'low';
}

export function useLocationFilters(locations: EnrichedLocationData[]) {
  const [filters, setFilters] = useState<LocationFilters>({
    removeDuplicates: false,
    timeRange: 'all',
    accuracyFilter: 'all',
    minDistance: 50,
    sortBy: 'newest'
  });

  const filteredLocations = useMemo(() => {
    let filtered = [...locations];

    // Filtro por período
    if (filters.timeRange !== 'all') {
      const now = new Date();
      const timeRanges = {
        '1h': 1 * 60 * 60 * 1000,
        '6h': 6 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      };
      
      const cutoffTime = new Date(now.getTime() - timeRanges[filters.timeRange]);
      filtered = filtered.filter(location => 
        new Date(location.timestamp) >= cutoffTime
      );
    }

    // Filtro por precisão
    if (filters.accuracyFilter !== 'all') {
      filtered = filtered.filter(location => 
        getAccuracyLevel(location) === filters.accuracyFilter
      );
    }

    // Remover duplicatas
    if (filters.removeDuplicates) {
      const unique: EnrichedLocationData[] = [];
      
      for (const location of filtered) {
        const isDuplicate = unique.some(existing => {
          const distance = calculateDistance(
            location.latitude,
            location.longitude,
            existing.latitude,
            existing.longitude
          );
          
          // Considerar duplicata se estiver dentro do raio E for muito próximo no tempo
          const timeDiff = Math.abs(
            new Date(location.timestamp).getTime() - new Date(existing.timestamp).getTime()
          );
          
          return distance <= filters.minDistance && timeDiff <= 5 * 60 * 1000; // 5 minutos
        });
        
        if (!isDuplicate) {
          unique.push(location);
        }
      }
      
      filtered = unique;
    }

    // Ordenação
    switch (filters.sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        break;
      case 'accuracy':
        filtered.sort((a, b) => {
          const accuracyOrder = { high: 3, medium: 2, low: 1 };
          const aLevel = getAccuracyLevel(a);
          const bLevel = getAccuracyLevel(b);
          return accuracyOrder[bLevel] - accuracyOrder[aLevel];
        });
        break;
    }

    return filtered;
  }, [locations, filters]);

  return {
    filters,
    setFilters,
    filteredLocations,
    totalCount: locations.length,
    filteredCount: filteredLocations.length
  };
}