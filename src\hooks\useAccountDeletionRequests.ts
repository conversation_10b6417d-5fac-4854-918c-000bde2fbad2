import { useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useToast } from '@/hooks/use-toast';

interface AccountDeletionRequest {
  id: string;
  student_id: string;
  student_name: string;
  student_email: string;
  reason: string | null;
  status: string;
  requested_at: string;
  processed_at: string | null;
  guardian_notes: string | null;
}

export const useAccountDeletionRequests = () => {
  const { user } = useUser();
  const { toast } = useToast();
  const [requests, setRequests] = useState<AccountDeletionRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  const userType = useMemo(() => user?.user_metadata?.user_type, [user?.user_metadata?.user_type]);
  const userEmail = useMemo(() => user?.email, [user?.email]);

  const fetchRequests = useCallback(async () => {
    if (!user || userType !== 'parent') {
      console.log('[useAccountDeletionRequests] User not parent or not logged in');
      setIsLoading(false);
      setRequests([]);
      return;
    }

    try {
      setIsLoading(true);
      console.log('[DIAGNÓSTICO] Iniciando fetchRequests...');
      console.log('[DIAGNÓSTICO] Usuário atual:', user);
      console.log('[DIAGNÓSTICO] Email do usuário:', userEmail);
      console.log('[DIAGNÓSTICO] Tipo do usuário:', userType);
      console.log('[DIAGNÓSTICO] É tipo responsável?', userType === 'parent');
      console.log('[DIAGNÓSTICO] Tentando função RPC primeiro...');

      // Timeout para evitar loading infinito
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Deletion requests fetch timeout')), 3000);
      });

      try {
        const rpcPromise = supabase.rpc('get_guardian_deletion_requests');
        const { data, error } = await Promise.race([rpcPromise, timeoutPromise]) as any;

        if (error) {
          console.error('[DIAGNÓSTICO] Erro na função RPC:', error);
          throw error;
        }

        console.log('[DIAGNÓSTICO] Dados recebidos da RPC:', data);
        
        if (!data || !Array.isArray(data)) {
          console.log('[DIAGNÓSTICO] Nenhum dado retornado ou dados inválidos');
          setRequests([]);
          return;
        }

        const formattedRequests: AccountDeletionRequest[] = data.map((item: any) => ({
          id: item.id,
          student_id: item.student_id,
          student_name: item.student_name || 'Nome não disponível',
          student_email: item.student_email || 'Email não disponível',
          reason: item.reason,
          status: item.status,
          requested_at: item.requested_at,
          processed_at: item.processed_at,
          guardian_notes: item.guardian_notes
        }));

        console.log('[DIAGNÓSTICO] Solicitações formatadas:', formattedRequests);
        setRequests(formattedRequests);
      } catch (timeoutError) {
        console.warn('[DIAGNÓSTICO] RPC timeout, retornando array vazio');
        setRequests([]);
      }

    } catch (error: any) {
      console.error('[DIAGNÓSTICO] Erro ao buscar solicitações:', error);
      // Não mostrar toast de erro para não interferir na UI
      console.warn('[DIAGNÓSTICO] Definindo requests como array vazio devido ao erro');
      setRequests([]);
    } finally {
      setIsLoading(false);
    }
  }, [user, userType, userEmail]);

  const processRequest = useCallback(async (
    requestId: string, 
    action: 'approve' | 'reject', 
    notes?: string
  ) => {
    if (!user || isProcessing) return;

    try {
      setIsProcessing(true);
      console.log(`[useAccountDeletionRequests] Processing ${action} for request:`, requestId);

      // Timeout para processamento
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Process request timeout')), 10000);
      });

      const processPromise = supabase.rpc('process_account_deletion_request', {
        p_request_id: requestId,
        p_action: action,
        p_guardian_notes: notes || null
      });

      const { error } = await Promise.race([processPromise, timeoutPromise]) as any;

      if (error) {
        console.error('[useAccountDeletionRequests] Error processing request:', error);
        throw error;
      }

      toast({
        title: action === 'approve' ? "Solicitação aprovada" : "Solicitação rejeitada",
        description: action === 'approve' 
          ? "A conta do estudante será excluída em breve."
          : "A solicitação foi rejeitada e o estudante foi notificado.",
      });

      // Recarregar requests
      await fetchRequests();

    } catch (error: any) {
      console.error('[useAccountDeletionRequests] Error:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: `Não foi possível ${action === 'approve' ? 'aprovar' : 'rejeitar'} a solicitação.`,
      });
    } finally {
      setIsProcessing(false);
    }
  }, [user, isProcessing, toast, fetchRequests]);

  // Usar debounce no useEffect para evitar chamadas excessivas
  useEffect(() => {
    let mounted = true;
    let timeoutId: NodeJS.Timeout;
    
    if (user && userType === 'parent' && mounted) {
      // Debounce de 1 segundo para evitar múltiplas chamadas
      timeoutId = setTimeout(() => {
        if (mounted) {
          fetchRequests();
        }
      }, 1000);
    } else if (mounted) {
      // Se não é parent, definir como vazio imediatamente
      setRequests([]);
      setIsLoading(false);
    }

    return () => {
      mounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [user?.id, userType]); // Usar user.id em vez de user completo para evitar re-renders

  return {
    requests,
    isLoading,
    isProcessing,
    processRequest,
    refetch: fetchRequests
  };
};
