
import { useState, useEffect } from 'react';

export interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  connectionType: string | null;
  effectiveType: string | null;
  downlink: number | null;
  rtt: number | null;
}

export function useNetworkStatus(): NetworkStatus {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    isSlowConnection: false,
    connectionType: null,
    effectiveType: null,
    downlink: null,
    rtt: null
  });

  useEffect(() => {
    const updateNetworkStatus = () => {
      const isOnline = navigator.onLine;
      
      // Informações de conexão (se disponível)
      const connection = (navigator as any).connection || 
                        (navigator as any).mozConnection || 
                        (navigator as any).webkitConnection;
      
      let isSlowConnection = false;
      let connectionType = null;
      let effectiveType = null;
      let downlink = null;
      let rtt = null;

      if (connection) {
        connectionType = connection.type || null;
        effectiveType = connection.effectiveType || null;
        downlink = connection.downlink || null;
        rtt = connection.rtt || null;
        
        // Determinar se é conexão lenta
        isSlowConnection = 
          effectiveType === 'slow-2g' || 
          effectiveType === '2g' ||
          (downlink !== null && downlink < 0.5) ||
          (rtt !== null && rtt > 2000);
      }

      setNetworkStatus({
        isOnline,
        isSlowConnection,
        connectionType,
        effectiveType,
        downlink,
        rtt
      });
    };

    // Listeners para mudanças de conectividade
    const handleOnline = () => updateNetworkStatus();
    const handleOffline = () => updateNetworkStatus();
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listener para mudanças na conexão (se suportado)
    const connection = (navigator as any).connection;
    if (connection) {
      connection.addEventListener('change', updateNetworkStatus);
    }

    // Update inicial
    updateNetworkStatus();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if (connection) {
        connection.removeEventListener('change', updateNetworkStatus);
      }
    };
  }, []);

  return networkStatus;
}
