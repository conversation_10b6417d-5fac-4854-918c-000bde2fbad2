-- Tabela para solicitações de remoção de responsáveis
CREATE TABLE public.removal_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES auth.users(id) NOT NULL,
    guardian_id UUID REFERENCES auth.users(id) NOT NULL,
    student_name TEXT NOT NULL,
    guardian_name TEXT NOT NULL,
    guardian_email TEXT NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
    request_token UUID DEFAULT uuid_generate_v4() UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES auth.users(id)
);

-- Índices para performance
CREATE INDEX idx_removal_requests_student_id ON public.removal_requests(student_id);
CREATE INDEX idx_removal_requests_guardian_id ON public.removal_requests(guardian_id);
CREATE INDEX idx_removal_requests_status ON public.removal_requests(status);
CREATE INDEX idx_removal_requests_token ON public.removal_requests(request_token);

-- RLS Policies
ALTER TABLE public.removal_requests ENABLE ROW LEVEL SECURITY;

-- Estudantes podem ver suas próprias solicitações
CREATE POLICY "Students view their removal requests" 
ON public.removal_requests FOR SELECT 
USING (student_id = auth.uid());

-- Responsáveis podem ver solicitações direcionadas a eles
CREATE POLICY "Guardians view their removal requests" 
ON public.removal_requests FOR SELECT 
USING (guardian_id = auth.uid());

-- Estudantes podem criar solicitações
CREATE POLICY "Students create removal requests" 
ON public.removal_requests FOR INSERT 
WITH CHECK (student_id = auth.uid());

-- Responsáveis podem atualizar status das solicitações
CREATE POLICY "Guardians update removal requests" 
ON public.removal_requests FOR UPDATE 
USING (guardian_id = auth.uid())
WITH CHECK (guardian_id = auth.uid());

-- Função RPC para criar solicitação de remoção
CREATE OR REPLACE FUNCTION request_guardian_removal(
    p_guardian_id UUID,
    p_reason TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_student_id UUID;
    v_student_name TEXT;
    v_guardian_name TEXT;
    v_guardian_email TEXT;
    v_request_id UUID;
    v_token UUID;
    v_relationship_exists BOOLEAN;
BEGIN
    -- Verificar autenticação
    v_student_id := auth.uid();
    IF v_student_id IS NULL THEN
        RETURN json_build_object('success', false, 'message', 'Usuário não autenticado');
    END IF;

    -- Verificar se a relação existe
    SELECT EXISTS(
        SELECT 1 FROM student_guardian_relationships sgr
        WHERE sgr.student_id = v_student_id 
        AND sgr.guardian_id = p_guardian_id
    ) INTO v_relationship_exists;

    IF NOT v_relationship_exists THEN
        RETURN json_build_object('success', false, 'message', 'Relação não encontrada');
    END IF;

    -- Verificar se já existe solicitação pendente
    IF EXISTS(
        SELECT 1 FROM removal_requests 
        WHERE student_id = v_student_id 
        AND guardian_id = p_guardian_id 
        AND status = 'pending'
        AND expires_at > NOW()
    ) THEN
        RETURN json_build_object('success', false, 'message', 'Já existe uma solicitação pendente para este responsável');
    END IF;

    -- Obter dados do estudante
    SELECT p.full_name INTO v_student_name
    FROM profiles p
    WHERE p.id = v_student_id;

    -- Obter dados do responsável
    SELECT p.full_name, u.email INTO v_guardian_name, v_guardian_email
    FROM profiles p
    JOIN auth.users u ON u.id = p.id
    WHERE p.id = p_guardian_id;

    -- Criar solicitação
    INSERT INTO removal_requests (
        student_id,
        guardian_id,
        student_name,
        guardian_name,
        guardian_email,
        reason
    ) VALUES (
        v_student_id,
        p_guardian_id,
        COALESCE(v_student_name, 'Estudante'),
        COALESCE(v_guardian_name, 'Responsável'),
        v_guardian_email,
        p_reason
    ) RETURNING id, request_token INTO v_request_id, v_token;

    RETURN json_build_object(
        'success', true, 
        'message', 'Solicitação enviada com sucesso',
        'request_id', v_request_id,
        'token', v_token
    );
END;
$$;

-- Função RPC para processar solicitação (aprovar/rejeitar)
CREATE OR REPLACE FUNCTION process_removal_request(
    p_request_token UUID,
    p_action TEXT -- 'approve' ou 'reject'
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guardian_id UUID;
    v_request_record RECORD;
    v_result JSON;
BEGIN
    -- Verificar autenticação
    v_guardian_id := auth.uid();
    IF v_guardian_id IS NULL THEN
        RETURN json_build_object('success', false, 'message', 'Usuário não autenticado');
    END IF;

    -- Buscar solicitação
    SELECT * INTO v_request_record
    FROM removal_requests
    WHERE request_token = p_request_token
    AND guardian_id = v_guardian_id
    AND status = 'pending'
    AND expires_at > NOW();

    IF v_request_record IS NULL THEN
        RETURN json_build_object('success', false, 'message', 'Solicitação não encontrada ou expirada');
    END IF;

    -- Processar ação
    IF p_action = 'approve' THEN
        -- Remover relacionamento
        DELETE FROM student_guardian_relationships 
        WHERE student_id = v_request_record.student_id 
        AND guardian_id = v_request_record.guardian_id;

        -- Atualizar status da solicitação
        UPDATE removal_requests 
        SET status = 'approved', 
            processed_at = NOW(), 
            processed_by = v_guardian_id
        WHERE id = v_request_record.id;

        v_result := json_build_object(
            'success', true, 
            'message', 'Relação removida com sucesso',
            'action', 'approved'
        );

    ELSIF p_action = 'reject' THEN
        -- Atualizar status da solicitação
        UPDATE removal_requests 
        SET status = 'rejected', 
            processed_at = NOW(), 
            processed_by = v_guardian_id
        WHERE id = v_request_record.id;

        v_result := json_build_object(
            'success', true, 
            'message', 'Solicitação rejeitada',
            'action', 'rejected'
        );
    ELSE
        RETURN json_build_object('success', false, 'message', 'Ação inválida');
    END IF;

    RETURN v_result;
END;
$$;

-- Função para buscar solicitações pendentes do responsável
CREATE OR REPLACE FUNCTION get_guardian_removal_requests()
RETURNS TABLE (
    id UUID,
    student_name TEXT,
    student_email TEXT,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    request_token UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guardian_id UUID;
BEGIN
    v_guardian_id := auth.uid();
    IF v_guardian_id IS NULL THEN
        RAISE EXCEPTION 'Usuário não autenticado';
    END IF;

    RETURN QUERY
    SELECT 
        rr.id,
        rr.student_name,
        u.email as student_email,
        rr.reason,
        rr.created_at,
        rr.expires_at,
        rr.request_token
    FROM removal_requests rr
    JOIN auth.users u ON u.id = rr.student_id
    WHERE rr.guardian_id = v_guardian_id
    AND rr.status = 'pending'
    AND rr.expires_at > NOW()
    ORDER BY rr.created_at DESC;
END;
$$; 