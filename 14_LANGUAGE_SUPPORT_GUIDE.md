# Multilanguage Support Guide - Locate-Family-Connect

## 🌐 Visão Geral

Este documento explica como habilitar e manter o suporte a dois idiomas (Português e Inglês) no projeto Locate-Family-Connect. O sistema utiliza a biblioteca **i18next** integrada ao React através do **react-i18next** e faz detecção automática do idioma.

## 1. Estrutura de Traduções

As traduções ficam em `/src/i18n/`:

- `pt-BR.json` – texto em Português (Brasil)
- `en-GB.json` – texto em Inglês (Reino Unido)
- `config.ts` – lista os idiomas suportados e define `defaultLocale`

Para adicionar ou alterar frases, edite os arquivos JSON mantendo a estrutura de chaves.

## 2. Configuração do i18n

O arquivo `/src/lib/i18n.ts` inicializa o i18next e importa as traduções. Ele também define as estratégias de detecção (cookie, localStorage, navegador) e o idioma padrão.

```typescript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
...
```

Não é necessário modificar essa inicialização para novos idiomas, basta incluir o JSON correspondente e atualizar `supportedLocales`.

## 3. Uso nos Componentes

Importe o `useTranslation` do `react-i18next` e utilize o método `t` para recuperar as strings traduzidas.

```tsx
import { useTranslation } from 'react-i18next';
...
const { t } = useTranslation();
return <span>{t('common.loading')}</span>;
```

Certifique-se de que todas as frases visíveis ao usuário estejam no arquivo de tradução.

## 4. Troca de Idioma

O projeto já possui um componente `LanguageSwitcher` e uma página `index.tsx` para seleção inicial. O idioma escolhido é armazenado em `localStorage` e em um cookie `NEXT_LOCALE`.

```tsx
localStorage.setItem('lang', languageCode);
i18n.changeLanguage(languageCode);
```

O idioma ativo pode ser alterado a qualquer momento pelo usuário e se manterá persistente entre sessões.

## 5. Boas Práticas

1. **Chaves Descritivas** – utilize nomes curtos e claros para as entradas de tradução.
2. **Consistência** – mantenha o mesmo conjunto de chaves nos dois arquivos JSON.
3. **Contexto** – agrupe traduções por tela ou funcionalidade se o arquivo crescer muito.
4. **Revisão** – valide com falantes nativos sempre que possível.

## 6. Adicionando Novos Idiomas (Opcional)

Para incluir outro idioma:

1. Crie um novo arquivo JSON em `/src/i18n/` (ex.: `es-ES.json`).
2. Importe esse arquivo em `/src/lib/i18n.ts` e adicione-o em `resources`.
3. Atualize `supportedLocales` em `/src/i18n/config.ts` com a etiqueta e descrição do idioma.
4. Opcionalmente, atualize o `LanguageSwitcher` para exibir a nova opção.

## ✅ Resultado

Seguindo este guia, o Locate-Family-Connect permanecerá acessível tanto em Português quanto em Inglês, garantindo experiência consistente aos diferentes usuários.
