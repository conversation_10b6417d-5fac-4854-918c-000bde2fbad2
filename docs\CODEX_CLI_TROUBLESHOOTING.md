# Guia de Solução de Problemas do Codex CLI

Este documento reúne passos para resolver falhas comuns ao utilizar o **Codex CLI** no projeto.

## 1. Verificar instalação do Codex CLI
Execute:
```bash
codex --version
```
Se o comando não existir ou retornar erro, instale globalmente com:
```bash
npm install -g @openai/codex
```

## 2. Checar variável `OPENAI_API_KEY`
O Codex precisa da variável de ambiente `OPENAI_API_KEY`. Defina-a temporariamente na sessão ou adicione ao arquivo `.env`:
```bash
export OPENAI_API_KEY="sua-chave"
```

## 3. Conferir restrições de rede
O Codex utiliza acesso à internet restrito para alguns domínios. Se houver bloqueios adicionais, ele pode falhar. Verifique as configurações de rede conforme `codex-configuration.md`.

## 4. Testar novamente
Após as verificações aci<PERSON>, rode:
```bash
codex --version
```

Se tudo estiver correto, o CLI mostrará a versão instalada e estará pronto para uso.
