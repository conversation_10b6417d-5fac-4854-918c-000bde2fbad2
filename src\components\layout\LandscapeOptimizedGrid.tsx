import React from 'react';
import { useDevice } from '@/hooks/useDevice';
import { cn } from '@/lib/utils';

interface LandscapeOptimizedGridProps {
  children?: React.ReactNode;
  className?: string;
  sidebarContent?: React.ReactNode;
  mainContent?: React.ReactNode;
}

const LandscapeOptimizedGrid: React.FC<LandscapeOptimizedGridProps> = ({
  children,
  className,
  sidebarContent,
  mainContent
}) => {
  const device = useDevice();

  // Grid layout based on orientation and device type
  const getGridLayout = () => {
    if (device.type === 'mobile' && device.orientation === 'landscape') {
      return 'grid-cols-3 gap-1'; // Compact 3-column layout for landscape mobile
    }
    
    if (device.type === 'tablet' && device.orientation === 'landscape') {
      return 'grid-cols-4 gap-2'; // 4-column layout for landscape tablet
    }
    
    // Default responsive layout
    return 'grid-cols-1 lg:grid-cols-12 gap-2 md:gap-4 lg:gap-6';
  };

  // Content sizing for landscape
  const getContentClasses = () => {
    if (device.type === 'mobile' && device.orientation === 'landscape') {
      return {
        sidebar: 'col-span-1',
        main: 'col-span-2'
      };
    }
    
    if (device.type === 'tablet' && device.orientation === 'landscape') {
      return {
        sidebar: 'col-span-1',
        main: 'col-span-3'
      };
    }
    
    return {
      sidebar: 'lg:col-span-4',
      main: 'lg:col-span-8'
    };
  };

  const contentClasses = getContentClasses();

  if (sidebarContent && mainContent) {
    return (
      <div className={cn('grid w-full', getGridLayout(), className)}>
        <div className={cn(contentClasses.sidebar, 'space-y-1 md:space-y-2')}>
          {sidebarContent}
        </div>
        <div className={contentClasses.main}>
          {mainContent}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('grid w-full', getGridLayout(), className)}>
      {children}
    </div>
  );
};

export default LandscapeOptimizedGrid;