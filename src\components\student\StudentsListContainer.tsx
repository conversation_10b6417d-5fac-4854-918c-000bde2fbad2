
import React from 'react';
import { useToast } from "@/components/ui/use-toast";
import StudentsList from './StudentsList';
import { Student } from '@/types/auth';
import { useStudents } from '@/hooks/queries/useStudents';

interface StudentsListContainerProps {
  onSelectStudent?: (student: Student) => void;
  selectedStudent?: Student | null;
  onStudentUpdated?: () => void;
}

const StudentsListContainer = ({
  onSelectStudent,
  selectedStudent,
  onStudentUpdated
}: StudentsListContainerProps) => {
  const { toast } = useToast();
  const { 
    data: students = [], 
    isLoading: loading, 
    error,
    refetch
  } = useStudents();

  // Tratar erros
  React.useEffect(() => {
    if (error) {
      console.error('Error fetching students:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível carregar a lista de estudantes."
      });
    }
  }, [error, toast]);

  const handleStudentUpdated = () => {
    refetch();
    if (onStudentUpdated) onStudentUpdated();
  };

  return (
    <StudentsList
      students={students}
      loading={loading}
      error={error?.message || null}
      onSelectStudent={onSelectStudent}
      selectedStudent={selectedStudent}
      onStudentUpdated={handleStudentUpdated}
      onRefresh={refetch}
    />
  );
};

export default StudentsListContainer;
