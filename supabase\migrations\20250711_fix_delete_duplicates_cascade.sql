-- Drop existing function
DROP FUNCTION IF EXISTS delete_student_locations_duplicates(uuid, integer, integer, text);

-- Create function with cascade delete handling
CREATE OR REPLACE FUNCTION delete_student_locations_duplicates(
  p_user_id uuid,
  p_radius_meters integer DEFAULT 50,
  p_time_window_min integer DEFAULT 10,
  p_accuracy text DEFAULT NULL
)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count integer := 0;
  location_ids_to_delete uuid[];
BEGIN
  -- First, identify duplicate locations to delete
  WITH ranked AS (
    SELECT
      id,
      row_number() OVER (
        PARTITION BY
          user_id,
          round(latitude::numeric, 4), -- Group by rounded latitude
          round(longitude::numeric, 4), -- Group by rounded longitude
          date_trunc('minute', timestamp), -- Group by minute
          accuracy -- Group by accuracy
        ORDER BY timestamp DESC, id DESC -- Keep most recent, use id as tiebreaker
      ) AS rn
    FROM locations
    WHERE user_id = p_user_id
      AND (
        p_accuracy IS NULL 
        OR p_accuracy = 'all' 
        OR accuracy::text = p_accuracy
      )
  )
  SELECT array_agg(id) INTO location_ids_to_delete
  FROM ranked 
  WHERE rn > 1;

  -- If there are no duplicates, return 0
  IF location_ids_to_delete IS NULL OR array_length(location_ids_to_delete, 1) IS NULL THEN
    RETURN 0;
  END IF;

  -- Delete related records from location_history first
  DELETE FROM location_history
  WHERE location_id = ANY(location_ids_to_delete);

  -- Now delete the duplicate locations
  DELETE FROM locations
  WHERE id = ANY(location_ids_to_delete);

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION delete_student_locations_duplicates(uuid, integer, integer, text) TO authenticated;

-- Add comment to document the function
COMMENT ON FUNCTION delete_student_locations_duplicates(uuid, integer, integer, text) IS 
'Remove duplicate location records for a student based on spatial and temporal proximity. 
Handles cascade deletion of related location_history records.
Parameters:
- p_user_id: Student UUID
- p_radius_meters: Not used in current implementation (uses coordinate rounding instead)
- p_time_window_min: Not used in current implementation (groups by minute)
- p_accuracy: Filter by accuracy level (high/medium/low/all/null for all)
Returns: Number of deleted location records'; 