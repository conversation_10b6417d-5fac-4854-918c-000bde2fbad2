-- Migration: Create student account function (CORRECTED VERSION)
-- Date: 2025-01-31
-- Purpose: Create RPC that works WITHOUT non-existent auth functions

-- Create the RPC that validates and prepares data for Edge Function
CREATE OR REPLACE FUNCTION public.create_student_account_direct(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_student_phone TEXT DEFAULT NULL
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT,
  student_id UUID,
  temp_password TEXT,
  activation_token TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_guardian_id UUID := auth.uid();
  v_clean_cpf TEXT;
  v_temp_password TEXT;
  v_activation_token TEXT;
  v_guardian_name TEXT;
  v_guardian_email TEXT;
  v_existing_user UUID;
  v_invitation_id UUID;
BEGIN
  -- Check authentication
  IF v_guardian_id IS NULL THEN
    RETURN QUERY SELECT FALSE, '<PERSON><PERSON><PERSON>rio não autenticado', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Get guardian data
  SELECT u.email, COALESCE(p.full_name, u.email)
  INTO v_guardian_email, v_guardian_name
  FROM auth.users u
  LEFT JOIN public.profiles p ON p.user_id = u.id
  WHERE u.id = v_guardian_id;

  -- Validate CPF
  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  IF char_length(v_clean_cpf) <> 11 THEN
    RETURN QUERY SELECT FALSE, 'CPF inválido', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Check if student already exists by CPF
  SELECT user_id INTO v_existing_user
  FROM public.profiles
  WHERE regexp_replace(cpf, '[^0-9]', '', 'g') = v_clean_cpf
  AND user_type = 'student';

  IF v_existing_user IS NOT NULL THEN
    -- Check if relationship already exists
    IF EXISTS (
      SELECT 1 FROM public.student_guardian_relationships 
      WHERE student_id = v_existing_user AND guardian_id = v_guardian_id
    ) THEN
      RETURN QUERY SELECT FALSE, 'Estudante já está vinculado à sua conta', NULL::UUID, NULL::TEXT, NULL::TEXT;
      RETURN;
    ELSE
      -- Create relationship with existing student
      INSERT INTO public.student_guardian_relationships (
        student_id, guardian_id, relationship_type, is_primary
      ) VALUES (
        v_existing_user, v_guardian_id, 'parent', TRUE
      );
      
      RETURN QUERY SELECT TRUE, 'Estudante vinculado com sucesso', v_existing_user, NULL::TEXT, NULL::TEXT;
      RETURN;
    END IF;
  END IF;

  -- Check if email already exists
  SELECT user_id INTO v_existing_user
  FROM public.profiles
  WHERE LOWER(email) = LOWER(p_student_email);

  IF v_existing_user IS NOT NULL THEN
    RETURN QUERY SELECT FALSE, 'Email já está em uso', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Generate temporary credentials
  v_temp_password := substring(upper(p_student_name) from 1 for 7) || substr(v_clean_cpf, 9, 2) || '!';
  v_activation_token := 'act_' || encode(gen_random_bytes(32), 'hex') || '_' || extract(epoch from now())::bigint;

  -- Create family invitation record to track the request
  INSERT INTO public.family_invitations(
    id, guardian_id, student_name, student_email, student_cpf, student_phone,
    invitation_type, status, temp_password, activation_token, expires_at
  ) VALUES (
    gen_random_uuid(), v_guardian_id, p_student_name, p_student_email, 
    v_clean_cpf, p_student_phone, 'guardian_to_student', 'pending',
    v_temp_password, v_activation_token, NOW() + interval '7 days'
  ) RETURNING id INTO v_invitation_id;

  -- Log the operation
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_invitation_created', v_guardian_id,
          jsonb_build_object(
            'invitation_id', v_invitation_id,
            'student_email', p_student_email,
            'student_cpf_partial', substr(v_clean_cpf, 1, 3) || '***',
            'method', 'rpc_direct'
          ),
          NOW());

  -- Return success with data for Edge Function to use
  RETURN QUERY SELECT TRUE, 'Convite criado com sucesso', v_invitation_id, v_temp_password, v_activation_token;
END;
$$;

-- Create helper function to complete student account creation (called by Edge Function)
CREATE OR REPLACE FUNCTION public.complete_student_account_creation(
  p_invitation_id UUID,
  p_auth_user_id UUID
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invitation RECORD;
  v_guardian_id UUID;
BEGIN
  -- Get invitation data
  SELECT * INTO v_invitation
  FROM public.family_invitations
  WHERE id = p_invitation_id AND status = 'pending';

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'Convite não encontrado ou já processado';
    RETURN;
  END IF;

  -- Create student profile
  INSERT INTO public.profiles(
    user_id, full_name, email, cpf, phone, user_type,
    account_creation_method, created_by_user_id,
    requires_password_change, activation_token,
    activation_expires_at, status
  ) VALUES (
    p_auth_user_id, v_invitation.student_name, v_invitation.student_email, 
    v_invitation.student_cpf, v_invitation.student_phone, 'student', 
    'edge_function', v_invitation.guardian_id,
    TRUE, v_invitation.activation_token, v_invitation.expires_at, 'pending_activation'
  );

  -- Create guardian-student relationship
  INSERT INTO public.student_guardian_relationships(
    student_id, guardian_id, relationship_type, is_primary
  ) VALUES (
    p_auth_user_id, v_invitation.guardian_id, 'parent', TRUE
  );

  -- Update invitation status
  UPDATE public.family_invitations
  SET status = 'accepted', processed_at = NOW()
  WHERE id = p_invitation_id;

  -- Log completion
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_account_completed', p_auth_user_id,
          jsonb_build_object(
            'invitation_id', p_invitation_id,
            'guardian_id', v_invitation.guardian_id
          ),
          NOW());

  RETURN QUERY SELECT TRUE, 'Conta do estudante criada com sucesso';
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.create_student_account_direct(TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.complete_student_account_creation(UUID, UUID) TO service_role;

-- Add helpful comment
COMMENT ON FUNCTION public.create_student_account_direct IS 'Creates student invitation and prepares data for Edge Function account creation. Does NOT use non-existent auth functions.';
COMMENT ON FUNCTION public.complete_student_account_creation IS 'Completes student account creation after Edge Function creates auth user. Called by send-student-credentials Edge Function.'; 