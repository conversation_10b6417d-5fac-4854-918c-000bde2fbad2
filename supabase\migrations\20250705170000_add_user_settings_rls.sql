-- Enable RLS and add policies for user_settings
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read their own settings
CREATE POLICY "select_own_settings" ON public.user_settings
  FOR SELECT USING (user_id = auth.uid());

-- Allow authenticated users to update their own settings
CREATE POLICY "update_own_settings" ON public.user_settings
  FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid()); 