# Solução de Problemas no Build Docker

## Diagnóstico dos Problemas

Durante a configuração do ambiente Docker para o projeto Locate-Family-Connect, encontramos os seguintes problemas:

1. **Conflitos no docker-compose.override.yml**
   - O serviço `db` estava conflitando com o Supabase local na porta 5432
   - Solução: Remover o serviço `db` e manter apenas `app` com network_mode host

2. **Caracteres especiais em variáveis de ambiente**
   - A variável `TEST_STUDENT_PASSWORD` no arquivo `.env` continha caracteres `$` que eram interpretados pelo Docker
   - Solução: Escapar o caractere `$` com `\$` para evitar interpretação errada

3. **Referência incorreta ao arquivo tsconfig.app.json**
   - O script build no `package.json` referenciava `tsconfig.app.json` que não estava disponível no container
   - Solução: Modificar o script para usar apenas `tsc` e eliminar a referência específica

4. **Problemas de path aliases durante o build**
   - O Vite não conseguia resolver imports com o prefixo `@/` durante o build
   - Solução: Criar uma configuração Vite específica para build com aliases explícitos

## Solução Final Implementada

### 1. Modificações no docker-compose.override.yml

Removemos as referências ao serviço `db` e configuramos apenas o serviço `app` para usar o modo de rede host, permitindo acesso ao Supabase local:

```yaml
version: '3.8'
services:
  app:
    network_mode: host
    environment:
      - NODE_ENV=development
```

### 2. Correção de Variáveis de Ambiente

Escapamos caracteres especiais no arquivo `.env`:

```
TEST_STUDENT_PASSWORD=Teste@123\$\$
```

### 3. Solução para o Build TypeScript

Nosso Dockerfile final implementa várias camadas de solução:

1. **Cópia de Arquivos de Configuração**: Copiamos os arquivos tsconfig*.json antes de qualquer outra coisa
2. **Verificação e Recuperação**: O Dockerfile verifica se o `tsconfig.json` existe e, se não, cria um com as configurações corretas
3. **Configuração Customizada para Vite**: Criamos um arquivo `vite.config.build.ts` específico para o ambiente Docker
4. **Path Aliases Explícitos**: Configuramos corretamente os aliases `@/` tanto no TypeScript quanto no Vite

### 4. Estrutura do Arquivo vite.config.build.ts

```typescript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    target: "es2020",
    outDir: "dist",
    assetsDir: "assets",
    sourcemap: false,
    emptyOutDir: true,
    chunkSizeWarningLimit: 2000
  }
});
```

## Resultados do Build

O build Docker foi concluído com sucesso, conforme mostrado nos logs:

```
[builder  8/10] RUN if [ ! -f "tsconfig.json" ]; then echo "tsconfig.json não encontrado. Criando arquivo..." && echo '{...}' > tsconfig.json; fi
```

O script detectou que o `tsconfig.json` não existia e criou um novo com todas as configurações necessárias, incluindo os path aliases.

```
[builder  9/10] RUN echo "Criando configuração otimizada para build..." && echo 'import { defineConfig } from "vite";...' > vite.config.build.ts
```

Em seguida, foi criado um arquivo `vite.config.build.ts` especializado para o ambiente Docker.

```
[builder 10/10] RUN echo "\nCompilando com Vite usando configuração otimizada:" && npx vite build --config vite.config.build.ts

Compilando com Vite usando configuração otimizada:
vite v5.4.18 building for production...
transforming...
✓ 2791 modules transformed.
rendering chunks...
computing gzip size...
dist/index.html                      3.48 kB │ gzip:   1.31 kB
dist/assets/index-EaFXocK2.css      39.47 kB │ gzip:   5.90 kB
dist/assets/browser-RKnkbUtD.js      0.30 kB │ gzip:   0.25 kB
dist/assets/index-DATfSM-u.js    2,401.10 kB │ gzip: 681.18 kB

✓ built in 6.63s
```

O build Vite foi concluído com sucesso, transformando 2791 módulos e gerando os arquivos de saída necessários. Não foram encontrados erros de resolução de path aliases ou problemas de configuração.

## Lições Aprendidas

1. **Isolamento de Ambientes**: É crucial garantir que o Docker não conflite com serviços locais como Supabase
2. **Gerenciamento de Configuração**: Os arquivos de configuração (tsconfig, vite.config) precisam ser gerenciados corretamente no container
3. **Path Aliases**: A resolução de imports com aliases (`@/`) requer configuração explícita tanto no TypeScript quanto no Vite
4. **Variáveis de Ambiente**: Caracteres especiais em variáveis de ambiente devem ser escapados para evitar interpretação incorreta pelo Docker
5. **Recuperação Automática**: Implementar verificações e criação automática de arquivos de configuração ausentes aumenta a robustez do processo de build

## Comandos para Implementar a Solução

```bash
# Parar contêineres existentes
docker compose -f 'docker-compose.yml' down

# Limpar cache do Docker
docker builder prune -f

# Reconstruir a aplicação sem cache
docker compose -f 'docker-compose.yml' build --no-cache app

# Iniciar os serviços
docker compose -f 'docker-compose.yml' up -d
```
