
-- CORREÇÃO COMPLETA: Token de Confirmação e Relacionamentos Familiares (CORRIGIDA)
-- Objetivo: Corrigir login e estabelecer relacionamentos pai-filho automaticamente

-- 1. CORRIGIR TOKEN DE CONFIRMAÇÃO PARA O USUÁRIO <EMAIL>
-- Nota: confirmed_at é uma coluna gerada, não pode ser atualizada diretamente
UPDATE auth.users 
SET 
  confirmation_token = encode(gen_random_bytes(32), 'base64url'),
  email_confirmed_at = NOW()
WHERE email = '<EMAIL>';

-- 2. ESTABELECER RELACIONAMENTO FAMILIAR AUTOMÁTICO
-- Identificar o pai criador (<PERSON>) e criar relacionamento
DO $$
DECLARE
    v_student_id UUID;
    v_guardian_id UUID;
    v_relationship_exists BOOLEAN;
BEGIN
    -- Buscar IDs do estudante e do responsável
    SELECT id INTO v_student_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    SELECT id INTO v_guardian_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    -- Verificar se relacionamento já existe
    SELECT EXISTS(
        SELECT 1 FROM public.student_guardian_relationships 
        WHERE student_id = v_student_id AND guardian_id = v_guardian_id
    ) INTO v_relationship_exists;
    
    -- Se ambos usuários existem e relacionamento não existe, criar
    IF v_student_id IS NOT NULL AND v_guardian_id IS NOT NULL AND NOT v_relationship_exists THEN
        INSERT INTO public.student_guardian_relationships (
            student_id,
            guardian_id,
            relationship_type,
            is_primary
        ) VALUES (
            v_student_id,
            v_guardian_id,
            'parent',
            true
        );
        
        -- Registrar nos logs
        INSERT INTO public.auth_logs (
            event_type,
            user_id,
            metadata,
            occurred_at
        ) VALUES (
            'family_relationship_created',
            v_guardian_id,
            jsonb_build_object(
                'student_id', v_student_id,
                'student_email', '<EMAIL>',
                'guardian_email', '<EMAIL>',
                'relationship_type', 'parent',
                'is_primary', true,
                'created_automatically', true
            ),
            NOW()
        );
    END IF;
END $$;

-- 3. CRIAR TRIGGER PARA RELACIONAMENTOS AUTOMÁTICOS FUTUROS
-- Função para criar relacionamento automático quando um convite familiar é aceito
CREATE OR REPLACE FUNCTION public.auto_create_guardian_relationship()
RETURNS TRIGGER AS $$
DECLARE
    v_guardian_id UUID;
    v_student_id UUID;
BEGIN
    -- Só executar para convites aceitos
    IF NEW.status = 'accepted' AND OLD.status = 'pending' THEN
        v_student_id := NEW.student_id;
        v_guardian_id := NEW.accepted_by_guardian_id;
        
        -- Verificar se já existe relacionamento
        IF NOT EXISTS (
            SELECT 1 FROM public.student_guardian_relationships 
            WHERE student_id = v_student_id AND guardian_id = v_guardian_id
        ) THEN
            -- Criar relacionamento
            INSERT INTO public.student_guardian_relationships (
                student_id,
                guardian_id,
                relationship_type,
                is_primary
            ) VALUES (
                v_student_id,
                v_guardian_id,
                'parent',
                false -- Não é primário por padrão via convite
            );
            
            -- Log da criação automática
            INSERT INTO public.auth_logs (
                event_type,
                user_id,
                metadata,
                occurred_at
            ) VALUES (
                'auto_relationship_created_from_invitation',
                v_guardian_id,
                jsonb_build_object(
                    'invitation_id', NEW.id,
                    'student_id', v_student_id,
                    'guardian_id', v_guardian_id,
                    'invitation_token', NEW.invitation_token
                ),
                NOW()
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Criar trigger na tabela family_invitations
DROP TRIGGER IF EXISTS auto_create_relationship_on_invitation_accept ON public.family_invitations;
CREATE TRIGGER auto_create_relationship_on_invitation_accept
    AFTER UPDATE ON public.family_invitations
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_create_guardian_relationship();

-- 4. CORRIGIR FUNÇÃO PARA CRIAR RELACIONAMENTO QUANDO PAI CRIA FILHO
CREATE OR REPLACE FUNCTION public.create_student_with_relationship(
    p_student_email TEXT,
    p_student_name TEXT,
    p_student_cpf TEXT,
    p_student_phone TEXT DEFAULT NULL,
    p_guardian_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_student_id UUID;
    v_guardian_id UUID;
BEGIN
    -- Usar o guardian_id fornecido ou pegar do usuário atual
    v_guardian_id := COALESCE(p_guardian_id, auth.uid());
    
    -- Buscar o ID do estudante pelos dados fornecidos
    SELECT id INTO v_student_id
    FROM auth.users
    WHERE email = p_student_email;
    
    -- Se encontrou o estudante, criar relacionamento
    IF v_student_id IS NOT NULL AND v_guardian_id IS NOT NULL THEN
        -- Verificar se relacionamento já existe
        IF NOT EXISTS (
            SELECT 1 FROM public.student_guardian_relationships
            WHERE student_id = v_student_id AND guardian_id = v_guardian_id
        ) THEN
            -- Criar relacionamento
            INSERT INTO public.student_guardian_relationships (
                student_id,
                guardian_id,
                relationship_type,
                is_primary
            ) VALUES (
                v_student_id,
                v_guardian_id,
                'parent',
                true
            );
            
            -- Log da criação
            INSERT INTO public.auth_logs (
                event_type,
                user_id,
                metadata,
                occurred_at
            ) VALUES (
                'student_guardian_relationship_created',
                v_guardian_id,
                jsonb_build_object(
                    'student_id', v_student_id,
                    'student_email', p_student_email,
                    'created_by_guardian', true
                ),
                NOW()
            );
        END IF;
    END IF;
    
    RETURN v_student_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. LOGS DE DIAGNÓSTICO FINAL
INSERT INTO public.auth_logs (
    event_type,
    user_id,
    metadata,
    occurred_at
) VALUES (
    'complete_fix_applied_corrected',
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    jsonb_build_object(
        'phase', 'complete_correction_v2',
        'actions', ARRAY[
            'token_fixed_without_confirmed_at',
            'relationship_created',
            'auto_trigger_installed',
            'create_student_function_added'
        ],
        'student_email', '<EMAIL>',
        'guardian_email', '<EMAIL>',
        'timestamp', NOW()
    ),
    NOW()
);
