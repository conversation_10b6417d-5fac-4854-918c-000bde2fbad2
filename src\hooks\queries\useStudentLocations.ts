
import { useQuery } from '@tanstack/react-query';
import { locationService } from '@/lib/services/location/LocationService';

export function useStudentLocations(studentId: string) {
  return useQuery({
    queryKey: ['studentLocations', studentId],
    queryFn: () => locationService.getStudentLocations(studentId),
    enabled: !!studentId,
    staleTime: 30 * 1000, // Reduzido de 2 minutos para 30 segundos
    gcTime: 5 * 60 * 1000, // 5 minutos (novo nome para cacheTime)
    refetchInterval: 30 * 1000, // Refetch a cada 30 segundos
    refetchIntervalInBackground: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: (failureCount, error) => {
      // Retry strategy mais inteligente
      if (failureCount >= 3) return false;
      if (error?.message?.includes('Network')) return true;
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Manter dados anteriores durante refetch para evitar "sumir"
    placeholderData: (previousData) => previousData,
  });
}
