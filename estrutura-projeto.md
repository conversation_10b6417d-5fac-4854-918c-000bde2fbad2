# Estrutura Completa do Projeto Locate-Family-Connect

Este documento mapeia a estrutura completa do projeto para referência técnica e teste do Codex CLI.

## Visão Geral

O projeto Locate-Family-Connect é uma aplicação React + TypeScript com backend Supabase, focada em:
- Compartilhamento de localização segura
- Autenticação PKCE
- Sistema de guardiões/estudantes
- Edge Functions para funcionalidades críticas

## Diretórios Principais

```
📁 src/
  📁 components/      # Componentes React (auth, map, location, ui)
  📁 contexts/        # Contextos React (Auth, User)
  📁 hooks/           # Custom hooks
  📁 layouts/         # Layouts da aplicação
  📁 lib/             # Utilitários e configurações
    📁 api/           # Serviços de API
    📁 db/            # Migrações e schema do banco
    📁 utils/         # Funções utilitárias
  📁 pages/           # Páginas/rotas da aplicação
  📁 types/           # Definições de tipos TypeScript

📁 supabase/
  📁 functions/       # Edge Functions (share-location, send-multi-channel)
  📁 migrations/      # Migrações SQL para Supabase

📁 docs/              # Documentação técnica e de processo
  📁 General/         # Guias gerais do projeto
  
📁 cypress/           # Testes e2e com Cypress

📁 scripts/           # Scripts de utilidade e automação
```

## Detalhamento de Arquivos Críticos

### Autenticação & Contextos
- `src/contexts/UnifiedAuthContext.tsx` - Sistema de autenticação unificado
- `src/contexts/AuthContext.tsx` - Contexto de autenticação
- `src/lib/supabase.ts` - Cliente Supabase configurado

### Componentes & UI
- `src/components/auth/` - Componentes de autenticação
- `src/components/map/` - Componentes de mapa e localização
- `src/components/guardian/` - Gestão de guardiões
- `src/components/ui/` - Sistema de componentes UI

### Hooks Especializados
- `src/hooks/useLocationSharing.ts` - Compartilhamento de localização
- `src/hooks/useMapInitialization.ts` - Inicialização do mapbox
- `src/hooks/useGuardianList.ts` - Gerenciamento de guardiões

### Edge Functions
- `supabase/functions/share-location/index.ts` - Função de compartilhamento
- `supabase/functions/send-multi-channel/index.ts` - Notificações multi-canal

### Migrações
- `src/lib/db/migrations/` - Migrações SQL organizadas por data

### Utilitários
- `src/lib/utils/email-validator.ts` - Validação de email
- `src/lib/utils/cache-manager.ts` - Gerenciamento de cache
- `src/lib/utils/circuit-breaker.ts` - Implementação de circuit breaker

## Arquivos Específicos para Testes do Codex

Para testes iniciais do Codex CLI, os seguintes arquivos são recomendados:
- `src/lib/utils/email-validator.ts` - Validação de email completa
- `src/hooks/useLocationSharing.ts` - Hook de compartilhamento de localização
- `src/components/map/MapContainer.tsx` - Componente principal de mapa
- `supabase/functions/share-location/index.ts` - Edge function crítica
