
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { clearAppCache } from '@/lib/utils/cache-manager';
import { useToast } from '@/components/ui/use-toast';

interface CacheClearButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

const CacheClearButton: React.FC<CacheClearButtonProps> = ({ 
  variant = 'outline', 
  size = 'sm',
  className = ''
}) => {
  const [clearing, setClearing] = useState(false);
  const { toast } = useToast();

  const handleClearCache = async () => {
    if (clearing) return;
    
    setClearing(true);
    toast({
      title: "Limpando cache completo",
      description: "Service Worker, cache e dados serão limpos...",
    });
    
    // Timeout para mostrar toast e executar limpeza completa
    setTimeout(async () => {
      try {
        // 1. Desregistrar Service Worker
        if ('serviceWorker' in navigator) {
          const registrations = await navigator.serviceWorker.getRegistrations();
          for (const registration of registrations) {
            await registration.unregister();
            console.log('[CACHE_CLEAR] Service Worker desregistrado');
          }
        }
        
        // 2. Limpar todos os caches
        if ('caches' in window) {
          const cacheNames = await caches.keys();
          await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
          );
          console.log('[CACHE_CLEAR] Todos os caches limpos');
        }
        
        // 3. Limpar localStorage e sessionStorage
        localStorage.clear();
        sessionStorage.clear();
        console.log('[CACHE_CLEAR] Storage limpo');
        
        // 4. Recarregar página
        clearAppCache(true);
      } catch (error) {
        console.error('[CACHE_CLEAR] Erro na limpeza:', error);
        // Fallback para limpeza básica
        clearAppCache(true);
      }
    }, 1500);
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={`gap-2 ${className}`}
      onClick={handleClearCache}
      disabled={clearing}
    >
      <Trash2 size={16} />
      {clearing ? "Limpando..." : "Limpar Cache"}
    </Button>
  );
};

export default CacheClearButton;
