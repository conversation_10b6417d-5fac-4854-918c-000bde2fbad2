# Database Schema and Migrations - Locate-Family-Connect

## 🗄️ Visão Geral do Modelo de Dados

O Locate-Family-Connect utiliza PostgreSQL através do Supabase, com um modelo de dados focado nas relações entre estudantes, responsáveis e localizações, protegido por políticas de Row Level Security (RLS).

### 📊 Diagrama de Entidade-Relacionamento Simplificado

```
auth.users ──┐
             ├── profiles
             │
guardians ───┼── students
             │
             └── locations
```

## 📋 Tabelas Principais

### auth.users
Gerenciada pelo Supabase Auth, armazena dados básicos de autenticação.

```sql
-- Estrutura implícita, gerenciada pelo Supabase
-- Visualização parcial:
SELECT id, email, user_metadata FROM auth.users;
```

### public.profiles
Dados estendidos de usuário, vinculados ao auth.users.

```sql
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    user_type VARCHAR NOT NULL CHECK (user_type IN ('student', 'guardian', 'developer')),
    full_name VARCHA<PERSON>,
    avatar_url VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índice para melhorar performance em filtros por tipo de usuário
CREATE INDEX idx_users_user_type ON public.profiles(user_type);
```

### public.students
Informações específicas de estudantes.

```sql
CREATE TABLE public.students (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    school_id VARCHAR,
    grade VARCHAR,
    class VARCHAR,
    additional_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### public.guardians
Relação entre responsáveis e estudantes.

```sql
CREATE TABLE public.guardians (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    guardian_id UUID REFERENCES auth.users(id) NOT NULL,
    student_id UUID REFERENCES auth.users(id) NOT NULL,
    relationship VARCHAR NOT NULL, -- 'parent', 'sibling', etc.
    email VARCHAR,
    phone VARCHAR,
    notification_preferences JSONB DEFAULT '{"email": true, "push": true}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (guardian_id, student_id)
);

-- Índices para melhorar performance
CREATE INDEX guardians_student_id_idx ON public.guardians(student_id);
CREATE INDEX guardians_email_idx ON public.guardians(email);
```

### public.locations
Histórico de localizações dos estudantes.

```sql
CREATE TABLE public.locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES auth.users(id) NOT NULL,
    latitude NUMERIC NOT NULL,
    longitude NUMERIC NOT NULL,
    accuracy NUMERIC,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB
);

-- Índice para consultas temporais eficientes
CREATE INDEX location_created_at_idx ON public.locations(created_at);
CREATE INDEX locations_student_id_idx ON public.locations(student_id);
```

### public.geofences
Definições de geocercas para alertas baseados em localização.

```sql
CREATE TABLE public.geofences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR NOT NULL,
    student_id UUID REFERENCES auth.users(id) NOT NULL,
    created_by UUID REFERENCES auth.users(id) NOT NULL,
    polygon JSONB NOT NULL, -- GeoJSON format
    active BOOLEAN DEFAULT true,
    alert_on_enter BOOLEAN DEFAULT true,
    alert_on_exit BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔐 Políticas de Row Level Security (RLS)

### Habilitando RLS

```sql
-- Habilitar RLS em todas as tabelas públicas
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guardians ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.geofences ENABLE ROW LEVEL SECURITY;
```

### Políticas para Profiles

```sql
-- Usuários podem ver e editar apenas seu próprio perfil
CREATE POLICY "Users can view own profile" 
ON public.profiles FOR SELECT 
USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
ON public.profiles FOR UPDATE
USING (auth.uid() = id);
```

### Políticas para Students

```sql
-- Estudantes podem ver apenas seu próprio registro
CREATE POLICY "Students view own data" 
ON public.students FOR SELECT 
USING (auth.uid() = id);

-- Responsáveis podem ver dados de estudantes vinculados a eles
CREATE POLICY "Guardians view their students" 
ON public.students FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM guardians
        WHERE guardians.student_id = students.id
        AND guardians.guardian_id = auth.uid()
    )
);
```

### Políticas para Guardians

```sql
-- Estudantes podem ver seus próprios responsáveis
CREATE POLICY "Students view their guardians" 
ON public.guardians FOR SELECT 
USING (student_id = auth.uid());

-- Responsáveis podem ver relações onde são responsáveis
CREATE POLICY "Guardians view their relationships" 
ON public.guardians FOR SELECT 
USING (guardian_id = auth.uid());

-- Responsáveis só podem adicionar/remover relações para si mesmos
CREATE POLICY "Guardians manage their relationships" 
ON public.guardians FOR ALL
USING (guardian_id = auth.uid());
```

### Políticas para Locations

```sql
-- Estudantes podem inserir apenas suas próprias localizações
CREATE POLICY "Students insert own locations" 
ON public.locations FOR INSERT
WITH CHECK (student_id = auth.uid());

-- Estudantes podem ver apenas suas próprias localizações
CREATE POLICY "Students view own locations" 
ON public.locations FOR SELECT
USING (student_id = auth.uid());

-- Responsáveis podem ver localizações de estudantes vinculados a eles
CREATE POLICY "Guardians view their students locations" 
ON public.locations FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM guardians
        WHERE guardians.student_id = locations.student_id
        AND guardians.guardian_id = auth.uid()
    )
);
```

## 🔄 Triggers e Funções

### Trigger para Notificação de Localização

```sql
-- Função para registrar notificação quando localização é atualizada
CREATE OR REPLACE FUNCTION notify_location_update()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.location_notifications (
        student_id,
        location_id,
        notification_status
    ) VALUES (
        NEW.student_id,
        NEW.id,
        'pending'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger que executa a função após inserção
CREATE TRIGGER location_notification_trigger
AFTER INSERT ON public.locations
FOR EACH ROW
EXECUTE PROCEDURE notify_location_update();
```

### Função para Verificação de Geocercas

```sql
-- Verifica se uma localização está dentro de alguma geocerca
CREATE OR REPLACE FUNCTION check_geofence_alerts()
RETURNS TRIGGER AS $$
DECLARE
    fence RECORD;
BEGIN
    FOR fence IN 
        SELECT * FROM public.geofences 
        WHERE student_id = NEW.student_id AND active = true
    LOOP
        -- Lógica para verificar se ponto está dentro do polígono
        -- Esta é uma versão simplificada
        -- Em produção, usar PostGIS para geometria precisa
        IF point_in_polygon(NEW.latitude, NEW.longitude, fence.polygon) THEN
            INSERT INTO public.geofence_alerts (
                geofence_id, 
                location_id, 
                alert_type
            ) VALUES (
                fence.id, 
                NEW.id, 
                'enter'
            );
        END IF;
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para verificar alertas de geocercas
CREATE TRIGGER geofence_check_trigger
AFTER INSERT ON public.locations
FOR EACH ROW
EXECUTE PROCEDURE check_geofence_alerts();
```

## 📦 Migrações

As migrações são gerenciadas através de arquivos SQL numerados sequencialmente em:

```
/src/lib/db/migrations/
```

### Aplicação de Migrações

As migrações são aplicadas automaticamente durante o deploy através da Supabase CLI:

```bash
# Aplicar migrações localmente
supabase db push

# Verificar status
supabase migration list
```

### Nomenclatura de Arquivos de Migração

```
YYYYMMDDHHMMSS_migration_name.sql
```

Exemplo:
```
20250424120000_create_base_tables.sql
20250425093000_add_geofences.sql
```

## 🔄 Transações e Segurança

### Exemplo de Transação para Criação de Usuário

```sql
BEGIN;
  -- Criar perfil após autenticação
  INSERT INTO public.profiles (id, user_type, full_name)
  VALUES (auth.uid(), 'student', 'Nome do Estudante');

  -- Adicionar à tabela de estudantes
  INSERT INTO public.students (id, school_id, grade)
  VALUES (auth.uid(), '12345', '9º Ano');
COMMIT;
```

### Evitar SQL Injection

Sempre utilizar consultas parametrizadas:

```typescript
// ✅ Correto: consulta parametrizada
const { data, error } = await supabase
  .from('locations')
  .select('*')
  .eq('student_id', studentId);

// ❌ Incorreto: concatenação de strings
const query = `SELECT * FROM locations WHERE student_id = '${studentId}'`; // NEVER DO THIS
```

## 📚 Comandos Úteis

### Verificação da Estrutura

```bash
# Listar todas as tabelas
SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname = 'public';

# Descrição de tabela específica
\d public.locations

# Listar todas as políticas RLS
SELECT * FROM pg_policies;
```

### Manutenção

```bash
# Vacuum para otimização
VACUUM ANALYZE public.locations;

# Verificar índices
SELECT * FROM pg_indexes WHERE schemaname = 'public';
```

## 📝 Referências

- [Documentação Supabase sobre RLS](https://supabase.com/docs/guides/auth/row-level-security)
- [Guia de Migrações Supabase](https://supabase.com/docs/guides/cli/local-development#database-migrations)
- [Triggers PostgreSQL](https://www.postgresql.org/docs/current/trigger-definition.html)
- [Políticas de Segurança](docs/rls-student-guardians-policy.md)
