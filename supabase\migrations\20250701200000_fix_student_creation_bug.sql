-- Migration: Fix critical bug in student account creation
-- Problem: create_student_account_direct uses auth.create_user() which doesn't exist
-- Solution: Remove buggy RPC and rely on working Edge Functions

-- Drop the buggy function that uses non-existent auth.create_user()
DROP FUNCTION IF EXISTS public.create_student_account_direct(TEXT, TEXT, TEXT, TEXT);

-- Add comprehensive comment explaining the issue
COMMENT ON SCHEMA public IS 'Student account creation now handled exclusively by Edge Functions. The previous RPC create_student_account_direct was removed due to using non-existent auth.create_user() function. Emails are sent successfully, but user creation failed silently.';

-- Create a simple validation function for frontend to use
CREATE OR REPLACE FUNCTION public.validate_student_data(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_guardian_id UUID := auth.uid();
  v_clean_cpf TEXT;
BEGIN
  -- Check authentication
  IF v_guardian_id IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Usu<PERSON>rio não autenticado';
    RETURN;
  END IF;

  -- Validate CPF
  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  IF char_length(v_clean_cpf) <> 11 THEN
    RETURN QUERY SELECT FALSE, 'CPF inválido';
    RETURN;
  END IF;

  -- Validate email
  IF p_student_email IS NULL OR p_student_email = '' OR p_student_email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
    RETURN QUERY SELECT FALSE, 'Email inválido';
    RETURN;
  END IF;

  -- Check if email already exists
  IF EXISTS (SELECT 1 FROM profiles WHERE email = p_student_email) THEN
    RETURN QUERY SELECT FALSE, 'Email já cadastrado no sistema';
    RETURN;
  END IF;

  -- All validations passed
  RETURN QUERY SELECT TRUE, 'Dados válidos - encaminhando para Edge Function';
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.validate_student_data(TEXT, TEXT, TEXT) TO authenticated;

-- Log this critical fix
INSERT INTO public.system_logs(
  event_type, 
  details, 
  created_at
) VALUES (
  'CRITICAL_BUG_FIXED',
  'Removed create_student_account_direct RPC that used non-existent auth.create_user(). System now relies on working Edge Functions for account creation.',
  NOW()
); 