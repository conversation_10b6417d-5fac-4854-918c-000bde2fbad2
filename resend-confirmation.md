VAMOS ENVIAR UM EMAIL PARA O ESTUDANTE POR AQUI PARA QUE ELE TENHA CONFIRME SUA CONTA E POSSA LOGAR , TEMOS QUE ENVIAR UM TOKEN VALIDO E SUA SENHA TEMPORARIA. Emails
Broadcasts
Audiences
Metrics
Domains
Logs
API Keys
Webhooks
Settings
Help
Docs
Settings
SMTP
Send emails using SMTP instead of the REST API.

See documentation for more information.

Host
smtp.resend.com

Port
465

For encrypted/TLS connections use
,
or
User
resend

Password
YOUR_API_KEY
SMTP · Resend    @https://supabase.com/dashboard/authorize?auth_id=143f2e21-6c6c-4c14-b027-334aa86507d9  Supabase Logo
Authorize API access for Resend

This authorization request has been approved
Resend has read and write access to the organization "Webber-Lubenham's Org" and all of its projects

Approved on: 02 Jul 2025 13:04:55 (+0100)   @https://resend.com/webhooks/909ffceb-bf19-4dd1-ab05-b50741f335d4   Emails
Broadcasts
Audiences
Metrics
Domains
Logs
API Keys
Webhooks
Settings
Help
Docs
Webhook
https://www.sistema-monitore.com.br

Created
5 months ago
Status
Listening for

Signing Secret
••••••••••••••••••••••••••••••••••••••


No webhook events yet
Once you start sending emails, you'll be able to see all the webhook events.
https://www.sistema-monitore.com.br · Webhooks · Resend  whsec_3PJt8OntzAgvdOGUmC5U0gJWTC3H4cCQ  

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://rsvjnndhbyyxktbczlnk.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MDk3NzksImV4cCI6MjA1ODk4NTc3OX0.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4";

const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

const resendConfirmation = async () => {
  try {
    const { data, error } = await supabase.auth.resend({
      type: 'signup',
      email: '<EMAIL>'
    });

    if (error) {
      console.error('Error resending confirmation:', error);
      return;
    }

    console.log('Confirmation email resent successfully');
  } catch (error) {
    console.error('Error:', error);
  }
};

resendConfirmation(); 