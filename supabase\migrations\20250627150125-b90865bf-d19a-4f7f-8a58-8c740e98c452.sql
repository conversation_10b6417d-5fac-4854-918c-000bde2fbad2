
-- Correção imediata: Atualizar status do usu<PERSON><PERSON> <EMAIL>
UPDATE public.profiles 
SET 
  registration_status = 'active',
  status = 'active',
  last_login_at = NOW(),
  updated_at = NOW()
WHERE email = '<EMAIL>';

-- Verificar se existem outros usuários com status pending que deveriam estar ativos
UPDATE public.profiles 
SET 
  registration_status = 'active',
  status = 'active',
  updated_at = NOW()
WHERE registration_status = 'pending' 
  AND created_at < NOW() - INTERVAL '24 hours'
  AND user_id IS NOT NULL;

-- Adicionar logs para monitoramento de login
INSERT INTO public.auth_logs (
  event_type,
  user_id,
  metadata,
  occurred_at
) VALUES (
  'login_status_fix_applied',
  (SELECT user_id FROM public.profiles WHERE email = '<EMAIL>'),
  jsonb_build_object(
    'action', 'status_updated_to_active',
    'email', '<EMAIL>',
    'timestamp', NOW(),
    'reason', 'fix_pending_registration_status'
  ),
  NOW()
);
