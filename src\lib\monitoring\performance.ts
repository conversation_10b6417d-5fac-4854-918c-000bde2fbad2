// Monitoramento básico de performance
export const performanceMonitor = {
  // Medir tempo de operações críticas
  measureOperation: <T>(name: string, operation: () => Promise<T>): Promise<T> => {
    const start = performance.now();
    return operation().finally(() => {
      const duration = performance.now() - start;
      console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
      
      // Alertar se operação demorar muito
      if (duration > 5000) {
        console.warn(`[Performance] Operação lenta detectada: ${name} (${duration.toFixed(2)}ms)`);
      }
    });
  },

  // Medir Web Vitals básicas
  measureWebVitals: () => {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          console.log('[Performance] Load time:', navigation.loadEventEnd - navigation.loadEventStart, 'ms');
        }, 0);
      });
    }
  }
};