
import React from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { OfflineStatusBanner } from '@/components/offline/OfflineStatusBanner';
import { NetworkStatusIndicator } from '@/components/offline/NetworkStatusIndicator';
import { SmartCacheIndicator } from '@/components/offline/SmartCacheIndicator';
import { SyncStatusIndicator } from '@/components/offline/SyncStatusIndicator';

export default function AppLayout() {
  const { user, isLoading } = useUser();
  const navigate = useNavigate();

  // Show loading spinner while checking auth
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-blue-600 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    navigate('/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Banner de status offline */}
      <OfflineStatusBanner />
      
      {/* Header with network status, smart cache, and sync status */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900">
              EduConnect
            </h1>
            
            <div className="flex items-center gap-4">
              <NetworkStatusIndicator variant="badge" showDetails />
              <SmartCacheIndicator variant="minimal" showSyncStatus />
              <SyncStatusIndicator variant="minimal" />
              
              <div className="text-sm text-gray-600">
                {user?.user_metadata?.full_name || user?.email}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto px-4 py-6">
        <Outlet />
      </main>
    </div>
  );
}
