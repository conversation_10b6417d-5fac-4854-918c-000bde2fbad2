# Guia Completo: Internacionalização (i18n) PT/EN com i18next no React

## 1. Estrutura dos Arquivos de Tradução

- Crie dois arquivos JSON principais:
  - `src/i18n/pt-BR.json`
  - `src/i18n/en-GB.json`
- Cada arquivo deve conter **todas as chaves** usadas na interface, organizadas por seções (ex: `profile`, `dashboard`, `common`, etc).
- **Nunca duplique blocos de chave** (ex: só um `"profile"` por arquivo).

**Exemplo:**
```json
// pt-BR.json
{
  "profile": {
    "greeting": "O<PERSON><PERSON>, {{name}}",
    "personalInfo": "Informações Pessoais",
    "name": "Nome",
    "email": "Email",
    "phone": "Telefone"
  },
  "common": {
    "profile": "Perfil",
    "logout": "Sair",
    "actions": "Ações"
  }
}
```
```json
// en-GB.json
{
  "profile": {
    "greeting": "Hello, {{name}}",
    "personalInfo": "Personal Information",
    "name": "Name",
    "email": "Email",
    "phone": "Phone"
  },
  "common": {
    "profile": "Profile",
    "logout": "Logout",
    "actions": "Actions"
  }
}
```

---

## 2. Configuração do i18next

- Instale as dependências:
  ```bash
  npm install i18next react-i18next i18next-browser-languagedetector
  ```
- Configure o i18n (exemplo: `src/i18n/config.ts`):

```typescript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import ptBR from './pt-BR.json';
import enGB from './en-GB.json';

const resources = {
  'pt-BR': { translation: ptBR },
  'en-GB': { translation: enGB }
};

const languageAliases: Record<string, string> = { en: 'en-GB', pt: 'pt-BR' };

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: ['en-GB', 'pt-BR', 'en', 'pt'],
    lng: languageAliases[localStorage.getItem('i18nextLng') || 'en'] || 'en-GB',
    interpolation: { escapeValue: false },
    detection: {
      order: ['localStorage', 'navigator'],
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage']
    }
  });

export default i18n;
```

---

## 3. Uso nos Componentes

- Importe e use o hook:
  ```typescript
  import { useTranslation } from 'react-i18next';

  const { t } = useTranslation();

  return <h1>{t('profile.greeting', { name: userName })}</h1>;
  ```
- Sempre use `t('chave')` para textos, nunca texto fixo.

---

## 4. Seletor de Idioma

- Implemente um seletor para alternar o idioma:
  ```typescript
  const { i18n } = useTranslation();
  <button onClick={() => i18n.changeLanguage('pt-BR')}>PT</button>
  <button onClick={() => i18n.changeLanguage('en-GB')}>EN</button>
  ```
- O idioma selecionado será salvo no `localStorage` e persistido.

---

## 5. Boas Práticas

- **Nunca** duplique blocos de chave nos JSONs.
- Sempre adicione novas chaves em ambos os arquivos.
- Use nomes de chave descritivos e agrupados por contexto.
- Teste a troca de idioma em toda a interface.
- Para textos dinâmicos, use interpolação: `"greeting": "Olá, {{name}}"`.

---

## 6. Debug

- Se aparecer a chave literal (`profile.greeting`), verifique:
  - Se a chave existe no JSON.
  - Se não há duplicidade de blocos.
  - Se o idioma ativo é `en-GB` ou `pt-BR`.
  - Se o cache/localStorage não está corrompido.

---

## 7. Checklist de Internacionalização

- [x] Todos os textos estão em `t('chave')`
- [x] Arquivos de tradução completos e sem duplicidade
- [x] Seletor de idioma funcional
- [x] Teste de troca de idioma em toda a interface
- [x] Chaves dinâmicas/interpoladas funcionando

---

**Seguindo este guia, seu sistema estará 100% pronto para funcionar em português e inglês, com troca dinâmica e sem bugs de tradução!** 