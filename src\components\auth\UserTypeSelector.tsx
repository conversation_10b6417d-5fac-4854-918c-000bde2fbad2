import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  GraduationCap, 
  Users, 
  MapPin, 
  Shield, 
  Clock, 
  Bell,
  CheckCircle,
  AlertTriangle,
  ArrowRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { UserType } from '@/lib/auth-redirects';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface UserTypeSelectorProps {
  selectedType: UserType | null;
  onSelect: (type: UserType) => void;
  onConfirm: () => void;
}

const UserTypeSelector: React.FC<UserTypeSelectorProps> = ({
  selectedType,
  onSelect,
  onConfirm
}) => {
  const { t } = useTranslation();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmedType, setConfirmedType] = useState<UserType | null>(null);

  const handleSelectAndConfirm = (type: UserType) => {
    setConfirmedType(type);
    setShowConfirmation(true);
  };

  const handleConfirmSelection = () => {
    if (confirmedType) {
      onSelect(confirmedType);
      setShowConfirmation(false);
      onConfirm();
    }
  };

  const userTypeData = {
    student: {
      title: t('auth.userTypeSelector.student.title', 'I am a Student'),
      icon: GraduationCap,
      color: "blue",
      description: t('auth.userTypeSelector.student.description', 'I share my location with my guardians'),
      features: [
        { icon: MapPin, text: t('auth.userTypeSelector.student.features.shareLocation', 'Share your location') },
        { icon: Shield, text: t('auth.userTypeSelector.student.features.privacyControl', 'Privacy controlled by you') },
        { icon: Bell, text: t('auth.userTypeSelector.student.features.receiveNotifications', 'Receive notifications from guardians') },
        { icon: Clock, text: t('auth.userTypeSelector.student.features.locationWhenNeeded', 'Location only when necessary') }
      ],
      examples: [
        t('auth.userTypeSelector.student.examples.school', '�� You are at school/university'),
        t('auth.userTypeSelector.student.examples.parents', '🎒 Your parents want to know if you arrived safely'),
        t('auth.userTypeSelector.student.examples.control', '📱 You control when to share'),
        t('auth.userTypeSelector.student.examples.age', '✅ You are between 6-25 years old')
      ],
      warning: t('auth.userTypeSelector.student.warning', '⚠️ Choose this option only if you are the STUDENT who will share location')
    },
    parent: {
      title: t('auth.userTypeSelector.parent.title', 'I am a Guardian'),
      icon: Users,
      color: "emerald",
      description: t('auth.userTypeSelector.parent.description', 'I monitor my students\' location'),
      features: [
        { icon: Users, text: t('auth.userTypeSelector.parent.features.manageStudents', 'Manage multiple students') },
        { icon: MapPin, text: t('auth.userTypeSelector.parent.features.viewLocation', 'View students\' location') },
        { icon: Bell, text: t('auth.userTypeSelector.parent.features.sendNotifications', 'Send notifications and alerts') },
        { icon: Shield, text: t('auth.userTypeSelector.parent.features.responsibleSecurity', 'Responsible for security') }
      ],
      examples: [
        t('auth.userTypeSelector.parent.examples.parentRole', '👨‍👩‍👧‍👦 You are a parent or guardian'),
        t('auth.userTypeSelector.parent.examples.schoolArrival', '🏫 Want to know if child arrived at school'),
        t('auth.userTypeSelector.parent.examples.mapView', '📍 View on map where they are'),
        t('auth.userTypeSelector.parent.examples.inviteStudents', '🔗 Invite students to connect')
      ],
      warning: t('auth.userTypeSelector.parent.warning', '⚠️ Choose this option only if you are a PARENT/GUARDIAN')
    }
  };

  const getCardClasses = (type: UserType) => {
    const data = userTypeData[type];
    const isSelected = selectedType === type;
    
    return cn(
      "cursor-pointer transition-all duration-300 border-2 h-full",
      "hover:scale-[1.02] hover:shadow-lg",
      isSelected 
        ? `border-${data.color}-500 bg-${data.color}-50 shadow-lg ring-2 ring-${data.color}-200`
        : "border-gray-200 hover:border-gray-300",
      data.color === 'blue' && isSelected && "border-blue-500 bg-blue-50 ring-blue-200",
      data.color === 'emerald' && isSelected && "border-emerald-500 bg-emerald-50 ring-emerald-200"
    );
  };

  const getIconClasses = (type: UserType) => {
    const data = userTypeData[type];
    const isSelected = selectedType === type;
    
    return cn(
      "w-12 h-12 mb-4 transition-colors",
      isSelected 
        ? data.color === 'blue' 
          ? "text-blue-600" 
          : "text-emerald-600"
        : "text-gray-400"
    );
  };

  const getBadgeClasses = (type: UserType) => {
    const data = userTypeData[type];
    return data.color === 'blue' 
      ? "bg-blue-100 text-blue-700" 
      : "bg-emerald-100 text-emerald-700";
  };

  const getButtonClasses = (type: UserType) => {
    const data = userTypeData[type];
    return data.color === 'blue'
      ? "bg-blue-600 hover:bg-blue-700"
      : "bg-emerald-600 hover:bg-emerald-700";
  };

  return (
    <>
      <div className="w-full max-w-4xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-bold text-gray-900">
            {t('auth.userTypeSelector.title', 'What is your profile?')}
          </h2>
          <div className="text-gray-600">
            {t('auth.userTypeSelector.subtitle', 'Choose carefully - this will define all features available to you')}
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {Object.entries(userTypeData).map(([type, data]) => {
            const Icon = data.icon;
            const isSelected = selectedType === type;
            
            return (
              <Card 
                key={type}
                className={getCardClasses(type as UserType)}
                onClick={() => onSelect(type as UserType)}
              >
                <CardHeader className="text-center pb-4">
                  <div className="flex justify-center">
                    <Icon className={getIconClasses(type as UserType)} />
                  </div>
                  <CardTitle className="text-xl">
                    {data.title}
                  </CardTitle>
                  <div className="text-sm text-gray-600">
                    {data.description}
                  </div>
                  {isSelected && (
                    <Badge className={getBadgeClasses(type as UserType)}>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      {t('auth.userTypeSelector.selected', 'Selected')}
                    </Badge>
                  )}
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2 text-gray-900">{t('auth.userTypeSelector.featuresLabel', 'Features:')}</h4>
                    <div className="space-y-2">
                      {data.features.map((feature, index) => {
                        const FeatureIcon = feature.icon;
                        return (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <FeatureIcon className="w-4 h-4 text-gray-500" />
                            <span>{feature.text}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2 text-gray-900">{t('auth.userTypeSelector.examplesLabel', 'Use cases:')}</h4>
                    <div className="space-y-1">
                      {data.examples.map((example, index) => (
                        <div key={index} className="text-sm text-gray-600">
                          {example}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm text-yellow-800">
                        {data.warning}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {selectedType && (
          <div className="text-center pt-4">
            <Button
              onClick={() => handleSelectAndConfirm(selectedType)}
              className={cn(
                "px-8 py-3 text-white font-medium",
                getButtonClasses(selectedType)
              )}
              size="lg"
            >
              {t('auth.userTypeSelector.continueAs', 'Continue as {{type}}', {
                type: selectedType === 'student' 
                  ? t('auth.userTypes.student', 'Student')
                  : t('auth.userTypes.parent', 'Guardian')
              })}
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}
      </div>

      {/* Dialog de Confirmação */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-amber-500" />
              {t('auth.userTypeSelector.dialog.title', 'Confirm Account Type')}
            </DialogTitle>
            <DialogDescription className="space-y-3 text-left">
              <div>
                {t('auth.userTypeSelector.dialog.creatingAs', 'You are creating an account as {{type}}.', {
                  type: confirmedType === 'student' 
                    ? t('auth.userTypes.student', 'STUDENT').toUpperCase()
                    : t('auth.userTypes.parent', 'GUARDIAN').toUpperCase()
                })}
              </div>
              
              {confirmedType === 'student' ? (
                <div className="space-y-2">
                  <div className="font-medium text-gray-900">{t('auth.userTypeSelector.dialog.studentWillDo', 'As a student, you will:')}</div>
                  <ul className="list-disc list-inside text-sm space-y-1">
                    <li>{t('auth.userTypeSelector.dialog.studentFeatures.shareLocation', 'Share your location with guardians')}</li>
                    <li>{t('auth.userTypeSelector.dialog.studentFeatures.receiveFamilyRequests', 'Receive family link requests')}</li>
                    <li>{t('auth.userTypeSelector.dialog.studentFeatures.controlSharing', 'Control when to share your location')}</li>
                  </ul>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="font-medium text-gray-900">{t('auth.userTypeSelector.dialog.parentWillDo', 'As a guardian, you will:')}</div>
                  <ul className="list-disc list-inside text-sm space-y-1">
                    <li>{t('auth.userTypeSelector.dialog.parentFeatures.viewStudentLocation', 'View location of linked students')}</li>
                    <li>{t('auth.userTypeSelector.dialog.parentFeatures.requestLinks', 'Request links with students')}</li>
                    <li>{t('auth.userTypeSelector.dialog.parentFeatures.manageFamilyConnections', 'Manage multiple family connections')}</li>
                  </ul>
                </div>
              )}
              
              <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
                <div className="text-sm text-red-800 font-medium">
                  {t('auth.userTypeSelector.dialog.warning', '\u26a0\ufe0f ATTENTION: This choice cannot be changed later!')}
                </div>
              </div>
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowConfirmation(false)}
            >
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              onClick={handleConfirmSelection}
              className={cn(
                "text-white",
                confirmedType === 'student' 
                  ? "bg-blue-600 hover:bg-blue-700"
                  : "bg-emerald-600 hover:bg-emerald-700"
              )}
            >
              {t('auth.userTypeSelector.dialog.confirmAs', 'Yes, confirm as {{type}}', {
                type: confirmedType === 'student' 
                  ? t('auth.userTypes.student', 'Student')
                  : t('auth.userTypes.parent', 'Guardian')
              })}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UserTypeSelector; 