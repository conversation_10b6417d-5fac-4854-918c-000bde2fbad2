
-- 1. CORRIGIR PERMISSÕES DA TABELA auth_logs
-- Conceder per<PERSON><PERSON><PERSON><PERSON> necessárias para roles authenticated, anon, service_role
GRANT SELECT, INSERT ON public.auth_logs TO authenticated, anon, service_role;
GRANT USAGE ON SEQUENCE public.auth_logs_id_seq TO authenticated, anon, service_role;

-- 2. LIMPAR DADOS INCONSISTENTES DO USUÁRIO <EMAIL>
-- Remover entrada incorreta da tabela guardian_profiles (estudante não deveria estar lá)
DELETE FROM public.guardian_profiles 
WHERE user_id = '1ed55e48-d90f-493e-995d-5202d7e86320';

-- 3. VERIFICAR SE O USUÁRIO PRECISA SER ADICIONADO À TABELA students
-- Baseado nos dados do perfil, este usuário é um estudante e deveria estar na tabela students
INSERT INTO public.students (
  id,
  name,
  school_id,
  school_name,
  grade,
  class,
  status,
  guardian_id,
  location_sharing,
  created_at,
  updated_at
) VALUES (
  '1ed55e48-d90f-493e-995d-5202d7e86320',
  '<PERSON>abi<PERSON> Cunha',
  'ESC001',
  'Escola Municipal Exemplo',
  '9º Ano',
  'A',
  'active',
  '024bb19d-8159-4ac1-82ac-4db95e39c0cf', -- Guardian baseado no parent_email no profiles
  true,
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  updated_at = NOW();

-- 4. REGISTRAR A CORREÇÃO NOS LOGS
INSERT INTO public.auth_logs (
  event_type,
  user_id,
  metadata,
  occurred_at
) VALUES (
  'login_fix_permissions_applied',
  '1ed55e48-d90f-493e-995d-5202d7e86320',
  jsonb_build_object(
    'action', 'auth_logs_permissions_fixed_and_data_cleaned',
    'email', '<EMAIL>',
    'timestamp', NOW(),
    'changes', jsonb_build_array(
      'granted_auth_logs_permissions',
      'removed_from_guardian_profiles',
      'added_to_students_table'
    )
  ),
  NOW()
);
