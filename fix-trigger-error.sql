-- Fix trigger error: add updated_at column to family_invitations table
-- The trigger_set_timestamp() function expects an updated_at column

-- Add updated_at column if it doesn't exist
ALTER TABLE public.family_invitations 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT now();

-- Update the trigger to use the correct timestamp function
DROP TRIGGER IF EXISTS family_invitations_updated_at ON public.family_invitations;

CREATE OR REPLACE FUNCTION public.trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger
CREATE TRIGGER family_invitations_updated_at
    BEFORE UPDATE ON public.family_invitations
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp(); 