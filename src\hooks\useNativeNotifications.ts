import { useEffect, useCallback } from 'react';
import { Capacitor } from '@capacitor/core';
import { LocalNotifications } from '@capacitor/local-notifications';
import { LocationData } from '@/types/database';

interface UseNativeNotificationsProps {
  isEnabled?: boolean;
  locations?: LocationData[];
  lastNotificationTimestamp?: number;
}

export function useNativeNotifications({
  isEnabled = true,
  locations = [],
  lastNotificationTimestamp = 0
}: UseNativeNotificationsProps = {}) {
  const isNative = Capacitor.isNativePlatform();
  const isIOS = isNative && Capacitor.getPlatform() === 'ios';
  const isAndroid = isNative && Capacitor.getPlatform() === 'android';

  // Request permission for notifications
  const requestPermissions = useCallback(async () => {
    if (!isNative) return false;
    
    try {
      const result = await LocalNotifications.requestPermissions();
      return result.display === 'granted';
    } catch (error) {
      console.error('[Notifications] Error requesting permissions:', error);
      return false;
    }
  }, [isNative]);

  // Check if notifications are permitted
  const checkPermissions = useCallback(async () => {
    if (!isNative) return false;
    
    try {
      const result = await LocalNotifications.checkPermissions();
      return result.display === 'granted';
    } catch (error) {
      console.error('[Notifications] Error checking permissions:', error);
      return false;
    }
  }, [isNative]);

  // Send a location update notification
  const sendLocationNotification = useCallback(async (location: LocationData) => {
    if (!isNative || !isEnabled) return;
    
    try {
      // Format the location timestamp
      const timestamp = new Date(location.timestamp).toLocaleTimeString();
      
      // Create a notification
      await LocalNotifications.schedule({
        notifications: [
          {
            id: Date.now(),
            title: 'Location Updated',
            body: `Location updated at ${timestamp}${location.address ? ` near ${location.address}` : ''}`,
            schedule: { at: new Date(Date.now()) },
            sound: isAndroid ? 'notification_sound' : undefined,
            smallIcon: isAndroid ? 'ic_stat_location_on' : undefined,
            actionTypeId: 'LOCATION_UPDATE',
            extra: {
              locationId: location.id,
              latitude: location.latitude,
              longitude: location.longitude,
              timestamp: location.timestamp
            }
          }
        ]
      });
      
      console.log('[Notifications] Sent location notification');
      return true;
    } catch (error) {
      console.error('[Notifications] Error sending notification:', error);
      return false;
    }
  }, [isNative, isEnabled, isAndroid]);

  // Send a notification when entering or exiting a geofence
  const sendGeofenceNotification = useCallback(async (
    type: 'enter' | 'exit',
    locationName: string
  ) => {
    if (!isNative || !isEnabled) return;
    
    try {
      const title = type === 'enter' ? 'Entered Area' : 'Left Area';
      const body = type === 'enter' 
        ? `You've entered ${locationName}`
        : `You've left ${locationName}`;
      
      await LocalNotifications.schedule({
        notifications: [
          {
            id: Date.now(),
            title,
            body,
            schedule: { at: new Date(Date.now()) },
            sound: isAndroid ? 'notification_sound' : undefined,
            smallIcon: isAndroid ? 'ic_stat_location_on' : undefined,
            actionTypeId: 'GEOFENCE_UPDATE',
            extra: {
              type,
              locationName,
              timestamp: Date.now()
            }
          }
        ]
      });
      
      console.log(`[Notifications] Sent geofence ${type} notification for ${locationName}`);
      return true;
    } catch (error) {
      console.error('[Notifications] Error sending geofence notification:', error);
      return false;
    }
  }, [isNative, isEnabled, isAndroid]);

  // Send a notification for offline mode
  const sendOfflineModeNotification = useCallback(async (isOffline: boolean) => {
    if (!isNative || !isEnabled) return;
    
    try {
      const title = isOffline ? 'Offline Mode Activated' : 'Back Online';
      const body = isOffline 
        ? 'App is now in offline mode. Some features may be limited.'
        : 'Internet connection restored. All features available.';
      
      await LocalNotifications.schedule({
        notifications: [
          {
            id: Date.now(),
            title,
            body,
            schedule: { at: new Date(Date.now()) },
            sound: isAndroid ? 'notification_sound' : undefined,
            smallIcon: isAndroid ? 'ic_stat_wifi' : undefined,
            actionTypeId: 'CONNECTIVITY_UPDATE',
            extra: {
              isOffline,
              timestamp: Date.now()
            }
          }
        ]
      });
      
      console.log(`[Notifications] Sent ${isOffline ? 'offline' : 'online'} mode notification`);
      return true;
    } catch (error) {
      console.error('[Notifications] Error sending offline mode notification:', error);
      return false;
    }
  }, [isNative, isEnabled, isAndroid]);

  // Initialize notifications
  useEffect(() => {
    if (!isNative || !isEnabled) return;
    
    // Request permissions on component mount
    requestPermissions().then(granted => {
      if (granted) {
        console.log('[Notifications] Permissions granted');
      } else {
        console.log('[Notifications] Permissions denied');
      }
    });
    
    // Set up notification action handlers
    LocalNotifications.registerActionTypes({
      types: [
        {
          id: 'LOCATION_UPDATE',
          actions: [
            {
              id: 'view',
              title: 'View Location'
            },
            {
              id: 'dismiss',
              title: 'Dismiss',
              destructive: true
            }
          ]
        },
        {
          id: 'GEOFENCE_UPDATE',
          actions: [
            {
              id: 'view',
              title: 'View Details'
            },
            {
              id: 'dismiss',
              title: 'Dismiss',
              destructive: true
            }
          ]
        },
        {
          id: 'CONNECTIVITY_UPDATE',
          actions: [
            {
              id: 'settings',
              title: 'Settings'
            },
            {
              id: 'dismiss',
              title: 'Dismiss',
              destructive: true
            }
          ]
        }
      ]
    });
    
    // Listen for notification actions
    let listenerHandle: any = null;
    
    const setupListener = async () => {
      listenerHandle = await LocalNotifications.addListener(
        'localNotificationActionPerformed',
        (notificationAction) => {
          console.log('[Notifications] Action performed:', notificationAction);
          
          // Handle different action types
          const { actionId, notification } = notificationAction;
          
          if (actionId === 'view' && notification.extra?.locationId) {
            // Handle view location action
            console.log('[Notifications] View location:', notification.extra);
            // In a real implementation, we would navigate to the location on the map
          } else if (actionId === 'settings' && notification.actionTypeId === 'CONNECTIVITY_UPDATE') {
            // Handle settings action for connectivity updates
            console.log('[Notifications] Open settings');
            // In a real implementation, we would open the app settings
          }
        }
      );
    };
    
    setupListener();
    
    return () => {
      // Clean up listeners
      if (listenerHandle) {
        listenerHandle.remove();
      }
    };
  }, [isNative, isEnabled, requestPermissions]);

  // Monitor locations for notification triggers
  useEffect(() => {
    if (!isNative || !isEnabled || !locations.length) return;
    
    // Get the most recent location
    const latestLocation = locations[0];
    
    // Check if we should send a notification (only if it's a new location since last notification)
    const locationTimestamp = new Date(latestLocation.timestamp).getTime();
    if (latestLocation && locationTimestamp > lastNotificationTimestamp) {
      sendLocationNotification(latestLocation);
    }
  }, [isNative, isEnabled, locations, lastNotificationTimestamp, sendLocationNotification]);

  return {
    isNative,
    isIOS,
    isAndroid,
    requestPermissions,
    checkPermissions,
    sendLocationNotification,
    sendGeofenceNotification,
    sendOfflineModeNotification
  };
}