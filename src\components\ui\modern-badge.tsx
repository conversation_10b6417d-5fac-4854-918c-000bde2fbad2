import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const modernBadgeVariants = cva(
  'inline-flex items-center rounded-full text-xs font-medium transition-all duration-300 hover:scale-105',
  {
    variants: {
      variant: {
        default: 'modern-badge',
        secondary: 'bg-gradient-to-r from-secondary/80 to-secondary text-secondary-foreground border border-secondary/30',
        destructive: 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300 dark:from-red-900/30 dark:to-red-800/30 dark:text-red-400 dark:border-red-800',
        outline: 'border border-input bg-background/50 backdrop-blur-sm',
        success: 'bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300 dark:from-green-900/30 dark:to-green-800/30 dark:text-green-400 dark:border-green-800',
        warning: 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 border border-yellow-300 dark:from-yellow-900/30 dark:to-yellow-800/30 dark:text-yellow-400 dark:border-yellow-800',
      },
      size: {
        default: 'px-3 py-1',
        sm: 'px-2 py-0.5 text-xs',
        lg: 'px-4 py-2 text-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ModernBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof modernBadgeVariants> {}

function ModernBadge({ className, variant, size, ...props }: ModernBadgeProps) {
  return (
    <div className={cn(modernBadgeVariants({ variant, size }), className)} {...props} />
  );
}

export { ModernBadge, modernBadgeVariants };