Knowledge Base and References - Locate-Family-Connect
🧠 Visão Geral
Esta base de conhecimento centraliza informações essenciais, decisões técnicas, referências e recursos para o projeto Locate-Family-Connect. Use este documento como ponto de partida para entender conceitos fundamentais, legislação aplicável e decisões arquiteturais que moldaram o sistema.

📚 Índice da Base de Conhecimento
Decisões Técnicas
Comparativos Tecnológicos
Notebooks & Tutoriais
Legislação Aplicável
Referências Externas
Glossário
🧩 Decisões Técnicas
Arquitetura Backend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | Adoção do Supabase como plataforma backend | Necessidade de autenticação integrada, banco de dados com RLS e Edge Functions em uma única plataforma | 2025-01-15 | Equipe de Arquitetura | | Implementação de PKCE para autenticação | Garantir segurança no fluxo de autenticação SPA sem depender de segredos no cliente | 2025-01-20 | Time de Segurança | | Uso de Row Level Security (RLS) para controle de acesso | Implementação de segurança a nível de dados para garantir isolamento entre perfis de usuários | 2025-02-05 | DBA + Arquiteto de Segurança | | Adoção de Edge Functions para processamento de emails | Melhorar performance e reduzir latência no envio de notificações | 2025-03-10 | Time de Backend |

Arquitetura Frontend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | React + TypeScript como stack principal | Type safety e melhor manutenabilidade a longo prazo | 2025-01-10 | Time Frontend | | Vite como bundler | Performance superior em desenvolvimento e builds mais rápidos | 2025-01-10 | Time Frontend | | TailwindCSS para estilização | Produtividade, consistência e baixo overhead de CSS | 2025-01-15 | Time Frontend + UX | | Radix UI para componentes acessíveis | Componentes headless com foco em acessibilidade e personalização | 2025-02-01 | Time Frontend + UX |

Padrões de Código
| Padrão | Descrição | Onde é Aplicado | |--------|-----------|----------------| | Repository Pattern | Abstração da camada de dados para facilitar testes e manutenção | /src/repositories/ | | Composition over Inheritance | Preferência por composição de hooks e componentes | Toda a base de código | | Context + Hooks para gerenciamento de estado | Estado global gerenciado via React Context API e hooks customizados | /src/contexts/, /src/hooks/ | | Fail-fast para validação | Validação antecipada de entradas para falhar o mais cedo possível | Funções Edge, componentes de formulário |

Métricas e Monitoramento
| Componente | Ferramenta | Limites de Alerta | Responsável | |------------|------------|-------------------|-------------| | API Performance | Supabase Logs | >500ms por request | DevOps | | Taxas de Erro | Sentry | >1% de taxa de erros | Time de Suporte | | Disponibilidade | UptimeRobot | <99.9% de uptime | DevOps | | Performance Frontend | Lighthouse | Score <90 | Time Frontend |

📊 Comparativos Tecnológicos
Plataformas Backend
| Critério | Supabase | Firebase | AWS Amplify | Solução Custom | |----------|---------|----------|-------------|---------------| | Custo Inicial | Médio | Baixo | Alto | Muito Alto | | Curva de Aprendizado | Média | Baixa | Alta | Alta | | Vendor Lock-in | Médio | Alto | Alto | Baixo | | PostgreSQL Nativo | ✅ | ❌ | ✅ | ✅ | | Row Level Security | ✅ | ❌ | ❌ | ✅ | | Edge Functions | ✅ | ✅ | ❌ | ⚠️ | | Open Source | ✅ | ❌ | ⚠️ | ✅ |

Decisão Final: Supabase foi escolhido devido ao suporte nativo a PostgreSQL com RLS, menor vendor lock-in comparado ao Firebase, e menor custo de desenvolvimento comparado a uma solução custom.

APIs de Mapas
| Critério | MapBox | Google Maps | OpenStreetMap | |----------|--------|------------|---------------| | Preço | Plano Free Generoso | Caro após volume | Gratuito | | Limites API | 50K carregamentos/mês | 28K carregamentos/mês | Sem limite técnico | | Opções de Personalização | Excelentes | Boas | Limitadas | | Qualidade de Mapas | Muito Boa | Excelente | Boa | | Documentação | Extensa | Extensa | Média | | Precisão de Geolocalização | Alta | Muito Alta | Média |

Decisão Final: MapBox foi escolhido pelo melhor equilíbrio entre custo (plano gratuito generoso) e recursos avançados de personalização necessários para geocercas.

Serviços de Email
| Critério | Resend | SendGrid | AWS SES | Mailgun | |----------|--------|----------|---------|---------| | Preço | Bom (1K emails grátis/mês) | Médio | Muito Bom | Médio | | Facilidade de Integração | Excelente | Boa | Média | Boa | | Taxas de Entrega | Muito Boa | Excelente | Boa | Muito Boa | | Suporte a Templates | ✅ | ✅ | ✅ | ✅ | | Analytics | Básico | Avançado | Básico | Avançado |

Decisão Final: Resend foi escolhido pela facilidade de integração com Edge Functions e boa relação custo-benefício para o volume esperado.

📔 Notebooks & Tutoriais
Notebooks Explicativos
| Nome | Descrição | Link | |------|-----------|------| | developer_guide_improved.ipynb | Guia completo para novos desenvolvedores | Visualizar | | authentication_flow_pkce.ipynb | Explicação detalhada do fluxo PKCE | Visualizar | | mapbox_integration_tutorial.ipynb | Tutorial de integração com MapBox | Visualizar | | rls_policies_explained.ipynb | Explicação das políticas RLS | Visualizar |

Vídeos Tutoriais
Onboarding para Desenvolvedores: Assistir (20min)
Debug Avançado do Fluxo de Autenticação: Assistir (15min)
Criação de Geocercas com MapBox: Assistir (18min)
Deploy de Edge Functions no Supabase: Assistir (12min)
📜 Legislação Aplicável
LEI 15100/2025 - Aparelhos Eletrônicos em Escolas
Esta lei regula o uso de aparelhos eletrônicos em ambiente escolar e estabelece os seguintes pontos relevantes para nosso sistema:

Artigo 3: Define que o uso de dispositivos móveis pode ser autorizado para finalidades pedagógicas ou de segurança.
Artigo 5: Estabelece que aplicativos de localização devem:
Receber consentimento explícito dos alunos e responsáveis
Garantir que dados sejam processados seguindo a LGPD
Permitir desativar o rastreamento a qualquer momento
Artigo 8: Define que notificações devem ser claras e discretas, sem interromper atividades escolares.
Artigo 12: Estabelece multas e sanções para violações.
Impacto no Sistema: Nossa implementação garante conformidade através de:

Consentimento explícito no registro
Controles para ativar/desativar compartilhamento de localização
Notificações silenciosas e discretas
Criptografia e políticas RLS para proteção de dados
LGPD - Lei Geral de Proteção de Dados
Os principais aspectos da LGPD aplicáveis ao projeto:

Consentimento: Implementamos fluxos claros de consentimento para coleta de localização
Finalidade específica: Dados coletados apenas para finalidade de segurança
Minimização de dados: Coletamos apenas dados estritamente necessários
Direito ao esquecimento: Implementamos APIs para exclusão completa de dados
Segurança: Utilizamos criptografia e controles de acesso para proteger dados sensíveis
Documento de Conformidade: Ver documento completo

🌐 Referências Externas
Documentações Oficiais
Supabase Documentation
React Documentation
TypeScript Handbook
MapBox GL JS Documentation
Resend API Reference
TailwindCSS Documentation
Radix UI Documentation
Artigos e Tutoriais Recomendados
"Securing SPAs with PKCE"
"Building Realtime Applications with Supabase"
"Advanced Geofencing with MapBox"
"Row Level Security Best Practices"
"Optimizing React Applications"
"TypeScript Advanced Patterns"
Livros Recomendados
"TypeScript in 50 Lessons" - Stefan Baumgartner
"Real-World Next.js" - Michele Riva
"PostgreSQL 14 Administration Cookbook" - Simon Riggs
"Designing Data-Intensive Applications" - Martin Kleppmann
"Web Application Security" - Andrew Hoffman
📖 Glossário
| Termo | Definição | |-------|-----------| | PKCE | Proof Key for Code Exchange - Extensão do fluxo OAuth para aplicações públicas | | RLS | Row Level Security - Recurso do PostgreSQL para controle de acesso a nível de linha | | Edge Function | Função serverless executada em servidores próximos do usuário | | Geocerca | Perímetro virtual definido em mapa que gera alertas quando atravessado | | Realtime | Funcionalidade do Supabase para receber atualizações em tempo real via WebSockets | | Guardian | Perfil de usuário responsável que monitora estudantes | | Student | Perfil de usuário que compartilha sua localização | | Circuit Breaker | Padrão de design que previne falhas em cascata em sistemas distribuídos | | LGPD | Lei Geral de Proteção de Dados - Legislação brasileira sobre privacidade |

🔄 Evolução da Base de Conhecimento
Processos de Atualização
Revisão Trimestral: Revisão completa da base a cada 3 meses
Atualização Contínua: Adição de novas decisões técnicas ao serem tomadas
Arquivamento: Decisões obsoletas são movidas para um arquivo histórico
Contribuições
Para contribuir com esta base de conhecimento:

Crie uma branch docs/kb-update-[tópico]
Adicione ou atualize o conteúdo seguindo as convenções estabelecidas
Abra um PR com descrição detalhada das mudanças
Solicite revisão de pelo menos um membro do time técnico
🗃️ Anexos
Diagrama de Arquitetura (PDF)
Fluxograma de Autenticação (Figma)
Mapa de Integrações (Miro)
Apresentação do Projeto (PowerPoint)
Especificação de API (OpenAPI)
🔄 Histórico de Versões
| Versão | Data | Descrição | Autor | |--------|------|-----------|-------| | 1.0 | 2025-06-04 | Criação inicial da base de conhecimento | Equipe de Documentação | | 0.9 | 2025-05-20 | Rascunho inicial com estrutura básica | Mauro Silva | | 0.8 | 2025-05-10 | Compilação de recursos existentes | Time de Desenvolvimento |

Última atualização: 04 de junho de 2025

Feedback submitted
Me

salvou?

Revert
Thought Process

Não, o conteúdo que gerei para o arquivo "10_KNOWLEDGE_BASE_AND_REFERENCES.md" ainda não foi salvo. Você precisa criar manualmente o arquivo na pasta "C:\Users\<USER>\Documents\GitHub\locate-family-connect\docs\General" e copiar o conteúdo que forneci.

Você pode criar o arquivo de duas formas:

Criar um novo arquivo no seu editor e salvar com o nome "10_KNOWLEDGE_BASE_AND_REFERENCES.md" na pasta especificada
Usar o terminal para criar o arquivo
O conteúdo que forneci está pronto para ser copiado e colado no arquivo. Você quer que eu tente criar o arquivo para você?

Feedback submitted
Me

sim

Revert
Thought Process

Vou criar o arquivo para você. Utilizarei a ferramenta de escrita para salvar o documento:

Error while editing
10_KNOWLEDGE_BASE_AND_REFERENCES.md
Cascade error
Deadline exceeded: Encountered retryable error from model provider: context deadline exceeded (Client.Timeout or context cancellation while reading body)
Thought Process

Vou criar o arquivo para você:

Update: Tool calls no longer cost credits after April 21.
Status: Canceled
Canceled creation of
10_KNOWLEDGE_BASE_AND_REFERENCES.md
Feedback submitted
Me

gere o conteudo de "C:\Users\<USER>\Documents\GitHub\locate-family-connect\docs\General\10_KNOWLEDGE_BASE_AND_REFERENCES.md" 10_KNOWLEDGE_BASE_AND_REFERENCES.md
Foco: Base de conhecimento geral, guias, decisões e links úteis.

Links para notebooks explicativos

Resumos de decisões técnicas

Comparação entre alternativas

Guias técnicos, legislação aplicável (LEI_15100)

Anexos e referências externas

🔄 Fonte: knowledge-base-locate-family-connect.md, guia_desenvolvedor_educonnect.ipynb, developer_guide_improved.ipynb, LEI_15100_2025_APARELHOS_ELETRONICOS_ESCOLAS.md


Revert
Thought Process


Knowledge Base and References - Locate-Family-Connect
🧠 Visão Geral
Esta base de conhecimento centraliza informações essenciais, decisões técnicas, referências e recursos para o projeto Locate-Family-Connect. Use este documento como ponto de partida para entender conceitos fundamentais, legislação aplicável e decisões arquiteturais que moldaram o sistema.

📚 Índice da Base de Conhecimento
Decisões Técnicas
Comparativos Tecnológicos
Notebooks & Tutoriais
Legislação Aplicável
Referências Externas
Glossário
🧩 Decisões Técnicas
Arquitetura Backend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | Adoção do Supabase como plataforma backend | Necessidade de autenticação integrada, banco de dados com RLS e Edge Functions em uma única plataforma | 2025-01-15 | Equipe de Arquitetura | | Implementação de PKCE para autenticação | Garantir segurança no fluxo de autenticação SPA sem depender de segredos no cliente | 2025-01-20 | Time de Segurança | | Uso de Row Level Security (RLS) para controle de acesso | Implementação de segurança a nível de dados para garantir isolamento entre perfis de usuários | 2025-02-05 | DBA + Arquiteto de Segurança | | Adoção de Edge Functions para processamento de emails | Melhorar performance e reduzir latência no envio de notificações | 2025-03-10 | Time de Backend |

Arquitetura Frontend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | React + TypeScript como stack principal | Type safety e melhor manutenabilidade a longo prazo | 2025-01-10 | Time Frontend | | Vite como bundler | Performance superior em desenvolvimento e builds mais rápidos | 2025-01-10 | Time Frontend | | TailwindCSS para estilização | Produtividade, consistência e baixo overhead de CSS | 2025-01-15 | Time Frontend + UX | | Radix UI para componentes acessíveis | Componentes headless com foco em acessibilidade e personalização | 2025-02-01 | Time Frontend + UX |

Padrões de Código
| Padrão | Descrição | Onde é Aplicado | |--------|-----------|----------------| | Repository Pattern | Abstração da camada de dados para facilitar testes e manutenção | /src/repositories/ | | Composition over Inheritance | Preferência por composição de hooks e componentes | Toda a base de código | | Context + Hooks para gerenciamento de estado | Estado global gerenciado via React Context API e hooks customizados | /src/contexts/, /src/hooks/ | | Fail-fast para validação | Validação antecipada de entradas para falhar o mais cedo possível | Funções Edge, componentes de formulário |

Métricas e Monitoramento
| Componente | Ferramenta | Limites de Alerta | Responsável | |------------|------------|-------------------|-------------| | API Performance | Supabase Logs | >500ms por request | DevOps | | Taxas de Erro | Sentry | >1% de taxa de erros | Time de Suporte | | Disponibilidade | UptimeRobot | <99.9% de uptime | DevOps | | Performance Frontend | Lighthouse | Score <90 | Time Frontend |

📊 Comparativos Tecnológicos
Plataformas Backend
| Critério | Supabase | Firebase | AWS Amplify | Solução Custom | |----------|---------|----------|-------------|---------------| | Custo Inicial | Médio | Baixo | Alto | Muito Alto | | Curva de Aprendizado | Média | Baixa | Alta | Alta | | Vendor Lock-in | Médio | Alto | Alto | Baixo | | PostgreSQL Nativo | ✅ | ❌ | ✅ | ✅ | | Row Level Security | ✅ | ❌ | ❌ | ✅ | | Edge Functions | ✅ | ✅ | ❌ | ⚠️ | | Open Source | ✅ | ❌ | ⚠️ | ✅ |

Decisão Final: Supabase foi escolhido devido ao suporte nativo a PostgreSQL com RLS, menor vendor lock-in comparado ao Firebase, e menor custo de desenvolvimento comparado a uma solução custom.

APIs de Mapas
| Critério | MapBox | Google Maps | OpenStreetMap | |----------|--------|------------|---------------| | Preço | Plano Free Generoso | Caro após volume | Gratuito | | Limites API | 50K carregamentos/mês | 28K carregamentos/mês | Sem limite técnico | | Opções de Personalização | Excelentes | Boas | Limitadas | | Qualidade de Mapas | Muito Boa | Excelente | Boa | | Documentação | Extensa | Extensa | Média | | Precisão de Geolocalização | Alta | Muito Alta | Média |

Decisão Final: MapBox foi escolhido pelo melhor equilíbrio entre custo (plano gratuito generoso) e recursos avançados de personalização necessários para geocercas.

Serviços de Email
| Critério | Resend | SendGrid | AWS SES | Mailgun | |----------|--------|----------|---------|---------| | Preço | Bom (1K emails grátis/mês) | Médio | Muito Bom | Médio | | Facilidade de Integração | Excelente | Boa | Média | Boa | | Taxas de Entrega | Muito Boa | Excelente | Boa | Muito Boa | | Suporte a Templates | ✅ | ✅ | ✅ | ✅ | | Analytics | Básico | Avançado | Básico | Avançado |

Decisão Final: Resend foi escolhido pela facilidade de integração com Edge Functions e boa relação custo-benefício para o volume esperado.

📔 Notebooks & Tutoriais
Notebooks Explicativos
| Nome | Descrição | Link | |------|-----------|------| | developer_guide_improved.ipynb | Guia completo para novos desenvolvedores | Visualizar | | authentication_flow_pkce.ipynb | Explicação detalhada do fluxo PKCE | Visualizar | | mapbox_integration_tutorial.ipynb | Tutorial de integração com MapBox | Visualizar | | rls_policies_explained.ipynb | Explicação das políticas RLS | Visualizar |

Vídeos Tutoriais
Onboarding para Desenvolvedores: Assistir (20min)
Debug Avançado do Fluxo de Autenticação: Assistir (15min)
Criação de Geocercas com MapBox: Assistir (18min)
Deploy de Edge Functions no Supabase: Assistir (12min)
📜 Legislação Aplicável
LEI 15100/2025 - Aparelhos Eletrônicos em Escolas
Esta lei regula o uso de aparelhos eletrônicos em ambiente escolar e estabelece os seguintes pontos relevantes para nosso sistema:

Artigo 3: Define que o uso de dispositivos móveis pode ser autorizado para finalidades pedagógicas ou de segurança.
Artigo 5: Estabelece que aplicativos de localização devem:
Receber consentimento explícito dos alunos e responsáveis
Garantir que dados sejam processados seguindo a LGPD
Permitir desativar o rastreamento a qualquer momento
Artigo 8: Define que notificações devem ser claras e discretas, sem interromper atividades escolares.
Artigo 12: Estabelece multas e sanções para violações.
Impacto no Sistema: Nossa implementação garante conformidade através de:

Consentimento explícito no registro
Controles para ativar/desativar compartilhamento de localização
Notificações silenciosas e discretas
Criptografia e políticas RLS para proteção de dados
LGPD - Lei Geral de Proteção de Dados
Os principais aspectos da LGPD aplicáveis ao projeto:

Consentimento: Implementamos fluxos claros de consentimento para coleta de localização
Finalidade específica: Dados coletados apenas para finalidade de segurança
Minimização de dados: Coletamos apenas dados estritamente necessários
Direito ao esquecimento: Implementamos APIs para exclusão completa de dados
Segurança: Utilizamos criptografia e controles de acesso para proteger dados sensíveis
Documento de Conformidade: Ver documento completo

🌐 Referências Externas
Documentações Oficiais
Supabase Documentation
React Documentation
TypeScript Handbook
MapBox GL JS Documentation
Resend API Reference
TailwindCSS Documentation
Radix UI Documentation
Artigos e Tutoriais Recomendados
"Securing SPAs with PKCE"
"Building Realtime Applications with Supabase"
"Advanced Geofencing with MapBox"
"Row Level Security Best Practices"
"Optimizing React Applications"
"TypeScript Advanced Patterns"
Livros Recomendados
"TypeScript in 50 Lessons" - Stefan Baumgartner
"Real-World Next.js" - Michele Riva
"PostgreSQL 14 Administration Cookbook" - Simon Riggs
"Designing Data-Intensive Applications" - Martin Kleppmann
"Web Application Security" - Andrew Hoffman
📖 Glossário
| Termo | Definição | |-------|-----------| | PKCE | Proof Key for Code Exchange - Extensão do fluxo OAuth para aplicações públicas | | RLS | Row Level Security - Recurso do PostgreSQL para controle de acesso a nível de linha | | Edge Function | Função serverless executada em servidores próximos do usuário | | Geocerca | Perímetro virtual definido em mapa que gera alertas quando atravessado | | Realtime | Funcionalidade do Supabase para receber atualizações em tempo real via WebSockets | | Guardian | Perfil de usuário responsável que monitora estudantes | | Student | Perfil de usuário que compartilha sua localização | | Circuit Breaker | Padrão de design que previne falhas em cascata em sistemas distribuídos | | LGPD | Lei Geral de Proteção de Dados - Legislação brasileira sobre privacidade |

🔄 Evolução da Base de Conhecimento
Processos de Atualização
Revisão Trimestral: Revisão completa da base a cada 3 meses
Atualização Contínua: Adição de novas decisões técnicas ao serem tomadas
Arquivamento: Decisões obsoletas são movidas para um arquivo histórico
Contribuições
Para contribuir com esta base de conhecimento:

Crie uma branch docs/kb-update-[tópico]
Adicione ou atualize o conteúdo seguindo as convenções estabelecidas
Abra um PR com descrição detalhada das mudanças
Solicite revisão de pelo menos um membro do time técnico
🗃️ Anexos
[Diagrama de
Cascade error
Deadline exceeded: Encountered retryable error from model provider: context deadline exceeded (Client.Timeout or context cancellation while reading body)
Thought Process

Knowledge Base and References - Locate-Family-Connect
🧠 Visão Geral
Esta base de conhecimento centraliza informações essenciais, decisões técnicas, referências e recursos para o projeto Locate-Family-Connect. Use este documento como ponto de partida para entender conceitos fundamentais, legislação aplicável e decisões arquiteturais que moldaram o sistema.

📚 Índice da Base de Conhecimento
Decisões Técnicas
Comparativos Tecnológicos
Notebooks & Tutoriais
Legislação Aplicável
Referências Externas
Glossário
🧩 Decisões Técnicas
Arquitetura Backend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | Adoção do Supabase como plataforma backend | Necessidade de autenticação integrada, banco de dados com RLS e Edge Functions em uma única plataforma | 2025-01-15 | Equipe de Arquitetura | | Implementação de PKCE para autenticação | Garantir segurança no fluxo de autenticação SPA sem depender de segredos no cliente | 2025-01-20 | Time de Segurança | | Uso de Row Level Security (RLS) para controle de acesso | Implementação de segurança a nível de dados para garantir isolamento entre perfis de usuários | 2025-02-05 | DBA + Arquiteto de Segurança | | Adoção de Edge Functions para processamento de emails | Melhorar performance e reduzir latência no envio de notificações | 2025-03-10 | Time de Backend |

Arquitetura Frontend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | React + TypeScript como stack principal | Type safety e melhor manutenabilidade a longo prazo | 2025-01-10 | Time Frontend | | Vite como bundler | Performance superior em desenvolvimento e builds mais rápidos | 2025-01-10 | Time Frontend | | TailwindCSS para estilização | Produtividade, consistência e baixo overhead de CSS | 2025-01-15 | Time Frontend + UX | | Radix UI para componentes acessíveis | Componentes headless com foco em acessibilidade e personalização | 2025-02-01 | Time Frontend + UX |

Padrões de Código
| Padrão | Descrição | Onde é Aplicado | |--------|-----------|----------------| | Repository Pattern | Abstração da camada de dados para facilitar testes e manutenção | /src/repositories/ | | Composition over Inheritance | Preferência por composição de hooks e componentes | Toda a base de código | | Context + Hooks para gerenciamento de estado | Estado global gerenciado via React Context API e hooks customizados | /src/contexts/, /src/hooks/ | | Fail-fast para validação | Validação antecipada de entradas para falhar o mais cedo possível | Funções Edge, componentes de formulário |

Métricas e Monitoramento
| Componente | Ferramenta | Limites de Alerta | Responsável | |------------|------------|-------------------|-------------| | API Performance | Supabase Logs | >500ms por request | DevOps | | Taxas de Erro | Sentry | >1% de taxa de erros | Time de Suporte | | Disponibilidade | UptimeRobot | <99.9% de uptime | DevOps | | Performance Frontend | Lighthouse | Score <90 | Time Frontend |

📊 Comparativos Tecnológicos
Plataformas Backend
| Critério | Supabase | Firebase | AWS Amplify | Solução Custom | |----------|---------|----------|-------------|---------------| | Custo Inicial | Médio | Baixo | Alto | Muito Alto | | Curva de Aprendizado | Média | Baixa | Alta | Alta | | Vendor Lock-in | Médio | Alto | Alto | Baixo | | PostgreSQL Nativo | ✅ | ❌ | ✅ | ✅ | | Row Level Security | ✅ | ❌ | ❌ | ✅ | | Edge Functions | ✅ | ✅ | ❌ | ⚠️ | | Open Source | ✅ | ❌ | ⚠️ | ✅ |

Decisão Final: Supabase foi escolhido devido ao suporte nativo a PostgreSQL com RLS, menor vendor lock-in comparado ao Firebase, e menor custo de desenvolvimento comparado a uma solução custom.

APIs de Mapas
| Critério | MapBox | Google Maps | OpenStreetMap | |----------|--------|------------|---------------| | Preço | Plano Free Generoso | Caro após volume | Gratuito | | Limites API | 50K carregamentos/mês | 28K carregamentos/mês | Sem limite técnico | | Opções de Personalização | Excelentes | Boas | Limitadas | | Qualidade de Mapas | Muito Boa | Excelente | Boa | | Documentação | Extensa | Extensa | Média | | Precisão de Geolocalização | Alta | Muito Alta | Média |

Decisão Final: MapBox foi escolhido pelo melhor equilíbrio entre custo (plano gratuito generoso) e recursos avançados de personalização necessários para geocercas.

Serviços de Email
| Critério | Resend | SendGrid | AWS SES | Mailgun | |----------|--------|----------|---------|---------| | Preço | Bom (1K emails grátis/mês) | Médio | Muito Bom | Médio | | Facilidade de Integração | Excelente | Boa | Média | Boa | | Taxas de Entrega | Muito Boa | Excelente | Boa | Muito Boa | | Suporte a Templates | ✅ | ✅ | ✅ | ✅ | | Analytics | Básico | Avançado | Básico | Avançado |

Decisão Final: Resend foi escolhido pela facilidade de integração com Edge Functions e boa relação custo-benefício para o volume esperado.

📔 Notebooks & Tutoriais
Notebooks Explicativos
| Nome | Descrição | Link | |------|-----------|------| | developer_guide_improved.ipynb | Guia completo para novos desenvolvedores | Visualizar | | authentication_flow_pkce.ipynb | Explicação detalhada do fluxo PKCE | Visualizar | | mapbox_integration_tutorial.ipynb | Tutorial de integração com MapBox | Visualizar | | rls_policies_explained.ipynb | Explicação das políticas RLS | Visualizar |

Vídeos Tutoriais
Onboarding para Desenvolvedores: Assistir (20min)
Debug Avançado do Fluxo de Autenticação: Assistir (15min)
Criação de Geocercas com MapBox: Assistir (18min)
Deploy de Edge Functions no Supabase: Assistir (12min)
📜 Legislação Aplicável
LEI 15100/2025 - Aparelhos Eletrônicos em Escolas
Esta lei regula o uso de aparelhos eletrônicos em ambiente escolar e estabelece os seguintes pontos relevantes para nosso sistema:

Artigo 3: Define que o uso de dispositivos móveis pode ser autorizado para finalidades pedagógicas ou de segurança.
Artigo 5: Estabelece que aplicativos de localização devem:
Receber consentimento explícito dos alunos e responsáveis
Garantir que dados sejam processados seguindo a LGPD
Permitir desativar o rastreamento a qualquer momento
Artigo 8: Define que notificações devem ser claras e discretas, sem interromper atividades escolares.
Artigo 12: Estabelece multas e sanções para violações.
Impacto no Sistema: Nossa implementação garante conformidade através de:

Consentimento explícito no registro
Controles para ativar/desativar compartilhamento de localização
Notificações silenciosas e discretas
Criptografia e políticas RLS para proteção de dados
LGPD - Lei Geral de Proteção de Dados
Os principais aspectos da LGPD aplicáveis ao projeto:

Consentimento: Implementamos fluxos claros de consentimento para coleta de localização
Finalidade específica: Dados coletados apenas para finalidade de segurança
Minimização de dados: Coletamos apenas dados estritamente necessários
Direito ao esquecimento: Implementamos APIs para exclusão completa de dados
Segurança: Utilizamos criptografia e controles de acesso para proteger dados sensíveis
Documento de Conformidade: Ver documento completo

🌐 Referências Externas
Documentações Oficiais
Supabase Documentation
React Documentation
TypeScript Handbook
MapBox GL JS Documentation
Resend API Reference
TailwindCSS Documentation
Radix UI Documentation
Artigos e Tutoriais Recomendados
"Securing SPAs with PKCE"
"Building Realtime Applications with Supabase"
"Advanced Geofencing with MapBox"
"Row Level Security Best Practices"
"Optimizing React Applications"
"TypeScript Advanced Patterns"
Livros Recomendados
"TypeScript in 50 Lessons" - Stefan Baumgartner
"Real-World Next.js" - Michele Riva
"PostgreSQL 14 Administration Cookbook" - Simon Riggs
"Designing Data-Intensive Applications" - Martin Kleppmann
"Web Application Security" - Andrew Hoffman
📖 Glossário
| Termo | Definição | |-------|-----------| | PKCE | Proof Key for Code Exchange - Extensão do fluxo OAuth para aplicações públicas | | RLS | Row Level Security - Recurso do PostgreSQL para controle de acesso a nível de linha | | Edge Function | Função serverless executada em servidores próximos do usuário | | Geocerca | Perímetro virtual definido em mapa que gera alertas quando atravessado | | Realtime | Funcionalidade do Supabase para receber atualizações em tempo real via WebSockets | | Guardian | Perfil de usuário responsável que monitora estudantes | | Student | Perfil de usuário que compartilha sua localização | | Circuit Breaker | Padrão de design que previne falhas em cascata em sistemas distribuídos | | LGPD | Lei Geral de Proteção de Dados - Legislação brasileira sobre privacidade |

🔄 Evolução da Base de Conhecimento
Processos de Atualização
Revisão Trimestral: Revisão completa da base a cada 3 meses
Atualização Contínua: Adição de novas decisões técnicas ao serem tomadas
Arquivamento: Decisões obsoletas são movidas para um arquivo histórico
Contribuições
Para contribuir com esta base de conhecimento:

Crie uma branch docs/kb-update-[tópico]
Adicione ou atualize o conteúdo seguindo as convenções estabelecidas
Abra um PR com descrição detalhada das mudanças
Solicite revisão de pelo menos um membro do time técnico
🗃️ Anex
Cascade error
Deadline exceeded: Encountered retryable error from model provider: context deadline exceeded (Client.Timeout or context cancellation while reading body)
Thought Process

Knowledge Base and References - Locate-Family-Connect
🧠 Visão Geral
Esta base de conhecimento centraliza informações essenciais, decisões técnicas, referências e recursos para o projeto Locate-Family-Connect. Use este documento como ponto de partida para entender conceitos fundamentais, legislação aplicável e decisões arquiteturais que moldaram o sistema.

📚 Índice da Base de Conhecimento
Decisões Técnicas
Comparativos Tecnológicos
Notebooks & Tutoriais
Legislação Aplicável
Referências Externas
Glossário
🧩 Decisões Técnicas
Arquitetura Backend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | Adoção do Supabase como plataforma backend | Necessidade de autenticação integrada, banco de dados com RLS e Edge Functions em uma única plataforma | 2025-01-15 | Equipe de Arquitetura | | Implementação de PKCE para autenticação | Garantir segurança no fluxo de autenticação SPA sem depender de segredos no cliente | 2025-01-20 | Time de Segurança | | Uso de Row Level Security (RLS) para controle de acesso | Implementação de segurança a nível de dados para garantir isolamento entre perfis de usuários | 2025-02-05 | DBA + Arquiteto de Segurança | | Adoção de Edge Functions para processamento de emails | Melhorar performance e reduzir latência no envio de notificações | 2025-03-10 | Time de Backend |

Arquitetura Frontend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | React + TypeScript como stack principal | Type safety e melhor manutenabilidade a longo prazo | 2025-01-10 | Time Frontend | | Vite como bundler | Performance superior em desenvolvimento e builds mais rápidos | 2025-01-10 | Time Frontend | | TailwindCSS para estilização | Produtividade, consistência e baixo overhead de CSS | 2025-01-15 | Time Frontend + UX | | Radix UI para componentes acessíveis | Componentes headless com foco em acessibilidade e personalização | 2025-02-01 | Time Frontend + UX |

Padrões de Código
| Padrão | Descrição | Onde é Aplicado | |--------|-----------|----------------| | Repository Pattern | Abstração da camada de dados para facilitar testes e manutenção | /src/repositories/ | | Composition over Inheritance | Preferência por composição de hooks e componentes | Toda a base de código | | Context + Hooks para gerenciamento de estado | Estado global gerenciado via React Context API e hooks customizados | /src/contexts/, /src/hooks/ | | Fail-fast para validação | Validação antecipada de entradas para falhar o mais cedo possível | Funções Edge, componentes de formulário |

Métricas e Monitoramento
| Componente | Ferramenta | Limites de Alerta | Responsável | |------------|------------|-------------------|-------------| | API Performance | Supabase Logs | >500ms por request | DevOps | | Taxas de Erro | Sentry | >1% de taxa de erros | Time de Suporte | | Disponibilidade | UptimeRobot | <99.9% de uptime | DevOps | | Performance Frontend | Lighthouse | Score <90 | Time Frontend |

📊 Comparativos Tecnológicos
Plataformas Backend
| Critério | Supabase | Firebase | AWS Amplify | Solução Custom | |----------|---------|----------|-------------|---------------| | Custo Inicial | Médio | Baixo | Alto | Muito Alto | | Curva de Aprendizado | Média | Baixa | Alta | Alta | | Vendor Lock-in | Médio | Alto | Alto | Baixo | | PostgreSQL Nativo | ✅ | ❌ | ✅ | ✅ | | Row Level Security | ✅ | ❌ | ❌ | ✅ | | Edge Functions | ✅ | ✅ | ❌ | ⚠️ | | Open Source | ✅ | ❌ | ⚠️ | ✅ |

Decisão Final: Supabase foi escolhido devido ao suporte nativo a PostgreSQL com RLS, menor vendor lock-in comparado ao Firebase, e menor custo de desenvolvimento comparado a uma solução custom.

APIs de Mapas
| Critério | MapBox | Google Maps | OpenStreetMap | |----------|--------|------------|---------------| | Preço | Plano Free Generoso | Caro após volume | Gratuito | | Limites API | 50K carregamentos/mês | 28K carregamentos/mês | Sem limite técnico | | Opções de Personalização | Excelentes | Boas | Limitadas | | Qualidade de Mapas | Muito Boa | Excelente | Boa | | Documentação | Extensa | Extensa | Média | | Precisão de Geolocalização | Alta | Muito Alta | Média |

Decisão Final: MapBox foi escolhido pelo melhor equilíbrio entre custo (plano gratuito generoso) e recursos avançados de personalização necessários para geocercas.

Serviços de Email
| Critério | Resend | SendGrid | AWS SES | Mailgun | |----------|--------|----------|---------|---------| | Preço | Bom (1K emails grátis/mês) | Médio | Muito Bom | Médio | | Facilidade de Integração | Excelente | Boa | Média | Boa | | Taxas de Entrega | Muito Boa | Excelente | Boa | Muito Boa | | Suporte a Templates | ✅ | ✅ | ✅ | ✅ | | Analytics | Básico | Avançado | Básico | Avançado |

Decisão Final: Resend foi escolhido pela facilidade de integração com Edge Functions e boa relação custo-benefício para o volume esperado.

📔 Notebooks & Tutoriais
Notebooks Explicativos
| Nome | Descrição | Link | |------|-----------|------| | developer_guide_improved.ipynb | Guia completo para novos desenvolvedores | Visualizar | | authentication_flow_pkce.ipynb | Explicação detalhada do fluxo PKCE | Visualizar | | mapbox_integration_tutorial.ipynb | Tutorial de integração com MapBox | Visualizar | | rls_policies_explained.ipynb | Explicação das políticas RLS | Visualizar |

Vídeos Tutoriais
Onboarding para Desenvolvedores: Assistir (20min)
Debug Avançado do Fluxo de Autenticação: Assistir (15min)
Criação de Geocercas com MapBox: Assistir (18min)
Deploy de Edge Functions no Supabase: Assistir (12min)
📜 Legislação Aplicável
LEI 15100/2025 - Aparelhos Eletrônicos em Escolas
Esta lei regula o uso de aparelhos eletrônicos em ambiente escolar e estabelece os seguintes pontos relevantes para nosso sistema:

Artigo 3: Define que o uso de dispositivos móveis pode ser autorizado para finalidades pedagógicas ou de segurança.
Artigo 5: Estabelece que aplicativos de localização devem:
Receber consentimento explícito dos alunos e responsáveis
Garantir que dados sejam processados seguindo a LGPD
Permitir desativar o rastreamento a qualquer momento
Artigo 8: Define que notificações devem ser claras e discretas, sem interromper atividades escolares.
Artigo 12: Estabelece multas e sanções para violações.
Impacto no Sistema: Nossa implementação garante conformidade através de:

Consentimento explícito no registro
Controles para ativar/desativar compartilhamento de localização
Notificações silenciosas e discretas
Criptografia e políticas RLS para proteção de dados
LGPD - Lei Geral de Proteção de Dados
Os principais aspectos da LGPD aplicáveis ao projeto:

Consentimento: Implementamos fluxos claros de consentimento para coleta de localização
Finalidade específica: Dados coletados apenas para finalidade de segurança
Minimização de dados: Coletamos apenas dados estritamente necessários
Direito ao esquecimento: Implementamos APIs para exclusão completa de dados
Segurança: Utilizamos criptografia e controles de acesso para proteger dados sensíveis
Documento de Conformidade: Ver documento completo

🌐 Referências Externas
Documentações Oficiais
Supabase Documentation
React Documentation
TypeScript Handbook
MapBox GL JS Documentation
Resend API Reference
TailwindCSS Documentation
Radix UI Documentation
Artigos e Tutoriais Recomendados
"Securing SPAs with PKCE"
"Building Realtime Applications with Supabase"
"Advanced Geofencing with MapBox"
"Row Level Security Best Practices"
"Optimizing React Applications"
"TypeScript Advanced Patterns"
Livros Recomendados
"TypeScript in 50 Lessons" - Stefan Baumgartner
"Real-World Next.js" - Michele Riva
"PostgreSQL 14 Administration Cookbook" - Simon Riggs
"Designing Data-Intensive Applications" - Martin Kleppmann
"Web Application Security" - Andrew Hoffman
📖 Glossário
| Termo | Definição | |-------|-----------| | PKCE | Proof Key for Code Exchange - Extensão do fluxo OAuth para aplicações públicas | | RLS | Row Level Security - Recurso do PostgreSQL para controle de acesso a nível de linha | | Edge Function | Função serverless executada em servidores próximos do usuário | | Geocerca | Perímetro virtual definido em mapa que gera alertas quando atravessado | | Realtime | Funcionalidade do Supabase para receber atualizações em tempo real via WebSockets | | Guardian | Perfil de usuário responsável que monitora estudantes | | Student | Perfil de usuário que compartilha sua localização | | Circuit Breaker | Padrão de design que previne falhas em cascata em sistemas distribuídos | | LGPD | Lei Geral de Proteção de Dados - Legislação brasileira sobre privacidade |

🔄 Evolução da Base de Conhecimento
Processos de Atualização
Revisão Trimestral: Revisão completa da base a cada 3 meses
Atualização Contínua: Adição de novas decisões técnicas ao serem tomadas
Arquivamento: Decisões obsoletas são movidas para um arquivo histórico
Contribuições
Para contribuir com esta base de conhecimento:

Crie uma branch docs/kb-update-[tópico]
Adicione ou atualize o conteúdo seguindo as convenções estabelecidas
Abra um PR com descrição det
Cascade error
Deadline exceeded: Encountered retryable error from model provider: context deadline exceeded (Client.Timeout or context cancellation while reading body)
Thought Process



Knowledge Base and References - Locate-Family-Connect
🧠 Visão Geral
Esta base de conhecimento centraliza informações essenciais, decisões técnicas, referências e recursos para o projeto Locate-Family-Connect. Use este documento como ponto de partida para entender conceitos fundamentais, legislação aplicável e decisões arquiteturais que moldaram o sistema.

📚 Índice da Base de Conhecimento
Decisões Técnicas
Comparativos Tecnológicos
Notebooks & Tutoriais
Legislação Aplicável
Referências Externas
Glossário
🧩 Decisões Técnicas
Arquitetura Backend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | Adoção do Supabase como plataforma backend | Necessidade de autenticação integrada, banco de dados com RLS e Edge Functions em uma única plataforma | 2025-01-15 | Equipe de Arquitetura | | Implementação de PKCE para autenticação | Garantir segurança no fluxo de autenticação SPA sem depender de segredos no cliente | 2025-01-20 | Time de Segurança | | Uso de Row Level Security (RLS) para controle de acesso | Implementação de segurança a nível de dados para garantir isolamento entre perfis de usuários | 2025-02-05 | DBA + Arquiteto de Segurança | | Adoção de Edge Functions para processamento de emails | Melhorar performance e reduzir latência no envio de notificações | 2025-03-10 | Time de Backend |

Arquitetura Frontend
| Decisão | Justificativa | Data | Responsáveis | |---------|---------------|------|--------------| | React + TypeScript como stack principal | Type safety e melhor manutenabilidade a longo prazo | 2025-01-10 | Time Frontend | | Vite como bundler | Performance superior em desenvolvimento e builds mais rápidos | 2025-01-10 | Time Frontend | | TailwindCSS para estilização | Produtividade, consistência e baixo overhead de CSS | 2025-01-15 | Time Frontend + UX | | Radix UI para componentes acessíveis | Componentes headless com foco em acessibilidade e personalização | 2025-02-01 | Time Frontend + UX |

Padrões de Código
| Padrão | Descrição | Onde é Aplicado | |--------|-----------|----------------| | Repository Pattern | Abstração da camada de dados para facilitar testes e manutenção | /src/repositories/ | | Composition over Inheritance | Preferência por composição de hooks e componentes | Toda a base de código | | Context + Hooks para gerenciamento de estado | Estado global gerenciado via React Context API e hooks customizados | /src/contexts/, /src/hooks/ | | Fail-fast para validação | Validação antecipada de entradas para falhar o mais cedo possível | Funções Edge, componentes de formulário |

Métricas e Monitoramento
| Componente | Ferramenta | Limites de Alerta | Responsável | |------------|------------|-------------------|-------------| | API Performance | Supabase Logs | >500ms por request | DevOps | | Taxas de Erro | Sentry | >1% de taxa de erros | Time de Suporte | | Disponibilidade | UptimeRobot | <99.9% de uptime | DevOps | | Performance Frontend | Lighthouse | Score <90 | Time Frontend |

📊 Comparativos Tecnológicos
Plataformas Backend
| Critério | Supabase | Firebase | AWS Amplify | Solução Custom | |----------|---------|----------|-------------|---------------| | Custo Inicial | Médio | Baixo | Alto | Muito Alto | | Curva de Aprendizado | Média | Baixa | Alta | Alta | | Vendor Lock-in | Médio | Alto | Alto | Baixo | | PostgreSQL Nativo | ✅ | ❌ | ✅ | ✅ | | Row Level Security | ✅ | ❌ | ❌ | ✅ | | Edge Functions | ✅ | ✅ | ❌ | ⚠️ | | Open Source | ✅ | ❌ | ⚠️ | ✅ |

Decisão Final: Supabase foi escolhido devido ao suporte nativo a PostgreSQL com RLS, menor vendor lock-in comparado ao Firebase, e menor custo de desenvolvimento comparado a uma solução custom.

APIs de Mapas
| Critério | MapBox | Google Maps | OpenStreetMap | |----------|--------|------------|---------------| | Preço | Plano Free Generoso | Caro após volume | Gratuito | | Limites API | 50K carregamentos/mês | 28K carregamentos/mês | Sem limite técnico | | Opções de Personalização | Excelentes | Boas | Limitadas | | Qualidade de Mapas | Muito Boa | Excelente | Boa | | Documentação | Extensa | Extensa | Média | | Precisão de Geolocalização | Alta | Muito Alta | Média |

Decisão Final: MapBox foi escolhido pelo melhor equilíbrio entre custo (plano gratuito generoso) e recursos avançados de personalização necessários para geocercas.

Serviços de Email
| Critério | Resend | SendGrid | AWS SES | Mailgun | |----------|--------|----------|---------|---------| | Preço | Bom (1K emails grátis/mês) | Médio | Muito Bom | Médio | | Facilidade de Integração | Excelente | Boa | Média | Boa | | Taxas de Entrega | Muito Boa | Excelente | Boa | Muito Boa | | Suporte a Templates | ✅ | ✅ | ✅ | ✅ | | Analytics | Básico | Avançado | Básico | Avançado |

Decisão Final: Resend foi escolhido pela facilidade de integração com Edge Functions e boa relação custo-benefício para o volume esperado.

📔 Notebooks & Tutoriais
Notebooks Explicativos
| Nome | Descrição | Link | |------|-----------|------| | developer_guide_improved.ipynb | Guia completo para novos desenvolvedores | Visualizar | | authentication_flow_pkce.ipynb | Explicação detalhada do fluxo PKCE | Visualizar | | mapbox_integration_tutorial.ipynb | Tutorial de integração com MapBox | Visualizar | | rls_policies_explained.ipynb | Explicação das políticas RLS | Visualizar |

Vídeos Tutoriais
Onboarding para Desenvolvedores: Assistir (20min)
Debug Avançado do Fluxo de Autenticação: Assistir (15min)
Criação de Geocercas com MapBox: Assistir (18min)
Deploy de Edge Functions no Supabase: Assistir (12min)
📜 Legislação Aplicável
LEI 15100/2025 - Aparelhos Eletrônicos em Escolas
Esta lei regula o uso de aparelhos eletrônicos em ambiente escolar e estabelece os seguintes pontos relevantes para nosso sistema:

Artigo 3: Define que o uso de dispositivos móveis pode ser autorizado para finalidades pedagógicas ou de segurança.
Artigo 5: Estabelece que aplicativos de localização devem:
Receber consentimento explícito dos alunos e responsáveis
Garantir que dados sejam processados seguindo a LGPD
Permitir desativar o rastreamento a qualquer momento
Artigo 8: Define que notificações devem ser claras e discretas, sem interromper atividades escolares.
Artigo 12: Estabelece multas e sanções para violações.
Impacto no Sistema: Nossa implementação garante conformidade através de:

Consentimento explícito no registro
Controles para ativar/desativar compartilhamento de localização
Notificações silenciosas e discretas
Criptografia e políticas RLS para proteção de dados
LGPD - Lei Geral de Proteção de Dados
Os principais aspectos da LGPD aplicáveis ao projeto:

Consentimento: Implementamos fluxos claros de consentimento para coleta de localização
Finalidade específica: Dados coletados apenas para finalidade de segurança
Minimização de dados: Coletamos apenas dados estritamente necessários
Direito ao esquecimento: Implementamos APIs para exclusão completa de dados
Segurança: Utilizamos criptografia e controles de acesso para proteger dados sensíveis
Documento de Conformidade: Ver documento completo

🌐 Referências Externas
Documentações Oficiais
Supabase Documentation
React Documentation
TypeScript Handbook
MapBox GL JS Documentation
Resend API Reference
TailwindCSS Documentation
Radix UI Documentation
Artigos e Tutoriais Recomendados
"Securing SPAs with PKCE"