-- =====================================================
-- POLÍTICA MAIS PERMISSIVA PARA GUARDIANS
-- Permitir busca por email próprio mesmo sem registros
-- =====================================================

-- Remover políticas restritivas
DROP POLICY IF EXISTS "Guardians read policy" ON public.guardians;
DROP POLICY IF EXISTS "Guardians insert policy" ON public.guardians;
DROP POLICY IF EXISTS "Guardians update policy" ON public.guardians;
DROP POLICY IF EXISTS "Guardians delete policy" ON public.guardians;

-- Política READ mais permissiva: usuário pode ver registros onde ele é guardian OU por seu email
CREATE POLICY "Guardians read permissive" ON public.guardians 
  FOR SELECT TO authenticated 
  USING (
    auth.uid() = guardian_id OR 
    email = auth.jwt() ->> 'email' OR
    auth.role() = 'service_role'
  );

-- Política INSERT: usuário pode inserir registros onde ele é guardian
CREATE POLICY "Guardians insert permissive" ON public.guardians 
  FOR INSERT TO authenticated 
  WITH CHECK (
    auth.uid() = guardian_id OR 
    auth.role() = 'service_role'
  );

-- Política UPDATE: usuário pode atualizar seus próprios registros
CREATE POLICY "Guardians update permissive" ON public.guardians 
  FOR UPDATE TO authenticated 
  USING (
    auth.uid() = guardian_id OR 
    auth.role() = 'service_role'
  )
  WITH CHECK (
    auth.uid() = guardian_id OR 
    auth.role() = 'service_role'
  );

-- Política DELETE: usuário pode deletar seus próprios registros  
CREATE POLICY "Guardians delete permissive" ON public.guardians 
  FOR DELETE TO authenticated 
  USING (
    auth.uid() = guardian_id OR 
    auth.role() = 'service_role'
  );
