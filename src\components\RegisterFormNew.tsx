import React, { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { Calendar as CalendarIcon } from 'lucide-react';

export interface RegisterFormNewProps {
  userType: 'student' | 'parent';
  onLoginClick?: () => void;
  variant?: 'login' | 'register';
}

const RegisterFormNew: React.FC<RegisterFormNewProps> = ({ 
  userType,
  onLoginClick,
  variant = 'register' 
}) => {
  const { toast } = useToast();
  const [birthDate, setBirthDate] = useState<Date | undefined>(undefined);

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-center text-primary">
        NOVO FORMULÁRIO DE REGISTRO
      </h2>
      
      <div className="bg-yellow-100 p-4 rounded-md border border-yellow-300 text-yellow-800">
        <p className="font-medium">Formulário Temporário para Teste</p>
        <p className="text-sm">Este é um novo formulário para testar o sistema de roteamento.</p>
      </div>
      
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Nome Completo</Label>
          <Input 
            type="text"
            placeholder="Digite seu nome completo" 
            className="w-full"
          />
        </div>
        
        <div className="space-y-2">
          <Label>Data de Nascimento</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !birthDate && "text-gray-500"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {birthDate ? (
                  format(birthDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })
                ) : (
                  <span>Selecione sua data de nascimento</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={birthDate}
                onSelect={setBirthDate}
                disabled={(date) => date > new Date()}
                initialFocus
                locale={ptBR}
              />
            </PopoverContent>
          </Popover>
        </div>
        
        <Button 
          type="button" 
          className="w-full"
          onClick={() => {
            toast({
              title: "Formulário de teste",
              description: "Este é apenas um formulário de teste para verificar o roteamento.",
            });
          }}
        >
          Testar Formulário
        </Button>
        
        {onLoginClick && (
          <p className="text-sm text-center mt-4">
            Já tem uma conta?{" "}
            <button
              type="button"
              onClick={onLoginClick}
              className="text-primary hover:underline"
            >
              Faça login
            </button>
          </p>
        )}
      </div>
    </div>
  );
};

export default RegisterFormNew;
