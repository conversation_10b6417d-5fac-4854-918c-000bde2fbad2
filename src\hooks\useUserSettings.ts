import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useToast } from '@/hooks/use-toast';

export interface UserSettings {
  id?: string;
  user_id?: string;
  profile_visibility: boolean;
  auto_location_sharing: boolean;
  data_retention_days: number;
  location_history_enabled: boolean;
  font_size: 'small' | 'medium' | 'large' | 'extra-large';
  layout_style: 'default' | 'compact' | 'spacious';
  high_contrast: boolean;
  reduced_motion: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
  sms_notifications: boolean;
}

const DEFAULT_SETTINGS: UserSettings = {
  profile_visibility: true,
  auto_location_sharing: false,
  data_retention_days: 30,
  location_history_enabled: true,
  font_size: 'medium',
  layout_style: 'default',
  high_contrast: false,
  reduced_motion: false,
  email_notifications: true,
  push_notifications: true,
  sms_notifications: false,
};

export const useUserSettings = () => {
  const { user } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Query para buscar configurações
  const {
    data: settings,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['user-settings', user?.id],
    queryFn: async (): Promise<UserSettings> => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching user settings:', error);
        throw error;
      }

      // Se não existe configuração, retornar padrão
      if (!data) {
        return DEFAULT_SETTINGS;
      }

      return data as UserSettings;
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  // Mutation para atualizar configurações
  const updateSettingsMutation = useMutation({
    mutationFn: async (newSettings: Partial<UserSettings>): Promise<UserSettings> => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user.id,
          ...newSettings,
        })
        .select()
        .single();

      if (error) throw error;
      return data as UserSettings;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(['user-settings', user?.id], data);
      toast({
        title: 'Configurações salvas',
        description: 'Suas configurações foram atualizadas com sucesso.',
      });
    },
    onError: (error) => {
      console.error('Error updating settings:', error);
      toast({
        variant: 'destructive',
        title: 'Erro ao salvar',
        description: 'Não foi possível salvar suas configurações.',
      });
    },
  });

  // Função helper para atualizar uma configuração específica
  const updateSetting = (key: keyof UserSettings, value: any) => {
    if (!settings) return;
    
    const newSettings = { ...settings, [key]: value };
    updateSettingsMutation.mutate(newSettings);
  };

  return {
    settings: settings || DEFAULT_SETTINGS,
    isLoading,
    error,
    updateSetting,
    updateSettings: updateSettingsMutation.mutate,
    isUpdating: updateSettingsMutation.isPending,
  };
};