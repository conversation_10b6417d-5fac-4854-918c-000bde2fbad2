-- Fix save_student_location function overloading issue
-- This migration resolves PGRST203 error by ensuring only one version exists

-- 1. Drop ALL existing versions of save_student_location function
DROP FUNCTION IF EXISTS public.save_student_location(double precision, double precision, boolean);
DROP FUNCTION IF EXISTS public.save_student_location(double precision, double precision, boolean, numeric, text);
DROP FUNCTION IF EXISTS public.save_student_location(uuid, double precision, double precision, timestamptz, text, boolean);
DROP FUNCTION IF EXISTS public.save_student_location(double precision, double precision, boolean, numeric);

-- 2. Create the definitive version that matches our TypeScript code
CREATE OR REPLACE FUNCTION public.save_student_location(
  p_latitude DOUBLE PRECISION,
  p_longitude DOUBLE PRECISION,
  p_shared_with_guardians BOOLEAN DEFAULT true,
  p_accuracy NUMERIC DEFAULT NULL,
  p_address TEXT DEFAULT NULL
)
RETURNS TABLE(id UUID, success BOOLEAN, message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_location_id UUID;
  v_user_id UUID;
BEGIN
  -- Get authenticated user ID
  v_user_id := auth.uid();
  
  IF v_user_id IS NULL THEN
    RETURN QUERY SELECT NULL::UUID, FALSE, 'Usuário não autenticado'::TEXT;
    RETURN;
  END IF;
  
  -- Insert new location
  INSERT INTO public.locations (
    user_id,
    latitude,
    longitude,
    shared_with_guardians,
    accuracy,
    address,
    source,
    timestamp
  ) VALUES (
    v_user_id,
    p_latitude,
    p_longitude,
    p_shared_with_guardians,
    p_accuracy,
    p_address,
    'gps',
    NOW()
  ) RETURNING locations.id INTO v_location_id;
  
  -- Log the operation for debugging
  INSERT INTO public.auth_logs (
    event_type,
    user_id,
    metadata,
    occurred_at
  ) VALUES (
    'location_saved_via_rpc',
    v_user_id,
    jsonb_build_object(
      'location_id', v_location_id,
      'latitude', p_latitude,
      'longitude', p_longitude,
      'accuracy', p_accuracy,
      'shared_with_guardians', p_shared_with_guardians
    ),
    NOW()
  );
  
  RETURN QUERY SELECT 
    v_location_id,
    TRUE,
    'Localização salva com sucesso'::TEXT;
    
EXCEPTION
  WHEN OTHERS THEN
    -- Return error information
    RETURN QUERY SELECT 
      NULL::UUID,
      FALSE,
      ('Erro ao salvar localização: ' || SQLERRM)::TEXT;
END;
$$;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.save_student_location TO authenticated;

-- 4. Add comment for documentation
COMMENT ON FUNCTION public.save_student_location IS 
'Saves student location with enhanced error handling and logging. 
Returns TABLE with id, success boolean, and message.
Parameters: latitude, longitude, shared_with_guardians (default true), accuracy (optional), address (optional)';

-- 5. Verify the function exists and is unique
DO $$
DECLARE
  func_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO func_count
  FROM pg_proc p
  JOIN pg_namespace n ON p.pronamespace = n.oid
  WHERE n.nspname = 'public' 
    AND p.proname = 'save_student_location';
    
  IF func_count != 1 THEN
    RAISE EXCEPTION 'Expected exactly 1 save_student_location function, found %', func_count;
  END IF;
  
  RAISE NOTICE 'Successfully created unique save_student_location function';
END;
$$;
