
import { supabase } from '@/integrations/supabase/client';

interface GuardianCheck {
  student_id: string;
  guardian_email: string;
  relationship_exists: boolean;
  student_name?: string;
}

/**
 * Check guardian relationships and data consistency
 */
async function checkGuardianRelationships(): Promise<GuardianCheck[]> {
  try {
    console.log('🔍 Checking guardian relationships...');

    // Get all student-guardian relationships
    const { data: relationships, error: relationshipsError } = await supabase
      .from('student_guardian_relationships')
      .select(`
        id,
        student_id,
        guardian_id,
        relationship_type,
        is_primary,
        created_at
      `);

    if (relationshipsError) {
      console.error('Error fetching relationships:', relationshipsError);
      return [];
    }

    console.log(`Found ${relationships?.length || 0} relationships`);

    const checks: GuardianCheck[] = [];

    if (relationships) {
      for (const rel of relationships) {
        // Get student info
        const { data: studentProfile } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('user_id', rel.student_id)
          .single();

        // Get guardian info
        const { data: guardianProfile } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('user_id', rel.guardian_id)
          .single();

        checks.push({
          student_id: rel.student_id,
          guardian_email: guardianProfile?.email || 'Unknown',
          relationship_exists: true,
          student_name: studentProfile?.full_name || 'Unknown'
        });
      }
    }

    return checks;
  } catch (error) {
    console.error('Error in checkGuardianRelationships:', error);
    return [];
  }
}

/**
 * Test RPC functions
 */
async function testRPCFunctions() {
  try {
    console.log('🧪 Testing RPC functions...');

    // Test get_guardian_students
    const { data: guardianStudents, error: guardianError } = await supabase
      .rpc('get_guardian_students');

    if (guardianError) {
      console.warn('get_guardian_students RPC error:', guardianError);
    } else {
      console.log(`✅ get_guardian_students returned ${guardianStudents?.length || 0} results`);
    }

    // Test get_student_guardians_secure (requires student ID)
    console.log('Note: get_student_guardians_secure requires authentication and student ID');

  } catch (error) {
    console.error('Error testing RPC functions:', error);
  }
}

/**
 * Main check function
 */
async function runGuardianChecks() {
  console.log('🚀 Starting guardian relationship checks...');
  
  const checks = await checkGuardianRelationships();
  
  console.log('\n📊 Summary:');
  console.log(`Total relationships found: ${checks.length}`);
  
  if (checks.length > 0) {
    console.log('\n📋 Relationships:');
    checks.forEach((check, index) => {
      console.log(`${index + 1}. Student: ${check.student_name} | Guardian: ${check.guardian_email}`);
    });
  }
  
  await testRPCFunctions();
  
  console.log('\n✅ Guardian checks completed!');
}

// Export for potential use in other scripts
export { checkGuardianRelationships, testRPCFunctions };

// Run if this script is executed directly
if (typeof window === 'undefined') {
  runGuardianChecks().catch(console.error);
}

