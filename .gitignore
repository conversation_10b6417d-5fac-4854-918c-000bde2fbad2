# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
out/
.next/
.vercel/


# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity


# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Supabase
.supabase/
supabase/.branches
supabase/.temp

# Database dumps
*.sql.gz
*.dump

# Backup files
*.bak
*.backup
*~

# Local Netlify folder
.netlify

# Cypress
cypress/downloads/
cypress/screenshots/
cypress/videos/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Local development
.local

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# Git files (in case of nested repos)
.git/

# Lockfiles (keep only one)
# yarn.lock
# package-lock.json

# Local history
.history/

# JetBrains
*.iml
.idea/

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Archives
*.zip
*.tar.gz
*.rar

# Temporary files
*.tmp
*.temp

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# Local Supabase config (secrets)
supabase/config.toml

# TypeScript incremental compilation cache
*.tsbuildinfo

# Optional stylelint cache
.stylelintcache

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Sentry
.sentryclirc

# Serverless
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Local development certificates
*.pem

# Windows shortcuts
*.lnk

# ==========================================
# PROJECT-SPECIFIC PATTERNS
# ==========================================

# Personal documentation and analysis files (EXCLUDING AGENTS files)
# Note: AGENTS*.md files are kept in repository as they contain important project documentation
ANALISE_*.md
SOLUCAO_*.md
SNAPSHOT_*.md
CYPRESS_*.md
SUPABASE_*.md
DIAGNOSTICO_*.md
diagnostico.md
MIGRATIONS.md
TASKS_COMPLETED.md
windsurf-rules*.md

# Test and debug files
test-*.js
test-*.mjs
test-*.html
test-*.ts
teste-*.js
teste-*.mjs
deploy-*.js
create-test-user.js
check-database.js
verificar-conexao.js
conexao-supabase.mjs
listar-banco-dados.mjs
supabase-bridge.mjs
share-location-fixed.ts
temp-button-doc.tsx
atualizar-token.js

# Personal configuration files
.windsurfrules
.roomodes
.gitmessage
.codex-setup.sh
blackbox_mcp_settings.json
.mcp.json

# Batch and script files
*.bat
iniciar-*.bat
start-*.bat

# Log files and temporary outputs
*-test-log.txt
*-log.txt
diagnostico_saida.txt
lista-arquivos.txt
ssh-key-generation-instructions.txt
context*.json

# Python scripts and outputs
*.py
diagnostico_react.py
userinput.py

# PowerShell scripts
*.ps1
send_email.ps1

# Additional IDE folders
.cursor/
.junie/
.roo/
.windsurf/

# Temporary component files
temp-*.tsx
temp-*.ts
