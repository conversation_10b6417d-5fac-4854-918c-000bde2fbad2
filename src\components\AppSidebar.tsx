
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next"; // Added
import { useUser } from "@/contexts/UnifiedAuthContext";
import { 
  Sidebar, 
  SidebarHeader, 
  SidebarContent, 
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import Logo from "@/components/Logo";
import { Home, Map, Users, Book, User, LifeBuoy } from "lucide-react";

export function AppSidebar() {
  const location = useLocation();
  const { user } = useUser();
  const { t } = useTranslation(); // Added
  const userType = user?.user_metadata?.user_type || 'student';
  
  // Função para verificar se um link está ativo
  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };
  
  const getDashboardLink = () => {
    switch(userType) {
      case 'parent':
        return '/parent-dashboard';
      case 'student':
        return '/student-dashboard';
      default:
        return '/dashboard';
    }
  };

  const getHelpLink = () => {
    switch(userType) {
      case 'parent':
        return '/ajuda/responsavel';
      case 'student':
        return '/ajuda/estudante';
      default:
        return '/ajuda/estudante';
    }
  };

  const dashboardLink = getDashboardLink();
  const helpLink = getHelpLink();

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="p-2 flex items-center">
          <Logo />
          <span className="ml-2 text-xl font-bold">EduConnect</span>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={isActive(dashboardLink)} tooltip={t('common.dashboard')}>
              <Link to={dashboardLink}>
                <Home />
                <span>{t('common.dashboard')}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          
          {userType === 'student' && (
            <>
              <SidebarMenuItem>
                <SidebarMenuButton asChild isActive={isActive('/student-map')} tooltip={t('navigation.myMap')}>
                  <Link to="/student-map">
                    <Map />
                    <span>{t('navigation.myMap')}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild isActive={isActive('/guardians')} tooltip={t('navigation.myGuardians')}>
                  <Link to="/guardians">
                    <Users />
                    <span>{t('navigation.myGuardians')}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </>
          )}
          
          {userType === 'parent' && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={isActive('/student-map')} tooltip={t('navigation.studentMap')}>
                <Link to="/student-map">
                  <Map />
                  <span>{t('navigation.studentMap')}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}
          
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={isActive('/api-docs')} tooltip={t('navigation.apiDocs')}>
              <Link to="/api-docs">
                <Book />
                <span>{t('navigation.apiDocs')}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={isActive(helpLink)} tooltip={t('common.help')}>
              <Link to={helpLink}>
                <LifeBuoy />
                <span>{t('common.help')}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={isActive('/profile')} tooltip={t('common.profile')}>
              <Link to="/profile">
                <User />
                <span>{t('common.profile')}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>
      
      <SidebarFooter>
        {/* Footer content */}
      </SidebarFooter>
    </Sidebar>
  );
}

export default AppSidebar;
