# 📋 Documento Técnico: Pendências no Formulário de Registro Aprimorado

**Data:** 4 de junho de 2025  
**Projeto:** Locate-Family-Connect  
**Status:** Em desenvolvimento  

## 🔍 Contexto

Durante o desenvolvimento do formulário de registro aprimorado, identificamos que a implementação inicial apresentou erros de renderização. Embora o problema principal de hooks inválidos no `UnifiedAuthContext` tenha sido resolvido, ainda existem pendências para a implementação completa dos requisitos de validação e UX da tela de cadastro.

## ✅ Requisitos a Implementar

### 1. **Data de nascimento obrigatória**
   - Campo com seletor de calendário implementado parcialmente
   - Pendente: validação para impedir menores de idade criando conta de guardião
   - Componente: `<CalendarPopover>` integrado com React Hook Form

### 2. **Confirmação explícita do tipo de conta**
   - Solicitação explícita sobre o tipo de conta (guardião/estudante)
   - Checkbox de confirmação para evitar erros de cadastro
   - Mensagens explicativas sobre as diferenças entre os tipos

### 3. **Validação de senha em tempo real**
   - Exibir feedback visual sobre força da senha
   - Alertar sobre requisitos:
     - Mínimo 8 caracteres
     - Pelo menos uma letra maiúscula
     - Pelo menos um número
     - Pelo menos um caractere especial
   - Componente `<PasswordField>` já modificado para aceitar esta funcionalidade

### 4. **Confirmação por e-mail após cadastro**
   - Notificação visual informando envio do e-mail de confirmação
   - Texto explicativo sobre necessidade de verificar o e-mail
   - Redirecionamento para página apropriada após cadastro

## 🚨 Problemas Técnicos Atuais

1. **Erro de renderização no formulário aprimorado:**
   - O erro "Invalid hook call" no `UnifiedAuthContext` foi corrigido
   - Problema com a função `debounce` estava definida após ser usada
   - Solução: movemos a função para o topo do arquivo

2. **Conflito de cliente Supabase:**
   - Warning: "Multiple GoTrueClient instances detected"
   - Potencial conflito entre `AuthContext` e `UnifiedAuthContext`
   - Não crítico, mas potencial fonte de comportamento indefinido

3. **Problema com hot-reload no Vite:**
   - Alterações no `RegisterForm.tsx` não refletidas imediatamente
   - Necessário limpar cache ou reiniciar servidor

4. **Validação do campo de data:**
   - Integração parcial do componente de calendário
   - Pendente validação por idade baseada no tipo de conta

## 💡 Estratégia Break-Safe para Implementação

Seguindo o protocolo "Break-Safe Philosophy", recomendamos:

1. **Verificar primeiro:** confirmar funcionamento do formulário básico antes de adicionar campos
2. **Etapas pequenas:** implementar um requisito de cada vez, testando a cada mudança
3. **Compatibilidade durante transições:** manter o formulário original funcional enquanto desenvolvemos
4. **Reversível sempre:** commits atômicos para cada feature, permitindo rollback fácil

## 📚 Verificação prévia

Checklist para próxima sessão de trabalho:
- [ ] Verificar se o build completa sem erros
- [ ] Confirmar que o formulário básico está renderizando corretamente
- [ ] Validar se o AuthContext está funcionando sem erros
- [ ] Testar outras rotas da aplicação para garantir que não foram afetadas

## 🧩 Sugestão de Ordem de Implementação

1. Integrar `RegisterFormEnhanced` no `AuthContainer`
2. Implementar validação de data de nascimento
3. Adicionar confirmação explícita de tipo de conta
4. Implementar feedback visual para força de senha
5. Adicionar notificação de confirmação por e-mail

---

**Problema resolvido hoje:** Corrigimos o erro "Invalid hook call" movendo a função `debounce` para o topo do arquivo `UnifiedAuthContext.tsx`, permitindo o funcionamento correto do provedor de autenticação.
