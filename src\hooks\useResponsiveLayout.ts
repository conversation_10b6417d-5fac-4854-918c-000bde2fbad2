
import { useDevice } from './useDevice';

export const useResponsiveLayout = () => {
  const device = useDevice();

  // Enhanced text sizes with landscape optimization
  const textSizes = {
    title: device.type === 'mobile' && device.orientation === 'landscape' ? 'text-base' :
           device.size === 'xxs' ? 'text-lg' : 
           device.size === 'xs' ? 'text-xl' : 'text-2xl',
    subtitle: device.type === 'mobile' && device.orientation === 'landscape' ? 'text-sm' :
              device.size === 'xxs' ? 'text-base' : 
              device.size === 'xs' ? 'text-lg' : 'text-xl',
    body: device.type === 'mobile' && device.orientation === 'landscape' ? 'text-xs' :
          device.size === 'xxs' ? 'text-sm' : 'text-base',
    small: device.type === 'mobile' && device.orientation === 'landscape' ? 'text-xs' :
           device.size === 'xxs' ? 'text-xs' : 'text-sm'
  };

  // Button styles with landscape considerations
  const buttonStyles = {
    size: (device.type === 'mobile' && device.orientation === 'landscape') || device.size === 'xxs' ? 'sm' : 'default',
    iconSize: (device.type === 'mobile' && device.orientation === 'landscape') || device.size === 'xxs' ? 'h-3 w-3' : 'h-4 w-4',
    padding: (device.type === 'mobile' && device.orientation === 'landscape') || device.size === 'xxs' ? 'px-2 py-1' : 'px-4 py-2'
  };

  // Spacing with landscape optimization
  const spacing = {
    padding: device.type === 'mobile' && device.orientation === 'landscape' ? 'px-1' :
             device.size === 'xxs' ? 'px-2' : 
             device.size === 'xs' ? 'px-3' : 'px-4',
    margin: device.type === 'mobile' && device.orientation === 'landscape' ? 'mb-1' :
            device.size === 'xxs' ? 'mb-2' :
            device.size === 'xs' ? 'mb-3' : 'mb-4'
  };

  // Navigation height based on device
  const getNavigationHeight = () => {
    if (device.orientation === 'portrait') {
      switch (device.size) {
        case 'xxs':
          return 'h-12';
        case 'xs':
          return 'h-14';
        case 'sm':
          return 'h-16';
        default:
          return 'h-16';
      }
    } else {
      // Landscape - more compact
      switch (device.size) {
        case 'xxs':
        case 'xs':
          return 'h-10';
        case 'sm':
          return 'h-12';
        default:
          return 'h-12';
      }
    }
  };

  // Container classes based on device
  const getContainerClasses = () => {
    const basePadding = device.size === 'xxs' ? 'px-2' : 
                       device.size === 'xs' ? 'px-3' : 'px-4';
    const safeArea = device.hasNotch ? 'pt-safe-area-top' : '';
    
    return `${basePadding} ${safeArea}`.trim();
  };

  return {
    device,
    textSizes,
    buttonStyles,
    spacing,
    getNavigationHeight,
    getContainerClasses
  };
};
