#!/usr/bin/env node

const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (prompt) => new Promise((resolve) => rl.question(prompt, resolve));

async function setupEnvironment() {
  console.log('🔧 Configuração de variáveis de ambiente para monitoramento\n');
  
  const envPath = '.env';
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }
  
  const addOrUpdateEnv = (key, value) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    const newLine = `${key}=${value}`;
    
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, newLine);
    } else {
      envContent += `\n${newLine}`;
    }
  };
  
  console.log('📝 Configure as seguintes variáveis (pressione Enter para pular):');
  
  const sentryDsn = await question('Sentry DSN: ');
  if (sentryDsn) addOrUpdateEnv('VITE_SENTRY_DSN', sentryDsn);
  
  const posthogKey = await question('PostHog Project Key: ');
  if (posthogKey) addOrUpdateEnv('VITE_POSTHOG_KEY', posthogKey);
  
  const posthogHost = await question('PostHog Host (padrão: https://app.posthog.com): ');
  addOrUpdateEnv('VITE_POSTHOG_HOST', posthogHost || 'https://app.posthog.com');
  
  const redisPassword = await question('Redis Password (padrão: educonnect123): ');
  addOrUpdateEnv('REDIS_PASSWORD', redisPassword || 'educonnect123');
  addOrUpdateEnv('REDIS_HOST', 'localhost');
  addOrUpdateEnv('REDIS_PORT', '6379');
  addOrUpdateEnv('REDIS_DB', '0');
  
  fs.writeFileSync(envPath, envContent.trim() + '\n');
  
  console.log('\n✅ Arquivo .env atualizado com sucesso!');
  console.log('\n📋 Próximos passos:');
  console.log('1. Execute: npm run redis:start');
  console.log('2. Execute: npm run dev');
  console.log('3. Execute: npm run test:e2e:chrome');
  
  rl.close();
}

setupEnvironment().catch(console.error); 