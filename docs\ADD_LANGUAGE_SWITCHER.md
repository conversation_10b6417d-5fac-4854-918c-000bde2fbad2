# 🔤 Adicionando um Seletor de Idioma (PT / EN)

Este guia mostra o passo-a-passo para incluir um botão/dropdown que permite ao usuário alternar o idioma da interface usando **react-i18next**.

## 1. Pré-requisitos

```bash
npm install i18next react-i18next i18next-browser-languagedetector --save
```

Garanta que o arquivo `src/i18n.ts` esteja configurado (já incluído no projeto).

## 2. Arquivos de tradução

```
src/i18n/en-GB.json
src/i18n/pt-BR.json
```
Adicione/atualize as chaves que deseja exibir.

## 3. Exemplo de Botão Dropdown

```tsx
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';

export const LanguageSwitcher = () => {
  const { i18n } = useTranslation();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="capitalize">
          {i18n.language === 'en' ? 'English' : 'Português'}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-32">
        <DropdownMenuItem onClick={() => i18n.changeLanguage('pt')}>
          Português
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => i18n.changeLanguage('en')}>
          English
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
```

## 4. Onde Colocar

1. **Header global** (`src/components/AppHeader.tsx`) – posição recomendada.  
2. **Menu lateral** em apps mobile / PWA.  
3. **Página de configurações** caso prefira algo menos visível.

## 5. Persistência da Escolha

O `i18next-browser-languagedetector` já armazena a língua selecionada em `localStorage` (configuração `caches: ['localStorage']`). Nada extra é necessário.

## 6. Boas práticas

- Mostre o idioma no seu próprio idioma (“English”, “Português”).
- Mantenha o botão sempre acessível para fácil troca.
- Traduza também emails e mensagens de sistema se o `country_code` do usuário não for `BR`.

Pronto! O usuário agora pode alternar entre PT e EN sem recarregar a página. 