import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Languages } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface LanguageOption {
  code: string;
  label: string;
  flag?: string;
}

interface LanguageSwitcherProps {
  variant?: 'default' | 'ghost' | 'outline';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  options?: LanguageOption[];
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'ghost',
  size = 'sm',
  className = '',
  options
}) => {
  const { i18n } = useTranslation();
  const navigate = useNavigate();

  const languages: LanguageOption[] = options ?? [
    { code: 'pt-BR', label: '🇧🇷 Português', flag: '🇧🇷' },
    { code: 'en-GB', label: '🇬🇧 English', flag: '🇬🇧' }
  ];

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const handleLanguageChange = (languageCode: string) => {
    // Persist selected language using both keys to avoid detector overrides
    localStorage.setItem('lang', languageCode);
    localStorage.setItem('i18nextLng', languageCode);
    i18n.changeLanguage(languageCode);
    navigate('/');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant={variant} 
          size={size} 
          className={cn('flex items-center gap-2', className)}
        >
          {size === 'icon' ? (
            <Languages className="h-4 w-4" />
          ) : (
            <>
              <span className="text-sm">{currentLanguage.flag}</span>
              <span className="hidden sm:inline-block capitalize">
                {currentLanguage.label}
              </span>
              <Languages className="h-3 w-3 sm:hidden" />
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-36 z-50">
        {languages.map((language) => (
          <DropdownMenuItem 
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={cn(
              'flex items-center gap-2 cursor-pointer',
              i18n.language === language.code && 'bg-accent'
            )}
          >
            <span>{language.flag}</span>
            <span>{language.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
