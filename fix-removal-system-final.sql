-- =============================================
-- SCRIPT: Sistema de Solicitações de Remoção - VERSÃO FINAL
-- =============================================
-- ESTE SCRIPT REMOVE TUDO E RECRIA COMPLETAMENTE
-- Execute no Supabase: Database > SQL Editor

-- 1. DROPAR TODAS AS DEPENDÊNCIAS EM ORDEM
-- Dropar políticas RLS primeiro
DROP POLICY IF EXISTS "Students can view their own removal requests" ON public.removal_requests;
DROP POLICY IF EXISTS "Guardians can view requests directed to them" ON public.removal_requests;
DROP POLICY IF EXISTS "Students can create removal requests" ON public.removal_requests;
DROP POLICY IF EXISTS "Guardians can update their requests status" ON public.removal_requests;

-- Dropar todas as funções relacionadas
DROP FUNCTION IF EXISTS public.get_guardian_removal_requests();
DROP FUNCTION IF EXISTS public.get_removal_request_by_token(UUID);
DROP FUNCTION IF EXISTS public.process_removal_request(UUID, TEXT);
DROP FUNCTION IF EXISTS public.request_guardian_removal(UUID, TEXT);

-- Dropar a tabela completamente
DROP TABLE IF EXISTS public.removal_requests CASCADE;

-- 2. RECRIAR TABELA DO ZERO
CREATE TABLE public.removal_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    guardian_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    student_name TEXT NOT NULL,
    guardian_name TEXT NOT NULL,
    guardian_email TEXT NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
    request_token UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES auth.users(id)
);

-- 3. CRIAR ÍNDICES PARA PERFORMANCE
CREATE INDEX idx_removal_requests_student_id ON public.removal_requests(student_id);
CREATE INDEX idx_removal_requests_guardian_id ON public.removal_requests(guardian_id);
CREATE INDEX idx_removal_requests_status ON public.removal_requests(status);
CREATE INDEX idx_removal_requests_token ON public.removal_requests(request_token);
CREATE INDEX idx_removal_requests_expires ON public.removal_requests(expires_at);

-- 4. HABILITAR RLS
ALTER TABLE public.removal_requests ENABLE ROW LEVEL SECURITY;

-- 5. CRIAR POLÍTICAS RLS
CREATE POLICY "Students can view their own removal requests" ON public.removal_requests
    FOR SELECT USING (auth.uid() = student_id);

CREATE POLICY "Guardians can view requests directed to them" ON public.removal_requests
    FOR SELECT USING (auth.uid() = guardian_id);

CREATE POLICY "Students can create removal requests" ON public.removal_requests
    FOR INSERT WITH CHECK (auth.uid() = student_id);

CREATE POLICY "Guardians can update their requests status" ON public.removal_requests
    FOR UPDATE USING (auth.uid() = guardian_id);

-- 6. FUNÇÃO: Criar solicitação de remoção
CREATE OR REPLACE FUNCTION public.request_guardian_removal(
    p_guardian_id UUID,
    p_reason TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_student_id UUID;
    v_student_name TEXT;
    v_guardian_name TEXT;
    v_guardian_email TEXT;
    v_request_id UUID;
    v_token UUID;
BEGIN
    -- Obter ID do estudante atual
    v_student_id := auth.uid();
    
    IF v_student_id IS NULL THEN
        RETURN json_build_object('success', false, 'message', 'Usuário não autenticado');
    END IF;
    
    -- Verificar se a relação estudante-responsável existe
    IF NOT EXISTS (
        SELECT 1 FROM public.student_guardian_relationships 
        WHERE student_id = v_student_id AND guardian_id = p_guardian_id
    ) THEN
        RETURN json_build_object('success', false, 'message', 'Relação não encontrada');
    END IF;
    
    -- Verificar se já existe uma solicitação pendente
    IF EXISTS (
        SELECT 1 FROM public.removal_requests 
        WHERE student_id = v_student_id 
        AND guardian_id = p_guardian_id 
        AND status = 'pending'
        AND expires_at > NOW()
    ) THEN
        RETURN json_build_object('success', false, 'message', 'Já existe uma solicitação pendente');
    END IF;
    
    -- Obter nomes e email do estudante
    SELECT COALESCE(raw_user_meta_data->>'full_name', email) INTO v_student_name
    FROM auth.users 
    WHERE id = v_student_id;
    
    -- Obter nomes e email do responsável
    SELECT COALESCE(raw_user_meta_data->>'full_name', email), email INTO v_guardian_name, v_guardian_email
    FROM auth.users 
    WHERE id = p_guardian_id;
    
    -- Criar a solicitação
    INSERT INTO public.removal_requests (
        student_id,
        guardian_id,
        student_name,
        guardian_name,
        guardian_email,
        reason
    ) VALUES (
        v_student_id,
        p_guardian_id,
        COALESCE(v_student_name, 'Estudante'),
        COALESCE(v_guardian_name, 'Responsável'),
        v_guardian_email,
        p_reason
    ) RETURNING id, request_token INTO v_request_id, v_token;
    
    RETURN json_build_object(
        'success', true, 
        'message', 'Solicitação criada com sucesso',
        'request_id', v_request_id,
        'token', v_token
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false, 
            'message', 'Erro interno: ' || SQLERRM
        );
END;
$$;

-- 7. FUNÇÃO: Obter solicitações do responsável
CREATE OR REPLACE FUNCTION public.get_guardian_removal_requests()
RETURNS TABLE (
    id UUID,
    student_id UUID,
    student_name TEXT,
    student_email TEXT,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    request_token UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guardian_id UUID;
BEGIN
    v_guardian_id := auth.uid();
    
    IF v_guardian_id IS NULL THEN
        RAISE EXCEPTION 'Usuário não autenticado';
    END IF;
    
    RETURN QUERY
    SELECT 
        rr.id,
        rr.student_id,
        rr.student_name,
        u.email as student_email,
        rr.reason,
        rr.created_at,
        rr.expires_at,
        rr.request_token
    FROM public.removal_requests rr
    JOIN auth.users u ON u.id = rr.student_id
    WHERE rr.guardian_id = v_guardian_id
    AND rr.status = 'pending'
    AND rr.expires_at > NOW()
    ORDER BY rr.created_at DESC;
END;
$$;

-- 8. FUNÇÃO: Obter solicitação por token (para email)
CREATE OR REPLACE FUNCTION public.get_removal_request_by_token(p_token UUID)
RETURNS TABLE (
    id UUID,
    student_name TEXT,
    student_email TEXT,
    guardian_name TEXT,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    request_token UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rr.id,
        rr.student_name,
        u.email as student_email,
        rr.guardian_name,
        rr.reason,
        rr.created_at,
        rr.expires_at,
        rr.request_token
    FROM public.removal_requests rr
    JOIN auth.users u ON u.id = rr.student_id
    WHERE rr.request_token = p_token
    AND rr.status = 'pending'
    AND rr.expires_at > NOW();
END;
$$;

-- 9. FUNÇÃO: Processar solicitação (aprovar/rejeitar)
CREATE OR REPLACE FUNCTION public.process_removal_request(
    p_token UUID,
    p_action TEXT -- 'approve' ou 'reject'
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_request RECORD;
    v_guardian_id UUID;
    v_rows_deleted INTEGER;
BEGIN
    v_guardian_id := auth.uid();
    
    -- Buscar a solicitação
    SELECT * INTO v_request
    FROM public.removal_requests
    WHERE request_token = p_token
    AND status = 'pending'
    AND expires_at > NOW();
    
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'message', 'Solicitação não encontrada ou expirada');
    END IF;
    
    -- Verificar se o usuário é o responsável correto (apenas se autenticado)
    IF v_guardian_id IS NOT NULL AND v_guardian_id != v_request.guardian_id THEN
        RETURN json_build_object('success', false, 'message', 'Usuário não autorizado');
    END IF;
    
    -- Processar a ação
    IF p_action = 'approve' THEN
        -- Primeiro, remover a relação estudante-responsável
        DELETE FROM public.student_guardian_relationships
        WHERE student_id = v_request.student_id 
        AND guardian_id = v_request.guardian_id;
        
        GET DIAGNOSTICS v_rows_deleted = ROW_COUNT;
        
        -- Atualizar status da solicitação
        UPDATE public.removal_requests
        SET status = 'approved',
            processed_at = NOW(),
            processed_by = v_guardian_id
        WHERE request_token = p_token;
        
        RETURN json_build_object(
            'success', true, 
            'message', 'Remoção aprovada com sucesso. Relação removida.',
            'relationships_removed', v_rows_deleted
        );
        
    ELSIF p_action = 'reject' THEN
        -- Atualizar status da solicitação
        UPDATE public.removal_requests
        SET status = 'rejected',
            processed_at = NOW(),
            processed_by = v_guardian_id
        WHERE request_token = p_token;
        
        RETURN json_build_object('success', true, 'message', 'Solicitação rejeitada com sucesso');
        
    ELSE
        RETURN json_build_object('success', false, 'message', 'Ação inválida. Use "approve" ou "reject"');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false, 
            'message', 'Erro interno: ' || SQLERRM
        );
END;
$$;

-- 10. CONCEDER PERMISSÕES
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.removal_requests TO authenticated;
GRANT EXECUTE ON FUNCTION public.request_guardian_removal(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_guardian_removal_requests() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_removal_request_by_token(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.process_removal_request(UUID, TEXT) TO authenticated;

-- 11. TESTE DE VERIFICAÇÃO COMPLETA
SELECT 
    'VERIFICAÇÃO FINAL' as status,
    'Tabela removal_requests criada' as tabela,
    EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'removal_requests') as existe;

SELECT 
    'Função request_guardian_removal' as funcao,
    EXISTS (SELECT 1 FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = 'public' AND p.proname = 'request_guardian_removal') as existe;

SELECT 
    'Função get_guardian_removal_requests' as funcao,
    EXISTS (SELECT 1 FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = 'public' AND p.proname = 'get_guardian_removal_requests') as existe;

SELECT 
    'Função get_removal_request_by_token' as funcao,
    EXISTS (SELECT 1 FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = 'public' AND p.proname = 'get_removal_request_by_token') as existe;

SELECT 
    'Função process_removal_request' as funcao,
    EXISTS (SELECT 1 FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = 'public' AND p.proname = 'process_removal_request') as existe;

-- 12. CONTAGEM FINAL
SELECT COUNT(*) as total_removal_requests FROM public.removal_requests;

-- 13. TESTAR UMA FUNÇÃO (opcional)
-- SELECT public.request_guardian_removal('00000000-0000-0000-0000-000000000000'::UUID, 'Teste de funcionalidade'); 