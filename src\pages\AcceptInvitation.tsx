
import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, CheckCircle, XCircle, UserPlus } from 'lucide-react';

interface InvitationData {
  id: string;
  student_name: string;
  guardian_email: string;
  expires_at: string;
  status: string;
}

const AcceptInvitation: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setError('Token de convite não fornecido');
      setIsLoading(false);
      return;
    }

    fetchInvitation();
  }, [token]);

  const fetchInvitation = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('family_invitations')
        .select('id, student_name, guardian_email, expires_at, status')
        .eq('invitation_token', token)
        .single();

      if (error) {
        console.error('Error fetching invitation:', error);
        setError('Convite não encontrado ou inválido');
        return;
      }

      if (data.status !== 'pending') {
        setError('Este convite já foi processado ou expirou');
        return;
      }

      if (new Date(data.expires_at) < new Date()) {
        setError('Este convite expirou');
        return;
      }

      setInvitation(data);
    } catch (error: any) {
      console.error('Error:', error);
      setError('Erro ao carregar convite');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptInvitation = async () => {
    if (!invitation || !token) return;

    try {
      setIsProcessing(true);

      // Call the RPC function to accept the invitation
      const { data, error } = await supabase.rpc('accept_family_invitation', {
        p_invitation_token: token
      });

      if (error) {
        console.error('Error accepting invitation:', error);
        throw new Error(error.message || 'Erro ao aceitar convite');
      }

      if (data && data.length > 0 && data[0].success) {
        toast({
          title: 'Convite aceito com sucesso!',
          description: `Você agora está vinculado(a) como responsável de ${invitation.student_name}`,
        });

        // Redirect to dashboard after success
        setTimeout(() => {
          navigate('/parent-dashboard');
        }, 2000);
      } else {
        throw new Error(data?.[0]?.message || 'Falha ao aceitar convite');
      }

    } catch (error: any) {
      console.error('Error accepting invitation:', error);
      toast({
        title: 'Erro ao aceitar convite',
        description: error.message || 'Tente novamente mais tarde',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRejectInvitation = async () => {
    if (!invitation || !token) return;

    try {
      setIsProcessing(true);

      const { error } = await supabase
        .from('family_invitations')
        .update({ status: 'rejected' })
        .eq('invitation_token', token);

      if (error) {
        throw new Error(error.message);
      }

      toast({
        title: 'Convite rejeitado',
        description: 'O convite foi rejeitado com sucesso',
      });

      setTimeout(() => {
        navigate('/');
      }, 2000);

    } catch (error: any) {
      console.error('Error rejecting invitation:', error);
      toast({
        title: 'Erro ao rejeitar convite',
        description: error.message || 'Tente novamente mais tarde',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-6">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Carregando convite...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-2" />
            <CardTitle>Convite Inválido</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => navigate('/')} 
              className="w-full"
            >
              Voltar ao Início
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!invitation) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-2" />
            <CardTitle>Convite não encontrado</CardTitle>
            <CardDescription>O convite solicitado não foi encontrado</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => navigate('/')} 
              className="w-full"
            >
              Voltar ao Início
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <UserPlus className="h-12 w-12 text-blue-500 mx-auto mb-2" />
          <CardTitle>Convite Familiar</CardTitle>
          <CardDescription>
            Você foi convidado(a) para se tornar responsável
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">Detalhes do Convite</h3>
            <p className="text-blue-700">
              <strong>Estudante:</strong> {invitation.student_name}
            </p>
            <p className="text-blue-700 text-sm mt-1">
              <strong>Expira em:</strong> {new Date(invitation.expires_at).toLocaleDateString('pt-BR')}
            </p>
          </div>

          <div className="space-y-2">
            <Button 
              onClick={handleAcceptInvitation}
              disabled={isProcessing}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Processando...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Aceitar Convite
                </>
              )}
            </Button>

            <Button 
              onClick={handleRejectInvitation}
              disabled={isProcessing}
              variant="outline"
              className="w-full"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Rejeitar Convite
            </Button>
          </div>

          <p className="text-xs text-gray-500 text-center">
            Ao aceitar, você poderá acompanhar a localização do estudante quando compartilhada.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default AcceptInvitation;
