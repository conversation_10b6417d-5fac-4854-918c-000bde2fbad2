# Native Features Test Plan

This document outlines the testing procedures for the native features implemented in the Monitore MVP app. The goal is to ensure that all native features work correctly on both iOS and Android devices.

## Prerequisites

- iOS device running iOS 14 or later
- Android device running Android 10 or later
- Capacitor CLI installed (`npm install -g @capacitor/cli`)
- Xcode installed (for iOS testing)
- Android Studio installed (for Android testing)

## Build and Deploy

### iOS

1. Build the web app:
   ```bash
   npm run build
   ```

2. Sync the web build with the iOS project:
   ```bash
   npx cap sync ios
   ```

3. Open the iOS project in Xcode:
   ```bash
   npx cap open ios
   ```

4. Connect your iOS device and select it as the build target
5. Build and run the app on your device

### Android

1. Build the web app:
   ```bash
   npm run build
   ```

2. Sync the web build with the Android project:
   ```bash
   npx cap sync android
   ```

3. Open the Android project in Android Studio:
   ```bash
   npx cap open android
   ```

4. Connect your Android device and select it as the build target
5. Build and run the app on your device

## Test Cases

### 1. Native Gestures and Haptic Feedback

#### 1.1 Map Zoom Gestures

- **Test:** Pinch to zoom in and out on the map
- **Expected:** Smooth zoom with haptic feedback
- **iOS:** Should feel like native Apple Maps
- **Android:** Should feel like native Google Maps

#### 1.2 Map Pan Gestures

- **Test:** Drag to pan the map
- **Expected:** Smooth panning with slight haptic feedback on start/stop
- **iOS:** Should have subtle haptic feedback
- **Android:** Should have more pronounced haptic feedback

#### 1.3 Map Button Interactions

- **Test:** Tap the "Ampliar" button
- **Expected:** Map zooms in with medium haptic feedback
- **Test:** Tap the "Histórico" button
- **Expected:** Map shows history with medium haptic feedback
- **Test:** Tap the "Atualizar" button
- **Expected:** Location updates with success haptic feedback

### 2. Platform-Specific Styling

#### 2.1 iOS Styling

- **Test:** Open the app on an iOS device
- **Expected:** UI elements should follow iOS design patterns:
  - Rounded corners on buttons
  - iOS-style shadows
  - Native-looking bottom sheet
  - iOS-style notifications

#### 2.2 Android Styling

- **Test:** Open the app on an Android device
- **Expected:** UI elements should follow Material Design patterns:
  - Slightly squared corners on buttons
  - Material Design shadows
  - Native-looking bottom sheet
  - Android-style notifications

### 3. Offline Capabilities

#### 3.1 Map Caching

- **Test:** Enable airplane mode after using the app with internet
- **Expected:** Map should still be visible with cached tiles

#### 3.2 Location Caching

- **Test:** Share location with internet, then enable airplane mode
- **Expected:** Previously shared locations should still be visible

#### 3.3 Offline Indicator

- **Test:** Enable airplane mode
- **Expected:** Offline indicator should appear
- **Test:** Disable airplane mode
- **Expected:** Offline indicator should disappear

#### 3.4 Download Map Region

- **Test:** Tap the download button to save the current map region
- **Expected:** Progress indicator should appear, and success indicator when complete
- **Test:** Enable airplane mode after downloading
- **Expected:** Downloaded region should be fully navigable offline

### 4. Native Notifications

#### 4.1 Local Notifications

- **Test:** Share location
- **Expected:** Native notification should appear on the device
- **iOS:** Should look like an iOS notification
- **Android:** Should look like an Android notification

#### 4.2 Offline Mode Notifications

- **Test:** Enable airplane mode
- **Expected:** Offline mode notification should appear
- **Test:** Disable airplane mode
- **Expected:** Online mode notification should appear

#### 4.3 Notification Actions

- **Test:** Tap on a location notification
- **Expected:** App should focus on the location
- **Test:** Dismiss a notification
- **Expected:** Notification should disappear

#### 4.4 In-App Notifications

- **Test:** Perform actions that trigger notifications (zoom, update location)
- **Expected:** Native-like in-app notifications should appear
- **iOS:** Should look like iOS in-app notifications
- **Android:** Should look like Android in-app notifications

### 5. Bottom Sheet

#### 5.1 Bottom Sheet Gestures

- **Test:** Tap on a location marker
- **Expected:** Bottom sheet should appear with location details
- **Test:** Drag the bottom sheet up
- **Expected:** Sheet should expand smoothly with haptic feedback
- **Test:** Drag the bottom sheet down
- **Expected:** Sheet should collapse or dismiss with haptic feedback

#### 5.2 Bottom Sheet Content

- **Test:** Open bottom sheet for a location
- **Expected:** Should show location details, timestamp, and address
- **iOS:** Should have iOS-style sheet appearance
- **Android:** Should have Material Design sheet appearance

## Bug Reporting

When reporting bugs, please include:

1. Device model
2. OS version
3. Steps to reproduce
4. Expected behavior
5. Actual behavior
6. Screenshots or videos if possible

## Performance Metrics

During testing, pay attention to:

1. App startup time
2. Map loading time
3. Smoothness of animations
4. Battery usage
5. Memory usage

## Conclusion

This test plan covers the main native features implemented in the Monitore MVP app. By following these test cases, we can ensure that the app provides a native-like experience on both iOS and Android devices.

Remember to test on multiple device sizes and OS versions if possible to ensure broad compatibility.