
export const isServer = typeof window === 'undefined';

export const env = {
  MODE: import.meta.env.MODE || 'development',
  VITE_API_URL: import.meta.env.VITE_API_URL,
  VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
  VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY,
  VITE_RESEND_API_KEY: import.meta.env.VITE_RESEND_API_KEY,
  VITE_RESEND_FROM_EMAIL: import.meta.env.VITE_RESEND_FROM_EMAIL,
  VITE_IPINFO_TOKEN: import.meta.env.VITE_IPINFO_TOKEN,
  
  // CORREÇÃO CRÍTICA: Usar VITE_MAPBOX_ACCESS_TOKEN que existe no Supabase
  VITE_MAPBOX_TOKEN: import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || import.meta.env.VITE_MAPBOX_TOKEN,
  
  VITE_LOCATION_TARGET_ACCURACY: import.meta.env.VITE_LOCATION_TARGET_ACCURACY,
  VITE_LOCATION_MAX_ATTEMPTS: import.meta.env.VITE_LOCATION_MAX_ATTEMPTS,
  VITE_LOCATION_ENABLE_HYBRID: import.meta.env.VITE_LOCATION_ENABLE_HYBRID,
  VITE_SENTRY_DSN: import.meta.env.VITE_SENTRY_DSN,
  VITE_POSTHOG_KEY: import.meta.env.VITE_POSTHOG_KEY,
  VITE_POSTHOG_HOST: import.meta.env.VITE_POSTHOG_HOST,
  
  // Server-side only
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  PORT: parseInt(import.meta.env.PORT || '3000'),
  API_URL: import.meta.env.API_URL,
  SUPABASE_URL: import.meta.env.SUPABASE_URL,
  SUPABASE_ANON_KEY: import.meta.env.SUPABASE_ANON_KEY,
  RESEND_API_KEY: import.meta.env.RESEND_API_KEY,
  RESEND_FROM_EMAIL: import.meta.env.RESEND_FROM_EMAIL,
  IPINFO_TOKEN: import.meta.env.IPINFO_TOKEN,
  
  // CORREÇÃO: Usar token correto do Mapbox
  MAPBOX_TOKEN: import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || import.meta.env.MAPBOX_TOKEN,
  
  LOCATION_TARGET_ACCURACY: import.meta.env.LOCATION_TARGET_ACCURACY,
  LOCATION_MAX_ATTEMPTS: import.meta.env.LOCATION_MAX_ATTEMPTS,
  LOCATION_ENABLE_HYBRID: import.meta.env.LOCATION_ENABLE_HYBRID,
  REDIS_HOST: import.meta.env.REDIS_HOST,
  REDIS_PORT: import.meta.env.REDIS_PORT,
  REDIS_PASSWORD: import.meta.env.REDIS_PASSWORD,
  REDIS_DB: import.meta.env.REDIS_DB,
  REDIS_URL: import.meta.env.REDIS_URL,
  APP_DOMAIN: import.meta.env.APP_DOMAIN,
  
  // CORREÇÃO: Computed values usando token correto
  MAPBOX_TOKEN_FALLBACK: import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoidGVjaC1lZHUtbGFiIiwiYSI6ImNtN3cxaTFzNzAwdWwyanMxeHJkb3RrZjAifQ.h0g6a56viW7evC7P0c5mwQ',
  IPINFO_TOKEN_FALLBACK: import.meta.env.VITE_IPINFO_TOKEN || undefined,
  LOCATION_TARGET_ACCURACY_NUM: parseInt(import.meta.env.VITE_LOCATION_TARGET_ACCURACY || '10'),
  LOCATION_MAX_ATTEMPTS_NUM: parseInt(import.meta.env.VITE_LOCATION_MAX_ATTEMPTS || '3'),
  LOCATION_ENABLE_HYBRID_BOOL: import.meta.env.VITE_LOCATION_ENABLE_HYBRID !== 'false',
  
  isClient: !isServer,
  isServer,
} as const;

export function isMapboxTokenValid(): boolean {
  const token = env.MAPBOX_TOKEN_FALLBACK;
  const isValid = !!(token && token.startsWith('pk.') && token.length > 20);
  
  if (import.meta.env.DEV) {
    console.log('[env] Validando token Mapbox:', {
      hasToken: !!token,
      startsWithPk: token?.startsWith('pk.'),
      length: token?.length,
      isValid,
      tokenPreview: token ? `${token.substring(0, 15)}...` : 'undefined'
    });
  }
  
  return isValid;
}
