# Expo Setup Guide

This guide summarizes how to start a new Expo project using the existing Supabase backend.

## 1. Create the project

```bash
npx create-expo-app MinhaApp
cd MinhaApp
```

## 2. Install dependencies

```bash
npx expo install react-native-url-polyfill @react-native-async-storage/async-storage
npm install @supabase/supabase-js
```

## 3. Environment variables

Create a `.env` file and define the Supabase details:

```env
EXPO_PUBLIC_SUPABASE_URL=<your-supabase-url>
EXPO_PUBLIC_SUPABASE_KEY=<your-publishable-key>
```

Use the publishable key instead of the anon key when building mobile apps.

## 4. Supabase client

```ts
import 'react-native-url-polyfill/auto'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { createClient, processLock } from '@supabase/supabase-js'

export const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL!,
  process.env.EXPO_PUBLIC_SUPABASE_KEY!,
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
      lock: processLock,
    },
  }
)
```

## 5. Run the app

```bash
npx expo start
```

Your Expo app will now communicate with the existing Supabase backend.
