import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

/**
 * LanguageSelector component allowing users to choose locale.
 * Selection is stored in localStorage and cookie for persistence.
 */
const LanguageSelector: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { i18n } = useTranslation();
  const [locale, setLocale] = React.useState(
    () => localStorage.getItem('i18nextLng') || i18n.language
  );

  const handleChange = (value: string) => {
    setLocale(value);
    localStorage.setItem('i18nextLng', value);
    document.cookie = `NEXT_LOCALE=${value};path=/;max-age=31536000`;
    i18n.changeLanguage(value);
  };

  return (
    <Select onValueChange={handleChange} value={locale} defaultValue={locale}>
      <SelectTrigger className={cn('w-auto', className)}>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="pt-BR">🇧🇷 Português (Brasil)</SelectItem>
        <SelectItem value="en-GB">🇬🇧 English (UK)</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default LanguageSelector;
