
#!/bin/bash

# 🛡️ Setup Anti-Break Protocol - Configuração Automática
# Protocolo Anti-Quebra - Setup Completo

echo "🛡️  CONFIGURANDO PROTOCOLO ANTI-QUEBRA"
echo "======================================"
echo "📅 Data: $(date)"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log_step() {
    local status=$1
    local message=$2
    
    if [ "$status" = "SUCCESS" ]; then
        echo -e "${GREEN}✅${NC} $message"
    elif [ "$status" = "ERROR" ]; then
        echo -e "${RED}❌${NC} $message"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}ℹ️${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️${NC} $message"
    fi
}

# 1. Verificar dependências
echo ""
echo "🔍 Verificando dependências..."

if ! command -v git &> /dev/null; then
    log_step "ERROR" "Git não encontrado - instale o Git primeiro"
    exit 1
fi

if ! command -v node &> /dev/null; then
    log_step "ERROR" "Node.js não encontrado - instale o Node.js primeiro"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    log_step "ERROR" "npm não encontrado - instale o npm primeiro"
    exit 1
fi

log_step "SUCCESS" "Todas as dependências estão disponíveis"

# 2. Verificar se estamos em um repositório Git
echo ""
echo "📋 Verificando repositório Git..."

if ! git rev-parse --git-dir > /dev/null 2>&1; then
    log_step "ERROR" "Não é um repositório Git - execute 'git init' primeiro"
    exit 1
fi

log_step "SUCCESS" "Repositório Git válido encontrado"

# 3. Criar diretório de hooks se não existir
echo ""
echo "📁 Configurando hooks do Git..."

HOOKS_DIR=".git/hooks"
if [ ! -d "$HOOKS_DIR" ]; then
    mkdir -p "$HOOKS_DIR"
    log_step "SUCCESS" "Diretório de hooks criado"
else
    log_step "INFO" "Diretório de hooks já existe"
fi

# 4. Criar hook pre-commit
PRE_COMMIT_HOOK="$HOOKS_DIR/pre-commit"
cat > "$PRE_COMMIT_HOOK" << 'EOF'
#!/bin/bash

# 🛡️ Pre-commit Hook - Protocolo Anti-Quebra
# Executa verificações obrigatórias antes do commit

echo "🛡️ [PRE-COMMIT] Executando verificações de segurança..."

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m'

# Executar health check
echo "🔍 Executando health check..."
if ! ./scripts/health-check.sh; then
    echo -e "${RED}❌ Health check falhou - commit bloqueado!${NC}"
    echo "💡 Corrija os problemas antes de fazer commit"
    exit 1
fi

# Executar testes críticos
echo "🧪 Executando testes críticos..."
if ! ./scripts/critical-test.sh; then
    echo -e "${RED}❌ Testes críticos falharam - commit bloqueado!${NC}"
    echo "💡 Corrija os problemas antes de fazer commit"
    exit 1
fi

echo -e "${GREEN}✅ Todas as verificações passaram - commit autorizado!${NC}"
exit 0
EOF

chmod +x "$PRE_COMMIT_HOOK"
log_step "SUCCESS" "Hook pre-commit configurado"

# 5. Criar hook pre-push
PRE_PUSH_HOOK="$HOOKS_DIR/pre-push"
cat > "$PRE_PUSH_HOOK" << 'EOF'
#!/bin/bash

# 🛡️ Pre-push Hook - Protocolo Anti-Quebra
# Executa verificações críticas antes do push

echo "🛡️ [PRE-PUSH] Executando verificações críticas antes do push..."

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m'

# Executar build completo
echo "🏗️ Verificando build..."
if ! npm run build; then
    echo -e "${RED}❌ Build falhou - push bloqueado!${NC}"
    echo "💡 Corrija os erros de build antes de fazer push"
    exit 1
fi

# Executar testes críticos
echo "🧪 Executando suite de testes críticos..."
if ! ./scripts/critical-test.sh; then
    echo -e "${RED}❌ Testes críticos falharam - push bloqueado!${NC}"
    echo "💡 Corrija os problemas antes de fazer push"
    exit 1
fi

# Verificar performance
echo "📊 Verificando métricas de performance..."
if ! ./scripts/performance-baseline.sh; then
    echo -e "${YELLOW}⚠️ Problemas de performance detectados${NC}"
    echo "💡 Considere otimizar antes do push"
    # Não bloquear por performance, apenas avisar
fi

echo -e "${GREEN}✅ Todas as verificações críticas passaram - push autorizado!${NC}"
exit 0
EOF

chmod +x "$PRE_PUSH_HOOK"
log_step "SUCCESS" "Hook pre-push configurado"

# 6. Tornar scripts executáveis
echo ""
echo "🔧 Configurando permissões dos scripts..."

SCRIPTS=(
    "scripts/health-check.sh"
    "scripts/critical-test.sh"
    "scripts/performance-baseline.sh"
)

for script in "${SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        chmod +x "$script"
        log_step "SUCCESS" "Script $script configurado como executável"
    else
        log_step "WARN" "Script $script não encontrado"
    fi
done

# 7. Criar arquivo de configuração do protocolo
echo ""
echo "📝 Criando configuração do protocolo..."

CONFIG_FILE=".anti-break-config"
cat > "$CONFIG_FILE" << EOF
# 🛡️ Configuração do Protocolo Anti-Quebra
# Gerado automaticamente em: $(date)

# Status do protocolo
PROTOCOL_ENABLED=true
PROTOCOL_VERSION=1.0

# Configurações de verificação
HEALTH_CHECK_ENABLED=true
CRITICAL_TESTS_ENABLED=true
PERFORMANCE_MONITORING_ENABLED=true

# Configurações de Git hooks
PRE_COMMIT_HOOK_ENABLED=true
PRE_PUSH_HOOK_ENABLED=true

# Configurações de alertas
BUNDLE_SIZE_LIMIT_MB=10
BUILD_TIME_LIMIT_SECONDS=60
DEPENDENCIES_LIMIT=100

# Última atualização
LAST_UPDATED=$(date -Iseconds)
EOF

log_step "SUCCESS" "Arquivo de configuração criado: $CONFIG_FILE"

# 8. Executar teste inicial
echo ""
echo "🧪 Executando teste inicial do protocolo..."

echo "🔍 Testando health check..."
if ./scripts/health-check.sh; then
    log_step "SUCCESS" "Health check funcionando"
else
    log_step "WARN" "Health check com problemas - verifique manualmente"
fi

echo "🧪 Testando testes críticos..."
if ./scripts/critical-test.sh; then
    log_step "SUCCESS" "Testes críticos funcionando"
else
    log_step "WARN" "Testes críticos com problemas - verifique manualmente"
fi

echo "📊 Testando métricas de performance..."
if ./scripts/performance-baseline.sh; then
    log_step "SUCCESS" "Coleta de métricas funcionando"
else
    log_step "WARN" "Coleta de métricas com problemas - verifique manualmente"
fi

# 9. Resumo final
echo ""
echo "🎉 PROTOCOLO ANTI-QUEBRA CONFIGURADO COM SUCESSO!"
echo "================================================="
echo ""
echo "✅ Componentes instalados:"
echo "   🛡️  Git hooks (pre-commit, pre-push)"
echo "   📊 Scripts de verificação"
echo "   📝 Configuração do protocolo"
echo ""
echo "🚀 Como usar:"
echo "   • Os hooks serão executados automaticamente"
echo "   • Execute manualmente: npm run health-check"
echo "   • Execute manualmente: npm run critical-test"
echo "   • Execute manualmente: npm run performance-baseline"
echo ""
echo "📋 Comandos disponíveis:"
echo "   npm run safety-check    # Health check + testes críticos"
echo "   npm run pre-deploy      # Verificação completa antes do deploy"
echo ""
echo -e "${GREEN}🛡️ SEU PROJETO ESTÁ PROTEGIDO CONTRA QUEBRAS!${NC}"
echo ""
echo "📚 Documentação completa em: docs/ANTI_BREAK_PROTOCOL.md"
