
-- Verificação segura para corrigir campos problemáticos no schema auth
-- Usando verificação condicional para evitar erros com colunas inexistentes

DO $$
BEGIN
    -- Verificar e corrigir campo reauthentication_token se existir
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = 'auth' AND table_name = 'users' AND column_name = 'reauthentication_token') THEN
        UPDATE auth.users 
        SET reauthentication_token = '' 
        WHERE reauthentication_token IS NULL;
        
        ALTER TABLE auth.users 
        ALTER COLUMN reauthentication_token SET DEFAULT '';
    END IF;

    -- Verificar e corrigir campo recovery_token se existir
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = 'auth' AND table_name = 'users' AND column_name = 'recovery_token') THEN
        UPDATE auth.users 
        SET recovery_token = '' 
        WHERE recovery_token IS NULL;
        
        ALTER TABLE auth.users 
        ALTER COLUMN recovery_token SET DEFAULT '';
    END IF;

    -- Verificar e corrigir campo confirmation_token se existir
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = 'auth' AND table_name = 'users' AND column_name = 'confirmation_token') THEN
        UPDATE auth.users 
        SET confirmation_token = '' 
        WHERE confirmation_token IS NULL;
        
        ALTER TABLE auth.users 
        ALTER COLUMN confirmation_token SET DEFAULT '';
    END IF;

    -- Limpar campos de telefone inconsistentes se existirem
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = 'auth' AND table_name = 'users' AND column_name = 'phone') THEN
        UPDATE auth.users 
        SET phone = NULL 
        WHERE phone = '';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = 'auth' AND table_name = 'users' AND column_name = 'phone_confirmed_at') THEN
        UPDATE auth.users 
        SET phone_confirmed_at = NULL 
        WHERE phone_confirmed_at IS NOT NULL AND (phone IS NULL OR phone = '');
    END IF;
END $$;

-- Registrar esta correção segura
INSERT INTO public.auth_logs (
    event_type,
    metadata,
    occurred_at
) VALUES (
    'auth_schema_safe_fix',
    jsonb_build_object(
        'description', 'Correção segura do schema auth.users - verificação condicional de colunas',
        'timestamp', NOW(),
        'issue', 'Safe correction of auth token fields',
        'solution', 'Conditional updates based on column existence'
    ),
    NOW()
);
