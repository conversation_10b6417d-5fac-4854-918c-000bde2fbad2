
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

const EmailConfirmationPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(true);
  const [confirmationStatus, setConfirmationStatus] = useState<'loading' | 'success' | 'error' | 'password_setup'>('loading');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSettingPassword, setIsSettingPassword] = useState(false);

  // Extrair parâmetros da URL
  const token = searchParams.get('token');
  const type = searchParams.get('type');

  useEffect(() => {
    if (!token || !type) {
      setConfirmationStatus('error');
      setIsLoading(false);
      return;
    }

    handleEmailConfirmation();
  }, [token, type]);

  const handleEmailConfirmation = async () => {
    try {
      console.log('[EMAIL_CONFIRMATION] Processing confirmation:', { type, hasToken: !!token });

      if (type === 'signup') {
        // Confirmação de email normal
        const { error } = await supabase.auth.verifyOtp({
          token_hash: token!,
          type: 'email'
        });

        if (error) {
          console.error('[EMAIL_CONFIRMATION] Verification error:', error);
          setConfirmationStatus('error');
          toast({
            title: 'Erro na confirmação',
            description: error.message,
            variant: 'destructive'
          });
        } else {
          setConfirmationStatus('success');
          toast({
            title: 'Email confirmado!',
            description: 'Sua conta foi ativada com sucesso.'
          });

          // Redirecionar após 2 segundos
          setTimeout(() => {
            navigate('/login');
          }, 2000);
        }
      } else if (type === 'recovery') {
        // Ativação de conta criada por responsável - definir senha
        setConfirmationStatus('password_setup');
      }
    } catch (error: any) {
      console.error('[EMAIL_CONFIRMATION] Error:', error);
      setConfirmationStatus('error');
      toast({
        title: 'Erro na confirmação',
        description: 'Ocorreu um erro ao confirmar seu email.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordSetup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      toast({
        title: 'Senhas não coincidem',
        description: 'Por favor, confirme sua senha corretamente.',
        variant: 'destructive'
      });
      return;
    }

    if (password.length < 8) {
      toast({
        title: 'Senha muito curta',
        description: 'A senha deve ter pelo menos 8 caracteres.',
        variant: 'destructive'
      });
      return;
    }

    setIsSettingPassword(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        console.error('[EMAIL_CONFIRMATION] Password setup error:', error);
        throw error;
      }

      // Atualizar status do perfil para ativo
      const { data: user } = await supabase.auth.getUser();
      if (user.user) {
        await supabase
          .from('profiles')
          .update({ 
            status: 'active',
            registration_status: 'completed'
          })
          .eq('user_id', user.user.id);
      }

      toast({
        title: 'Conta ativada!',
        description: 'Sua senha foi definida e sua conta está ativa.'
      });

      setConfirmationStatus('success');

      // Redirecionar para dashboard do estudante
      setTimeout(() => {
        navigate('/student-dashboard');
      }, 2000);

    } catch (error: any) {
      console.error('[EMAIL_CONFIRMATION] Password setup error:', error);
      toast({
        title: 'Erro ao definir senha',
        description: error.message || 'Erro ao definir sua senha.',
        variant: 'destructive'
      });
    } finally {
      setIsSettingPassword(false);
    }
  };

  const renderContent = () => {
    switch (confirmationStatus) {
      case 'loading':
        return (
          <div className="text-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Confirmando seu email...</p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Email confirmado!</h3>
            <p className="text-gray-600 mb-4">Sua conta foi ativada com sucesso.</p>
            <p className="text-sm text-gray-500">Redirecionando...</p>
          </div>
        );

      case 'error':
        return (
          <div className="text-center py-8">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Erro na confirmação</h3>
            <p className="text-gray-600 mb-4">
              Não foi possível confirmar seu email. O link pode ter expirado.
            </p>
            <Button onClick={() => navigate('/login')} variant="outline">
              Voltar ao Login
            </Button>
          </div>
        );

      case 'password_setup':
        return (
          <div className="py-8">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold mb-2">Defina sua senha</h3>
              <p className="text-gray-600">
                Sua conta foi criada! Defina uma senha para acessar o sistema.
              </p>
            </div>

            <form onSubmit={handlePasswordSetup} className="space-y-4">
              <div>
                <Label htmlFor="password">Nova senha</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Digite sua nova senha"
                  required
                  minLength={8}
                />
              </div>

              <div>
                <Label htmlFor="confirmPassword">Confirmar senha</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirme sua nova senha"
                  required
                  minLength={8}
                />
              </div>

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isSettingPassword}
              >
                {isSettingPassword ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Definindo senha...
                  </>
                ) : (
                  'Ativar conta'
                )}
              </Button>
            </form>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Confirmação de Email</CardTitle>
          <CardDescription>
            Processando a confirmação do seu email
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailConfirmationPage;

