
import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { MapPin, Shield, AlertTriangle, Users } from 'lucide-react';

interface LocationSharingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  guardianCount: number;
  studentName?: string;
}

const LocationSharingModal: React.FC<LocationSharingModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  guardianCount,
  studentName
}) => {
  const [acknowledged, setAcknowledged] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);

  const handleConfirm = () => {
    if (dontShowAgain) {
      localStorage.setItem('lgpd-location-consent', 'true');
    }
    onConfirm();
    onClose();
    setAcknowledged(false);
    setDontShowAgain(false);
  };

  const handleClose = () => {
    onClose();
    setAcknowledged(false);
    setDontShowAgain(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-blue-600" />
            Compartilhar Localização
          </DialogTitle>
          <DialogDescription>
            Antes de compartilhar sua localização, leia as informações importantes sobre privacidade.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>Dados Sensíveis:</strong> Sua localização GPS é considerada um dado pessoal sensível pela LGPD.
            </AlertDescription>
          </Alert>

          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <Users className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <p><strong>Será compartilhado com:</strong></p>
                <p className="text-gray-600">
                  {guardianCount} responsável(is) autorizado(s)
                  {studentName && ` de ${studentName}`}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
              <div>
                <p><strong>Informações compartilhadas:</strong></p>
                <ul className="text-gray-600 list-disc list-inside ml-2 mt-1">
                  <li>Coordenadas GPS exatas</li>
                  <li>Data e hora do compartilhamento</li>
                  <li>Endereço aproximado</li>
                </ul>
              </div>
            </div>

            <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
              <p className="text-xs">
                <strong>Base Legal:</strong> Consentimento específico para compartilhamento de localização (Art. 7º, I da LGPD).
                Você pode revogar este consentimento a qualquer momento nas configurações de privacidade.
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="acknowledge-sharing"
                checked={acknowledged}
                onCheckedChange={(checked) => setAcknowledged(!!checked)}
              />
              <label 
                htmlFor="acknowledge-sharing" 
                className="text-sm cursor-pointer flex-1"
              >
                Entendo que minha localização será compartilhada e consinto com o tratamento deste dado sensível.
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="dont-show-again"
                checked={dontShowAgain}
                onCheckedChange={(checked) => setDontShowAgain(!!checked)}
              />
              <label 
                htmlFor="dont-show-again" 
                className="text-sm cursor-pointer flex-1 text-gray-600"
              >
                Não mostrar este aviso novamente
              </label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancelar
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={!acknowledged}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Compartilhar Localização
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LocationSharingModal;
