-- ==========================================
-- VERIFICAR ESTRUTURAS REAIS DAS TABELAS
-- Data: 24/06/2025
-- Objetivo: Descobrir estrutura sem assumir colunas
-- ==========================================

-- 1. CONTAGEM SIMPLES (SEM ERROS)
SELECT 'profiles' as tabela, COUNT(*) as registros FROM public.profiles
UNION ALL
SELECT 'guardians' as tabela, COUNT(*) as registros FROM public.guardians  
UNION ALL
SELECT 'students' as tabela, COUNT(*) as registros FROM public.students
UNION ALL
SELECT 'account_deletion_requests' as tabela, COUNT(*) as registros FROM public.account_deletion_requests
ORDER BY registros DESC;

-- 2. ESTRUTURA COMPLETA DA TABELA STUDENTS
SELECT 
    'ESTRUTURA_STUDENTS' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'students'
ORDER BY ordinal_position;

-- 3. ESTRUTURA COMPLETA DA TABELA GUARDIANS
SELECT 
    'ESTRUTURA_GUARDIANS' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'guardians'
ORDER BY ordinal_position;

-- 4. TABELAS QUE PODEM TER RELACIONAMENTOS
SELECT 
    'TABELAS_RELACIONAMENTO' as info,
    table_name
FROM information_schema.tables
WHERE table_schema = 'public' 
  AND (table_name LIKE '%guardian%'
  OR table_name LIKE '%relationship%'
  OR table_name LIKE '%student%')
ORDER BY table_name;

-- 5. DADOS DO FRANK - SEM ASSUMIR COLUNAS
SELECT 
    'FRANK_PROFILE' as info,
    *
FROM public.profiles
WHERE email = '<EMAIL>'
LIMIT 1;

-- 6. PRIMEIROS REGISTROS DA TABELA STUDENTS (SE EXISTIR)
SELECT 
    'STUDENTS_SAMPLE' as info,
    *
FROM public.students
LIMIT 3; 