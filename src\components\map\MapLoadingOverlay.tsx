
import React from 'react';

interface MapLoadingOverlayProps {
  show: boolean;
}

const MapLoadingOverlay: React.FC<MapLoadingOverlayProps> = ({ show }) => {
  if (!show) return null;

  return (
    <div className="absolute inset-0 bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
        <p className="text-gray-600">Carregando mapa...</p>
      </div>
    </div>
  );
};

export default MapLoadingOverlay;
