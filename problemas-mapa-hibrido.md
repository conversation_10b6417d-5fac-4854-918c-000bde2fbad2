# Problemas com o Mapa Híbrido no Dashboard de Responsáveis

**Data**: 2025-06-04
**Autor**: Equipe de Desenvolvimento

## Descrição do Problema

Ao tentar implementar e corrigir a visualização híbrida do mapa no dashboard de responsáveis (`parent-dashboard`), encontramos diversos desafios técnicos relacionados à estrutura atual do código e ao sistema de tipos do TypeScript.

## Diagnóstico Detalhado

### 1. Conflito de Implementações do Hook

O sistema possui duas implementações diferentes do mesmo hook `useMapInitialization`:

1. **`useMapInitialization.ts`**:
   - Aceita um objeto do tipo `MapViewport` (latitude, longitude, zoom) 
   - Retorna: `{ mapContainer, mapInstance, viewport, mapInitialized, isTokenValid, ... }`
   - Não suporta parâmetros de estilo ou controles do mapa

2. **`useMapInitialization.tsx`**:
   - <PERSON><PERSON> parâmet<PERSON> `{ showControls, mapStyle }`
   - Retorna: `{ mapContainer, map, mapError, tokenValid, mapLoaded }`
   - Suporta configuração de estilo híbrido (`standard-satellite`)

Este conflito causa problemas na resolução de módulos do TypeScript, que acaba preferindo a versão `.ts` sobre a `.tsx`.

### 2. Erros de Tipagem no TypeScript

- Erro: `Object literal may only specify known properties, and 'showControls' does not exist in type 'MapViewport'`
- Causa: Ao passar `showControls` e `mapStyle` para o hook, o TypeScript não reconhece essas propriedades porque está usando a interface `MapViewport` da versão `.ts`

### 3. Conflito na Build

Na compilação do TypeScript, os dois arquivos (`useMapInitialization.ts` e `useMapInitialization.tsx`) estão gerando o mesmo arquivo de saída:
- Erro: `Cannot write file 'c:/Users/<USER>/Documents/GitHub/locate-family-connect/src/hooks/useMapInitialization.js' because it would be overwritten by multiple input files.`

### 4. Incompatibilidade de Propriedades

Existem diferenças nos nomes das propriedades retornadas pelas duas versões:
- `.ts` retorna `mapInstance` enquanto `.tsx` retorna `map`
- `.ts` retorna `isTokenValid` enquanto `.tsx` retorna `tokenValid`
- `.ts` retorna `mapInitialized` enquanto `.tsx` retorna `mapLoaded`

## Soluções Tentadas

### Tentativa 1: Forçar Importação Específica
```typescript
// @ts-ignore - Ignoring TypeScript's module resolution
import { useMapInitialization } from '@/hooks/useMapInitialization.tsx';
```
**Resultado**: O TypeScript continua apresentando erros, e em tempo de execução o módulo correto pode não ser carregado.

### Tentativa 2: Renomear Propriedades de Retorno
```typescript
const {
  mapContainer,
  map: mapInstance, 
  mapError,
  tokenValid: isTokenValid, 
  mapLoaded: mapInitialized
} = useMapInitialization({ showControls, mapStyle: currentMapStyle });
```
**Resultado**: Erros de tipo persistiram devido à resolução incorreta do módulo.

### Tentativa 3: Typecasting e Asserções
```typescript
const mapInit = useMapInitialization({
  latitude: -23.5489,
  longitude: -46.6388,
  zoom: 12,
  mapStyle: currentMapStyle, 
  showControls: showControls
} as any);
```
**Resultado**: Suprime erros de TypeScript, mas não resolve o problema subjacente de qual implementação do hook está sendo usada em tempo de execução.

### Tentativa 4: Detecção de Propriedades em Tempo de Execução
```typescript
const mapInstance = useRef(null);
useEffect(() => {
  if ('map' in mapInit) {
    mapInstance.current = mapInit.map;
  } else if ('mapInstance' in mapInit) {
    mapInstance.current = mapInit.mapInstance.current;
  }
}, [mapInit]);
```
**Resultado**: Tenta acomodar ambas as versões, mas não resolve o problema fundamental.

## Recomendações para Resolução

1. **Recomendação Principal**: Consolidar as duas implementações do hook em um único arquivo que suporte todos os parâmetros necessários:
   - Criar um hook unificado que aceite tanto `MapViewport` quanto opções de estilo
   - Padronizar os nomes das propriedades retornadas
   - Atualizar todas as referências ao hook no código

2. **Solução Alternativa**: Renomear um dos hooks para evitar o conflito:
   - `useMapInitializationWithStyle.tsx` para a versão com suporte a estilos
   - Atualizar o componente `MapView.tsx` para usar o hook renomeado

3. **Ajuste no Sistema de Build**:
   - Modificar a configuração do TypeScript para permitir arquivos `.ts` e `.tsx` com o mesmo nome base
   - Ou definir uma estratégia clara para evitar duplicação de nomes de arquivos

## Próximos Passos

1. Decidir qual abordagem seguir para resolver o conflito de hooks
2. Implementar a solução escolhida com testes abrangentes
3. Verificar a funcionalidade do mapa híbrido no dashboard de responsáveis
4. Atualizar a documentação técnica após a solução ser implementada

## Impacto no Projeto

Este problema afeta diretamente a experiência do usuário no dashboard de responsáveis, onde o mapa híbrido é essencial para a localização visual de estudantes. A resolução deste problema deve ser priorizada para garantir a usabilidade completa do sistema.
