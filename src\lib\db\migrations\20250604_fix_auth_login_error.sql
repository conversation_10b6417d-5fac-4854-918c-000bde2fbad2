-- Migration: <PERSON>rri<PERSON><PERSON> erro de autenticação "Database error granting user"
-- Data: 04/06/2025
-- Esta migração corrige problemas que causam erros 500 durante o processo de login

SET search_path = public, auth;

-- 1. Verificar e corrigir permissões da tabela auth_logs
GRANT ALL ON public.auth_logs TO postgres, service_role;
GRANT USAGE ON SEQUENCE public.auth_logs_id_seq TO postgres, service_role, authenticated, anon;

-- 2. Verificar e recriar a função log_auth_event de forma segura
CREATE OR REPLACE FUNCTION public.log_auth_event(
  event_type TEXT,
  user_id UUID DEFAULT NULL,
  metadata JSONB DEFAULT '{}'::jsonb
) 
RETURNS void
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Usar uma estrutura try-catch para evitar falhas na autenticação
  BEGIN
    INSERT INTO public.auth_logs (event, user_id, metadata, occurred_at)
    VALUES (event_type, user_id, metadata, now());
  EXCEPTION WHEN OTHERS THEN
    -- Não propagar erros de logging para a autenticação
    RAISE NOTICE 'Erro ao registrar evento de autenticação: %', SQLERRM;
  END;
END;
$$ LANGUAGE plpgsql;

-- 3. Definir permissões corretas para a função
REVOKE ALL ON FUNCTION public.log_auth_event(TEXT, UUID, JSONB) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION public.log_auth_event(TEXT, UUID, JSONB) TO authenticated, anon, service_role;

-- 4. Corrige a estrutura da tabela auth_logs para garantir que tenha todos os campos necessários
DO $$
BEGIN
  -- Adicionar coluna user_id se não existir
  IF NOT EXISTS (SELECT 1 FROM pg_attribute WHERE attrelid = 'public.auth_logs'::regclass AND attname = 'user_id') THEN
    ALTER TABLE public.auth_logs ADD COLUMN user_id UUID;
  END IF;
  
  -- Adicionar coluna occurred_at se não existir (ou usar created_at existente)
  IF NOT EXISTS (SELECT 1 FROM pg_attribute WHERE attrelid = 'public.auth_logs'::regclass AND attname = 'occurred_at') THEN
    ALTER TABLE public.auth_logs ADD COLUMN occurred_at TIMESTAMPTZ DEFAULT now();
  END IF;
  
  -- Renomear coluna event para event_type se necessário
  IF EXISTS (SELECT 1 FROM pg_attribute WHERE attrelid = 'public.auth_logs'::regclass AND attname = 'event') 
     AND NOT EXISTS (SELECT 1 FROM pg_attribute WHERE attrelid = 'public.auth_logs'::regclass AND attname = 'event_type') THEN
    ALTER TABLE public.auth_logs RENAME COLUMN event TO event_type;
  END IF;
END
$$;

-- 5. Registrar a execução da migração
INSERT INTO public.auth_logs (event_type, metadata, occurred_at)
VALUES (
  'auth_error_fix_applied', 
  jsonb_build_object(
    'migration', '20250604_fix_auth_login_error.sql',
    'description', 'Correção para erro Database error granting user no login'
  ), 
  now()
);

-- Comentários explicativos
COMMENT ON FUNCTION public.log_auth_event(TEXT, UUID, JSONB) IS 'Função segura para registrar eventos de autenticação sem afetar o fluxo de login';
