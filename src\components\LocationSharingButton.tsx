
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { MapPin, Share2, X, Loader2 } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog';
import { MultiChannelLocationSharing } from '@/components/communication/MultiChannelLocationSharing';
import { useToast } from '@/hooks/use-toast';

interface LocationSharingButtonProps {
  className?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  senderName?: string;
}

const LocationSharingButton: React.FC<LocationSharingButtonProps> = ({
  className = '',
  variant = "default",
  size = "default",
  senderName
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [recipientEmail, setRecipientEmail] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [currentLocation, setCurrentLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      setError("Geolocalização não é suportada neste navegador");
      return;
    }

    setIsGettingLocation(true);
    setError(null);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setCurrentLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
        setIsGettingLocation(false);
      },
      (error) => {
        setError("Não foi possível obter sua localização: " + error.message);
        setIsGettingLocation(false);
        toast({
          variant: "destructive",
          title: "Erro de localização",
          description: "Não foi possível obter sua localização. Verifique as permissões do navegador.",
        });
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 300000
      }
    );
  };

  const handleDialogOpen = (open: boolean) => {
    setIsDialogOpen(open);
    if (open && !currentLocation) {
      getCurrentLocation();
    }
    if (!open) {
      setRecipientEmail('');
      setRecipientName('');
      setError(null);
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={variant} 
          size={size} 
          className={className}
        >
          <Share2 className="h-4 w-4 mr-2" />
          Compartilhar Localização
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Compartilhar Localização
          </DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4"
            onClick={() => setIsDialogOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>
        
        <div className="space-y-4">
          {isGettingLocation && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <Loader2 className="h-4 w-4 animate-spin" />
              <p className="text-sm text-blue-900">Obtendo sua localização...</p>
            </div>
          )}

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-2"
                onClick={getCurrentLocation}
              >
                Tentar novamente
              </Button>
            </div>
          )}
          
          {currentLocation && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm font-medium text-green-900">Localização Obtida:</p>
              <p className="text-sm text-green-700">
                {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
              </p>
            </div>
          )}
          
          <div className="space-y-3">
            <div>
              <Label htmlFor="recipientEmail">Email do Destinatário</Label>
              <Input
                id="recipientEmail"
                type="email"
                placeholder="<EMAIL>"
                value={recipientEmail}
                onChange={(e) => setRecipientEmail(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="recipientName">Nome do Destinatário (opcional)</Label>
              <Input
                id="recipientName"
                placeholder="Nome da pessoa"
                value={recipientName}
                onChange={(e) => setRecipientName(e.target.value)}
              />
            </div>
          </div>
          
          {currentLocation && recipientEmail && (
            <MultiChannelLocationSharing
              recipientEmail={recipientEmail}
              recipientName={recipientName}
              latitude={currentLocation.latitude}
              longitude={currentLocation.longitude}
              senderName={senderName}
            />
          )}
          
          {!currentLocation && !isGettingLocation && !error && (
            <div className="text-center py-4">
              <p className="text-sm text-muted-foreground mb-3">
                Clique no botão abaixo para obter sua localização atual
              </p>
              <Button onClick={getCurrentLocation} variant="outline">
                <MapPin className="h-4 w-4 mr-2" />
                Obter Localização
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LocationSharingButton;
