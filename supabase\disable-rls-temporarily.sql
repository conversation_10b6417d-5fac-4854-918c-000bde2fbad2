-- ==========================================
-- DESABILITAR RLS TEMPORARIAMENTE PARA TESTE
-- Data: 24/06/2025
-- ATENÇÃO: SOMENTE PARA DIAGNÓSTICO!
-- ==========================================

-- 1. DESABILITAR RLS TEMPORARIAMENTE
ALTER TABLE public.account_deletion_requests DISABLE ROW LEVEL SECURITY;

-- 2. VERIFICAR SE RLS FOI DESABILITADO
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    pg_get_userbyid(relowner) as owner
FROM pg_tables pt
JOIN pg_class pc ON pc.relname = pt.tablename
WHERE pt.tablename = 'account_deletion_requests';

-- 3. LISTAR TODAS AS POLÍTICAS (PARA REFERÊNCIA)
SELECT 
    policyname,
    permissive,
    cmd
FROM pg_policies 
WHERE tablename = 'account_deletion_requests';

-- 4. APÓS O TESTE, REABILITAR RLS:
-- ALTER TABLE public.account_deletion_requests ENABLE ROW LEVEL SECURITY; 