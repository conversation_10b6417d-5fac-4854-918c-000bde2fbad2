
import React from 'react';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Settings, 
  Activity, 
  Database,
  Shield,
  BarChart3,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { getUserTypeFromMetadata, getUserTypeLabel } from '@/lib/types/user-types';

const AdminDashboard: React.FC = () => {
  const { user } = useUser();

  const adminCards = [
    {
      title: 'Gerenciar Usuários',
      description: 'Administrar contas de usuários, permissões e perfis',
      icon: Users,
      href: '/admin/users',
      color: 'bg-blue-500'
    },
    {
      title: 'Configurações do Sistema',
      description: 'Configurar parâmetros globais e preferências',
      icon: Settings,
      href: '/admin/system',
      color: 'bg-green-500'
    },
    {
      title: 'Monitoramento',
      description: 'Acompanhar métricas e desempenho do sistema',
      icon: BarChart3,
      href: '/admin/monitoring',
      color: 'bg-purple-500'
    },
    {
      title: 'Banco de Dados',
      description: 'Visualizar e gerenciar dados do sistema',
      icon: Database,
      href: '/admin/database',
      color: 'bg-orange-500'
    },
    {
      title: 'Segurança',
      description: 'Revisar logs de segurança e configurações',
      icon: Shield,
      href: '/admin/security',
      color: 'bg-red-500'
    },
    {
      title: 'Diagnóstico',
      description: 'Ferramentas de diagnóstico e solução de problemas',
      icon: Activity,
      href: '/diagnostic',
      color: 'bg-indigo-500'
    }
  ];

  const userType = getUserTypeFromMetadata(user?.user_metadata);

  return (
    <div className="w-full max-w-screen-xl mx-auto px-2 sm:px-4 md:px-8 py-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Painel Administrativo</h1>
          <p className="text-gray-600 mt-2">
            Bem-vindo, {user?.user_metadata?.full_name || user?.email}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <Shield className="w-3 h-3 mr-1" />
            {getUserTypeLabel(userType)}
          </Badge>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status do Sistema</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Online</div>
            <p className="text-xs text-muted-foreground">
              Todos os serviços funcionando normalmente
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usuários Ativos</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-</div>
            <p className="text-xs text-muted-foreground">
              Conecte ao painel de monitoramento para ver métricas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Alertas</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              Nenhum alerta ativo no momento
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Admin Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
        {adminCards.map((card, index) => {
          const IconComponent = card.icon;
          return (
            <Card key={index} className="col-span-1 min-w-0 hover:shadow-lg transition-shadow cursor-pointer">
              <Link to={card.href}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${card.color} text-white`}>
                      <IconComponent className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{card.title}</CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm">
                    {card.description}
                  </CardDescription>
                </CardContent>
              </Link>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesso rápido às funcionalidades mais utilizadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button asChild variant="outline" size="sm">
              <Link to="/admin/users">
                <Users className="w-4 h-4 mr-2" />
                Ver Usuários
              </Link>
            </Button>
            <Button asChild variant="outline" size="sm">
              <Link to="/diagnostic">
                <Activity className="w-4 h-4 mr-2" />
                Diagnóstico
              </Link>
            </Button>
            <Button asChild variant="outline" size="sm">
              <Link to="/admin/system">
                <Settings className="w-4 h-4 mr-2" />
                Configurações
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminDashboard;
