// Script para gerar ícones PWA para o aplicativo Locate Family Connect
// Este script cria um arquivo SVG básico e o converte para os tamanhos necessários
// Usando apenas APIs nativas do Node para evitar dependências externas

const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Configuração
const ICONS_DIR = path.join(__dirname, '..', 'public', 'icons');
const ICON_SIZES = [72, 96, 128, 144, 152, 192, 384, 512];
const ICON_COLOR = '#3b82f6'; // Azul (combina com o theme_color do manifest)

// Garantir que a pasta icons exista
if (!fs.existsSync(ICONS_DIR)) {
  fs.mkdirSync(ICONS_DIR, { recursive: true });
  console.log(`✓ Diretório ${ICONS_DIR} criado`);
}

// Função para criar um ícone
async function createIcon(size) {
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // Fundo
  ctx.fillStyle = ICON_COLOR;
  ctx.fillRect(0, 0, size, size);

  // Desenha um "LFC" estilizado para "Locate Family Connect"
  const fontSize = size * 0.4;
  ctx.fillStyle = '#ffffff';
  ctx.font = `bold ${fontSize}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('LFC', size / 2, size / 2);

  // Adiciona um pequeno círculo como um elemento visual - parece uma localização de mapa
  ctx.beginPath();
  ctx.arc(size / 2, size * 0.75, size * 0.1, 0, Math.PI * 2);
  ctx.fillStyle = '#ffffff';
  ctx.fill();

  // Salva o arquivo PNG
  const buffer = canvas.toBuffer('image/png');
  const filePath = path.join(ICONS_DIR, `icon-${size}x${size}.png`);
  fs.writeFileSync(filePath, buffer);
  
  return filePath;
}

// Gera ícones para todos os tamanhos
async function generateAllIcons() {
  console.log('Gerando ícones PWA...');
  
  for (const size of ICON_SIZES) {
    try {
      const filePath = await createIcon(size);
      console.log(`✓ Ícone ${size}x${size} gerado: ${path.basename(filePath)}`);
    } catch (error) {
      console.error(`✗ Erro ao gerar ícone ${size}x${size}:`, error);
    }
  }
  
  console.log('\nTodos os ícones foram gerados em:', ICONS_DIR);
}

generateAllIcons().catch(console.error);
