import React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const elegantButtonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'elegant-button text-primary-foreground shadow-lg hover:shadow-xl',
        destructive: 'bg-gradient-to-r from-destructive to-red-600 text-destructive-foreground hover:from-red-600 hover:to-red-700 shadow-lg hover:shadow-xl',
        outline: 'border border-input bg-background/80 hover:bg-accent hover:text-accent-foreground backdrop-blur-sm',
        secondary: 'bg-gradient-to-r from-secondary to-secondary-hover text-secondary-foreground hover:from-secondary-hover hover:to-secondary shadow-md hover:shadow-lg',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        glass: 'glass-button text-foreground',
        premium: 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg hover:shadow-xl hover:from-purple-700 hover:to-blue-700'
      },
      size: {
        default: 'h-11 px-6 py-3',
        sm: 'h-9 rounded-lg px-4',
        lg: 'h-12 rounded-xl px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ElegantButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof elegantButtonVariants> {
  asChild?: boolean;
}

const ElegantButton = React.forwardRef<HTMLButtonElement, ElegantButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(elegantButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
ElegantButton.displayName = 'ElegantButton';

export { ElegantButton, elegantButtonVariants };