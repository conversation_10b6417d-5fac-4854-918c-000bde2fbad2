import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";

export default function StudentDashboardDemo() {
  const { t } = useTranslation();

  return (
    <div className="max-w-screen-xl mx-auto px-4 py-6 space-y-6">
      {/* Top section: Profile + Quick Actions + Guardians */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {/* Profile Card */}
        <Card>
          <CardContent className="space-y-2">
            <h2 className="text-xl font-semibold">{t('profile.personalInfo', 'Profile')}</h2>
            <div>
              <strong>{t('studentDashboard.name', 'Name')}:</strong> Fabio Leda Cunha
            </div>
            <div>
              <strong>{t('studentDashboard.email', 'Email')}:</strong> <EMAIL>
            </div>
            <div>
              <strong>{t('studentDashboard.phone', 'Phone')}:</strong> +5592994897102
            </div>
            <div className="flex gap-2 pt-2">
              <Button variant="outline">{t('profile.edit', 'Edit')}</Button>
              <Button variant="destructive">{t('profile.logout', 'Logout')}</Button>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardContent className="space-y-3">
            <h2 className="text-xl font-semibold">{t('studentDashboard.quickActions', 'Quick Actions')}</h2>
            <div className="flex flex-col gap-2">
              <Button>{t('studentDashboard.updateLocation', 'Update Location')}</Button>
              <Button className="whitespace-nowrap">
                🚀 {t('studentDashboard.sendToGuardians', 'SEND TO {{count}} GUARDIANS', { count: 1 })}
              </Button>
            </div>
            <div className="text-green-600 text-sm">
              ✔️ {t('studentDashboard.readyToShare', 'Ready to share with {{count}} guardian(s)', { count: 1 })}
            </div>
          </CardContent>
        </Card>

        {/* Guardians */}
        <Card>
          <CardContent className="space-y-3">
            <h2 className="text-xl font-semibold">{t('guardians.title', 'Guardians')}</h2>
            <Button variant="secondary">+ {t('guardianManager.addGuardian', 'Add')}</Button>
            <div className="border p-2 rounded">
              <div className="font-semibold">Marcus Andre Lima de Lima</div>
              <div className="text-sm text-gray-600"><EMAIL></div>
              <span className="text-green-600 text-sm font-medium">{t('studentDashboard.active', 'Active')}</span>
              <div className="text-sm">Telefone: +5592992287144</div>
              <div className="text-sm">{t('studentDashboard.addedOn', 'Added on: {{date}}', { date: '02/07/2025' })}</div>
              <div className="flex gap-2 pt-2">
                <Button size="sm" variant="outline">{t('studentDashboard.connected', 'Connected')}</Button>
                <Button size="sm" variant="destructive">{t('studentDashboard.remove', 'Remove')}</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Guardian Requests */}
      <Card>
        <CardContent className="space-y-2">
          <h2 className="text-xl font-semibold">{t('studentDashboard.guardianRequests', 'Guardian Requests')}</h2>
          <div className="text-gray-700">
            {t('studentDashboard.noPendingRequests', 'You have no pending guardian requests at the moment.')}
          </div>
        </CardContent>
      </Card>

      {/* Location History */}
      <Card>
        <CardContent className="space-y-4">
          <h2 className="text-xl font-semibold">{t('locationHistory.title', 'Location History')}</h2>
          <div className="space-y-3">
            <div className="border p-3 rounded bg-blue-50">
              <div className="font-semibold">
                Fabio Leda Cunha
                <span className="ml-2 bg-green-100 text-green-700 text-xs px-2 py-1 rounded">
                  {t('locationHistory.recent', 'Most recent')}
                </span>
              </div>
              <div className="text-sm">
                1 Rushes Lane, Lubenham, Market Harborough, LE16 9TN, United Kingdom
              </div>
              <div className="text-xs text-gray-600">1 minute ago • 10/07/2025, 17:37:04</div>
              <div className="flex items-center gap-2 pt-1">
                <Button size="sm" variant="outline">
                  {t('studentDashboard.sendToGuardians', 'Share with guardians', { count: 1 })}
                </Button>
                <Button size="sm" variant="secondary">{t('common.sendEmail', 'Send by Email')}</Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Map always at the bottom */}
      <Card>
        <CardContent className="space-y-3">
          <h2 className="text-xl font-semibold">{t('studentDashboard.map', 'Location Map')}</h2>
          <div className="w-full h-[320px] bg-gray-200 rounded-xl overflow-hidden">
            <span className="text-center block pt-32 text-gray-600">[MAP HERE]</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

