describe('Student Creation Flow - Fixed Edge Function Test', () => {
  const guardianCredentials = {
    email: '<EMAIL>',
    password: 'Senh@97481716'
  };

  const studentData = {
    cpf: '717.102.482-20',
    name: '<PERSON><PERSON><PERSON>',
    email: 'fabi<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
    phone: '+5592994897102'
  };

  beforeEach(() => {
    // Visitar a página de login
    cy.visit('/login');
    
    // Aguardar a página carregar completamente
    cy.wait(2000);
  });

  it('should successfully create a new student after Edge Function fix', () => {
    // STEP 1: Login como responsável
    cy.log('🔐 Step 1: Logging in as guardian');
    
    cy.get('input[type="email"]', { timeout: 10000 })
      .should('be.visible')
      .clear()
      .type(guardianCredentials.email);
    
    cy.get('input[type="password"]')
      .should('be.visible')
      .clear()
      .type(guardianCredentials.password);
    
    cy.get('button[type="submit"]')
      .should('be.visible')
      .click();

    // Aguardar redirect para dashboard
    cy.url({ timeout: 15000 }).should('include', '/parent-dashboard');
    
    // STEP 2: Verificar se chegou no dashboard do responsável
    cy.log('📊 Step 2: Verifying parent dashboard');
    
    cy.contains('Dashboard do Responsável', { timeout: 10000 }).should('be.visible');
    
    // STEP 3: Encontrar e clicar no botão de adicionar estudante
    cy.log('👤 Step 3: Opening student creation form');
    
    // Procurar por diferentes variações do botão
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="add-student-btn"]').length > 0) {
        cy.get('[data-testid="add-student-btn"]').click();
      } else if ($body.find('button:contains("Adicionar Estudante")').length > 0) {
        cy.get('button:contains("Adicionar Estudante")').first().click();
      } else if ($body.find('button:contains("Convidar Estudante")').length > 0) {
        cy.get('button:contains("Convidar Estudante")').first().click();
      } else {
        // Fallback: procurar por qualquer botão com ícone de mais
        cy.get('button').contains('+').first().click();
      }
    });

    // STEP 4: Preencher o formulário de criação do estudante
    cy.log('📝 Step 4: Filling student creation form');

    // Aguardar modal ou formulário aparecer
    cy.wait(1000);

    // Preencher nome
    cy.get('input[name="name"], input[placeholder*="nome"], input[placeholder*="Nome"]', { timeout: 5000 })
      .should('be.visible')
      .clear()
      .type(studentData.name);

    // Preencher email
    cy.get('input[name="email"], input[type="email"], input[placeholder*="email"], input[placeholder*="Email"]')
      .should('be.visible')
      .clear()
      .type(studentData.email);

    // Preencher CPF
    cy.get('input[name="cpf"], input[placeholder*="CPF"], input[placeholder*="cpf"]')
      .should('be.visible')
      .clear()
      .type(studentData.cpf);

    // Preencher telefone (opcional)
    cy.get('input[name="phone"], input[name="telefone"], input[placeholder*="telefone"], input[placeholder*="Telefone"]')
      .should('be.visible')
      .clear()
      .type(studentData.phone);

    // STEP 5: Submeter o formulário
    cy.log('🚀 Step 5: Submitting form (testing fixed Edge Function)');

    // Interceptar a chamada para a Nova Edge Function
    cy.intercept('POST', '**/functions/v1/create-student-complete').as('createStudentRequest');
    // Interceptar também a Edge Function de fallback
    cy.intercept('POST', '**/functions/v1/create-student-account').as('createStudentFallback');

    // Clicar no botão de enviar/criar
    cy.get('button[type="submit"], button:contains("Criar"), button:contains("Enviar"), button:contains("Convidar")')
      .should('be.visible')
      .should('not.be.disabled')
      .click();

    // STEP 6: Verificar que a requisição foi bem-sucedida (não mais erro 500)
    cy.log('✅ Step 6: Verifying Edge Function success');

    cy.wait('@createStudentRequest', { timeout: 30000 }).then((interception) => {
      // Verificar que não retornou erro 500
      expect(interception.response.statusCode).to.not.equal(500);
      
      // Verificar se foi sucesso (200 ou 201)
      expect([200, 201]).to.include(interception.response.statusCode);
      
      // Verificar se a resposta contém dados de sucesso
      if (interception.response.body) {
        expect(interception.response.body).to.have.property('success');
        if (interception.response.body.success !== undefined) {
          expect(interception.response.body.success).to.be.true;
        }
      }
    });

    // STEP 7: Verificar feedback visual de sucesso
    cy.log('🎉 Step 7: Verifying success feedback');

    // Aguardar mensagem de sucesso aparecer
    cy.get('body', { timeout: 10000 }).should(($body) => {
      const text = $body.text();
      expect(text).to.satisfy((content) => {
        return content.includes('sucesso') || 
               content.includes('criado') || 
               content.includes('enviado') ||
               content.includes('Sucesso') ||
               content.includes('Criado') ||
               content.includes('Enviado');
      });
    });

    // STEP 8: Verificar se o estudante aparece na lista
    cy.log('📋 Step 8: Verifying student appears in list');

    // Aguardar um pouco para o dashboard atualizar
    cy.wait(3000);

    // Verificar se o nome do estudante aparece em algum lugar
    cy.get('body').should('contain.text', studentData.name);

    // STEP 9: Verificar logs do console para confirmar sucesso
    cy.log('🔍 Step 9: Checking console logs');

    cy.window().then((win) => {
      // Verificar se não há erros 500 nos logs
      cy.task('log', '✅ Student creation flow completed successfully!');
      cy.task('log', `📧 Student email: ${studentData.email}`);
      cy.task('log', '🎯 Edge Function create-student-complete worked without 500 error');
    });
  });

  it('should handle edge function errors gracefully', () => {
    // Teste adicional para verificar tratamento de erros
    cy.log('🧪 Testing error handling for Edge Function');

    // Login
    cy.get('input[type="email"]', { timeout: 10000 })
      .clear()
      .type(guardianCredentials.email);
    
    cy.get('input[type="password"]')
      .clear()
      .type(guardianCredentials.password);
    
    cy.get('button[type="submit"]').click();

    cy.url({ timeout: 15000 }).should('include', '/parent-dashboard');

    // Tentar criar estudante com email duplicado (deve falhar graciosamente)
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="add-student-btn"]').length > 0) {
        cy.get('[data-testid="add-student-btn"]').click();
      } else {
        cy.get('button').contains('Adicionar').first().click();
      }
    });

    // Preencher com email que já existe
    cy.get('input[name="name"], input[placeholder*="nome"]', { timeout: 5000 })
      .clear()
      .type('Teste Duplicado');

    cy.get('input[name="email"], input[type="email"]')
      .clear()
      .type('<EMAIL>'); // Email que já pode existir

    cy.get('input[name="cpf"], input[placeholder*="CPF"]')
      .clear()
      .type('123.456.789-01');

    // Interceptar a requisição
    cy.intercept('POST', '**/functions/v1/create-student-account').as('createDuplicateRequest');

    cy.get('button[type="submit"], button:contains("Criar")').click();

    // Verificar que mesmo com erro, não é 500
    cy.wait('@createDuplicateRequest', { timeout: 15000 }).then((interception) => {
      // Não deve ser erro 500 interno
      expect(interception.response.statusCode).to.not.equal(500);
      
      cy.task('log', `Response status: ${interception.response.statusCode}`);
      cy.task('log', 'Edge Function handles errors without 500 crash');
    });
  });

  afterEach(() => {
    // Cleanup: logout se estiver logado
    cy.window().then((win) => {
      if (win.location.pathname.includes('dashboard')) {
        cy.get('body').then(($body) => {
          if ($body.find('button:contains("Sair")').length > 0) {
            cy.get('button:contains("Sair")').click();
          }
        });
      }
    });
  });
}); 