import React from 'react';
import { Button } from "@/components/ui/button";
import { Navigation, Loader2, Share2, MapPin } from "lucide-react";
import { useMapControlsLayout } from '@/hooks/useMapControlsLayout';

interface LocationControlsProps {
  onGetLocation: () => void;
  isGettingLocation: boolean;
  onShareAll?: () => Promise<void>;
  guardianCount: number;
  isSendingAll: boolean;
  sharingStatus: 'idle' | 'loading' | 'success' | 'error';
  hasLocations: boolean;
}

const LocationControls: React.FC<LocationControlsProps> = ({
  onGetLocation,
  isGettingLocation,
  onShareAll,
  guardianCount,
  isSendingAll,
  sharingStatus,
  hasLocations
}) => {
  const { getLocationControlsPosition } = useMapControlsLayout();
  const { containerClass, buttonSize } = getLocationControlsPosition();

  return (
    <div className={`${containerClass} location-controls-container`}>
      <Button
        onClick={onGetLocation}
        disabled={isGettingLocation}
        size={buttonSize}
        variant="outline"
        className="bg-white shadow-lg hover:scale-105 transition-transform text-xs whitespace-nowrap"
      >
        {isGettingLocation ? (
          <Loader2 className="h-3 w-3 animate-spin" />
        ) : (
          <Navigation className="h-3 w-3" />
        )}
        <span className="ml-1">Atualizar</span>
      </Button>

      {guardianCount > 0 && hasLocations && onShareAll && (
        <Button
          onClick={() => {
            console.log('🚀 Botão de compartilhar clicado!');
            onShareAll();
          }}
          disabled={isSendingAll}
          size="default"
          variant="default"
          className="location-share-button pulse-on-hover text-sm whitespace-nowrap font-bold text-white relative overflow-hidden"
          style={{
            minHeight: '48px',
            minWidth: '200px',
            fontSize: '14px'
          }}
        >
          {isSendingAll ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
              <span>Enviando...</span>
            </>
          ) : (
            <>
              <MapPin className="h-5 w-5 mr-2 animate-pulse" />
              <span>
                🚀 ENVIAR LOCALIZAÇÃO
              </span>
            </>
          )}
          {/* Efeito de brilho adicional */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
        </Button>
      )}
    </div>
  );
};

export default LocationControls;
