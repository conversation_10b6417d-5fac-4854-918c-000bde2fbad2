
import { supabase } from '@/integrations/supabase/client';
import { GuardianData } from '@/types/database';

/**
 * Repository for guardian data access using proper database schema
 */
export class GuardianRepository {
  /**
   * Fetch guardians for a student using RPC function
   */
  async fetchGuardiansForStudent(studentId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase.rpc(
        'get_student_guardians_from_relationships',
        { p_student_id: studentId }
      );
      
      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('[GuardianRepository] Error fetching guardians:', error);
      throw error;
    }
  }
  
  /**
   * Check if guardian relationship exists for student
   */
  async checkGuardianExists(studentId: string, guardianId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('student_guardian_relationships')
        .select('id')
        .eq('student_id', studentId)
        .eq('guardian_id', guardianId)
        .maybeSingle();
      
      if (error) throw error;
      
      return !!data;
    } catch (error) {
      console.error('[GuardianRepository] Error checking guardian exists:', error);
      throw error;
    }
  }
  
  /**
   * Add a new guardian relationship
   */
  async addGuardianRelationship(
    studentId: string, 
    guardianId: string, 
    relationshipType?: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('student_guardian_relationships')
        .insert({
          student_id: studentId,
          guardian_id: guardianId,
          relationship_type: relationshipType || 'parent',
          is_primary: false
        });
      
      if (error) throw error;
    } catch (error) {
      console.error('[GuardianRepository] Error adding guardian relationship:', error);
      throw error;
    }
  }

  /**
   * Remove a guardian relationship
   */
  async removeGuardianRelationship(relationshipId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('student_guardian_relationships')
        .delete()
        .eq('id', relationshipId);
      
      if (error) throw error;
    } catch (error) {
      console.error('[GuardianRepository] Error removing guardian relationship:', error);
      throw error;
    }
  }
}

export const guardianRepository = new GuardianRepository();
