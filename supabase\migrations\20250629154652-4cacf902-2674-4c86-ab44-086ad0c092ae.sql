
-- Força a recriação completa da função RPC get_guardian_locations_bypass_v2
-- para resolver definitivamente o problema de coluna ambígua

-- Remover a função existente
DROP FUNCTION IF EXISTS public.get_guardian_locations_bypass_v2(uuid);

-- Recriar a função com todas as referências de coluna explícitas
CREATE OR REPLACE FUNCTION public.get_guardian_locations_bypass_v2(p_student_id uuid)
RETURNS TABLE(
  id uuid, 
  user_id uuid, 
  latitude double precision, 
  longitude double precision, 
  location_timestamp timestamp with time zone, 
  address text, 
  shared_with_guardians boolean, 
  student_name text
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public', 'auth'
AS $$
DECLARE
  v_guardian_email TEXT;
  v_guardian_exists BOOLEAN;
  v_location_count INTEGER;
BEGIN
  -- Obter o email do responsável autenticado (especificando explicitamente a tabela)
  SELECT auth.users.email INTO v_guardian_email 
  FROM auth.users 
  WHERE auth.users.id = auth.uid();
  
  -- Log inicial
  INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
  VALUES (
    'get_guardian_locations_bypass_v2_start',
    auth.uid(),
    jsonb_build_object(
      'guardian_email', v_guardian_email,
      'student_id', p_student_id,
      'timestamp', now()
    ),
    now()
  );

  -- Verificar se o usuário atual é um responsável do estudante usando student_guardian_relationships
  SELECT EXISTS (
    SELECT 1 FROM public.student_guardian_relationships sgr
    JOIN auth.users u ON sgr.guardian_id = u.id
    WHERE sgr.student_id = p_student_id
    AND u.email = v_guardian_email
  ) INTO v_guardian_exists;
  
  -- Log da verificação
  INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
  VALUES (
    'guardian_verification',
    auth.uid(),
    jsonb_build_object(
      'guardian_exists', v_guardian_exists,
      'guardian_email', v_guardian_email,
      'student_id', p_student_id
    ),
    now()
  );

  IF NOT v_guardian_exists THEN
    -- Log do erro de permissão
    INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
    VALUES (
      'permission_denied',
      auth.uid(),
      jsonb_build_object(
        'reason', 'Guardian not found in student_guardian_relationships',
        'guardian_email', v_guardian_email,
        'student_id', p_student_id
      ),
      now()
    );
    
    RAISE EXCEPTION 'Você não tem permissão para ver as localizações deste estudante';
  END IF;

  -- Contar localizações disponíveis (especificando explicitamente a tabela)
  SELECT COUNT(*) INTO v_location_count
  FROM public.locations l
  WHERE l.user_id = p_student_id
  AND l.shared_with_guardians = true;
  
  -- Log da contagem
  INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
  VALUES (
    'location_count_check',
    auth.uid(),
    jsonb_build_object(
      'location_count', v_location_count,
      'student_id', p_student_id
    ),
    now()
  );
  
  -- Retornar as localizações do estudante (especificando explicitamente todas as colunas)
  RETURN QUERY
  SELECT
    l.id AS id,
    l.user_id AS user_id,
    l.latitude AS latitude,
    l.longitude AS longitude,
    l."timestamp" AS location_timestamp,
    l.address AS address,
    l.shared_with_guardians AS shared_with_guardians,
    COALESCE(p.full_name, 'Estudante') AS student_name
  FROM
    public.locations l
    LEFT JOIN public.profiles p ON l.user_id = p.user_id
  WHERE
    l.user_id = p_student_id
    AND l.shared_with_guardians = true
  ORDER BY
    l."timestamp" DESC;
    
  -- Log final
  INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
  VALUES (
    'get_guardian_locations_bypass_v2_complete',
    auth.uid(),
    jsonb_build_object(
      'returned_count', v_location_count,
      'student_id', p_student_id
    ),
    now()
  );
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION public.get_guardian_locations_bypass_v2(UUID) TO authenticated;

-- Registrar a migração
INSERT INTO public.auth_logs (event_type, metadata, occurred_at)
VALUES (
  'migration_applied',
  jsonb_build_object(
    'migration', '20250629160000_fix_guardian_locations_rpc_definitive',
    'description', 'Recriada função RPC com colunas explícitas para resolver ambiguidade'
  ),
  now()
);
