
import React, { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { studentProfileService } from '@/lib/services/student/StudentProfileService';
import StudentManager from './StudentManager';

// Define a local Student interface matching what StudentManager expects
interface StudentForManager {
  id: string;
  full_name: string;
  email: string;
  birth_date?: string;
  user_type: string;
}

const StudentManagerContainer: React.FC = () => {
  const [students, setStudents] = useState<StudentForManager[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchStudents = async () => {
    setLoading(true);
    try {
      const result = await studentProfileService.getStudentsForGuardian();
      
      // Transform the data to match what StudentManager expects
      const transformedStudents: StudentForManager[] = (result || []).map(student => ({
        id: student.id,
        full_name: student.name || student.email || 'Estudante',
        email: student.email,
        birth_date: student.birth_date,
        user_type: 'student'
      }));
      
      setStudents(transformedStudents);
    } catch (error: any) {
      console.error('Error fetching students:', error);
      toast({
        variant: 'destructive',
        title: 'Erro',
        description: error.message || 'Não foi possível carregar os estudantes.',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStudents();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <StudentManager students={students} loading={loading} />;
};

export default StudentManagerContainer;
