
/**
 * Sistema de Monitoramento de Saúde
 * 
 * Provê endpoints e utilidades para verificar a saúde dos serviços
 * críticos do sistema, como Redis, Supabase e outros componentes.
 * 
 * Seguindo o Anti-Break Protocol:
 * - Verifica falhas antes que afetem os usuários
 * - Fornece diagnósticos claros para problemas
 * - Permite recuperação rápida de falhas
 * - Mantém histórico de estado de saúde
 */

import { redisService } from '@/lib/services/redis';
import { supabase } from '@/integrations/supabase/client';

export type HealthCheckResult = {
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: Record<string, any>;
  timestamp: string;
};

export type ServiceHealth = {
  status: 'healthy' | 'degraded' | 'unhealthy';
  message?: string;
  latency?: number;
  details?: Record<string, any>;
};

/**
 * Verifica a saúde do serviço Redis com métricas avançadas
 */
export async function checkRedisHealth(): Promise<ServiceHealth> {
  const startTime = Date.now();
  
  try {
    // Usar o health check nativo do Redis service
    const healthMetrics = await redisService.healthCheck();
    const latency = Date.now() - startTime;
    
    // Obter métricas do circuit breaker
    const circuitBreakerMetrics = redisService.getCircuitBreakerMetrics();
    
    // Determinar status baseado nas métricas
    let status: 'healthy' | 'degraded' | 'unhealthy';
    let message: string;
    
    if (healthMetrics.status === 'healthy' && circuitBreakerMetrics.state === 'CLOSED') {
      status = 'healthy';
      message = 'Redis está operacional e responsivo';
    } else if (healthMetrics.status === 'degraded' || circuitBreakerMetrics.state === 'HALF-OPEN') {
      status = 'degraded';
      message = 'Redis está operacional mas com problemas de performance';
    } else {
      status = 'unhealthy';
      message = 'Redis está inacessível ou com falhas críticas';
    }
    
    return {
      status,
      message,
      latency,
      details: {
        redis: {
          status: healthMetrics.status,
          latency: healthMetrics.latency,
          hitRate: healthMetrics.hitRate,
          memoryUsage: healthMetrics.memoryUsage,
          connectedClients: healthMetrics.connectedClients
        },
        circuitBreaker: {
          state: circuitBreakerMetrics.state,
          failures: circuitBreakerMetrics.failures,
          successes: circuitBreakerMetrics.successes,
          rejected: circuitBreakerMetrics.rejected,
          lastFailureTime: circuitBreakerMetrics.lastFailureTime,
          lastSuccessTime: circuitBreakerMetrics.lastSuccessTime
        },
        healthCheck: healthMetrics.circuitBreakerState,
        isHealthy: redisService.isHealthy()
      }
    };
  } catch (error: any) {
    return {
      status: 'unhealthy',
      message: `Redis health check falhou: ${error.message}`,
      latency: Date.now() - startTime,
      details: {
        error: error.message,
        stack: error.stack
      }
    };
  }
}

/**
 * Verifica a saúde da conexão com Supabase
 */
export async function checkSupabaseHealth(): Promise<ServiceHealth> {
  const startTime = Date.now();
  
  try {
    // Verifica conexão executando uma query simples
    const { data, error } = await supabase
      .from('locations')
      .select('id')
      .limit(1)
      .maybeSingle();
    
    const latency = Date.now() - startTime;
    
    if (error) {
      return {
        status: 'unhealthy',
        message: `Supabase query error: ${error.message}`,
        latency,
        details: { error: error.message }
      };
    }
    
    return {
      status: 'healthy',
      message: 'Supabase connection is healthy',
      latency,
      details: { querySuccess: true }
    };
  } catch (error: any) {
    return {
      status: 'unhealthy',
      message: `Supabase health check error: ${error.message}`,
      latency: Date.now() - startTime,
      details: { error: error.message }
    };
  }
}

/**
 * Executa uma verificação completa de saúde do sistema
 */
export async function checkSystemHealth(): Promise<HealthCheckResult> {
  const results: Record<string, ServiceHealth> = {
    redis: await checkRedisHealth(),
    supabase: await checkSupabaseHealth()
  };
  
  // Determinar status geral com base nos serviços
  let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
  
  // Se qualquer serviço estiver unhealthy, o sistema é unhealthy
  if (Object.values(results).some(r => r.status === 'unhealthy')) {
    overallStatus = 'unhealthy';
  } 
  // Se nenhum serviço for unhealthy mas algum for degraded, o sistema é degraded
  else if (Object.values(results).some(r => r.status === 'degraded')) {
    overallStatus = 'degraded';
  }
  
  return {
    status: overallStatus,
    details: results,
    timestamp: new Date().toISOString()
  };
}

/**
 * Recupera o Redis em caso de falha
 * - Tenta reset do circuit breaker
 * - Força reconexão
 * - Executa testes de validação
 */
export async function recoverRedisService(): Promise<boolean> {
  try {
    console.log('[Recovery] Iniciando recuperação do Redis service...');
    
    // 1. Reset do circuit breaker
    redisService.resetCircuitBreaker();
    console.log('[Recovery] Circuit breaker resetado');
    
    // 2. Aguardar um momento para estabilizar
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. Testar operações básicas
    const testKey = `recovery:test:${Date.now()}`;
    const testData = { test: true, timestamp: new Date().toISOString() };
    
    try {
      // Testar cache
      await redisService.cacheData(testKey, testData, 60);
      console.log('[Recovery] Teste de cache executado');
      
      // Testar recuperação
      const retrieved = await redisService.getCachedData(
        testKey, 
        async () => testData, 
        60
      );
      
      if (retrieved && retrieved.test === true) {
        console.log('[Recovery] Teste de recuperação bem-sucedido');
        
        // Limpeza
        await redisService.invalidateCache(`recovery:test:*`);
        console.log('[Recovery] Limpeza concluída');
        
        // Verificação final
        const health = await checkRedisHealth();
        const isRecovered = health.status === 'healthy';
        
        console.log(`[Recovery] Status final: ${health.status}`);
        return isRecovered;
      } else {
        console.warn('[Recovery] Teste de recuperação falhou - dados não correspondem');
        return false;
      }
    } catch (testError) {
      console.error('[Recovery] Erro durante testes de validação:', testError);
      return false;
    }
  } catch (error) {
    console.error('[Recovery] Falha na recuperação do Redis:', error);
    return false;
  }
}

/**
 * Executa diagnóstico avançado do Redis
 * Inclui métricas de performance e circuit breaker
 */
export async function diagnoseRedisIssues(): Promise<{
  status: string;
  issues: string[];
  recommendations: string[];
  metrics: any;
}> {
  const startTime = Date.now();
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  try {
    // Coletar métricas
    const healthMetrics = await redisService.healthCheck();
    const circuitBreakerMetrics = redisService.getCircuitBreakerMetrics();
    const isHealthy = redisService.isHealthy();
    
    // Analisar problemas
    if (healthMetrics.status === 'unhealthy') {
      issues.push('Redis está inacessível ou não responsivo');
      recommendations.push('Verificar se o servidor Redis está rodando');
      recommendations.push('Verificar conectividade de rede');
    }
    
    if (circuitBreakerMetrics.state === 'OPEN') {
      issues.push('Circuit breaker está OPEN - rejeitando operações');
      recommendations.push('Aguardar reset automático ou fazer reset manual');
      recommendations.push('Investigar causa das falhas recorrentes');
    }
    
    if (healthMetrics.hitRate < 0.5 && healthMetrics.hitRate !== -1) {
      issues.push(`Taxa de acertos baixa: ${healthMetrics.hitRate}`);
      recommendations.push('Revisar estratégia de cache');
      recommendations.push('Verificar TTL das chaves');
    }
    
    if (healthMetrics.latency > 100) {
      issues.push(`Latência alta: ${healthMetrics.latency}ms`);
      recommendations.push('Verificar rede entre aplicação e Redis');
      recommendations.push('Monitorar uso de CPU/memória do Redis');
    }
    
    if (circuitBreakerMetrics.failures > 10) {
      issues.push(`Muitas falhas registradas: ${circuitBreakerMetrics.failures}`);
      recommendations.push('Investigar logs de erro');
      recommendations.push('Considerar ajustar configurações do circuit breaker');
    }
    
    const totalTime = Date.now() - startTime;
    
    return {
      status: issues.length === 0 ? 'healthy' : issues.length <= 2 ? 'degraded' : 'critical',
      issues,
      recommendations,
      metrics: {
        diagnosticTime: totalTime,
        redis: healthMetrics,
        circuitBreaker: circuitBreakerMetrics,
        isHealthy,
        timestamp: new Date().toISOString()
      }
    };
  } catch (error) {
    return {
      status: 'error',
      issues: [`Erro durante diagnóstico: ${error.message}`],
      recommendations: ['Verificar conectividade e configuração'],
      metrics: {
        error: error.message,
        timestamp: new Date().toISOString()
      }
    };
  }
}
