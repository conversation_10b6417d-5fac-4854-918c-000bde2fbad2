
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Clock, Target, Wifi, Satellite } from 'lucide-react';
import { LocationData } from '@/types/database';
import { cn } from '@/lib/utils';

interface LocationQuality {
  accuracy: number;
  confidence: 'high' | 'medium' | 'low';
  source: 'gps_high' | 'gps_medium' | 'gps_low' | 'ip_premium' | 'ip_fallback' | 'hybrid';
  qualityScore: number;
}

interface LocationDisplayProps {
  currentLocation: LocationData | null;
  showQuality?: boolean;
  quality?: LocationQuality;
  className?: string;
}

const LocationDisplay: React.FC<LocationDisplayProps> = ({
  currentLocation,
  showQuality = false,
  quality,
  className
}) => {
  if (!currentLocation) return null;

  const getSourceIcon = (source?: string) => {
    switch (source) {
      case 'gps_high':
      case 'gps_medium':
      case 'gps_low':
        return Satellite;
      case 'ip_premium':
      case 'ip_fallback':
        return Wifi;
      case 'hybrid':
        return Target;
      default:
        return MapPin;
    }
  };

  const getSourceLabel = (source?: string) => {
    switch (source) {
      case 'gps_high':
        return 'GPS Alta Precisão';
      case 'gps_medium':
        return 'GPS Médio';
      case 'gps_low':
        return 'GPS Baixo';
      case 'ip_premium':
        return 'IP Premium';
      case 'ip_fallback':
        return 'IP Aproximado';
      case 'hybrid':
        return 'Híbrido';
      default:
        return 'GPS';
    }
  };

  const getConfidenceColor = (confidence?: string) => {
    switch (confidence) {
      case 'high':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const SourceIcon = getSourceIcon(quality?.source);

  return (
    <Card className={cn('absolute bottom-4 left-4 right-4 bg-white/95 backdrop-blur shadow-lg z-10', className)}>
      <CardContent className="p-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <MapPin className="h-4 w-4 text-blue-600 flex-shrink-0" />
              <span className="font-medium text-sm">Localização Atual</span>
              {showQuality && quality && (
                <Badge variant="outline" className="text-xs">
                  <SourceIcon className="h-3 w-3 mr-1" />
                  {getSourceLabel(quality.source)}
                </Badge>
              )}
            </div>
            
            <div className="text-xs text-muted-foreground space-y-1">
              <div className="flex items-center gap-1">
                <span>Lat: {currentLocation.latitude.toFixed(6)}</span>
                <span>•</span>
                <span>Lng: {currentLocation.longitude.toFixed(6)}</span>
              </div>
              
              {currentLocation.address && (
                <div className="truncate">{currentLocation.address}</div>
              )}
              
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{new Date(currentLocation.timestamp).toLocaleString()}</span>
              </div>
            </div>
          </div>

          {showQuality && quality && (
            <div className="flex flex-col items-end gap-1 ml-2">
              <Badge 
                className={cn('text-xs', getConfidenceColor(quality.confidence))}
              >
                {quality.confidence === 'high' ? 'Alta' : 
                 quality.confidence === 'medium' ? 'Média' : 'Baixa'} Confiança
              </Badge>
              
              <div className="text-xs text-muted-foreground text-right">
                <div>±{Math.round(quality.accuracy)}m</div>
                <div>{quality.qualityScore}/100</div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LocationDisplay;
