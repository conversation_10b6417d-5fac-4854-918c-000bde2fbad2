-- =============================================================================
-- INVESTIGAÇÃO: RPC get_student_guardians_from_relationships
-- Data: 27/01/2025
-- Problema: Erro 400 - incompatibilidade de tipos de dados
-- =============================================================================

-- 1. ANÁLISE DA FUNÇÃO RPC ATUAL
-- Verificar estrutura da função que está falhando
SELECT 
  proname as function_name,
  pg_get_function_result(oid) as return_type,
  pg_get_function_arguments(oid) as arguments,
  prosrc as source_code
FROM pg_proc 
WHERE proname = 'get_student_guardians_from_relationships';

-- 2. ESTRUTURA DA TABELA student_guardian_relationships
-- Verificar tipos de dados da tabela para identificar incompatibilidade
SELECT 
  column_name,
  data_type,
  character_maximum_length,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'student_guardian_relationships'
ORDER BY ordinal_position;

-- 3. ESTRUTURA DA TABELA profiles
-- Verificar tipos da tabela profiles que podem estar sendo joinadas
SELECT 
  column_name,
  data_type,
  character_maximum_length,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles'
ORDER BY ordinal_position;

-- 4. DADOS REAIS - Maurício e Luciana
-- Verificar se os dados existem e estão corretos
SELECT 
  sgr.id,
  sgr.student_id,
  sgr.guardian_id,
  sgr.created_at,
  u1.email as student_email,
  u2.email as guardian_email,
  p1.full_name as student_name,
  p2.full_name as guardian_name,
  p1.phone as student_phone,
  p2.phone as guardian_phone
FROM student_guardian_relationships sgr
JOIN auth.users u1 ON sgr.student_id = u1.id
JOIN auth.users u2 ON sgr.guardian_id = u2.id  
JOIN public.profiles p1 ON p1.user_id = u1.id
JOIN public.profiles p2 ON p2.user_id = u2.id
WHERE u1.email = '<EMAIL>'
  AND u2.email = '<EMAIL>';

-- 5. TESTE MANUAL DA QUERY (simular a função RPC)
-- Executar a query que provavelmente está dentro da função RPC
-- Para identificar onde está o problema de tipo
SELECT 
  p.full_name,
  u.email,
  p.phone  -- Esta pode ser a coluna 3 problemática (varchar vs text)
FROM student_guardian_relationships sgr
JOIN auth.users u ON sgr.guardian_id = u.id
JOIN public.profiles p ON p.user_id = u.id
WHERE sgr.student_id = '864a6c0b-4b17-4df7-8709-0c3f7cf0be91';

-- 6. VERIFICAR TODAS AS FUNÇÕES RPC RELACIONADAS
-- Para entender o contexto completo
SELECT 
  proname as function_name,
  pg_get_function_result(oid) as return_type,
  prosrc as source_code
FROM pg_proc 
WHERE proname LIKE '%student%guardian%' 
   OR proname LIKE '%guardian%student%'
ORDER BY proname;

-- 7. TESTE DE TIPOS ESPECÍFICOS
-- Verificar se o problema é especificamente com phone (varchar(255) vs text)
SELECT 
  column_name,
  data_type,
  character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'profiles' 
  AND column_name = 'phone';

-- =============================================================================
-- INSTRUÇÕES DE USO:
-- 1. Execute cada query uma por vez no Supabase SQL Editor
-- 2. Copie os resultados para análise
-- 3. A query #1 mostrará o código da função problemática
-- 4. A query #5 pode reproduzir o erro diretamente
-- ============================================================================= 