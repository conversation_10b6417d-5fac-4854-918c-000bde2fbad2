
/**
 * Utilitários para validação e formatação de CPF brasileiro
 */

export const formatCPF = (value: string): string => {
  // Remove tudo que não é número
  const numbers = value.replace(/\D/g, '');
  
  // Aplica a máscara XXX.XXX.XXX-XX
  return numbers
    .slice(0, 11) // Limita a 11 dígitos
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d{1,2})/, '$1-$2');
};

export const cleanCPF = (cpf: string): string => {
  // Remove tudo que não é número
  return cpf.replace(/\D/g, '');
};

export const validateCPF = (cpf: string): { isValid: boolean; error?: string } => {
  const cleanedCPF = cleanCPF(cpf);
  
  // Se vazio, consideramos válido (campo opcional)
  if (!cleanedCPF) {
    return { isValid: true };
  }
  
  // Deve ter exatamente 11 dígitos
  if (cleanedCPF.length !== 11) {
    return { 
      isValid: false, 
      error: 'CPF deve ter 11 dígitos' 
    };
  }
  
  // Verifica se todos os dígitos são iguais
  if (/^(\d)\1{10}$/.test(cleanedCPF)) {
    return { 
      isValid: false, 
      error: 'CPF não pode ter todos os dígitos iguais' 
    };
  }
  
  // Validação dos dígitos verificadores
  const digits = cleanedCPF.split('').map(Number);
  
  // Primeiro dígito verificador
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += digits[i] * (10 - i);
  }
  let firstDigit = 11 - (sum % 11);
  if (firstDigit >= 10) firstDigit = 0;
  
  if (digits[9] !== firstDigit) {
    return { 
      isValid: false, 
      error: 'CPF inválido - dígitos verificadores incorretos' 
    };
  }
  
  // Segundo dígito verificador
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += digits[i] * (11 - i);
  }
  let secondDigit = 11 - (sum % 11);
  if (secondDigit >= 10) secondDigit = 0;
  
  if (digits[10] !== secondDigit) {
    return { 
      isValid: false, 
      error: 'CPF inválido - dígitos verificadores incorretos' 
    };
  }
  
  return { isValid: true };
};

export const isValidCPFFormat = (cpf: string): boolean => {
  const cleaned = cleanCPF(cpf);
  return cleaned.length === 0 || validateCPF(cpf).isValid;
};
