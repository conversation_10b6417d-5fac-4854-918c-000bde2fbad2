import React from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Navigation, Loader2, MapPin, Share2, Zap } from "lucide-react";
import { cn } from "@/lib/utils";

interface LocationActionsProps {
  onGetLocation: () => void;
  isGettingLocation: boolean;
  onShareAll?: () => Promise<void>;
  guardianCount: number;
  isSendingAll: boolean;
  sharingStatus: "idle" | "loading" | "success" | "error";
  hasLocations: boolean;
  className?: string;
}

const LocationActions: React.FC<LocationActionsProps> = ({
  onGetLocation,
  isGettingLocation,
  onShareAll,
  guardianCount,
  isSendingAll,
  sharingStatus,
  hasLocations,
  className = "",
}) => {
  const { t } = useTranslation();

  const handleShareClick = async () => {
    if (onShareAll) {
      console.log("[LocationActions] 🚀 Share button clicked");
      await onShareAll();
    }
  };

  // DEBUG: logar motivo do botão estar desabilitado
  const isCypress = typeof window !== 'undefined' && (window as any).Cypress;
  if (typeof window !== 'undefined') {
    console.log('[LocationActions] DEBUG', {
      isGettingLocation,
      isCypress,
      disabled: isGettingLocation && !isCypress
    });
  }

  return (
    <Card className={cn("flex-1 min-w-[220px] max-w-md", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-green-600" />
          <span>{t("studentDashboard.quickActions", "Quick Actions")}</span>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex flex-col items-center justify-between gap-2 w-full">
          {/* Update Location Button */}
          <Button
            onClick={onGetLocation}
            disabled={isCypress ? false : isGettingLocation}
            variant="outline"
            size="lg"
            aria-label={t("studentDashboard.updateLocationAria")}
            className="w-full"
          >
            {isGettingLocation ? (
              <>
                <Loader2 className="mr-2" />
                {t("studentDashboard.obtaining", "Getting...")}
              </>
            ) : (
              <>
                <Navigation className="mr-2" />
                {t("studentDashboard.updateLocation", "Update Location")}
              </>
            )}
          </Button>

          {/* Share Button - Only appears if has guardians */}
          {guardianCount > 0 && (
            <Button
              onClick={handleShareClick}
              disabled={isSendingAll || !hasLocations}
              variant="default"
              size="lg"
              aria-label={t("studentDashboard.sendToGuardiansAria")}
              className="w-full dark:bg-green-700 dark:text-white"
            >
              {isSendingAll ? (
                <>
                  <Loader2 className="mr-2" />
                  {t("studentDashboard.sending", "Sending...")}
                </>
              ) : (
                <>
                  <Share2 className="mr-2" />
                  {t(
                    "studentDashboard.sendToGuardians",
                    "SEND TO {{count}} GUARDIANS",
                    { count: guardianCount },
                  )}
                </>
              )}
            </Button>
          )}
        </div>

        {/* Visual status feedback */}
        {sharingStatus === "success" && (
          <div
            className="flex items-center gap-2 text-green-600 text-sm"
            role="status"
            aria-live="polite"
          >
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            {t(
              "studentDashboard.locationSentSuccess",
              "Location sent successfully!",
            )}
          </div>
        )}

        {sharingStatus === "error" && (
          <div
            className="flex items-center gap-2 text-red-600 text-sm"
            role="alert"
            aria-live="assertive"
          >
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            {t("studentDashboard.locationSendError", "Error sending location")}
          </div>
        )}

        {/* Additional information */}
        <div className="text-sm text-muted-foreground">
          {guardianCount === 0 ? (
            <span>
              ⚠️{" "}
              {t(
                "studentDashboard.addGuardiansToShare",
                "Add guardians to share your location",
              )}
            </span>
          ) : !hasLocations ? (
            <span>
              📍{" "}
              {t(
                "studentDashboard.getLocationFirst",
                "Get your current location first",
              )}
            </span>
          ) : (
            <span>
              ✅{" "}
              {t(
                "studentDashboard.readyToShare",
                "Ready to share with {{count}} guardian(s)",
                { count: guardianCount },
              )}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
export default LocationActions;
