
import React from 'react';
import { Plus } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Mail, Check, AlertTriangle } from 'lucide-react';
import { EmptyState } from './guardian/GuardianListStates';

interface Guardian {
  id: string;
  full_name: string | null;
  email: string;
  phone: string | null;
  is_active: boolean;
  created_at: string;
}

interface GuardianListProps {
  guardians: Guardian[];
  loading: boolean;
  onAddClick: () => void;
  onRemoveGuardian: (id: string) => Promise<void>;
  onSendInvite: (email: string, name: string | null) => Promise<void>;
}

const GuardianList: React.FC<GuardianListProps> = ({
  guardians,
  loading,
  onAddClick,
  onRemoveGuardian,
  onSendInvite
}) => {

  const handleRemoveClick = (guardian: Guardian) => {
    // Mostrar mensagem explicativa sobre o novo processo
    alert(`🛡️ NOVO SISTEMA DE PROTEÇÃO

Para sua segurança, agora a remoção de responsáveis funciona assim:

✅ Estudante solicita remoção → Responsável aprova

Por favor, acesse o Student Dashboard e clique em "Solicitar Remoção" no card do responsável.

O responsável receberá um email e uma notificação no dashboard para aprovar ou rejeitar a solicitação.`);
  };

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array(3).fill(0).map((_, i) => (
          <div key={i} className="overflow-hidden">
            <div className="p-6">
              <Skeleton className="h-6 w-32 mb-2" />
              <Skeleton className="h-4 w-64 mb-4" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              <div className="flex justify-between mt-6">
                <Skeleton className="h-9 w-32" />
                <Skeleton className="h-9 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (guardians.length === 0) {
    return <EmptyState onAddClick={onAddClick} />;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {guardians.map((guardian) => (
        <Card key={guardian.id}>
          <CardHeader>
            <CardTitle>{guardian.full_name || "Responsável"}</CardTitle>
            <CardDescription className="flex items-center">
              {guardian.email}
              {guardian.is_active ? (
                <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-800">
                  Ativo
                </span>
              ) : (
                <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-amber-100 text-amber-800">
                  Pendente
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              {guardian.phone && (
                <p>Telefone: {guardian.phone}</p>
              )}
              <p>Adicionado em: {new Date(guardian.created_at).toLocaleDateString()}</p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              variant={guardian.is_active ? "secondary" : "outline"}
              size="sm"
              disabled={guardian.is_active}
              onClick={() => onSendInvite(guardian.email, guardian.full_name)}
              className={guardian.is_active ? "cursor-not-allowed" : ""}
            >
              {guardian.is_active ? (
                <>
                  <Check className="mr-2 h-4 w-4" /> Conectado
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" /> Convidar
                </>
              )}
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleRemoveClick(guardian)}
              className="border-orange-200 text-orange-700 hover:bg-orange-50"
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              Solicitar Remoção
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

export default GuardianList;
