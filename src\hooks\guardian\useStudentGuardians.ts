
import { useState, useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useUser } from '@/contexts/UnifiedAuthContext';

export interface Guardian {
  id: string;
  full_name: string | null;
  email: string;
  phone: string | null;
  is_active: boolean;
  created_at: string;
}

export function useStudentGuardians() {
  const [guardians, setGuardians] = useState<Guardian[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useUser();

  useEffect(() => {
    if (user?.id) {
      fetchGuardians();
    }
  }, [user?.id]);

  const fetchGuardians = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      if (!user?.id) {
        setError('Usuário não autenticado');
        setGuardians([]);
        return;
      }

      // Usar a função RPC existente para buscar guardiões
      const { data, error } = await supabase.rpc(
        'get_student_guardians_from_relationships',
        { p_student_id: user.id }
      );

      if (error) {
        console.error('Error fetching guardians:', error);
        
        if (error.code === 'PGRST116') {
          setError('Nenhum responsável encontrado.');
        } else {
          setError('Não foi possível carregar os responsáveis: ' + error.message);
        }
        setGuardians([]);
      } else {
        console.log('Guardians loaded:', data);
        setGuardians(data || []);
      }
    } catch (error: any) {
      console.error('Error fetching guardians:', error);
      setError('Erro ao buscar os responsáveis');
      setGuardians([]);
    } finally {
      setIsLoading(false);
    }
  };

  const deleteGuardian = async (relationshipId: string) => {
    try {
      // Use student_guardian_relationships em vez de guardians
      const { error } = await supabase
        .from('student_guardian_relationships')
        .delete()
        .eq('id', relationshipId);

      if (error) throw error;

      toast({
        title: "Sucesso",
        description: "Responsável removido com sucesso"
      });

      fetchGuardians();
    } catch (error: any) {
      console.error('Error deleting guardian:', error);
      toast({
        title: "Erro",
        description: error?.message || "Não foi possível remover o responsável",
        variant: "destructive"
      });
    }
  };

  const shareLocation = async (guardianId: string, email: string, name: string) => {
    try {
      // Implementar lógica de compartilhamento
      toast({
        title: "Convite enviado",
        description: `Convite enviado para ${name} (${email})`
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: "Não foi possível enviar o convite",
        variant: "destructive"
      });
    }
  };

  const resendEmail = async (email: string, name: string) => {
    await shareLocation('', email, name);
  };

  return {
    guardians,
    isLoading,
    error,
    fetchGuardians,
    deleteGuardian,
    shareLocation,
    resendEmail
  };
}

