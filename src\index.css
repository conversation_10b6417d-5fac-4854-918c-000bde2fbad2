@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

html, body, #root {
  height: 100%;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Refined Professional Color Palette */
    --background: 250 250% 98%;
    --foreground: 224 12% 12%;
    --card: 0 0% 100%;
    --card-foreground: 224 12% 12%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 12% 12%;
    
    /* Elegant Primary Colors */
    --primary: 234 89% 74%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 234 89% 68%;
    --primary-subtle: 234 89% 96%;
    
    /* Sophisticated Secondary */
    --secondary: 220 14% 96%;
    --secondary-foreground: 224 12% 12%;
    --secondary-hover: 220 14% 92%;
    
    /* Refined Muted Tones */
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;
    
    /* Professional Accent */
    --accent: 220 14% 96%;
    --accent-foreground: 224 12% 12%;
    
    /* Elegant Destructive */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    
    /* Refined Borders & Inputs */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 234 89% 74%;
    
    /* Modern Radius */
    --radius: 0.75rem;
    
    /* Professional Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(234, 89%, 74%), hsl(234, 89%, 84%));
    --gradient-secondary: linear-gradient(135deg, hsl(220, 14%, 96%), hsl(220, 14%, 100%));
    --gradient-card: linear-gradient(145deg, hsl(0, 0%, 100%), hsl(220, 14%, 99%));
    
    /* Elegant Shadows */
    --shadow-soft: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-glow: 0 0 0 1px hsl(234, 89%, 74% / 0.1);
    
    /* Professional Charts */
    --chart-1: 234 89% 74%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    /* Dark Mode - Sophisticated */
    --background: 224 12% 6%;
    --foreground: 210 40% 98%;
    --card: 224 12% 8%;
    --card-foreground: 210 40% 98%;
    --popover: 224 12% 8%;
    --popover-foreground: 210 40% 98%;
    
    /* Dark Primary */
    --primary: 234 89% 74%;
    --primary-foreground: 224 12% 6%;
    --primary-hover: 234 89% 68%;
    --primary-subtle: 234 89% 15%;
    
    /* Dark Secondary */
    --secondary: 224 12% 12%;
    --secondary-foreground: 210 40% 98%;
    --secondary-hover: 224 12% 16%;
    
    /* Dark Muted */
    --muted: 224 12% 12%;
    --muted-foreground: 215 20% 65%;
    
    /* Dark Accent */
    --accent: 224 12% 12%;
    --accent-foreground: 210 40% 98%;
    
    /* Dark Destructive */
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    
    /* Dark Borders */
    --border: 224 12% 16%;
    --input: 224 12% 16%;
    --ring: 234 89% 74%;
    
    /* Dark Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(234, 89%, 74%), hsl(234, 89%, 84%));
    --gradient-secondary: linear-gradient(135deg, hsl(224, 12%, 12%), hsl(224, 12%, 16%));
    --gradient-card: linear-gradient(145deg, hsl(224, 12%, 8%), hsl(224, 12%, 10%));
    
    /* Dark Shadows */
    --shadow-soft: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
    --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-glow: 0 0 0 1px hsl(234, 89%, 74% / 0.2);
    
    /* Dark Charts */
    --chart-1: 234 89% 74%;
    --chart-2: 173 58% 55%;
    --chart-3: 197 37% 40%;
    --chart-4: 43 74% 82%;
    --chart-5: 27 87% 83%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.01em;
  }
}

/* Modern Glassmorphism Components */
@layer components {
  .glass-card {
    @apply backdrop-blur-md bg-white/80 dark:bg-card/80 border border-white/20 dark:border-border/30;
    box-shadow: var(--shadow-large), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .glass-button {
    @apply backdrop-blur-md bg-white/10 hover:bg-white/20 border border-white/20;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .professional-card {
    @apply bg-gradient-to-br from-white to-muted/50 dark:from-card dark:to-muted/50;
    @apply border border-border/60 dark:border-border/60;
    @apply shadow-lg dark:shadow-2xl;
    box-shadow: var(--shadow-large);
  }
  
  .elegant-button {
    @apply relative overflow-hidden;
    @apply bg-gradient-to-r from-primary to-primary-hover;
    @apply shadow-lg hover:shadow-xl;
    @apply transform hover:scale-[1.02] active:scale-[0.98];
    @apply transition-all duration-300 ease-out;
  }
  
  .elegant-button::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0;
    @apply transform -skew-x-12 -translate-x-full;
    @apply transition-transform duration-700;
  }
  
  .elegant-button:hover::before {
    @apply translate-x-full;
  }
  
  .refined-input {
    @apply bg-white/50 dark:bg-card/50;
    @apply border border-border/60 dark:border-border/60;
    @apply focus:border-primary/60 focus:ring-2 focus:ring-primary/20;
    @apply backdrop-blur-sm;
    @apply transition-all duration-300;
  }
  
  .modern-badge {
    @apply px-3 py-1 rounded-full text-xs font-medium;
    @apply bg-gradient-to-r from-primary/10 to-primary/20;
    @apply border border-primary/20;
    @apply text-primary-foreground;
  }
}

/* Configurações de Acessibilidade */
:root {
  --font-scale: 1;
}

/* Aplicar escala de fonte */
* {
  font-size: calc(var(--base-font-size, 1rem) * var(--font-scale));
}

/* Alto contraste */
.high-contrast {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --card: 0 0% 10%;
  --card-foreground: 0 0% 100%;
  --primary: 0 0% 100%;
  --primary-foreground: 0 0% 0%;
  --secondary: 0 0% 20%;
  --secondary-foreground: 0 0% 100%;
  --muted: 0 0% 20%;
  --muted-foreground: 0 0% 80%;
  --border: 0 0% 30%;
}

/* Movimento reduzido */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Layout compacto */
.layout-compact {
  --spacing-sm: 0.25rem;
  --spacing-md: 0.5rem;
  --spacing-lg: 0.75rem;
}

/* Layout espaçoso */
.layout-spacious {
  --spacing-sm: 0.75rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2.5rem;
}

/* Safe Area Support for iOS */
@supports (padding: max(0px)) {
  .pt-safe-area-top {
    padding-top: max(env(safe-area-inset-top), 1rem);
  }

  .pb-safe-area-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 1rem);
  }

  .pl-safe-area-left {
    padding-left: max(env(safe-area-inset-left), 1rem);
  }

  .pr-safe-area-right {
    padding-right: max(env(safe-area-inset-right), 1rem);
  }

  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* iOS specific optimizations */
@supports (-webkit-touch-callout: none) {
  /* Prevent zoom on inputs */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px !important;
  }

  /* Improve touch responsiveness */
  button,
  [role="button"],
  .button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
}

/* Android specific optimizations */
@media screen and (max-width: 640px) {
  /* Better touch targets */
  button,
  [role="button"],
  a,
  input,
  textarea,
  select {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Responsive utilities */
@layer utilities {
  .touch-manipulation {
    touch-action: manipulation;
  }

  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Better focus visibility on mobile */
  .focus-mobile {
    @apply focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:outline-none;
  }

  /* Responsive text truncation */
  .truncate-mobile {
    @apply truncate max-w-[120px] sm:max-w-[160px] md:max-w-full;
  }

  /* Optimized transitions for mobile */
  .transition-mobile {
    transition: all 0.2s ease-out;
  }
}

/* Print optimizations */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break-before {
    break-before: page;
  }

  .print-break-after {
    break-after: page;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .focus\:ring-2:focus {
    --tw-ring-offset-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Indicadores de Status de Rede - Resiliência para ERR_NETWORK_CHANGED */
.network-offline {
  position: relative;
}

.network-offline::before {
  content: "Sem conexão";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgb(239 68 68); /* text-red-500 */
  color: white;
  text-align: center;
  padding: 4px;
  font-size: 0.85rem;
  z-index: 9999;
  font-weight: 500;
}

/* Animação de reconexão */
.network-reconnecting {
  position: relative;
}

.network-reconnecting::before {
  content: "Reconectando...";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgb(249 115 22); /* text-orange-500 */
  color: white;
  text-align: center;
  padding: 4px;
  font-size: 0.85rem;
  z-index: 9999;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.map-highlight {
  border: 2px solid hsl(var(--primary));
  box-shadow: var(--shadow-glow);
  border-radius: var(--radius);
}

/* Subtle Map Background */
@layer components {
  .subtle-map-background {
    position: relative;
    background: linear-gradient(135deg, hsl(234, 89%, 96%) 0%, hsl(220, 14%, 98%) 100%);
  }
  
  .subtle-map-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 20%, hsl(234, 89%, 90%) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, hsl(234, 89%, 90%) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, hsl(220, 14%, 95%) 0%, transparent 30%),
      linear-gradient(90deg, transparent 79px, hsl(220, 13%, 91%) 80px, hsl(220, 13%, 91%) 81px, transparent 82px),
      linear-gradient(hsl(220, 13%, 91%) 0.5px, transparent 0.5px);
    background-size: 
      400px 400px,
      600px 600px,
      200px 200px,
      80px 80px,
      20px 20px;
    opacity: 0.3;
    z-index: -1;
  }
  
  .subtle-map-background.dark::before {
    background-image: 
      radial-gradient(circle at 20% 20%, hsl(234, 89%, 20%) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, hsl(234, 89%, 20%) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, hsl(224, 12%, 15%) 0%, transparent 30%),
      linear-gradient(90deg, transparent 79px, hsl(224, 12%, 16%) 80px, hsl(224, 12%, 16%) 81px, transparent 82px),
      linear-gradient(hsl(224, 12%, 16%) 0.5px, transparent 0.5px);
    opacity: 0.2;
  }
  
  .content-overlay {
    position: relative;
    z-index: 1;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
  
  .dark .content-overlay {
    background: rgba(8, 8, 15, 0.85);
  }

  @media (max-width: 640px) {
    .content-overlay {
      border-radius: 0.75rem;
    }
  }
}

/* Professional Animations and Transitions */
@layer utilities {
  .elegant-fade-in {
    @apply animate-in fade-in-0 duration-500;
  }
  
  .elegant-slide-up {
    @apply animate-in slide-in-from-bottom-4 duration-300;
  }
  
  .elegant-scale-in {
    @apply animate-in zoom-in-95 duration-200;
  }
  
  .hover-lift {
    @apply transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  }
  
  .focus-ring {
    @apply focus:ring-2 focus:ring-primary/20 focus:ring-offset-2 focus:outline-none;
  }
  
  .smooth-transition {
    @apply transition-all duration-300 ease-out;
  }
}

/* Modern Scrollbars */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground)) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.5);
}

.map-responsive {
  min-height: 300px;
  height: 60vh;
}

@media (orientation: landscape) and (max-width: 1024px) {
  .map-responsive {
    height: 70vh;
  }
}

.parent-dashboard {
  display: flex;
  flex-direction: column;
}

.map-section {
  width: 100%;
  height: 60vh;
  position: relative;
  z-index: 1;
}

.student-info-section {
  background: #fff;
  padding: 1rem;
  border-top: 1px solid #ddd;
  max-height: 40vh;
  overflow-y: auto;
}

@media (min-width: 768px) {
  .parent-dashboard {
    flex-direction: row;
    height: calc(100vh - 60px);
  }

  .map-section {
    flex: 2;
    height: 100%;
  }

  .student-info-section {
    flex: 1;
    border-top: none;
    border-left: 1px solid #ddd;
    max-height: none;
  }
}

