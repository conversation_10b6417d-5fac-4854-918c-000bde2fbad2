
import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertTriangle, CheckCircle, XCircle, Clock, Info, UserCheck } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useAccountDeletionRequests } from '@/hooks/useAccountDeletionRequests';
import { useTranslation } from 'react-i18next';

const AccountDeletionRequestsList: React.FC = React.memo(() => {
  const { t } = useTranslation();
  const { requests, isLoading, isProcessing, processRequest } = useAccountDeletionRequests();
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null);
  const [guardianNotes, setGuardianNotes] = useState('');

  const pendingRequests = useMemo(() => 
    requests?.filter(req => req.status === 'pending') || [], 
    [requests]
  );

  console.log('[COMPONENTE] AccountDeletionRequestsList rendering');
  console.log('[COMPONENTE] isLoading:', isLoading);
  console.log('[COMPONENTE] requests:', requests);
  console.log('[COMPONENTE] pendingRequests:', pendingRequests);

  const handleApprove = useCallback(async (requestId: string, notes: string) => {
    await processRequest(requestId, 'approve', notes);
    setSelectedRequest(null);
    setGuardianNotes('');
  }, [processRequest]);

  const handleReject = useCallback(async (requestId: string) => {
    await processRequest(requestId, 'reject');
  }, [processRequest]);

  const handleNotesChange = useCallback((value: string) => {
    setGuardianNotes(value);
  }, []);

  const handleSelectRequest = useCallback((requestId: string) => {
    setSelectedRequest(requestId);
  }, []);

  const handleCancelRequest = useCallback(() => {
    setSelectedRequest(null);
  }, []);

  // Loading state
  if (isLoading) {
    console.log('[COMPONENTE] Renderizando estado de loading');
    return (
      <Card className="bg-transparent border-0 shadow-none">
        <CardHeader className="bg-transparent border-0 shadow-none">
          <CardTitle className="flex items-center gap-2 bg-transparent">
            <Clock className="h-5 w-5 text-blue-600" />
            {t('lgpd.deletionRequests.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="bg-transparent border-0 shadow-none">
          <div className="text-center py-8">
            <Clock className="h-8 w-8 mx-auto text-gray-400 mb-2" />
            <p className="text-gray-600">Carregando solicitações...</p>
            <p className="text-sm text-gray-500 mt-1">Aguarde enquanto verificamos as solicitações pendentes</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Empty state
  if (!requests || requests.length === 0 || pendingRequests.length === 0) {
    console.log('[COMPONENTE] Renderizando estado vazio');
    return (
      <Card className="bg-transparent border-0 shadow-none">
        <CardHeader className="bg-transparent border-0 shadow-none">
          <CardTitle className="flex items-center gap-2 bg-transparent">
            <UserCheck className="h-5 w-5 text-green-600" />
            {t('lgpd.deletionRequests.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="bg-transparent border-0 shadow-none">
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">{t('lgpd.deletionRequests.noPending')}</h3>
            <p className="text-gray-600 mb-1">{t('lgpd.deletionRequests.noPendingDescription')}</p>
            <p className="text-sm text-gray-500">{t('lgpd.deletionRequests.noPendingHint')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Main content
  return (
    <Card className="bg-transparent border-0 shadow-none">
      <CardHeader className="bg-transparent border-0 shadow-none">
        <CardTitle className="flex items-center gap-2 bg-transparent">
          <AlertTriangle className="h-5 w-5 text-orange-600" />
          {t('lgpd.deletionRequests.title')} ({pendingRequests.length})
        </CardTitle>
        {pendingRequests.length > 0 && (
          <div className="flex items-start gap-2 p-3 bg-transparent border-0 rounded-lg">
            <Info className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-orange-800">{t('lgpd.deletionRequests.actionRequired')}</p>
              <p className="text-orange-700">
                {pendingRequests.length === 1 
                  ? `${t('lgpd.deletionRequests.studentRequested', { count: pendingRequests.length })}` 
                  : `${t('lgpd.deletionRequests.studentsRequested', { count: pendingRequests.length })}`
                } 
                {t('lgpd.deletionRequests.reviewPrompt')}
              </p>
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent className="bg-transparent border-0 shadow-none">
        <div className="space-y-4">
          {pendingRequests.map((request) => (
            <div key={request.id} className="border-0 rounded-lg p-4 bg-transparent">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-gray-900">{request.student_name}</h4>
                    <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                      {t('lgpd.deletionRequests.pending')}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">{request.student_email}</p>
                  <p className="text-sm text-gray-500">
                    📅 {t('lgpd.deletionRequests.requestedAt', { date: new Date(request.requested_at) })}
                  </p>
                </div>
              </div>
              
              {request.reason && (
                <div className="mb-3 p-3 bg-transparent rounded border-0">
                  <p className="text-sm">
                    <strong className="text-gray-800">{t('lgpd.deletionRequests.reason')}:</strong>
                  </p>
                  <p className="text-sm text-gray-700 mt-1 italic">"{request.reason}"</p>
                </div>
              )}

              <div className="flex gap-2 pt-2 border-t border-orange-200">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button 
                      variant="destructive" 
                      size="sm"
                      className="flex-1"
                      onClick={() => handleSelectRequest(request.id)}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      {t('lgpd.deletionRequests.approve')}
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-orange-500" />
                        {t('lgpd.deletionRequests.confirmApprovalTitle')}
                      </DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="p-4 bg-transparent border-0 rounded-lg">
                        <p className="font-medium text-red-800 mb-2">{t('lgpd.deletionRequests.irreversibleAction')}</p>
                        <p className="text-red-700 text-sm">
                          {t('lgpd.deletionRequests.irreversibleDescription', { studentName: request.student_name })}
                        </p>
                      </div>
                      <div>
                        <Label htmlFor="notes">{t('lgpd.deletionRequests.notesLabel')}</Label>
                        <Input
                          id="notes"
                          value={guardianNotes}
                          onChange={(e) => handleNotesChange(e.target.value)}
                          placeholder={t('lgpd.deletionRequests.notesPlaceholder')}
                          className="mt-1"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          {t('lgpd.deletionRequests.notesDescription')}
                        </p>
                      </div>
                      <div className="flex gap-2 justify-end pt-4 border-t">
                        <Button variant="outline" onClick={handleCancelRequest}>
                          {t('lgpd.deletionRequests.cancel')}
                        </Button>
                        <Button 
                          variant="destructive"
                          disabled={isProcessing}
                          onClick={() => handleApprove(request.id, guardianNotes)}
                        >
                          {isProcessing ? t('lgpd.deletionRequests.processing') : t('lgpd.deletionRequests.confirmDeletion')}
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm" className="flex-1">
                      <XCircle className="h-4 w-4 mr-2" />
                      {t('lgpd.deletionRequests.reject')}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle className="flex items-center gap-2">
                        <XCircle className="h-5 w-5 text-blue-500" />
                        {t('lgpd.deletionRequests.rejectRequestTitle')}
                      </AlertDialogTitle>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <AlertDialogDescription>
                          {t('lgpd.deletionRequests.rejectDescription', { studentName: request.student_name })}
                        </AlertDialogDescription>
                        <div className="p-3 bg-transparent border-0 rounded">
                          <div className="text-blue-800 text-sm">
                            ℹ️ <strong>{t('lgpd.deletionRequests.rejectWhatHappens')}:</strong>
                          </div>
                          <ul className="text-blue-700 text-sm mt-1 list-disc list-inside">
                            <li>{t('lgpd.deletionRequests.rejectAccountActive')}</li>
                            <li>{t('lgpd.deletionRequests.rejectNotification')}</li>
                            <li>{t('lgpd.deletionRequests.rejectNewRequest')}</li>
                          </ul>
                        </div>
                      </div>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{t('lgpd.deletionRequests.cancel')}</AlertDialogCancel>
                      <AlertDialogAction
                        disabled={isProcessing}
                        onClick={() => handleReject(request.id)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {isProcessing ? t('lgpd.deletionRequests.processing') : t('lgpd.deletionRequests.confirmRejection')}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
});

AccountDeletionRequestsList.displayName = 'AccountDeletionRequestsList';

export default AccountDeletionRequestsList;
