-- =====================================================
-- CONCEDER PERMISSÕES CORRETAS PARA TABELA GUARDIANS
-- Erro 403 persiste porque faltam permissões PostgreSQL
-- =====================================================

-- Conceder permissões SELECT, INSERT, UPDATE, DELETE para authenticated
GRANT SELECT, INSERT, UPDATE, DELETE ON public.guardians TO authenticated;

-- Conceder permissões SELECT para anon (se necessário)
GRANT SELECT ON public.guardians TO anon;

-- Conceder permissões na sequência também (se houver)
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;

-- <PERSON><PERSON><PERSON><PERSON> que permissões sejam aplicadas em futuras tabelas
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO anon;
