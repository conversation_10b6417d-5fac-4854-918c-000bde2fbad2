import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import chokidar from 'chokidar';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Lista de arquivos protegidos
const protectedFiles = [
  'src/lib/map/mapbox-config.ts',
  'src/components/StudentLocationMap.tsx',
  'src/main.tsx',
  'src/env.ts'
];

// Lista de diretórios protegidos
const protectedDirs = [
  'src/lib/map',
  'src/components',
  'src/env'
];

// Função para verificar se um arquivo está protegido
function isProtectedFile(filePath) {
  const normalizedPath = path.normalize(filePath);
  
  // Verifica se o arquivo está na lista de arquivos protegidos
  if (protectedFiles.some(file => normalizedPath.includes(file))) {
    return true;
  }
  
  // Verifica se o arquivo está em um diretório protegido
  return protectedDirs.some(dir => normalizedPath.includes(dir));
}

// Função para fazer backup de um arquivo
function backupFile(filePath) {
  const backupPath = `${filePath}.backup`;
  fs.copyFileSync(filePath, backupPath);
  console.log(`[Protect] Backup criado: ${backupPath}`);
}

// Função para restaurar um arquivo do backup
function restoreFile(filePath) {
  const backupPath = `${filePath}.backup`;
  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, filePath);
    fs.unlinkSync(backupPath);
    console.log(`[Protect] Arquivo restaurado: ${filePath}`);
  }
}

// Função para verificar mudanças em um arquivo
function checkFileChanges(filePath) {
  if (isProtectedFile(filePath)) {
    console.log(`[Protect] Arquivo protegido modificado: ${filePath}`);
    console.log('Este arquivo requer revisão manual antes de modificações.');
    
    // Restaura o arquivo se houver um backup
    restoreFile(filePath);
    return false;
  }
  return true;
}

// Inicializa o watcher
const watcher = chokidar.watch(['src/**/*'], {
  ignored: /(^|[\/\\])\../, // Ignora arquivos ocultos
  persistent: true
});

// Cria backups dos arquivos protegidos
protectedFiles.forEach(file => {
  if (fs.existsSync(file)) {
    backupFile(file);
  }
});

// Monitora mudanças
watcher
  .on('change', (path) => checkFileChanges(path))
  .on('unlink', (path) => {
    if (isProtectedFile(path)) {
      console.log(`[Protect] Tentativa de deletar arquivo protegido: ${path}`);
      restoreFile(path);
    }
  });

console.log('[Protect] Monitoramento de arquivos protegidos iniciado');
console.log('Arquivos protegidos:', protectedFiles);
console.log('Diretórios protegidos:', protectedDirs); 