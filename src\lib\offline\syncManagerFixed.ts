
import { supabase } from '@/integrations/supabase/client';

interface OfflineAction {
  id: string;
  type: string;
  data: any;
  timestamp: number;
}

class SyncManagerFixed {
  private actions: OfflineAction[] = [];

  async syncGuardianActions() {
    const guardianActions = this.actions.filter(action => 
      action.type.includes('guardian')
    );

    for (const action of guardianActions) {
      try {
        if (action.type === 'add_guardian') {
          // Use family invitation instead of direct insertion
          await supabase.rpc('send_family_invitation', {
            p_guardian_email: action.data.email
          });
        } else if (action.type === 'remove_guardian') {
          // Use student_guardian_relationships instead
          await supabase
            .from('student_guardian_relationships')
            .delete()
            .eq('id', action.data.id);
        }
        
        // Remove synced action
        this.actions = this.actions.filter(a => a.id !== action.id);
      } catch (error) {
        console.error('Sync error:', error);
      }
    }
  }

  addAction(type: string, data: any) {
    this.actions.push({
      id: Date.now().toString(),
      type,
      data,
      timestamp: Date.now()
    });
  }

  async syncAll() {
    await this.syncGuardianActions();
  }
}

export const syncManager = new SyncManagerFixed();

