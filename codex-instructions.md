# Instruções para Revisão do Codex

Este documento define um checklist técnico para o agente Codex revisar a aplicação hospedada em **https://sistema-monitore.com.br/**.

## 1. <PERSON><PERSON><PERSON> (Student / Guardian)
- Verificar a implementação completa de ambos os perfis.
- Conferir se campos obrigatórios e rótulos estão corretos e distintos.
- Garantir que dados e comportamentos não se misturam entre perfis.
- Testar autenticação, recuperação de senha e vinculação de contas (student ↔ guardian).

## 2. Internacionalização (i18n)
- <PERSON><PERSON><PERSON> que todas as páginas possuam traduções `pt-BR` e `en-GB`.
- Confirmar que a escolha de idioma feita na tela inicial persiste sem botões extras de tradução.
- Sinalizar existência de botões redundantes de idioma após a escolha inicial.

## 3. Conformidade Legal Dinâmica
- Exibir LGPD para usuários brasileiros e UK GDPR para usuários do Reino Unido.
- Checar textos de consentimento adequados em formulários.
- Verificar links "See full policy" ou "Ver política completa" para `/privacy/lgpd` ou `/privacy/uk-gdpr`.

## 4. Consistência Visual e Funcional
- Testar funcionamento de todos os botões em ambas as versões.
- Conferir equivalência de layout entre idiomas e ausência de quebras.
- Garantir que o mapa funciona normalmente em `pt-BR` e `en-GB`.
- Validar que cada dashboard (student, guardian) apresenta apenas conteúdos do perfil correto.

## Entregáveis Esperados
1. Relatório de inconsistências entre perfis.
2. Lista de páginas ou componentes sem tradução.
3. Sinalização de botões de tradução redundantes.
4. Alertas de quebra de conformidade legal.
5. Sugestões para unificação do controle global de idioma.

## Páginas Principais para Teste
| Página | Verificações |
| ------ | ------------ |
| `/register` | Campos por perfil, legislação correta, tradução completa |
| `/login` | Traduções completas e sem botão de idioma redundante |
| `/parent-dashboard` | Conteúdo exclusivo para responsáveis, i18n aplicado |
| `/student-dashboard` | Conteúdo do estudante e mapa funcional |
| `/ajuda/responsavel` | Tradução coerente com perfil e idioma |
| `/ajuda/estudante` | Mesma estrutura, textos e acessibilidade |
