-- ==========================================
-- REABILITAR RLS APÓS TESTE
-- Data: 24/06/2025
-- Objetivo: Restaurar segurança da tabela
-- ==========================================

-- 1. REABILITAR RLS
ALTER TABLE public.account_deletion_requests ENABLE ROW LEVEL SECURITY;

-- 2. VERIFICAR SE RLS FOI REABILITADO
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    pg_get_userbyid(relowner) as owner
FROM pg_tables pt
JOIN pg_class pc ON pc.relname = pt.tablename
WHERE pt.tablename = 'account_deletion_requests';

-- 3. CONFIRMAR POLÍTICAS ATIVAS
SELECT 
    policyname,
    permissive,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'account_deletion_requests'
ORDER BY policyname; 