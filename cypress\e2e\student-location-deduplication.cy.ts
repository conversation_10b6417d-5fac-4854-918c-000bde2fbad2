describe('Student Location Deduplication', () => {
  const studentEmail = '<EMAIL>';
  const studentPassword = '4EG8GsjBT5KjD3k';

  it('should not allow duplicate locations within 60 seconds at the same place', () => {
    // 1. Login com mock de geolocalização de alta precisão
    cy.visit('/login', {
      onBeforeLoad(win) {
        cy.stub(win.navigator.geolocation, 'getCurrentPosition').callsFake((cb) => {
          setTimeout(() => {
            cb({
              coords: {
                latitude: -3.119028,
                longitude: -60.021731,
                accuracy: 5, // precisão alta
              }
            });
          }, 100);
        });
        cy.stub(win.navigator.geolocation, 'watchPosition').callsFake((cb) => {
          setTimeout(() => {
            cb({
              coords: {
                latitude: -3.119028,
                longitude: -60.021731,
                accuracy: 5, // precisão alta
              }
            });
          }, 100);
          return 1;
        });
      }
    });
    cy.get('input[type="email"]').type(studentEmail);
    cy.get('input[type="password"]').type(studentPassword);
    cy.get('button[type="submit"]').click();

    // 2. Espera redirecionar para dashboard
    cy.url().should('include', '/student-dashboard');

    // 3. Aciona envio de localização (duas vezes) - seletor robusto
    cy.get('button, [role="button"]').filter((i, el) => {
      const text = el.innerText || '';
      const aria = el.getAttribute('aria-label') || '';
      return (
        /Atualizar Localização|Update Location/i.test(text) ||
        /Atualizar Localização|Update Location/i.test(aria) ||
        /Atualize sua localização atual|Update your current location/i.test(aria)
      );
    }).first().should('not.be.disabled').click();

    // Aguarda resposta/sucesso
    cy.contains(/localização salva|compartilhada|enviada|sucesso|recorded|updated/i, { timeout: 10000 });

    // 4. Tenta enviar de novo (deve ser deduplicado)
    cy.get('button, [role="button"]').filter((i, el) => {
      const text = el.innerText || '';
      const aria = el.getAttribute('aria-label') || '';
      return (
        /Atualizar Localização|Update Location/i.test(text) ||
        /Atualizar Localização|Update Location/i.test(aria) ||
        /Atualize sua localização atual|Update your current location/i.test(aria)
      );
    }).first().should('not.be.disabled').click();

    // Aguarda resposta/sucesso
    cy.contains(/localização salva|compartilhada|enviada|sucesso|recorded|updated/i, { timeout: 10000 });

    // 5. Verifica que só existe uma localização para o instante/local
    // (Ajuste aqui conforme o seletor real da lista de localizações)
    cy.get('[data-cy="location-list"], .location-list, ul').then($list => {
      // Conta quantos itens têm a latitude/longitude mockada
      const items = $list.find(':contains("-3.119028")');
      expect(items.length).to.be.lte(1);
    });
  });
}); 