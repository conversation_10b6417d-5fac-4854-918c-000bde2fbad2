# Debug Protocols - Locate-Family-Connect

## 🔍 Protocolo Anti-Quebra

O protocolo anti-quebra foi desenvolvido para garantir zero perda de funcionalidade durante a manutenção ou implementação de novas features.

### 🛡️ Filosofia Operacional

> **"Mudanças VERIFICADAS são mudanças SEGURAS"**

### Princípios do Protocolo

- **VERIFICAR** antes de concluir que existe um problema
- **TESTAR** em ambiente seguro antes de modificar
- **EVIDENCIAR** a causa raiz antes de implementar soluções
- **MINIMIZAR** o escopo de alterações
- **REVISAR** após implementação

## ⚠️ Verificação Obrigatória (Pré-Diagnóstico)

```bash
# 1. Verificação de integridade básica
ls src/pages/ && npm run build && npm run dev

# 2. Verificação de porta antes de iniciar dev server
npx kill-port 8080 && npm run dev

# 3. Busca por funcionalidades similares
find src/ -name "*.tsx" | grep -i [functionality]
grep -r "similar_function" src/
```

## 🔄 Fluxo de Debug Estruturado

### Etapa 1: Coleta de Informações

1. **Console do Navegador**
   - Verificar erros no console
   - Analisar requisições de rede (Network tab)
   - Verificar estado da aplicação (Application tab)

2. **Logs do Servidor**
   - Verificar logs do Supabase
   - Analisar erros em Edge Functions
   - Revisar registros de autenticação

3. **Estado da Aplicação**
   - Utilizar React DevTools para inspecionar estado
   - Verificar contextos (Auth, etc.)
   - Analisar props e lifecycle

### Etapa 2: Isolamento do Problema

1. **Técnica de Bisecção**
   - Comentar metade do código suspeito
   - Se o problema persistir, está na outra metade
   - Repetir até isolar a causa

2. **Reprodução Controlada**
   - Criar um caso de teste mínimo
   - Documentar passos exatos para reproduzir
   - Verificar em diferentes ambientes/navegadores

### Etapa 3: Diagnóstico Profundo

1. **Problemas de Autenticação**
   - Verificar fluxo PKCE em `UnifiedAuthContext.tsx`
   - Validar tokens e refresh
   - Verificar redirecionamentos baseados em perfil

2. **Problemas no Mapa**
   - Validar API key do MapBox
   - Verificar permissões de geolocalização
   - Inspecionar hook `useMapInitialization`
   - Verificar processos de carregamento assíncrono

3. **Problemas de Email/Notificações**
   - Executar script de teste: `/scripts/test-resend.mjs`
   - Verificar configuração do domínio
   - Validar templates e dados dinâmicos

4. **Problemas de Banco de Dados**
   - Validar políticas RLS
   - Verificar queries e relacionamentos
   - Testar com `/scripts/test-db-connection.js`

## 🩺 Diagnósticos Específicos

### Login/Autenticação

**Sintomas e Causas:**
- **Redirecionamento em loop**: Inconsistência entre token e perfil de usuário
- **Falha ao iniciar sessão**: Erro nas políticas RLS ou credenciais inválidas
- **Refresh token não funciona**: Expiração de cookies ou invalidação da sessão

**Soluções:**
1. Validar fluxo PKCE em `UnifiedAuthContext.tsx`
2. Verificar armazenamento seguro de tokens
3. Testar com usuário alternativo para isolar problema de conta
4. Verificar sincronização `auth.users` com tabela de perfis

### MapBox

**Sintomas e Causas:**
- **Mapa não carrega**: API key inválida, erro de inicialização
- **Localização imprecisa**: Permissões negadas, GPS indisponível
- **Erro no compartilhamento**: Falha na Edge Function `share-location`

**Soluções:**
1. Verificar console para erros específicos do MapBox
2. Validar inicialização do mapa em `useMapInitialization`
3. Testar permissões de geolocalização
4. Verificar integração com hooks de localização

### Edge Functions

**Sintomas e Causas:**
- **Timeout**: Processamento muito longo, falha de conexão
- **CORS errors**: Configuração incorreta de headers
- **Acesso negado**: Problemas com autenticação JWT

**Soluções:**
1. Verificar logs no Dashboard do Supabase
2. Testar localmente com Supabase CLI
3. Verificar configuração de CORS e segurança
4. Reiniciar e reimplementar em caso de corrupção

## 🧰 Ferramentas de Debug

### Console Enriquecido

```typescript
// Console avançado com contexto
const debugLog = (context, data) => {
  console.group(`[DEBUG] ${context}`);
  console.log("Data:", data);
  console.trace("Call stack:");
  console.groupEnd();
};

// Visualizador de componentes
const debugRender = (Component, props) => {
  console.log(`[RENDER] ${Component.name}`, { props });
  return <Component {...props} />;
};
```

### Monitoramento de Tempo Real

```typescript
// Hook para monitorar alterações em estado
const useStateWithLogging = (initialState) => {
  const [state, setState] = useState(initialState);
  const loggedSetState = (newState) => {
    console.log(`[STATE CHANGE] Previous:`, state);
    console.log(`[STATE CHANGE] New:`, newState);
    setState(newState);
  };
  return [state, loggedSetState];
};
```

## 🆘 Procedimento de Emergência

### Rollback Seguro

Em caso de problema crítico em produção:

1. **Reversão Imediata**
   ```bash
   git reset --hard HEAD~1  # Reverter para commit anterior
   git push -f              # CUIDADO: Usar apenas em emergências
   ```

2. **Restauração de Backup**
   - Utilizar snapshot mais recente do banco
   - Restaurar Edge Functions para versão estável

3. **Comunicação**
   - Informar stakeholders sobre o incidente
   - Documentar evento para análise posterior

## 📝 Template de Documentação de Bugs

```markdown
## Descrição do Bug
[Descreva o comportamento observado]

## Passos de Reprodução
1. [Passo 1]
2. [Passo 2]
3. ...

## Comportamento Esperado
[Descreva o comportamento correto]

## Ambiente
- Navegador:
- Sistema Operacional:
- Usuário (tipo):

## Logs/Screenshots
[Links ou imagens relevantes]

## Causa Raiz
[Após análise]

## Solução Implementada
[Descrever a correção]
```

## 📚 Referências

- [Documentação do Supabase para Debug](https://supabase.io/docs/guides/troubleshooting)
- [React DevTools](https://reactjs.org/blog/2019/08/15/new-react-devtools.html)
- [MapBox Troubleshooting](https://docs.mapbox.com/help/troubleshooting/)
