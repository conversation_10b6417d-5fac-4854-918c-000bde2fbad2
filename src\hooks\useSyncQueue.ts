
/**
 * Hook for sync queue management
 * Provides React integration for the sync system
 */

import { useState, useEffect, useCallback } from 'react';
import { syncQueue, SyncAction, SyncActionType, SyncQueueStats } from '@/lib/offline/sync-queue';
import { syncManager } from '@/lib/offline/sync-manager';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { useUser } from '@/contexts/UnifiedAuthContext';

interface SyncStats {
  lastSyncTime?: Date;
  successRate: number;
  totalSynced: number;
  totalFailed: number;
}

interface UseSyncQueueReturn {
  // Queue operations
  enqueueAction: (
    type: SyncActionType,
    data: any,
    priority?: SyncAction['priority'],
    maxRetries?: number
  ) => Promise<string>;
  
  // Stats and status
  queueStats: SyncQueueStats | null;
  syncStats: SyncStats | null;
  isInitialized: boolean;
  isSyncing: boolean;
  
  // Manual controls
  forceSync: () => Promise<void>;
  clearQueue: () => Promise<void>;
  clearCompleted: () => Promise<number>;
  
  // Error handling
  error: string | null;
}

export function useSyncQueue(): UseSyncQueueReturn {
  const { user } = useUser();
  const { isOnline } = useNetworkStatus();
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [queueStats, setQueueStats] = useState<SyncQueueStats | null>(null);
  const [syncStats, setSyncStats] = useState<SyncStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Initialize sync system
  useEffect(() => {
    const initSync = async () => {
      try {
        setError(null);
        await syncQueue.init();
        setIsInitialized(true);
        console.log('[useSyncQueue] Sync queue initialized');
      } catch (err) {
        console.error('[useSyncQueue] Failed to initialize:', err);
        setError('Failed to initialize sync system');
      }
    };

    initSync();
  }, []);

  // Update stats periodically
  useEffect(() => {
    if (!isInitialized) return;

    const updateStats = async () => {
      try {
        const qStats = await syncQueue.getStats();
        setQueueStats(qStats);
        setIsSyncing(qStats.totalPending > 0);
        
        // Calculate sync stats
        const totalActions = qStats.totalCompleted + qStats.totalFailed;
        const successRate = totalActions > 0 ? (qStats.totalCompleted / totalActions) * 100 : 100;
        
        setSyncStats({
          lastSyncTime: new Date(),
          successRate,
          totalSynced: qStats.totalCompleted,
          totalFailed: qStats.totalFailed
        });
      } catch (err) {
        console.error('[useSyncQueue] Failed to update stats:', err);
      }
    };

    updateStats();
    const interval = setInterval(updateStats, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isInitialized]);

  // When connection is restored process queued actions
  useEffect(() => {
    if (isOnline && isInitialized) {
      syncManager.processQueue();
    }
  }, [isOnline, isInitialized]);

  const enqueueAction = useCallback(async (
    type: SyncActionType,
    data: any,
    priority: SyncAction['priority'] = 'medium',
    maxRetries: number = 3
  ): Promise<string> => {
    if (!isInitialized || !user?.id) {
      throw new Error('Sync system not initialized or user not logged in');
    }

    try {
      const actionId = await syncQueue.enqueue({
        type,
        data,
        userId: user.id,
        priority,
        maxRetries
      });

      console.log(`[useSyncQueue] Action enqueued: ${type} (${actionId})`);
      return actionId;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to enqueue action';
      setError(errorMsg);
      throw new Error(errorMsg);
    }
  }, [isInitialized, user?.id]);

  const forceSync = useCallback(async () => {
    if (!isInitialized) {
      throw new Error('Sync system not initialized');
    }

    try {
      setError(null);
      setIsSyncing(true);
      await syncManager.processQueue();
      setIsSyncing(false);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Force sync failed';
      setError(errorMsg);
      setIsSyncing(false);
      throw new Error(errorMsg);
    }
  }, [isInitialized]);

  const clearQueue = useCallback(async () => {
    if (!isInitialized) {
      throw new Error('Sync system not initialized');
    }

    try {
      await syncQueue.clear();
      console.log('[useSyncQueue] Queue cleared');
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to clear queue';
      setError(errorMsg);
      throw new Error(errorMsg);
    }
  }, [isInitialized]);

  const clearCompleted = useCallback(async (): Promise<number> => {
    if (!isInitialized) {
      throw new Error('Sync system not initialized');
    }

    try {
      const cleared = await syncQueue.clearCompleted();
      console.log(`[useSyncQueue] Cleared ${cleared} completed actions`);
      return cleared;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to clear completed actions';
      setError(errorMsg);
      throw new Error(errorMsg);
    }
  }, [isInitialized]);

  return {
    enqueueAction,
    queueStats,
    syncStats,
    isInitialized,
    isSyncing,
    forceSync,
    clearQueue,
    clearCompleted,
    error
  };
}
