
/**
 * Smart Cache Manager - Extends existing cache functionality
 * Integrates with Supabase and provides intelligent caching
 */

import { offlineDB } from './indexed-db-manager';
import { supabase } from '@/integrations/supabase/client';

// TTL constants (in milliseconds)
const TTL = {
  LOCATIONS: 1 * 60 * 60 * 1000,      // 1 hour
  PROFILES: 24 * 60 * 60 * 1000,      // 24 hours
  GUARDIANS: 12 * 60 * 60 * 1000,     // 12 hours
  SETTINGS: 7 * 24 * 60 * 60 * 1000,  // 7 days
  API_CACHE: 30 * 60 * 1000           // 30 minutes
} as const;

export interface CachedLocation {
  id: string;
  user_id: string;
  latitude: number;
  longitude: number;
  timestamp: string;
  address?: string;
  shared_with_guardians: boolean;
}

export interface CachedProfile {
  id: string | number;
  full_name: string;
  email: string;
  user_type: string;
  phone?: string;
}

export interface CachedGuardian {
  id: string;
  student_id: string;
  email: string;
  full_name: string;
  is_active: boolean;
}

export class SmartCache {
  private initialized = false;

  async init(): Promise<void> {
    if (this.initialized) return;
    
    try {
      await offlineDB.init();
      this.initialized = true;
      console.log('[SmartCache] Initialized successfully');
      
      // Schedule cleanup every 30 minutes
      setInterval(() => this.cleanup(), 30 * 60 * 1000);
    } catch (error) {
      console.error('[SmartCache] Failed to initialize:', error);
      throw error;
    }
  }

  // Location Management
  async cacheUserLocation(location: CachedLocation): Promise<void> {
    await this.ensureInitialized();
    const key = `user_${location.user_id}_latest`;
    await offlineDB.set('locations', key, location, TTL.LOCATIONS);
  }

  async getLastKnownLocation(userId: string): Promise<CachedLocation | null> {
    await this.ensureInitialized();
    const key = `user_${userId}_latest`;
    return await offlineDB.get<CachedLocation>('locations', key);
  }

  async cacheLocationHistory(userId: string, locations: CachedLocation[]): Promise<void> {
    await this.ensureInitialized();
    const key = `history_${userId}`;
    await offlineDB.set('locations', key, locations, TTL.LOCATIONS);
  }

  async getLocationHistory(userId: string): Promise<CachedLocation[]> {
    await this.ensureInitialized();
    const key = `history_${userId}`;
    const cached = await offlineDB.get<CachedLocation[]>('locations', key);
    return cached || [];
  }

  // Profile Management
  async cacheUserProfile(profile: CachedProfile): Promise<void> {
    await this.ensureInitialized();
    const key = `profile_${profile.id}`;
    await offlineDB.set('profiles', key, profile, TTL.PROFILES);
  }

  async getUserProfile(userId: string): Promise<CachedProfile | null> {
    await this.ensureInitialized();
    const key = `profile_${userId}`;
    return await offlineDB.get<CachedProfile>('profiles', key);
  }

  // Guardian Management
  async cacheGuardianList(userId: string, guardians: CachedGuardian[]): Promise<void> {
    await this.ensureInitialized();
    const key = `guardians_${userId}`;
    await offlineDB.set('guardians', key, guardians, TTL.GUARDIANS);
  }

  async getGuardianList(userId: string): Promise<CachedGuardian[]> {
    await this.ensureInitialized();
    const key = `guardians_${userId}`;
    const cached = await offlineDB.get<CachedGuardian[]>('guardians', key);
    return cached || [];
  }

  async cacheStudentList(guardianEmail: string, students: CachedProfile[]): Promise<void> {
    await this.ensureInitialized();
    const key = `students_${guardianEmail}`;
    await offlineDB.set('guardians', key, students, TTL.GUARDIANS);
  }

  async getStudentList(guardianEmail: string): Promise<CachedProfile[]> {
    await this.ensureInitialized();
    const key = `students_${guardianEmail}`;
    const cached = await offlineDB.get<CachedProfile[]>('guardians', key);
    return cached || [];
  }

  // API Cache
  async cacheApiResponse(endpoint: string, params: any, data: any): Promise<void> {
    await this.ensureInitialized();
    const key = `api_${endpoint}_${JSON.stringify(params)}`;
    await offlineDB.set('api_cache', key, data, TTL.API_CACHE);
  }

  async getApiResponse<T>(endpoint: string, params: any): Promise<T | null> {
    await this.ensureInitialized();
    const key = `api_${endpoint}_${JSON.stringify(params)}`;
    return await offlineDB.get<T>('api_cache', key);
  }

  // Settings Management
  async cacheSetting(key: string, value: any): Promise<void> {
    await this.ensureInitialized();
    await offlineDB.set('settings', key, value, TTL.SETTINGS);
  }

  async getSetting<T>(key: string, defaultValue: T): Promise<T> {
    await this.ensureInitialized();
    const cached = await offlineDB.get<T>('settings', key);
    return cached !== null ? cached : defaultValue;
  }

  // Sync with Supabase when online
  async syncWithSupabase(userId: string): Promise<{success: boolean, synced: string[]}> {
    await this.ensureInitialized();
    const synced: string[] = [];

    try {
      // Sync profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (profile) {
        await this.cacheUserProfile(profile as CachedProfile);
        synced.push('profile');
      }

      // Sync recent locations (last 24 hours)
      const { data: locations } = await supabase
        .from('locations')
        .select('*')
        .eq('user_id', userId)
        .gte('timestamp', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .order('timestamp', { ascending: false });

      if (locations && locations.length > 0) {
        await this.cacheLocationHistory(userId, locations as CachedLocation[]);
        // Cache latest location separately
        await this.cacheUserLocation(locations[0] as CachedLocation);
        synced.push('locations');
      }

      return { success: true, synced };
    } catch (error) {
      console.error('[SmartCache] Sync failed:', error);
      return { success: false, synced };
    }
  }

  // Cleanup expired entries
  async cleanup(): Promise<void> {
    await this.ensureInitialized();
    console.log('[SmartCache] Starting cleanup...');

    const stores = ['locations', 'profiles', 'guardians', 'settings', 'api_cache'];
    let totalCleaned = 0;

    for (const store of stores) {
      try {
        const cleaned = await offlineDB.cleanExpired(store);
        totalCleaned += cleaned;
      } catch (error) {
        console.error(`[SmartCache] Cleanup failed for ${store}:`, error);
      }
    }

    console.log(`[SmartCache] Cleanup completed, removed ${totalCleaned} expired items`);
  }

  // Get cache statistics
  async getStats(): Promise<{
    stores: Record<string, number>;
    totalItems: number;
  }> {
    await this.ensureInitialized();
    
    const stores = ['locations', 'profiles', 'guardians', 'settings', 'api_cache'];
    const stats: Record<string, number> = {};
    let totalItems = 0;

    for (const store of stores) {
      try {
        const keys = await offlineDB.getAllKeys(store);
        stats[store] = keys.length;
        totalItems += keys.length;
      } catch (error) {
        console.error(`[SmartCache] Failed to get stats for ${store}:`, error);
        stats[store] = 0;
      }
    }

    return { stores: stats, totalItems };
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.init();
    }
  }
}

// Singleton instance
export const smartCache = new SmartCache();

