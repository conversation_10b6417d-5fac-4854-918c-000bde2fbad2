
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Clock, 
  Shield, 
  XCircle, 
  CheckCircle, 
  AlertTriangle,
  Calendar,
  MessageSquare
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useStudentDeletionStatus } from '@/hooks/lgpd/useStudentDeletionStatus';

const StudentDeletionStatusCard: React.FC = () => {
  const { 
    request, 
    isLoading, 
    canRequestAgain, 
    cooldownDays, 
    createDeletionRequest 
  } = useStudentDeletionStatus();
  
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitRequest = async () => {
    setIsSubmitting(true);
    const success = await createDeletionRequest(reason);
    if (success) {
      setReason('');
    }
    setIsSubmitting(false);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Status da Solicitação de Exclusão
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <Clock className="h-8 w-8 mx-auto text-gray-400 mb-2" />
            <p className="text-gray-600">Verificando status...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Status: Nenhuma solicitação ou pode solicitar novamente
  if (!request || canRequestAgain) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-orange-600" />
            Exclusão de Conta (Protegida)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {request?.status === 'rejected' && cooldownDays > 0 ? (
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-yellow-800">Período de Espera</p>
                    <p className="text-yellow-700 text-sm mt-1">
                      Sua última solicitação foi rejeitada. Você poderá fazer uma nova solicitação em{' '}
                      <strong>{cooldownDays} dia{cooldownDays !== 1 ? 's' : ''}</strong>.
                    </p>
                    {request.guardian_notes && (
                      <div className="mt-3 p-2 bg-yellow-100 rounded border-l-4 border-yellow-400">
                        <p className="text-sm text-yellow-800">
                          <strong>Observação do responsável:</strong><br />
                          {request.guardian_notes}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Como estudante, sua exclusão de conta precisa ser aprovada por seus responsáveis.
                Isso é uma medida de proteção.
              </p>
              {request?.status === 'rejected' && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-blue-800 text-sm">
                    <strong>ℹ️ Nova solicitação permitida:</strong> Sua solicitação anterior foi rejeitada, 
                    mas você já pode fazer uma nova solicitação.
                  </p>
                </div>
              )}
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="w-full">
                    {request?.status === 'rejected' ? 'Nova Solicitação de Exclusão' : 'Solicitar Exclusão de Conta'}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5 text-orange-500" />
                      Solicitar Exclusão de Conta
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      Sua solicitação será enviada aos seus responsáveis para aprovação.
                      Eles poderão aprovar ou rejeitar esta solicitação.
                      <br /><br />
                      <strong>Esta é uma medida de proteção.</strong>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="reason">Motivo da solicitação (opcional)</Label>
                      <Input
                        id="reason"
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="Ex: Não quero mais usar o aplicativo..."
                        className="mt-1"
                      />
                    </div>
                  </div>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleSubmitRequest}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Enviando...' : 'Enviar Solicitação'}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Status: Solicitação pendente
  if (request.status === 'pending') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Solicitação de Exclusão Pendente
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-3">
                <Clock className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-800">Aguardando Aprovação</p>
                  <p className="text-blue-700 text-sm mt-1">
                    Sua solicitação foi enviada em{' '}
                    <strong>
                      {new Date(request.requested_at).toLocaleDateString('pt-BR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </strong>
                  </p>
                  <p className="text-blue-700 text-sm mt-2">
                    Seus responsáveis foram notificados e devem aprovar ou rejeitar sua solicitação em breve.
                  </p>
                </div>
              </div>
            </div>
            
            {request.reason && (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <MessageSquare className="h-4 w-4 text-gray-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">Motivo informado:</p>
                    <p className="text-sm text-gray-700 mt-1 italic">"{request.reason}"</p>
                  </div>
                </div>
              </div>
            )}
            
            <Button variant="outline" disabled className="w-full">
              <Clock className="h-4 w-4 mr-2" />
              Aguardando Decisão dos Responsáveis
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Status: Solicitação rejeitada (dentro do período de cooldown)
  if (request.status === 'rejected' && !canRequestAgain) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <XCircle className="h-5 w-5 text-red-600" />
            Solicitação Rejeitada
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start gap-3">
                <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800">Solicitação Rejeitada</p>
                  <p className="text-red-700 text-sm mt-1">
                    Sua solicitação foi rejeitada em{' '}
                    <strong>
                      {new Date(request.processed_at!).toLocaleDateString('pt-BR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </strong>
                  </p>
                  <p className="text-red-700 text-sm mt-2">
                    Você poderá fazer uma nova solicitação em{' '}
                    <strong>{cooldownDays} dia{cooldownDays !== 1 ? 's' : ''}</strong>.
                  </p>
                </div>
              </div>
            </div>
            
            {request.guardian_notes && (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <MessageSquare className="h-4 w-4 text-gray-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">Observação do responsável:</p>
                    <p className="text-sm text-gray-700 mt-1">{request.guardian_notes}</p>
                  </div>
                </div>
              </div>
            )}
            
            <Button variant="outline" disabled className="w-full">
              <Calendar className="h-4 w-4 mr-2" />
              Nova Solicitação em {cooldownDays} Dia{cooldownDays !== 1 ? 's' : ''}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Status: Solicitação aprovada
  if (request.status === 'approved') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Exclusão Aprovada
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-green-800">Solicitação Aprovada</p>
                  <p className="text-green-700 text-sm mt-1">
                    Sua conta será excluída em breve. Você receberá uma confirmação por email.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return null;
};

export default StudentDeletionStatusCard;
