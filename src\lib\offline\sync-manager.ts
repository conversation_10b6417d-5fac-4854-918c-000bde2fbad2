import { supabase } from '@/integrations/supabase/client'
import { offlineDexie, SyncQueueItem } from './dexie-db'

export type OfflineActionType =
  | 'add_location'
  | 'update_location'
  | 'add_guardian'
  | 'remove_guardian'

class SyncManager {
  /** Queue a new offline action */
  async addAction(type: OfflineActionType, data: any) {
    const item: SyncQueueItem = {
      id: crypto.randomUUID(),
      type,
      data,
      timestamp: Date.now()
    }
    await offlineDexie.sync_queue.put(item)

    // Register background sync so service worker triggers when back online
    if (navigator.serviceWorker) {
      try {
        const reg = await navigator.serviceWorker.ready
        ;(reg as any).sync?.register('sync-queue')
      } catch (err) {
        console.warn('[SyncManager] Failed to register background sync', err)
      }
    }
  }

  async getQueuedActions(): Promise<SyncQueueItem[]> {
    return offlineDexie.sync_queue.toArray()
  }

  /** Process all queued actions using Supabase */
  async processQueue() {
    const actions = await this.getQueuedActions()
    if (actions.length === 0) return

    // Batch locations
    const locationBatch = actions.filter(a => a.type === 'add_location')
    if (locationBatch.length) {
      try {
        const payload = locationBatch.map(a => a.data)
        await supabase.from('locations').insert(payload)
        for (const a of locationBatch) await offlineDexie.sync_queue.delete(a.id)
      } catch (err) {
        console.error('[SyncManager] Failed to sync locations', err)
      }
    }

    // Other actions sequentially
    const others = actions.filter(a => a.type !== 'add_location')
    for (const action of others) {
      try {
        if (action.type === 'update_location') {
          await supabase.from('locations').update(action.data.updates).eq('id', action.data.id)
        } else if (action.type === 'add_guardian') {
          await supabase.rpc('send_family_invitation', { p_guardian_email: action.data.email })
        } else if (action.type === 'remove_guardian') {
          await supabase.from('student_guardian_relationships').delete().eq('id', action.data.id)
        }
        await offlineDexie.sync_queue.delete(action.id)
      } catch (err) {
        console.error('[SyncManager] Failed to process action', action, err)
      }
    }
  }
}

export const syncManager = new SyncManager()
