# Plano de Solução Definitiva: Adicionar Funcionalidade de Vínculo Familiar ao Perfil

## 🎯 Situação Atual e Problema Principal

A mãe cria uma conta no aplicativo. Seus filhos já estão cadastrados, e o pai já é o guardião deles. A mãe precisa, ao logar, ver seus filhos listados no seu dashboard (integrado à página de perfil) e ter a capacidade de gerenciar essa relação.

**Problema Central:** Como a mãe se vincula aos filhos já existentes no sistema, e como essa informação é exibida em seu perfil?

## 🚨 Problemas Críticos Identificados

1.  **RESOLVIDO (26-Jun-2025): Função RPC `get_guardian_students` encontrada e validada**
    *   **Impacto:** Essencial. Sem esta função, nenhum guardião (pai ou mãe) conseguirá listar seus estudantes vinculados.
2.  **PROBLEMA DE DADOS: Cache Local Desatualizado**
    *   **Impacto:** O cache do perfil precisará ser invalidado para que a lista de estudantes seja atualizada imediatamente após um novo vínculo.
3.  **RLS Policies Inadequadas/Conflitantes:**
    *   **Impacto:** As políticas RLS precisam garantir que a mãe, uma vez vinculada, tenha permissão para ver os dados dos filhos.

## 📊 Esquema do Banco de Dados Proposto

*   **`guardians`:** Tabela existente que permite o vínculo `guardian_user_id` e `student_user_id`.
*   **`invitations` (NOVA TABELA):** Fundamental para um fluxo seguro de "vincular-se a um estudante existente".

---

## 📋 Plano de Solução por Fases

### FASE 1: Correção Crítica do Banco de Dados (URGENTE E INDISPENSÁVEL)

1.  **Criar e Executar Migração SQL Completa no Supabase:**
    *   **Ação:** Desenvolver e executar um script SQL para criar a tabela `guardians` (se não existir), a função RPC `get_guardian_students()`, e as políticas RLS associadas.
    *   **Resultado:** Habilitar a busca de estudantes por guardião.
2.  **Validar Execução:**
    *   Verificar no Supabase se a função e as políticas foram criadas corretamente.
    *   Testar com um usuário guardião existente para garantir que os estudantes vinculados são listados.

### FASE 2: Implementação da Funcionalidade de Vinculação no Perfil

1.  **Atualização do Esquema de Banco de Dados (Nova Migração SQL):**
    *   **Ação:** Criar um novo arquivo de migração SQL para adicionar a tabela `invitations`.
2.  **Desenvolvimento Backend (Supabase RPCs):**
    *   **`invite_guardian_to_student(student_id UUID, invited_email TEXT)`:** Permite a um guardião existente convidar outro responsável.
    *   **`accept_guardian_invitation(token UUID)`:** Processa a aceitação do convite.
3.  **Desenvolvimento Frontend (Página `/profile`):**
    *   **Na seção "Estudantes Vinculados":**
        *   Adicionar um botão "Vincular Novo Estudante".
        *   Ao clicar, abrir um modal com um formulário para buscar um estudante e enviar um convite/solicitação.
        *   O formulário chamará a RPC apropriada (`invite_guardian_to_student` ou uma nova `request_student_link`).
    *   **Página/Rota para Aceitar Convite (`/accept-invitation?token=...`):**
        *   Página acessada via e-mail que extrai o token, chama a RPC `accept_guardian_invitation`, e redireciona para o perfil.
    *   **Atualização do Componente da Lista de Estudantes:**
        *   Invalidar o cache de dados (React Query) após a aceitação do convite para atualizar a lista de estudantes automaticamente.

### FASE 3: Refinamento e RLS

1.  **Revisão das RLS Policies:**
    *   **Ação:** Revisar e ajustar todas as políticas RLS para `profiles`, `guardians`, `locations`, e a nova `invitations` para garantir a segurança e o acesso correto aos dados.
2.  **Tratamento de Cache no Perfil:**
    *   **Ação:** Implementar a lógica de limpeza de cache na página `/profile` para refletir as mudanças de vínculo em tempo real.

## 📈 Métricas de Sucesso

*   ✅ A mãe, após ser vinculada, vê seus filhos na sua página de perfil.
*   ✅ O fluxo de "Vincular Novo Estudante" funciona corretamente a partir do perfil.
*   ✅ O mapa no perfil exibe as localizações dos estudantes para a mãe.
*   ✅ A funcionalidade foi adicionada à página de perfil existente, sem criar novos dashboards. 