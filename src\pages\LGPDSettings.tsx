
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { AlertTriangle, Download, Trash2, Info } from "lucide-react"; // Added Info icon
import { useLGPDActions } from '@/hooks/lgpd/useLGPDActions';
import { useTranslation } from 'react-i18next';
import { getLegalPolicy } from '@/utils/policy';

const LGPDSettings = () => {
  const { loading, exportUserData, requestAccountDeletion } = useLGPDActions(); // Removed deleteUserData as it's not used directly
  const [deletionReason, setDeletionReason] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { t, i18n } = useTranslation();
  const activePolicy = getLegalPolicy(i18n.language).toLowerCase(); // e.g., "lgpd" or "uk_gdpr"

  const handleExportData = async () => {
    setIsExporting(true);
    try {
      await exportUserData();
    } finally {
      setIsExporting(false);
    }
  };

  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    try {
      await requestAccountDeletion(deletionReason);
    } finally {
      setIsDeleting(false);
    }
  };

  // Helper to render list items from translation
  const renderTranslatedList = (listKey: string) => {
    const items = t(`${listKey}`, { returnObjects: true }) as Array<string> | string;
    if (Array.isArray(items)) {
      return (
        <ul className="list-disc pl-5 space-y-1">
          {items.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
      );
    }
    return <div>{items}</div>; // Fallback if not an array
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{t(`legal.${activePolicy}.title`, t('lgpdSettingsPage.mainTitle'))}</h1>
        <p className="text-muted-foreground">
          {t(`legal.${activePolicy}.description`, t('lgpdSettingsPage.mainDescription'))}
        </p>
      </div>

      <div className="space-y-6">
        {/* Export Data Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              {t('lgpdSettingsPage.exportTitle')}
            </CardTitle>
            <CardDescription>
              {t('lgpdSettingsPage.exportDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleExportData} 
              disabled={loading || isExporting}
              className="w-full sm:w-auto"
            >
              <Download className="h-4 w-4 mr-2" />
              {isExporting ? t('lgpdSettingsPage.exportingButton') : t('lgpdSettingsPage.exportButton')}
            </Button>
          </CardContent>
        </Card>

        {/* Delete Account Section */}
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              {t('lgpdSettingsPage.deleteAccountTitle')}
            </CardTitle>
            <CardDescription>
              {t('lgpdSettingsPage.deleteAccountDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="deletion-reason">
                {t('lgpdSettingsPage.deleteReasonLabel')}
              </Label>
              <Textarea
                id="deletion-reason"
                placeholder={t('lgpdSettingsPage.deleteReasonPlaceholder')}
                value={deletionReason}
                onChange={(e) => setDeletionReason(e.target.value)}
                rows={4}
              />
            </div>
            <Button 
              onClick={handleDeleteAccount}
              disabled={loading || isDeleting}
              variant="destructive"
              className="w-full sm:w-auto"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isDeleting ? t('lgpdSettingsPage.deletingButton') : t('lgpdSettingsPage.deleteButton')}
            </Button>
          </CardContent>
        </Card>

        {/* Dynamic Information Section based on Policy */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t(`legal.${activePolicy}.dataPolicy`)}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-sm">
            <div><strong>{t('legal.common.baseLawLabel', 'Legal Basis')}:</strong> {t(`legal.${activePolicy}.baseLaw`)}</div>

            <div>
              <h3 className="font-semibold mb-1">{t(`legal.${activePolicy}.purposes`)}</h3>
              {renderTranslatedList(`legal.${activePolicy}.purposesList`)}
            </div>

            <div>
              <h3 className="font-semibold mb-1">{t(`legal.${activePolicy}.yourRights`)}</h3>
              {renderTranslatedList(`legal.${activePolicy}.rightsList`)}
            </div>

            <div>
              {t(`legal.${activePolicy}.contact`)}{' '}
              <a className="text-blue-600 hover:text-blue-700" href={`mailto:${t(`legal.${activePolicy}.emailUs`)}`}>
                {t(`legal.${activePolicy}.emailUs`)}
              </a>.
            </div>
            <div>{t(`legal.${activePolicy}.responseTime`)}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LGPDSettings;
