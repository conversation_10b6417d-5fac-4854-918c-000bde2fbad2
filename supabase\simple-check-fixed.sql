-- ==========================================
-- VERIFICAÇÃO SIMPLES - DADOS REAIS (CORRIGIDA)
-- Data: 24/06/2025
-- Objetivo: Verificar dados específicos com tipos corretos
-- ==========================================

-- 1. VERIFICAR DADOS NA TABELA GUARDIANS PARA FRANK
SELECT 
    'GUARDIANS_FRANK' as info,
    id, email, student_id, is_active, created_at
FROM public.guardians 
WHERE email = '<EMAIL>'
LIMIT 10;

-- 2. VERIFICAR DADOS DA TABELA ACCOUNT_DELETION_REQUESTS
SELECT 
    'ACCOUNT_DELETION_REQUESTS' as info,
    id, student_email, student_name, status, requested_at
FROM public.account_deletion_requests
ORDER BY requested_at DESC
LIMIT 10;

-- 3. VERIF<PERSON><PERSON> PERFIL DO FRANK
SELECT 
    'PROFILE_FRANK' as info,
    id, email, full_name, user_type, created_at
FROM public.profiles
WHERE email = '<EMAIL>'
LIMIT 5;

-- 4. VERIFICAR PERFIL DO MAURÍCIO
SELECT 
    'PROFILE_MAURICIO' as info,
    id, email, full_name, user_type, created_at  
FROM public.profiles
WHERE email = '<EMAIL>'
LIMIT 5;

-- 5. VERIFICAR SE RPC get_guardian_deletion_requests EXISTE
SELECT 
    'RPC_CHECK' as info,
    routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name = 'get_guardian_deletion_requests';

-- 6. VERIFICAR SE RPC get_guardian_students EXISTE  
SELECT 
    'RPC_STUDENTS_CHECK' as info,
    routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name = 'get_guardian_students';

-- 7. CONTAR REGISTROS NAS TABELAS PRINCIPAIS
SELECT 'CONTAGEM_PROFILES' as tabela, COUNT(*) as registros FROM public.profiles;
SELECT 'CONTAGEM_GUARDIANS' as tabela, COUNT(*) as registros FROM public.guardians;
SELECT 'CONTAGEM_ACCOUNT_DELETION' as tabela, COUNT(*) as registros FROM public.account_deletion_requests;
SELECT 'CONTAGEM_STUDENTS' as tabela, COUNT(*) as registros FROM public.students;

-- 8. VERIFICAR VINCULAÇÕES FRANK-ESTUDANTES (CORRIGIDO COM CAST)
SELECT 
    'VINCULACOES_FRANK' as info,
    g.id as guardian_id,
    g.student_id,
    g.email as guardian_email,
    p.email as student_email,
    p.full_name as student_name
FROM public.guardians g
LEFT JOIN public.profiles p ON p.id::text = g.student_id::text
WHERE g.email = '<EMAIL>'
ORDER BY g.created_at DESC;

-- 9. VERIFICAR TIPOS DE DADOS DAS TABELAS PRINCIPAIS
SELECT 
    'TIPOS_GUARDIANS' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'guardians'
  AND column_name IN ('id', 'student_id', 'email')
ORDER BY ordinal_position;

SELECT 
    'TIPOS_PROFILES' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'profiles'
  AND column_name IN ('id', 'user_id', 'email')
ORDER BY ordinal_position;

-- 10. VERIFICAR VINCULAÇÃO ALTERNATIVA (POR EMAIL)
SELECT 
    'VINCULACOES_FRANK_POR_EMAIL' as info,
    g.id as guardian_id,
    g.student_id,
    g.email as guardian_email,
    p.email as student_email,
    p.full_name as student_name
FROM public.guardians g
LEFT JOIN public.profiles p ON p.email = (
    SELECT p2.email FROM public.profiles p2 WHERE p2.id::text = g.student_id::text
)
WHERE g.email = '<EMAIL>'
ORDER BY g.created_at DESC; 