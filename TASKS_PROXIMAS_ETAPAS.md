# 📋 Plano de Próximas Etapas

Este documento resume as principais tarefas derivadas da documentação do projeto para as próximas semanas.

## 1. Refatoração do Student Dashboard
- Organizar novo diretório `src/components/student/dashboard`.
- Extrair hooks:
  - `useStudentLocation.ts`
  - `useLocationSharing.ts`
  - `useGuardianManagement.ts`
- Implementar componentes:
  - `LocationActions.tsx`
  - `LocationMap.tsx`
  - `LocationHistory.tsx`
  - `StudentLocationSection.tsx`
  - `StudentInfoSection.tsx`
  - `GuardianSection.tsx`
- Integrar em `StudentDashboard.tsx` (50 linhas) e escrever testes.

## 2. Funcionalidades PWA e Offline
- Criar/ajustar `public/manifest.json` e meta tags.
- Registrar service worker em `src/lib/offline/service-worker-registration.ts`.
- Implementar `NetworkStatus` e `OfflineBanner`.
- Implementar armazenamento offline com IndexedDB (`offline-storage.ts`).
- Desenvolver fila de sincronização (`sync-queue.ts` e `useSyncQueue.ts`).
- Permitir tracking em segundo plano e geofencing.
- Configurar notificações push e botão de SOS.

## 3. Checklist de Refatoração Segura
- Testar páginas de login/logout e dashboards.
- Configurar React Query com tratamento global de erros.
- Padronizar verificações de tipo de usuário e rotas protegidas.
- Executar testes de integração e atualizar README.

## 4. Plano de Implementação
1. **PWA e Service Worker** – 1 semana
2. **Cache de Dados e Fila de Sync** – 2 semanas
3. **Rastreamento em Segundo Plano e Geofencing** – 2 semanas
4. **Push Notifications e SOS** – 1 semana
5. **Polimento e Documentação** – contínuo

