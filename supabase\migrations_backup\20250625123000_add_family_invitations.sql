-- Migration: Add family invitations system
-- Description: Add invitations table and RPC functions for family linking

-- Create family_invitations table
CREATE TABLE IF NOT EXISTS public.family_invitations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    guardian_email TEXT NOT NULL,
    student_name TEXT NOT NULL,
    student_email TEXT NOT NULL,
    invitation_token TEXT NOT NULL UNIQUE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'expired')),
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (now() + INTERVAL '7 days'),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    accepted_at TIMESTAMPTZ,
    accepted_by_guardian_id UUID REFERENCES auth.users(id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_family_invitations_token ON public.family_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_family_invitations_guardian_email ON public.family_invitations(guardian_email);
CREATE INDEX IF NOT EXISTS idx_family_invitations_student_id ON public.family_invitations(student_id);
CREATE INDEX IF NOT EXISTS idx_family_invitations_status ON public.family_invitations(status);

-- Enable RLS
ALTER TABLE public.family_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for family_invitations
CREATE POLICY "Users can view invitations for their email" ON public.family_invitations
    FOR SELECT USING (
        guardian_email = auth.email() OR 
        student_id = auth.uid()
    );

CREATE POLICY "Students can create invitations" ON public.family_invitations
    FOR INSERT WITH CHECK (student_id = auth.uid());

CREATE POLICY "Guardians can accept/reject invitations" ON public.family_invitations
    FOR UPDATE USING (guardian_email = auth.email());

-- Function: Send family invitation
CREATE OR REPLACE FUNCTION public.send_family_invitation(
    p_guardian_email TEXT
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    invitation_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_student_id UUID;
    v_student_name TEXT;
    v_student_email TEXT;
    v_invitation_token TEXT;
    v_invitation_id UUID;
    v_existing_relationship UUID;
BEGIN
    -- Get current user (student)
    v_student_id := auth.uid();
    
    IF v_student_id IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Usuário não autenticado'::TEXT, NULL::UUID;
        RETURN;
    END IF;
    
    -- Get student profile
    SELECT u.email, COALESCE(p.full_name, u.email)
    INTO v_student_email, v_student_name
    FROM auth.users u
    LEFT JOIN public.profiles p ON p.user_id = u.id
    WHERE u.id = v_student_id;
    
    -- Check if relationship already exists
    SELECT id INTO v_existing_relationship
    FROM public.student_guardian_relationships sgr
    JOIN auth.users u ON sgr.guardian_id = u.id
    WHERE sgr.student_id = v_student_id
    AND u.email = p_guardian_email;
    
    IF v_existing_relationship IS NOT NULL THEN
        RETURN QUERY SELECT FALSE, 'Relacionamento já existe com este responsável'::TEXT, NULL::UUID;
        RETURN;
    END IF;
    
    -- Check for existing pending invitation
    SELECT id INTO v_invitation_id
    FROM public.family_invitations
    WHERE student_id = v_student_id
    AND guardian_email = p_guardian_email
    AND status = 'pending'
    AND expires_at > now();
    
    IF v_invitation_id IS NOT NULL THEN
        RETURN QUERY SELECT FALSE, 'Convite pendente já existe para este responsável'::TEXT, v_invitation_id;
        RETURN;
    END IF;
    
    -- Generate unique token
    v_invitation_token := encode(gen_random_bytes(32), 'base64url');
    
    -- Create invitation
    INSERT INTO public.family_invitations (
        student_id,
        guardian_email,
        student_name,
        student_email,
        invitation_token
    ) VALUES (
        v_student_id,
        p_guardian_email,
        v_student_name,
        v_student_email,
        v_invitation_token
    ) RETURNING id INTO v_invitation_id;
    
    RETURN QUERY SELECT TRUE, 'Convite enviado com sucesso'::TEXT, v_invitation_id;
END;
$$;

-- Function: Accept family invitation
CREATE OR REPLACE FUNCTION public.accept_family_invitation(
    p_invitation_token TEXT
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guardian_id UUID;
    v_guardian_email TEXT;
    v_invitation_record RECORD;
    v_existing_relationship UUID;
BEGIN
    -- Get current user (guardian)
    v_guardian_id := auth.uid();
    v_guardian_email := auth.email();
    
    IF v_guardian_id IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Usuário não autenticado'::TEXT;
        RETURN;
    END IF;
    
    -- Get invitation details
    SELECT * INTO v_invitation_record
    FROM public.family_invitations
    WHERE invitation_token = p_invitation_token
    AND status = 'pending'
    AND expires_at > now();
    
    IF v_invitation_record IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Convite inválido ou expirado'::TEXT;
        RETURN;
    END IF;
    
    -- Verify guardian email matches
    IF v_invitation_record.guardian_email != v_guardian_email THEN
        RETURN QUERY SELECT FALSE, 'Este convite não é para o seu email'::TEXT;
        RETURN;
    END IF;
    
    -- Check if relationship already exists
    SELECT id INTO v_existing_relationship
    FROM public.student_guardian_relationships
    WHERE student_id = v_invitation_record.student_id
    AND guardian_id = v_guardian_id;
    
    IF v_existing_relationship IS NOT NULL THEN
        -- Update invitation status
        UPDATE public.family_invitations
        SET status = 'accepted',
            accepted_at = now(),
            accepted_by_guardian_id = v_guardian_id
        WHERE id = v_invitation_record.id;
        
        RETURN QUERY SELECT TRUE, 'Relacionamento já existe'::TEXT;
        RETURN;
    END IF;
    
    -- Create relationship
    INSERT INTO public.student_guardian_relationships (
        student_id,
        guardian_id,
        relationship_type,
        is_primary
    ) VALUES (
        v_invitation_record.student_id,
        v_guardian_id,
        'parent',
        FALSE  -- Not primary by default
    );
    
    -- Update invitation status
    UPDATE public.family_invitations
    SET status = 'accepted',
        accepted_at = now(),
        accepted_by_guardian_id = v_guardian_id
    WHERE id = v_invitation_record.id;
    
    RETURN QUERY SELECT TRUE, 'Convite aceito e relacionamento criado com sucesso'::TEXT;
END;
$$;

-- Function: Get pending invitations for guardian
CREATE OR REPLACE FUNCTION public.get_guardian_pending_invitations()
RETURNS TABLE(
    invitation_id UUID,
    student_name TEXT,
    student_email TEXT,
    invitation_token TEXT,
    created_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        fi.id,
        fi.student_name,
        fi.student_email,
        fi.invitation_token,
        fi.created_at,
        fi.expires_at
    FROM public.family_invitations fi
    WHERE fi.guardian_email = auth.email()
    AND fi.status = 'pending'
    AND fi.expires_at > now()
    ORDER BY fi.created_at DESC;
END;
$$;

-- Function: Request family connection (alternative approach)
CREATE OR REPLACE FUNCTION public.request_student_connection(
    p_student_email TEXT,
    p_student_cpf TEXT DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    invitation_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guardian_id UUID;
    v_guardian_email TEXT;
    v_student_id UUID;
    v_student_name TEXT;
    v_invitation_token TEXT;
    v_invitation_id UUID;
    v_existing_relationship UUID;
BEGIN
    -- Get current user (guardian)
    v_guardian_id := auth.uid();
    v_guardian_email := auth.email();
    
    IF v_guardian_id IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Usuário não autenticado'::TEXT, NULL::UUID;
        RETURN;
    END IF;
    
    -- Find student by email and optionally CPF
    SELECT u.id, COALESCE(p.full_name, u.email)
    INTO v_student_id, v_student_name
    FROM auth.users u
    LEFT JOIN public.profiles p ON p.user_id = u.id
    WHERE u.email = p_student_email
    AND (p_student_cpf IS NULL OR p.cpf = p_student_cpf)
    AND p.user_type = 'student';
    
    IF v_student_id IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Estudante não encontrado com os dados fornecidos'::TEXT, NULL::UUID;
        RETURN;
    END IF;
    
    -- Check if relationship already exists
    SELECT id INTO v_existing_relationship
    FROM public.student_guardian_relationships
    WHERE student_id = v_student_id
    AND guardian_id = v_guardian_id;
    
    IF v_existing_relationship IS NOT NULL THEN
        RETURN QUERY SELECT FALSE, 'Relacionamento já existe com este estudante'::TEXT, NULL::UUID;
        RETURN;
    END IF;
    
    -- Check for existing pending invitation
    SELECT id INTO v_invitation_id
    FROM public.family_invitations
    WHERE student_id = v_student_id
    AND guardian_email = v_guardian_email
    AND status = 'pending'
    AND expires_at > now();
    
    IF v_invitation_id IS NOT NULL THEN
        RETURN QUERY SELECT FALSE, 'Solicitação pendente já existe para este estudante'::TEXT, v_invitation_id;
        RETURN;
    END IF;
    
    -- Generate unique token
    v_invitation_token := encode(gen_random_bytes(32), 'base64url');
    
    -- Create invitation (from guardian side)
    INSERT INTO public.family_invitations (
        student_id,
        guardian_email,
        student_name,
        student_email,
        invitation_token
    ) VALUES (
        v_student_id,
        v_guardian_email,
        v_student_name,
        p_student_email,
        v_invitation_token
    ) RETURNING id INTO v_invitation_id;
    
    RETURN QUERY SELECT TRUE, 'Solicitação de conexão enviada com sucesso'::TEXT, v_invitation_id;
END;
$$;

-- Create updated_at trigger
CREATE TRIGGER family_invitations_updated_at
    BEFORE UPDATE ON public.family_invitations
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

-- Cleanup function for expired invitations
CREATE OR REPLACE FUNCTION public.cleanup_expired_family_invitations()
RETURNS INT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_deleted_count INT;
BEGIN
    UPDATE public.family_invitations
    SET status = 'expired'
    WHERE status = 'pending'
    AND expires_at <= now();
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    RETURN v_deleted_count;
END;
$$;

-- Function: Get pending requests for current student (guardians who want to connect)
CREATE OR REPLACE FUNCTION public.get_student_pending_requests()
RETURNS TABLE(
    invitation_id UUID,
    guardian_email TEXT,
    invitation_token TEXT,
    created_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        fi.id,
        fi.guardian_email,
        fi.invitation_token,
        fi.created_at,
        fi.expires_at
    FROM public.family_invitations fi
    WHERE fi.student_id = auth.uid()
    AND fi.status = 'pending'
    AND fi.expires_at > now()
    ORDER BY fi.created_at DESC;
END;
$$;

-- Add comments for documentation
COMMENT ON TABLE public.family_invitations IS 'Convites para vínculos familiares entre estudantes e responsáveis';
COMMENT ON FUNCTION public.send_family_invitation(TEXT) IS 'Estudantes enviam convites para responsáveis se vincularem';
COMMENT ON FUNCTION public.accept_family_invitation(TEXT) IS 'Responsáveis aceitam convites usando token';
COMMENT ON FUNCTION public.request_student_connection(TEXT, TEXT) IS 'Responsáveis solicitam conexão com estudantes por email/CPF';
COMMENT ON FUNCTION public.get_guardian_pending_invitations() IS 'Lista convites pendentes para o responsável atual';
COMMENT ON FUNCTION public.get_student_pending_requests() IS 'Lista solicitações pendentes de responsáveis para o estudante atual'; 