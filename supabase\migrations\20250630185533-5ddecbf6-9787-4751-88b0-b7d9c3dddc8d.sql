
-- Corri<PERSON><PERSON> na tabela profiles para permitir service_role
-- Adicionar política específica para service_role (usado pelas Edge Functions)

CREATE POLICY "service_role_full_access_profiles" 
ON public.profiles 
FOR ALL 
TO service_role 
USING (true) 
WITH CHECK (true);

-- Verificar se a política foi criada corretamente
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE tablename = 'profiles' 
AND policyname = 'service_role_full_access_profiles';
