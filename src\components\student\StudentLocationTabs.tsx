import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ModernTabs, ModernTabsList, ModernTabsTrigger, ModernTabsContent } from '@/components/ui/modern-tabs';
import { ModernBadge } from '@/components/ui/modern-badge';
import StudentLocationMap from '@/components/StudentLocationMap';
import LocationHistoryList from '@/components/student/LocationHistoryList';
import { LocationData } from '@/types/database';
import { cn } from '@/lib/utils';
import { MapPin, History } from 'lucide-react';

interface StudentLocationTabsProps {
  userId: string;
  userName: string;
  userEmail: string;
  locations: LocationData[];
  loading: boolean;
  error: string | null;
  className?: string;
}

const StudentLocationTabs: React.FC<StudentLocationTabsProps> = ({
  userId,
  userName,
  userEmail,
  locations,
  loading,
  error,
  className
}) => {
  const [activeTab, setActiveTab] = useState('map');
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const { t } = useTranslation();

  return (
    <div className={cn('overflow-hidden', className)}>
      <ModernTabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="px-6 pt-6 pb-2">
            <ModernTabsList className="w-full grid grid-cols-2">
              <ModernTabsTrigger value="map" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                {t('dashboard.map', 'Map')}
                {selectedLocation && (
                  <ModernBadge variant="success" size="sm">
                    {t('dashboard.selected', 'Selected')}
                  </ModernBadge>
                )}
              </ModernTabsTrigger>
              <ModernTabsTrigger value="history" className="flex items-center gap-2">
                <History className="h-4 w-4" />
                {t('dashboard.history', 'History')}
                {locations.length > 0 && (
                  <ModernBadge variant="default" size="sm">
                    {locations.length}
                  </ModernBadge>
                )}
              </ModernTabsTrigger>
            </ModernTabsList>
          </div>

          <ModernTabsContent value="map" className="mt-4 px-4 pb-4">
              <StudentLocationMap
                selectedUserId={userId}
                locations={locations}
                loading={loading}
                error={error}
                userType="student"
                studentDetails={{ name: userName, email: userEmail }}
                focusOnLatest
              />
          </ModernTabsContent>

          <ModernTabsContent value="history" className="mt-4 px-4 pb-4">
            <div className="rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm">
              <LocationHistoryList
                  locationData={locations}
                  loading={loading}
                  error={error}
                  userType="student"
                  studentDetails={{ id: userId, name: userName, email: userEmail }}
                  onLocationSelect={setSelectedLocation}
                  selectedLocationId={selectedLocation?.id}
              />
            </div>
          </ModernTabsContent>
        </ModernTabs>
    </div>
  );
};

export default StudentLocationTabs;