import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ModernTabs, ModernTabsList, ModernTabsTrigger, ModernTabsContent } from '@/components/ui/modern-tabs';
import { ModernBadge } from '@/components/ui/modern-badge';
import StudentLocationMap from '@/components/StudentLocationMap';
import LocationHistoryList from '@/components/student/LocationHistoryList';
import { LocationData } from '@/types/database';
import { cn } from '@/lib/utils';
import { MapPin, History } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface StudentLocationTabsProps {
  userId: string;
  userName: string;
  userEmail: string;
  locations: LocationData[];
  loading: boolean;
  error: string | null;
  className?: string;
}

const StudentLocationTabs: React.FC<StudentLocationTabsProps> = ({
  userId,
  userName,
  userEmail,
  locations,
  loading,
  error,
  className
}) => {
  const [activeTab, setActiveTab] = useState('map');
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const { t } = useTranslation();
  const isMobile = useIsMobile();

  return (
    <div className={cn('h-full flex flex-col overflow-hidden', className, isMobile ? 'ios-tabs-container' : '')}>
      <ModernTabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          {/* Header das tabs - compacto e otimizado para iOS */}
          <div className={cn('flex-shrink-0 px-2 md:px-4 pt-2 md:pt-4 pb-2', isMobile ? 'ios-tabs-header mobile-tabs' : '')}>
            <ModernTabsList className="w-full grid grid-cols-2">
              <ModernTabsTrigger value="map" className={cn('flex items-center gap-2 text-xs md:text-sm', isMobile ? 'mobile-tabs' : '')}>
                <MapPin className="h-3 w-3 md:h-4 md:w-4" />
                <span className="hidden sm:inline">{t('dashboard.map', 'Map')}</span>
                <span className="sm:hidden">Mapa</span>
                {selectedLocation && (
                  <ModernBadge variant="success" size="sm">
                    {t('dashboard.selected', 'Selected')}
                  </ModernBadge>
                )}
              </ModernTabsTrigger>
              <ModernTabsTrigger value="history" className={cn('flex items-center gap-2 text-xs md:text-sm', isMobile ? 'mobile-tabs' : '')}>
                <History className="h-3 w-3 md:h-4 md:w-4" />
                <span className="hidden sm:inline">{t('dashboard.history', 'History')}</span>
                <span className="sm:hidden">Histórico</span>
                {locations.length > 0 && (
                  <ModernBadge variant="default" size="sm">
                    {locations.length}
                  </ModernBadge>
                )}
              </ModernTabsTrigger>
            </ModernTabsList>
          </div>

          {/* Conteúdo das tabs - ocupa todo espaço restante com otimizações iOS */}
          <ModernTabsContent value="map" className={cn('flex-1 px-0 md:px-2 pb-0 md:pb-4 m-0', isMobile ? 'ios-tab-content' : '')}>
              <div className={cn('h-full w-full', isMobile ? 'map-container-ios' : '')}>
                <StudentLocationMap
                  selectedUserId={userId}
                  locations={locations}
                  loading={loading}
                  error={error}
                  userType="student"
                  studentDetails={{ name: userName, email: userEmail }}
                  focusOnLatest
                />
              </div>
          </ModernTabsContent>

          <ModernTabsContent value="history" className={cn('flex-1 px-1 md:px-2 pb-2 md:pb-4 m-0', isMobile ? 'ios-tab-content' : '')}>
            <div className="h-full rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm">
              <LocationHistoryList
                  locationData={locations}
                  loading={loading}
                  error={error}
                  userType="student"
                  studentDetails={{ id: userId, name: userName, email: userEmail }}
                  onLocationSelect={setSelectedLocation}
                  selectedLocationId={selectedLocation?.id}
              />
            </div>
          </ModernTabsContent>
        </ModernTabs>
    </div>
  );
};

export default StudentLocationTabs;