
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface FamilyInvitationRequest {
  student_name: string;
  student_email: string;
  guardian_name: string;
  guardian_email: string;
  invitation_token: string;
  expires_at: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('🚀 Edge Function send-family-invitation iniciada');
    
    const body: FamilyInvitationRequest = await req.json();
    console.log('📝 Request body:', body);

    const {
      student_name,
      student_email,
      guardian_name,
      guardian_email,
      invitation_token,
      expires_at
    } = body;

    // Validar dados obrigatórios
    if (!student_name || !student_email || !guardian_name || !guardian_email || !invitation_token) {
      console.error('❌ Dados obrigatórios faltando');
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Dados obrigatórios faltando" 
        }),
        { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Criar URL de aceitação do convite
    const siteUrl = Deno.env.get("SITE_URL") || "https://monitore-mvp.lovable.app";
    const acceptUrl = `${siteUrl}/accept-invitation?token=${invitation_token}`;
    
    console.log('🔗 URL de aceitação:', acceptUrl);

    // Formatar data de expiração
    const expirationDate = new Date(expires_at).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Template do email simples (sem Resend por enquanto)
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">EduConnect - Convite de Vínculo Familiar</h1>
        
        <p>Olá <strong>${student_name}</strong>,</p>
        
        <p><strong>${guardian_name}</strong> (${guardian_email}) enviou uma solicitação para se vincular a você no EduConnect.</p>
        
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>O que isso significa?</h3>
          <ul>
            <li>O responsável poderá visualizar suas informações de localização</li>
            <li>Você manterá controle sobre quando compartilhar sua localização</li>
            <li>Este é um sistema de segurança consensual para sua proteção</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${acceptUrl}" 
             style="background: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px;">
            Aceitar Convite
          </a>
        </div>
        
        <p style="color: #6b7280; font-size: 14px;">
          ⏰ Este convite expira em <strong>${expirationDate}</strong>
        </p>
      </div>
    `;

    // Log do "envio" do email (simulado)
    console.log('📧 Email preparado para:', student_email);
    console.log('📧 Template HTML gerado com sucesso');

    // Registrar log do email
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (supabaseUrl && supabaseServiceKey) {
      const supabase = createClient(supabaseUrl, supabaseServiceKey);
      
      const { error: logError } = await supabase
        .from('email_logs')
        .insert({
          email_type: 'family_invitation',
          recipient_email: student_email,
          status: 'sent',
          metadata: {
            guardian_email,
            student_name,
            expires_at
          }
        });

      if (logError) {
        console.error('⚠️ Erro ao registrar log do email:', logError);
      } else {
        console.log('📝 Log do email registrado com sucesso');
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        message: "Convite familiar processado com sucesso",
        recipient: student_email,
        debug_html: emailHtml // Para debug
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error("💥 Erro na função send-family-invitation:", error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || "Erro interno do servidor"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);
