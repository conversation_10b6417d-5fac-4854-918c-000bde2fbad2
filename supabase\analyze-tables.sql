-- ==========================================
-- ANÁLISE DE TABELAS - IDENTIFY UNUSED/EMPTY TABLES
-- Data: 24/06/2025
-- Objetivo: Identificar tabelas vazias ou não utilizadas pelo frontend
-- ==========================================

-- 1. LISTAR TODAS AS TABELAS DO SCHEMA PUBLIC
SELECT 
    'TABELA: ' || table_name as info,
    table_name,
    'public' as schema_name
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- 2. CONTAR REGISTROS EM CADA TABELA
SELECT 
    'CONTAGEM_REGISTROS' as tipo,
    schemaname,
    tablename,
    n_tup_ins as total_inserts,
    n_tup_upd as total_updates,
    n_tup_del as total_deletes,
    n_live_tup as registros_atuais,
    n_dead_tup as registros_mortos
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY n_live_tup DESC;

-- 3. IDENTIFICAR TABELAS VAZIAS
SELECT 
    'TABELA_VAZIA' as status,
    schemaname,
    tablename,
    n_live_tup as registros
FROM pg_stat_user_tables 
WHERE schemaname = 'public' 
  AND n_live_tup = 0
ORDER BY tablename;

-- 4. VERIFICAR DEPENDÊNCIAS ENTRE TABELAS (FOREIGN KEYS)
SELECT 
    'FOREIGN_KEY' as tipo,
    tc.table_name as tabela_origem,
    kcu.column_name as coluna_origem,
    ccu.table_name AS tabela_referenciada,
    ccu.column_name AS coluna_referenciada
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, kcu.column_name;

-- 5. LISTAR FUNÇÕES RPC DISPONÍVEIS
SELECT 
    'RPC_FUNCTION' as tipo,
    routine_name as nome_funcao,
    routine_type as tipo_funcao,
    is_deterministic as deterministica
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_type = 'FUNCTION'
ORDER BY routine_name;

-- 6. VERIFICAR ESTRUTURA DE TABELAS CRÍTICAS
SELECT 
    'ESTRUTURA_GUARDIANS' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'guardians'
ORDER BY ordinal_position;

-- 7. VERIFICAR ESTRUTURA DA TABELA PROFILES
SELECT 
    'ESTRUTURA_PROFILES' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 8. VERIFICAR SE ACCOUNT_DELETION_REQUESTS EXISTE
SELECT 
    'ESTRUTURA_ACCOUNT_DELETION' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'account_deletion_requests'
ORDER BY ordinal_position;

-- 9. BUSCAR DADOS NA TABELA GUARDIANS PARA FRANK
SELECT 
    'DADOS_GUARDIANS_FRANK' as info,
    *
FROM public.guardians 
WHERE email = '<EMAIL>'
LIMIT 5;

-- 10. VERIFICAR SE RPC GET_GUARDIAN_DELETION_REQUESTS EXISTE
SELECT 
    'CHECK_RPC_DELETION' as info,
    routine_name,
    routine_definition
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name = 'get_guardian_deletion_requests';

-- 11. RESUMO FINAL - TABELAS POTENCIALMENTE INÚTEIS
WITH table_usage AS (
    SELECT 
        tablename,
        n_live_tup as registros_atuais,
        n_tup_ins as total_inserts,
        n_tup_upd + n_tup_del as total_modifications
    FROM pg_stat_user_tables 
    WHERE schemaname = 'public'
),
foreign_key_usage AS (
    SELECT DISTINCT
        tc.table_name,
        COUNT(*) as fk_count
    FROM information_schema.table_constraints AS tc
    WHERE tc.constraint_type = 'FOREIGN KEY' 
      AND tc.table_schema = 'public'
    GROUP BY tc.table_name
)
SELECT 
    'CANDIDATAS_REMOCAO' as analise,
    tu.tablename,
    tu.registros_atuais,
    tu.total_inserts,
    tu.total_modifications,
    COALESCE(fku.fk_count, 0) as foreign_keys,
    CASE 
        WHEN tu.registros_atuais = 0 AND tu.total_inserts = 0 THEN 'NUNCA_USADA'
        WHEN tu.registros_atuais = 0 AND tu.total_inserts > 0 THEN 'DADOS_REMOVIDOS'
        WHEN tu.registros_atuais > 0 AND tu.total_modifications = 0 THEN 'APENAS_LEITURA'
        ELSE 'EM_USO'
    END as status_uso
FROM table_usage tu
LEFT JOIN foreign_key_usage fku ON tu.tablename = fku.table_name
ORDER BY 
    tu.registros_atuais ASC,
    tu.total_inserts ASC; 