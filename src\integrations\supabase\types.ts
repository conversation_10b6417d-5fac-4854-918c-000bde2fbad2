export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      account_deletion_requests: {
        Row: {
          created_at: string
          guardian_notes: string | null
          id: string
          processed_at: string | null
          processed_by_guardian_email: string | null
          reason: string | null
          requested_at: string
          status: string
          student_email: string
          student_id: string
          student_name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          guardian_notes?: string | null
          id?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          reason?: string | null
          requested_at?: string
          status?: string
          student_email: string
          student_id: string
          student_name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          guardian_notes?: string | null
          id?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          reason?: string | null
          requested_at?: string
          status?: string
          student_email?: string
          student_id?: string
          student_name?: string
          updated_at?: string
        }
        Relationships: []
      }
      auth_logs: {
        Row: {
          event_type: string | null
          id: number
          metadata: Json | null
          occurred_at: string | null
          user_id: string | null
        }
        Insert: {
          event_type?: string | null
          id?: number
          metadata?: Json | null
          occurred_at?: string | null
          user_id?: string | null
        }
        Update: {
          event_type?: string | null
          id?: number
          metadata?: Json | null
          occurred_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      email_logs: {
        Row: {
          created_at: string | null
          email_type: string
          id: string
          metadata: Json | null
          recipient_email: string
          status: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          email_type: string
          id?: string
          metadata?: Json | null
          recipient_email: string
          status?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          email_type?: string
          id?: string
          metadata?: Json | null
          recipient_email?: string
          status?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      family_invitations: {
        Row: {
          accepted_at: string | null
          accepted_by_guardian_id: string | null
          activation_token: string | null
          created_at: string
          expires_at: string
          guardian_email: string | null
          guardian_id: string | null
          id: string
          invitation_token: string
          invitation_type: string | null
          status: string
          student_cpf: string | null
          student_email: string | null
          student_id: string | null
          student_name: string
          student_phone: string | null
          temp_password: string | null
          updated_at: string | null
        }
        Insert: {
          accepted_at?: string | null
          accepted_by_guardian_id?: string | null
          activation_token?: string | null
          created_at?: string
          expires_at?: string
          guardian_email?: string | null
          guardian_id?: string | null
          id?: string
          invitation_token: string
          invitation_type?: string | null
          status?: string
          student_cpf?: string | null
          student_email?: string | null
          student_id?: string | null
          student_name: string
          student_phone?: string | null
          temp_password?: string | null
          updated_at?: string | null
        }
        Update: {
          accepted_at?: string | null
          accepted_by_guardian_id?: string | null
          activation_token?: string | null
          created_at?: string
          expires_at?: string
          guardian_email?: string | null
          guardian_id?: string | null
          id?: string
          invitation_token?: string
          invitation_type?: string | null
          status?: string
          student_cpf?: string | null
          student_email?: string | null
          student_id?: string | null
          student_name?: string
          student_phone?: string | null
          temp_password?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      geofences: {
        Row: {
          created_at: string | null
          id: string
          latitude: number
          longitude: number
          name: string
          radius: number
          type: Database["public"]["Enums"]["geofence_type"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          latitude: number
          longitude: number
          name: string
          radius: number
          type: Database["public"]["Enums"]["geofence_type"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          latitude?: number
          longitude?: number
          name?: string
          radius?: number
          type?: Database["public"]["Enums"]["geofence_type"]
          updated_at?: string | null
        }
        Relationships: []
      }
      guardian_link_requests: {
        Row: {
          created_at: string
          current_guardian_id: string
          expires_at: string
          id: string
          justification: string
          processed_at: string | null
          processed_by_guardian_email: string | null
          requesting_guardian_email: string
          requesting_guardian_id: string
          requesting_guardian_name: string
          response_message: string | null
          status: string
          student_cpf: string
          student_id: string
          student_name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          current_guardian_id: string
          expires_at?: string
          id?: string
          justification: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          requesting_guardian_email: string
          requesting_guardian_id: string
          requesting_guardian_name: string
          response_message?: string | null
          status?: string
          student_cpf: string
          student_id: string
          student_name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          current_guardian_id?: string
          expires_at?: string
          id?: string
          justification?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          requesting_guardian_email?: string
          requesting_guardian_id?: string
          requesting_guardian_name?: string
          response_message?: string | null
          status?: string
          student_cpf?: string
          student_id?: string
          student_name?: string
          updated_at?: string
        }
        Relationships: []
      }
      guardian_profiles: {
        Row: {
          birth_date: string | null
          cpf: string | null
          created_at: string | null
          email: string
          full_name: string
          id: string
          phone: string | null
          status: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          birth_date?: string | null
          cpf?: string | null
          created_at?: string | null
          email: string
          full_name: string
          id?: string
          phone?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          birth_date?: string | null
          cpf?: string | null
          created_at?: string | null
          email?: string
          full_name?: string
          id?: string
          phone?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      guardian_removal_requests: {
        Row: {
          created_at: string
          guardian_email: string
          guardian_id: string
          guardian_notes: string | null
          id: string
          processed_at: string | null
          processed_by_guardian_email: string | null
          reason: string | null
          requested_at: string
          status: string
          student_id: string
          student_name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          guardian_email: string
          guardian_id: string
          guardian_notes?: string | null
          id?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          reason?: string | null
          requested_at?: string
          status?: string
          student_id: string
          student_name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          guardian_email?: string
          guardian_id?: string
          guardian_notes?: string | null
          id?: string
          processed_at?: string | null
          processed_by_guardian_email?: string | null
          reason?: string | null
          requested_at?: string
          status?: string
          student_id?: string
          student_name?: string
          updated_at?: string
        }
        Relationships: []
      }
      location_history: {
        Row: {
          accuracy: number | null
          battery_level: number | null
          bearing: number | null
          created_at: string | null
          id: string
          is_mocked: boolean | null
          latitude: number
          location_id: string | null
          longitude: number
          source: string | null
          speed: number | null
          user_id: string | null
        }
        Insert: {
          accuracy?: number | null
          battery_level?: number | null
          bearing?: number | null
          created_at?: string | null
          id?: string
          is_mocked?: boolean | null
          latitude: number
          location_id?: string | null
          longitude: number
          source?: string | null
          speed?: number | null
          user_id?: string | null
        }
        Update: {
          accuracy?: number | null
          battery_level?: number | null
          bearing?: number | null
          created_at?: string | null
          id?: string
          is_mocked?: boolean | null
          latitude?: number
          location_id?: string | null
          longitude?: number
          source?: string | null
          speed?: number | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "location_history_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      location_notifications: {
        Row: {
          created_at: string | null
          guardian_email: string
          guardian_id: string | null
          id: string
          location_id: string | null
          status: string
          student_id: string
          viewed_at: string | null
        }
        Insert: {
          created_at?: string | null
          guardian_email: string
          guardian_id?: string | null
          id?: string
          location_id?: string | null
          status?: string
          student_id: string
          viewed_at?: string | null
        }
        Update: {
          created_at?: string | null
          guardian_email?: string
          guardian_id?: string | null
          id?: string
          location_id?: string | null
          status?: string
          student_id?: string
          viewed_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "location_notifications_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      locations: {
        Row: {
          accuracy: number | null
          address: string | null
          battery_level: number | null
          bearing: number | null
          created_at: string | null
          id: string
          is_mocked: boolean | null
          latitude: number
          longitude: number
          shared_with_guardians: boolean | null
          source: string | null
          speed: number | null
          timestamp: string
          user_id: string
        }
        Insert: {
          accuracy?: number | null
          address?: string | null
          battery_level?: number | null
          bearing?: number | null
          created_at?: string | null
          id?: string
          is_mocked?: boolean | null
          latitude: number
          longitude: number
          shared_with_guardians?: boolean | null
          source?: string | null
          speed?: number | null
          timestamp?: string
          user_id?: string
        }
        Update: {
          accuracy?: number | null
          address?: string | null
          battery_level?: number | null
          bearing?: number | null
          created_at?: string | null
          id?: string
          is_mocked?: boolean | null
          latitude?: number
          longitude?: number
          shared_with_guardians?: boolean | null
          source?: string | null
          speed?: number | null
          timestamp?: string
          user_id?: string
        }
        Relationships: []
      }
      notification_logs: {
        Row: {
          content: string | null
          delivered_at: string | null
          error_message: string | null
          id: string
          message_type: string
          metadata: Json | null
          method: string
          recipient: string
          recipient_id: string | null
          sent_at: string | null
          status: string | null
          user_id: string
        }
        Insert: {
          content?: string | null
          delivered_at?: string | null
          error_message?: string | null
          id?: string
          message_type: string
          metadata?: Json | null
          method: string
          recipient: string
          recipient_id?: string | null
          sent_at?: string | null
          status?: string | null
          user_id: string
        }
        Update: {
          content?: string | null
          delivered_at?: string | null
          error_message?: string | null
          id?: string
          message_type?: string
          metadata?: Json | null
          method?: string
          recipient?: string
          recipient_id?: string | null
          sent_at?: string | null
          status?: string | null
          user_id?: string
        }
        Relationships: []
      }
      parent_confirmation_invites: {
        Row: {
          confirmed_at: string | null
          confirmed_by_parent_id: string | null
          created_at: string
          expires_at: string
          id: string
          parent_cpf: string
          parent_email: string
          status: string
          student_birth_date: string
          student_email: string
          student_id: string
          student_name: string
          token: string
        }
        Insert: {
          confirmed_at?: string | null
          confirmed_by_parent_id?: string | null
          created_at?: string
          expires_at?: string
          id?: string
          parent_cpf: string
          parent_email: string
          status?: string
          student_birth_date: string
          student_email: string
          student_id: string
          student_name: string
          token: string
        }
        Update: {
          confirmed_at?: string | null
          confirmed_by_parent_id?: string | null
          created_at?: string
          expires_at?: string
          id?: string
          parent_cpf?: string
          parent_email?: string
          status?: string
          student_birth_date?: string
          student_email?: string
          student_id?: string
          student_name?: string
          token?: string
        }
        Relationships: []
      }
      pending_shares: {
        Row: {
          created_at: string | null
          expires_at: string | null
          guardian_email: string
          id: string
          status: string | null
          student_id: string
        }
        Insert: {
          created_at?: string | null
          expires_at?: string | null
          guardian_email: string
          id?: string
          status?: string | null
          student_id: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string | null
          guardian_email?: string
          id?: string
          status?: string | null
          student_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          account_creation_method: string | null
          activated_at: string | null
          activation_expires_at: string | null
          activation_token: string | null
          birth_date: string | null
          class_name: string | null
          cpf: string | null
          created_at: string | null
          created_by_user_id: string | null
          email: string
          full_name: string | null
          grade: string | null
          id: number
          last_login_at: string | null
          login_count: number | null
          parent_cpf: string | null
          parent_email: string | null
          phone: string | null
          registration_status: string | null
          requires_parent_confirmation: boolean | null
          requires_password_change: boolean | null
          school_id: string | null
          school_name: string | null
          status: string | null
          updated_at: string | null
          user_id: string | null
          user_type: string
        }
        Insert: {
          account_creation_method?: string | null
          activated_at?: string | null
          activation_expires_at?: string | null
          activation_token?: string | null
          birth_date?: string | null
          class_name?: string | null
          cpf?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          email?: string
          full_name?: string | null
          grade?: string | null
          id?: number
          last_login_at?: string | null
          login_count?: number | null
          parent_cpf?: string | null
          parent_email?: string | null
          phone?: string | null
          registration_status?: string | null
          requires_parent_confirmation?: boolean | null
          requires_password_change?: boolean | null
          school_id?: string | null
          school_name?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_type: string
        }
        Update: {
          account_creation_method?: string | null
          activated_at?: string | null
          activation_expires_at?: string | null
          activation_token?: string | null
          birth_date?: string | null
          class_name?: string | null
          cpf?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          email?: string
          full_name?: string | null
          grade?: string | null
          id?: number
          last_login_at?: string | null
          login_count?: number | null
          parent_cpf?: string | null
          parent_email?: string | null
          phone?: string | null
          registration_status?: string | null
          requires_parent_confirmation?: boolean | null
          requires_password_change?: boolean | null
          school_id?: string | null
          school_name?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_type?: string
        }
        Relationships: []
      }
      removal_requests: {
        Row: {
          created_at: string | null
          expires_at: string | null
          guardian_email: string
          guardian_id: string
          guardian_name: string
          id: string
          processed_at: string | null
          processed_by: string | null
          reason: string | null
          request_token: string
          status: string | null
          student_id: string
          student_name: string
        }
        Insert: {
          created_at?: string | null
          expires_at?: string | null
          guardian_email: string
          guardian_id: string
          guardian_name: string
          id?: string
          processed_at?: string | null
          processed_by?: string | null
          reason?: string | null
          request_token?: string
          status?: string | null
          student_id: string
          student_name: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string | null
          guardian_email?: string
          guardian_id?: string
          guardian_name?: string
          id?: string
          processed_at?: string | null
          processed_by?: string | null
          reason?: string | null
          request_token?: string
          status?: string | null
          student_id?: string
          student_name?: string
        }
        Relationships: []
      }
      student_guardian_relationships: {
        Row: {
          created_at: string | null
          guardian_id: string
          id: string
          is_primary: boolean | null
          relationship_type: string
          student_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          guardian_id: string
          id?: string
          is_primary?: boolean | null
          relationship_type: string
          student_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          guardian_id?: string
          id?: string
          is_primary?: boolean | null
          relationship_type?: string
          student_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      student_location_history: {
        Row: {
          accuracy: number | null
          battery_level: number | null
          created_at: string | null
          id: string
          latitude: number
          longitude: number
          student_id: string
          timestamp: string | null
        }
        Insert: {
          accuracy?: number | null
          battery_level?: number | null
          created_at?: string | null
          id?: string
          latitude: number
          longitude: number
          student_id: string
          timestamp?: string | null
        }
        Update: {
          accuracy?: number | null
          battery_level?: number | null
          created_at?: string | null
          id?: string
          latitude?: number
          longitude?: number
          student_id?: string
          timestamp?: string | null
        }
        Relationships: []
      }
      student_permission_geofences: {
        Row: {
          geofence_id: string
          permission_id: string
        }
        Insert: {
          geofence_id: string
          permission_id: string
        }
        Update: {
          geofence_id?: string
          permission_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "student_permission_geofences_geofence_id_fkey"
            columns: ["geofence_id"]
            isOneToOne: false
            referencedRelation: "geofences"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_permission_geofences_permission_id_fkey"
            columns: ["permission_id"]
            isOneToOne: false
            referencedRelation: "student_permissions"
            referencedColumns: ["id"]
          },
        ]
      }
      student_permission_time_ranges: {
        Row: {
          permission_id: string
          time_range_id: string
        }
        Insert: {
          permission_id: string
          time_range_id: string
        }
        Update: {
          permission_id?: string
          time_range_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "student_permission_time_ranges_permission_id_fkey"
            columns: ["permission_id"]
            isOneToOne: false
            referencedRelation: "student_permissions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_permission_time_ranges_time_range_id_fkey"
            columns: ["time_range_id"]
            isOneToOne: false
            referencedRelation: "time_ranges"
            referencedColumns: ["id"]
          },
        ]
      }
      student_permissions: {
        Row: {
          created_at: string | null
          guardian_id: string
          id: string
          notification_preferences: Json
          student_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          guardian_id: string
          id?: string
          notification_preferences?: Json
          student_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          guardian_id?: string
          id?: string
          notification_preferences?: Json
          student_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      students_backup_final_31jan2025: {
        Row: {
          backup_created_at: string | null
          class: string | null
          created_at: string | null
          grade: string | null
          guardian_id: string | null
          id: string | null
          last_location: Json | null
          location_sharing: boolean | null
          name: string | null
          school_id: string | null
          school_name: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          backup_created_at?: string | null
          class?: string | null
          created_at?: string | null
          grade?: string | null
          guardian_id?: string | null
          id?: string | null
          last_location?: Json | null
          location_sharing?: boolean | null
          name?: string | null
          school_id?: string | null
          school_name?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          backup_created_at?: string | null
          class?: string | null
          created_at?: string | null
          grade?: string | null
          guardian_id?: string | null
          id?: string | null
          last_location?: Json | null
          location_sharing?: boolean | null
          name?: string | null
          school_id?: string | null
          school_name?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      time_ranges: {
        Row: {
          created_at: string | null
          day_of_week: number | null
          end_time: string
          id: string
          start_time: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          day_of_week?: number | null
          end_time: string
          id?: string
          start_time: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          day_of_week?: number | null
          end_time?: string
          id?: string
          start_time?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      user_communication_preferences: {
        Row: {
          created_at: string | null
          email_enabled: boolean | null
          id: string
          phone_number: string | null
          preferred_method: string | null
          sms_enabled: boolean | null
          updated_at: string | null
          user_id: string
          whatsapp_enabled: boolean | null
        }
        Insert: {
          created_at?: string | null
          email_enabled?: boolean | null
          id?: string
          phone_number?: string | null
          preferred_method?: string | null
          sms_enabled?: boolean | null
          updated_at?: string | null
          user_id: string
          whatsapp_enabled?: boolean | null
        }
        Update: {
          created_at?: string | null
          email_enabled?: boolean | null
          id?: string
          phone_number?: string | null
          preferred_method?: string | null
          sms_enabled?: boolean | null
          updated_at?: string | null
          user_id?: string
          whatsapp_enabled?: boolean | null
        }
        Relationships: []
      }
      user_settings: {
        Row: {
          auto_location_sharing: boolean | null
          created_at: string | null
          data_retention_days: number | null
          email_notifications: boolean | null
          font_size: string | null
          high_contrast: boolean | null
          id: string
          layout_style: string | null
          location_history_enabled: boolean | null
          profile_visibility: boolean | null
          push_notifications: boolean | null
          reduced_motion: boolean | null
          sms_notifications: boolean | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          auto_location_sharing?: boolean | null
          created_at?: string | null
          data_retention_days?: number | null
          email_notifications?: boolean | null
          font_size?: string | null
          high_contrast?: boolean | null
          id?: string
          layout_style?: string | null
          location_history_enabled?: boolean | null
          profile_visibility?: boolean | null
          push_notifications?: boolean | null
          reduced_motion?: boolean | null
          sms_notifications?: boolean | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          auto_location_sharing?: boolean | null
          created_at?: string | null
          data_retention_days?: number | null
          email_notifications?: boolean | null
          font_size?: string | null
          high_contrast?: boolean | null
          id?: string
          layout_style?: string | null
          location_history_enabled?: boolean | null
          profile_visibility?: boolean | null
          push_notifications?: boolean | null
          reduced_motion?: boolean | null
          sms_notifications?: boolean | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      webhook_events: {
        Row: {
          created_at: string | null
          data: Json
          id: string
          processed: boolean | null
          processed_at: string | null
          signature: string | null
          source: string | null
          type: string
        }
        Insert: {
          created_at?: string | null
          data?: Json
          id?: string
          processed?: boolean | null
          processed_at?: string | null
          signature?: string | null
          source?: string | null
          type: string
        }
        Update: {
          created_at?: string | null
          data?: Json
          id?: string
          processed?: boolean | null
          processed_at?: string | null
          signature?: string | null
          source?: string | null
          type?: string
        }
        Relationships: []
      }
    }
    Views: {
      profiles_cpf_check: {
        Row: {
          cpf: string | null
          full_name: string | null
          id: number | null
        }
        Insert: {
          cpf?: string | null
          full_name?: string | null
          id?: number | null
        }
        Update: {
          cpf?: string | null
          full_name?: string | null
          id?: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      accept_family_invitation: {
        Args: { p_invitation_token: string }
        Returns: {
          success: boolean
          message: string
        }[]
      }
      accept_student_request: {
        Args: { p_invitation_token: string }
        Returns: {
          success: boolean
          message: string
        }[]
      }
      activate_student_account: {
        Args: { p_activation_token: string; p_new_password: string }
        Returns: {
          success: boolean
          message: string
        }[]
      }
      add_guardian_relationship: {
        Args: {
          p_student_id: string
          p_guardian_email: string
          p_guardian_name?: string
          p_guardian_phone?: string
        }
        Returns: boolean
      }
      admin_update_profile: {
        Args: {
          p_user_id: string
          p_full_name: string
          p_email: string
          p_phone: string
          p_user_type: string
          p_cpf: string
          p_birth_date?: string
        }
        Returns: Json
      }
      check_cpf_exists: {
        Args: { p_cpf: string; p_exclude_user_id?: string }
        Returns: boolean
      }
      check_cpf_validation: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
          total_records: number
          null_cpfs: number
          invalid_cpfs: number
        }[]
      }
      check_guardian_relationship: {
        Args: { guardian_email: string; student_id: string }
        Returns: boolean
      }
      check_student_exists_by_cpf: {
        Args: { p_cpf: string }
        Returns: {
          student_id: string
          student_name: string
          student_email: string
          current_guardian_id: string
          current_guardian_name: string
          current_guardian_email: string
        }[]
      }
      cleanup_expired_family_invitations: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_expired_invites: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      complete_student_account_creation: {
        Args: { p_invitation_id: string; p_auth_user_id: string }
        Returns: {
          success: boolean
          message: string
        }[]
      }
      create_account_deletion_request: {
        Args: { p_reason?: string }
        Returns: string
      }
      create_guardian_removal_request: {
        Args: {
          p_guardian_id: string
          p_guardian_email: string
          p_reason?: string
        }
        Returns: string
      }
      create_profile_from_edge_function: {
        Args: {
          p_user_id: string
          p_full_name: string
          p_user_type: string
          p_cpf?: string
          p_email?: string
          p_phone?: string
          p_account_creation_method?: string
          p_created_by_user_id?: string
          p_requires_password_change?: boolean
          p_activation_token?: string
          p_activation_expires_at?: string
          p_status?: string
          p_registration_status?: string
        }
        Returns: {
          success: boolean
          message: string
          profile_id: string
        }[]
      }
      create_student_account_direct: {
        Args: {
          p_student_name: string
          p_student_email: string
          p_student_cpf: string
          p_guardian_id: string
          p_student_phone?: string
        }
        Returns: {
          success: boolean
          message: string
          student_id: string
          temp_password: string
          activation_token: string
        }[]
      }
      create_student_account_for_guardian: {
        Args: {
          p_guardian_id: string
          p_student_name: string
          p_student_email: string
          p_student_cpf: string
          p_student_phone?: string
        }
        Returns: {
          success: boolean
          message: string
          student_id: string
          temp_password: string
          activation_token: string
        }[]
      }
      create_student_invitation_for_fallback: {
        Args: {
          p_student_name: string
          p_student_email: string
          p_student_cpf: string
          p_guardian_id: string
          p_student_phone?: string
        }
        Returns: {
          success: boolean
          message: string
          invitation_id: string
          temp_password: string
          activation_token: string
        }[]
      }
      create_student_with_guardian: {
        Args: {
          p_student_name: string
          p_student_email: string
          p_student_cpf: string
          p_student_phone?: string
          p_temp_password?: string
        }
        Returns: {
          success: boolean
          message: string
          student_id: string
          activation_token: string
        }[]
      }
      create_test_location: {
        Args: {
          p_student_id: string
          p_latitude: number
          p_longitude: number
          p_address: string
        }
        Returns: string
      }
      debug_cpf_conflicts: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          full_name: string
          profiles_cpf: string
          guardian_profiles_cpf: string
          conflict_type: string
        }[]
      }
      debug_guardian_access: {
        Args: { p_guardian_email: string; p_student_id: string }
        Returns: {
          guardian_exists: boolean
          student_exists: boolean
          relationship_active: boolean
          location_count: number
        }[]
      }
      debug_guardian_table_structure: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
          column_name: string
          data_type: string
          is_nullable: string
        }[]
      }
      debug_jwt_email: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      debug_list_students_for_guardian: {
        Args: { p_guardian_email: string }
        Returns: {
          student_id: string
          student_email: string
          student_name: string
          relationship_created: string
          is_active: boolean
        }[]
      }
      debug_user_guardian_relationships: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          user_email: string
          relationship_info: string
        }[]
      }
      delete_student_locations_duplicates: {
        Args: {
          p_user_id: string
          p_radius_meters?: number
          p_time_window_min?: number
          p_accuracy?: string
        }
        Returns: number
      }
      execute_guardian_removal: {
        Args: { p_request_id: string }
        Returns: boolean
      }
      format_phone: {
        Args: { phone: string }
        Returns: string
      }
      get_guardian_deletion_requests: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          student_id: string
          student_email: string
          student_name: string
          reason: string
          status: string
          requested_at: string
          processed_at: string
          guardian_notes: string
          created_at: string
          updated_at: string
        }[]
      }
      get_guardian_link_requests: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          student_id: string
          student_name: string
          student_cpf: string
          requesting_guardian_name: string
          requesting_guardian_email: string
          justification: string
          status: string
          created_at: string
          processed_at: string
          response_message: string
          expires_at: string
        }[]
      }
      get_guardian_locations_bypass: {
        Args: { p_student_id: string }
        Returns: Json[]
      }
      get_guardian_locations_bypass_v2: {
        Args: { p_student_id: string }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          shared_with_guardians: boolean
          student_name: string
        }[]
      }
      get_guardian_locations_secure: {
        Args: { p_student_id: string }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          shared_with_guardians: boolean
          student_name: string
        }[]
      }
      get_guardian_notifications: {
        Args: { p_guardian_email: string }
        Returns: {
          id: string
          location_id: string
          student_id: string
          student_name: string
          status: string
          created_at: string
          latitude: number
          longitude: number
        }[]
      }
      get_guardian_pending_invitations: {
        Args: Record<PropertyKey, never>
        Returns: {
          invitation_id: string
          student_name: string
          student_email: string
          invitation_token: string
          created_at: string
          expires_at: string
        }[]
      }
      get_guardian_removal_requests: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          student_id: string
          student_name: string
          student_email: string
          reason: string
          created_at: string
          expires_at: string
          request_token: string
        }[]
      }
      get_guardian_students: {
        Args: Record<PropertyKey, never>
        Returns: {
          student_id: string
          student_email: string
          student_name: string
        }[]
      }
      get_guardian_students_locations: {
        Args: Record<PropertyKey, never>
        Returns: {
          accuracy: number | null
          address: string | null
          battery_level: number | null
          bearing: number | null
          created_at: string | null
          id: string
          is_mocked: boolean | null
          latitude: number
          longitude: number
          shared_with_guardians: boolean | null
          source: string | null
          speed: number | null
          timestamp: string
          user_id: string
        }[]
      }
      get_latest_location_for_all_students: {
        Args: Record<PropertyKey, never>
        Returns: {
          student_id: string
          student_name: string
          latitude: number
          longitude: number
          timestamp: string
          address: string
        }[]
      }
      get_removal_request_by_token: {
        Args: { p_token: string }
        Returns: {
          id: string
          student_name: string
          student_email: string
          guardian_name: string
          reason: string
          created_at: string
          expires_at: string
          request_token: string
        }[]
      }
      get_student_guardians_from_relationships: {
        Args: { p_student_id: string }
        Returns: {
          id: string
          student_id: string
          email: string
          full_name: string
          phone: string
          is_active: boolean
          created_at: string
        }[]
      }
      get_student_guardians_secure: {
        Args: { p_student_id?: string }
        Returns: {
          id: string
          student_id: string
          email: string
          full_name: string
          phone: string
          is_active: boolean
          created_at: string
        }[]
      }
      get_student_locations: {
        Args: { p_guardian_email: string; p_student_id: string }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          student_name: string
          student_email: string
        }[]
      }
      get_student_locations_deduped: {
        Args: { p_student_id: string; p_start?: string; p_end?: string }
        Returns: {
          id: string
          student_id: string
          latitude: number
          longitude: number
          accuracy: number
          timestamp: string
          address: string
        }[]
      }
      get_student_locations_for_guardian: {
        Args:
          | { p_student_id: string }
          | { p_student_id: string; p_time_filter?: unknown }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          timestamp: string
          address: string
          shared_with_guardians: boolean
          student_name: string
        }[]
      }
      get_student_locations_with_names: {
        Args: { p_student_id: string; p_time_filter?: unknown }
        Returns: {
          id: string
          lat: number
          lng: number
          timestamp_col: string
          user_id: string
          student_name: string
          student_email: string
          guardian_name: string
          guardian_email: string
          location_type: string
        }[]
      }
      get_student_pending_requests: {
        Args: Record<PropertyKey, never>
        Returns: {
          invitation_id: string
          guardian_email: string
          invitation_token: string
          created_at: string
          expires_at: string
        }[]
      }
      get_student_removal_requests: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          student_id: string
          guardian_id: string
          guardian_email: string
          student_name: string
          reason: string
          status: string
          requested_at: string
          processed_at: string
          guardian_notes: string
        }[]
      }
      get_unread_notifications_count: {
        Args: { p_guardian_email: string }
        Returns: number
      }
      get_user_communication_preferences: {
        Args: { p_user_id?: string }
        Returns: {
          user_id: string
          email_enabled: boolean
          sms_enabled: boolean
          whatsapp_enabled: boolean
          phone_number: string
          preferred_method: string
        }[]
      }
      insert_student_test_location: {
        Args: {
          p_student_id: string
          p_latitude?: number
          p_longitude?: number
          p_address?: string
          p_shared?: boolean
        }
        Returns: string
      }
      is_guardian_of_student: {
        Args:
          | Record<PropertyKey, never>
          | { student_id: string; guardian_email: string }
          | { student_id_param: string }
        Returns: boolean
      }
      is_strong_password: {
        Args: { password: string }
        Returns: boolean
      }
      is_valid_cpf: {
        Args: { cpf_input: string } | { cpf_input: string; user_type?: string }
        Returns: boolean
      }
      is_valid_email: {
        Args: { email: string }
        Returns: boolean
      }
      is_valid_guardian: {
        Args: { student_id_param: string; guardian_email_param: string }
        Returns: boolean
      }
      is_valid_phone: {
        Args: { phone_number: string }
        Returns: boolean
      }
      log_application_error: {
        Args:
          | Record<PropertyKey, never>
          | {
              p_error_message: string
              p_stack_trace: string
              p_user_id?: string
            }
        Returns: undefined
      }
      log_debug: {
        Args: { message: string }
        Returns: undefined
      }
      process_account_deletion_request: {
        Args: {
          p_request_id: string
          p_action: string
          p_guardian_notes?: string
        }
        Returns: boolean
      }
      process_guardian_link_request: {
        Args: {
          p_request_id: string
          p_action: string
          p_response_message?: string
        }
        Returns: boolean
      }
      process_guardian_removal_request: {
        Args: {
          p_request_id: string
          p_action: string
          p_guardian_notes?: string
        }
        Returns: boolean
      }
      process_removal_request: {
        Args: { p_token: string; p_action: string }
        Returns: Json
      }
      process_webhook_event: {
        Args: Record<PropertyKey, never> | { event_id: string }
        Returns: boolean
      }
      request_guardian_removal: {
        Args: { p_guardian_id: string; p_reason?: string }
        Returns: Json
      }
      request_student_connection: {
        Args: { p_student_email: string; p_student_cpf?: string }
        Returns: {
          success: boolean
          message: string
          invitation_id: string
        }[]
      }
      safe_update_profile: {
        Args: {
          p_full_name: string
          p_email: string
          p_phone: string
          p_user_type: string
          p_cpf: string
          p_birth_date?: string
        }
        Returns: Json
      }
      save_communication_preferences: {
        Args: {
          p_email_enabled?: boolean
          p_sms_enabled?: boolean
          p_whatsapp_enabled?: boolean
          p_phone_number?: string
          p_preferred_method?: string
        }
        Returns: boolean
      }
      save_student_location: {
        Args:
          | {
              p_latitude: number
              p_longitude: number
              p_shared_with_guardians?: boolean
            }
          | {
              p_latitude: number
              p_longitude: number
              p_shared_with_guardians?: boolean
              p_accuracy?: number
              p_address?: string
            }
        Returns: {
          id: string
          success: boolean
          message: string
        }[]
      }
      send_email_via_edge_function: {
        Args: Record<PropertyKey, never>
        Returns: {
          success: boolean
          message: string
          result: Json
        }[]
      }
      send_family_invitation: {
        Args: { p_guardian_email: string }
        Returns: {
          success: boolean
          message: string
          invitation_id: string
        }[]
      }
      send_student_email_direct: {
        Args: Record<PropertyKey, never>
        Returns: {
          success: boolean
          message: string
          email_data: Json
        }[]
      }
      test_guardian_access: {
        Args: { p_guardian_email?: string; p_student_id?: string }
        Returns: {
          has_permission: boolean
          guardian_count: number
          location_count: number
          locations_sample: Json
        }[]
      }
      test_location_access: {
        Args: { p_student_id: string }
        Returns: {
          id: string
          user_id: string
          latitude: number
          longitude: number
          location_timestamp: string
          address: string
          shared_with_guardians: boolean
          student_name: string
          student_user_id: string
        }[]
      }
      update_profile_basic: {
        Args: { p_full_name?: string; p_phone?: string }
        Returns: boolean
      }
      update_user_password_to_no_space: {
        Args: Record<PropertyKey, never>
        Returns: {
          success: boolean
          message: string
          user_id: string
        }[]
      }
      validate_email: {
        Args: { email: string }
        Returns: boolean
      }
      validate_phone: {
        Args: { phone: string }
        Returns: boolean
      }
      validate_student_account_data: {
        Args: {
          p_student_name: string
          p_student_email: string
          p_student_cpf: string
          p_student_phone?: string
        }
        Returns: {
          success: boolean
          message: string
          validation_id: string
        }[]
      }
      validate_student_data: {
        Args: {
          p_student_name: string
          p_student_email: string
          p_student_cpf: string
        }
        Returns: {
          success: boolean
          message: string
        }[]
      }
      verify_migration_integrity: {
        Args: Record<PropertyKey, never>
        Returns: {
          check_name: string
          count_value: number
          status: string
        }[]
      }
      verify_user_integrity: {
        Args: Record<PropertyKey, never>
        Returns: {
          email: string
          auth_exists: boolean
          user_exists: boolean
          profile_exists: boolean
          missing_data: string
        }[]
      }
    }
    Enums: {
      geofence_type: "allowed" | "restricted"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      geofence_type: ["allowed", "restricted"],
    },
  },
} as const
