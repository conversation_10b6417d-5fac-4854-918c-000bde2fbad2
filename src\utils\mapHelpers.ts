export const throttle = <T extends unknown[]>(
  func: (...args: T) => void, 
  delay: number
) => {
  let timeoutId: NodeJS.Timeout;
  let lastExecTime = 0;
  return (...args: T) => {
    const currentTime = Date.now();
    
    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
};

export const isParentDashboard = (): boolean => {
  return (
    window.location.pathname.includes('parent-dashboard') || 
    window.location.pathname.includes('parent/dashboard') ||
    window.location.pathname.includes('guardian') ||
    document.title.toLowerCase().includes('responsável')
  );
};

// Function to calculate a smart center and zoom based on the locations
export const calculateSmartCenter = (locations: Array<{latitude: number, longitude: number}>) => {
  // If there are no locations, fall back to São Paulo
  if (!locations || locations.length === 0) {
    return {
      latitude: -23.5489,
      longitude: -46.6388,
      zoom: 12
    };
  }

  // If there is only one location, use it as the center
  if (locations.length === 1) {
    return {
      latitude: locations[0].latitude,
      longitude: locations[0].longitude,
      zoom: isParentDashboard() ? 17 : 15
    };
  }

  // For multiple locations, calculate the geographic center
  const avgLat = locations.reduce((sum, loc) => sum + loc.latitude, 0) / locations.length;
  const avgLng = locations.reduce((sum, loc) => sum + loc.longitude, 0) / locations.length;

  // Calculate the zoom based on how spread out the locations are
  const latRange = Math.max(...locations.map(l => l.latitude)) - Math.min(...locations.map(l => l.latitude));
  const lngRange = Math.max(...locations.map(l => l.longitude)) - Math.min(...locations.map(l => l.longitude));
  const maxRange = Math.max(latRange, lngRange);

  let zoom = 15;
  if (maxRange > 0.1) zoom = 10;        // Very scattered
  else if (maxRange > 0.05) zoom = 12;   // Scattered
  else if (maxRange > 0.01) zoom = 14;   // Close
  else zoom = isParentDashboard() ? 17 : 15; // Very close

  return {
    latitude: avgLat,
    longitude: avgLng,
    zoom
  };
};
