-- ===================================================================
-- CORREÇÃO URGENTE: Remover todas as referências à tabela "guardians"
-- ===================================================================

-- 1. Remover função problemática
DROP FUNCTION IF EXISTS save_student_location(double precision, double precision, boolean);

-- 2. Temporariamente desabilitar RLS para debugar
ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;

-- 3. Remover todas as policies problemáticas
DROP POLICY IF EXISTS "Users can view own locations" ON public.locations;
DROP POLICY IF EXISTS "Users can insert own locations" ON public.locations;
DROP POLICY IF EXISTS "Students can manage own locations" ON public.locations;
DROP POLICY IF EXISTS "Guardians can view student locations" ON public.locations;
DROP POLICY IF EXISTS "Allow guardians to view locations" ON public.locations;

-- 4. Criar função limpa sem dependências
CREATE OR REPLACE FUNCTION save_student_location(
  p_latitude double precision,
  p_longitude double precision,
  p_shared_with_guardians boolean DEFAULT true
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
  new_location_id uuid;
BEGIN
  -- Get current user ID
  current_user_id := auth.uid();
  
  -- Check authentication
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;
  
  -- Insert location without triggers
  INSERT INTO public.locations (
    id,
    user_id,
    latitude,
    longitude,
    shared_with_guardians,
    timestamp,
    created_at
  ) VALUES (
    gen_random_uuid(),
    current_user_id,
    p_latitude,
    p_longitude,
    p_shared_with_guardians,
    NOW(),
    NOW()
  )
  RETURNING id INTO new_location_id;
  
  RETURN new_location_id;
END;
$$;

-- 5. Permissões simples
GRANT EXECUTE ON FUNCTION save_student_location TO authenticated;
GRANT ALL ON TABLE public.locations TO authenticated;

-- 6. Reabilitar RLS com policy simples
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- 7. Policy simples sem referências a guardians
CREATE POLICY "Simple access for authenticated users" ON public.locations
  FOR ALL USING (true);

-- 8. Comentário de sucesso
SELECT 'Correção aplicada com sucesso!' as status; 