import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import LogoutButton from '@/components/LogoutButton';
import { useNavigate } from 'react-router-dom';
import { PlusCircle, User } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { InviteStudentForm } from '@/components/student/InviteStudentForm';
import { cn } from '@/lib/utils';

interface GuardianInfoPanelProps {
  userFullName: string;
  email: string | null;
  onStudentAdded?: () => void;
  className?: string;
}

const GuardianInfoPanel: React.FC<GuardianInfoPanelProps> = ({
  userFullName,
  email,
  onStudentAdded,
  className
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <Card className={cn('h-full bg-white/60 backdrop-blur shadow-lg border border-white/30 text-foreground', className)}>
      <CardHeader className="flex flex-row items-center justify-between gap-2">
        <div>
          <CardTitle className="dark:text-white">{t('profile.greeting', { name: userFullName })}</CardTitle>
          <CardDescription className="dark:text-gray-200">{t('dashboard.parent.title')}</CardDescription>
        </div>
        <div className="flex gap-2 items-center">
          <Button variant="outline" className="gap-2 dark:border-white dark:text-white" onClick={() => navigate('/profile')}> 
            <User className="h-5 w-5" />
            {t('common.profile')}
          </Button>
          <LogoutButton className="h-10 px-4 py-2" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4 dark:text-white">{t('profile.personalInfo')}</h3>
            <div className="space-y-2">
              <p className="dark:text-white"><strong className="dark:text-white">{t('profile.name', 'Name')}:</strong> {userFullName}</p>
              <p className="dark:text-white"><strong className="dark:text-white">{t('profile.email', 'Email')}:</strong> {email}</p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 dark:text-white">{t('common.actions')}</h3>
            <div className="space-y-4">
              <Dialog>
                <DialogTrigger asChild>
                  <Button className="w-full flex items-center justify-center dark:bg-zinc-800 dark:text-white">
                    <PlusCircle className="mr-2 h-5 w-5" />
                    {t('dashboard.parent.addStudent')}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle className="dark:text-white">{t('student.addStudent')}</DialogTitle>
                  </DialogHeader>
                  <InviteStudentForm onStudentAdded={onStudentAdded} />
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GuardianInfoPanel;
