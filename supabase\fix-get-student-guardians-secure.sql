-- ==========================================
-- FIX: RPC get_student_guardians_secure
-- Data: 24/06/2025
-- Problema: Coluna g.phone não existe na tabela guardians
-- Solução: Remover referência à coluna phone
-- ==========================================

-- 1. PRIMEIRO, VAMOS VER A ESTRUTURA ATUAL DA TABELA GUARDIANS
SELECT 
    'CURRENT_GUARDIANS_STRUCTURE' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'guardians'
ORDER BY ordinal_position;

-- 2. CORRIGIR A FUNÇÃO get_student_guardians_secure
CREATE OR REPLACE FUNCTION public.get_student_guardians_secure(p_student_id UUID DEFAULT NULL)
RETURNS TABLE (
  id UUID,
  student_id UUID,
  email TEXT,
  full_name TEXT,
  phone TEXT, -- Manter na assinatura mas retornar NULL
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  v_student_id UUID;
BEGIN
  -- Se nenhum ID for fornecido, usa o ID do usuário atual
  IF p_student_id IS NULL THEN
    v_student_id := auth.uid();
  ELSE
    -- Verifica se o usuário atual tem permissão para ver os guardians do estudante fornecido
    IF p_student_id = auth.uid() THEN
      v_student_id := p_student_id;
    ELSE
      RAISE EXCEPTION 'Permissão negada';
    END IF;
  END IF;

  -- Retorna os guardians do estudante (SEM REFERENCIAR g.phone)
  RETURN QUERY
  SELECT 
    g.id,
    g.student_id,
    g.email,
    g.full_name,
    NULL::TEXT as phone, -- Retorna NULL para phone já que não existe
    g.is_active,
    g.created_at
  FROM 
    public.guardians g
  WHERE 
    g.student_id = v_student_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. GARANTIR PERMISSÕES
GRANT EXECUTE ON FUNCTION public.get_student_guardians_secure TO authenticated;

-- 4. TESTAR A FUNÇÃO CORRIGIDA
SELECT 
    'TEST_FIXED_RPC' as info,
    *
FROM get_student_guardians_secure();

-- 5. VERIFICAR SE A CORREÇÃO FUNCIONOU
SELECT 
    'VERIFY_FIX' as info,
    'RPC get_student_guardians_secure corrigida com sucesso' as message; 