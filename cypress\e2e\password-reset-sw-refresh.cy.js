/**
 * Teste de resiliência do fluxo de redefinição de senha com Service Worker e SPA refresh
 * Valida:
 * - Service worker registrado e servindo recursos do cache
 * - Componente ResetPassword inicializa corretamente após refresh
 * - Formulário permanece funcional após múltiplos refreshes
 * - Logs críticos presentes no console
 */

describe('Resiliência do Reset de Senha com Service Worker e Refresh', () => {
  const testEmail = '<EMAIL>';
  const newPassword = 'NovaSenha2025!';
  const resetUrl = `/reset-password?token=valid-token-simulation&email=${encodeURIComponent(testEmail)}`;

  before(() => {
    // Limpar estado e garantir SW limpo
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.visit('/');
    // Esperar service worker registrar
    cy.window().then(win => {
      return new Cypress.Promise((resolve) => {
        if ('serviceWorker' in win.navigator) {
          win.navigator.serviceWorker.ready.then(() => resolve());
        } else {
          resolve();
        }
      });
    });
  });

  it('deve garantir que o service worker está registrado', () => {
    cy.window().then(win => {
      if ('serviceWorker' in win.navigator) {
        return win.navigator.serviceWorker.getRegistration('/').then(reg => {
          expect(reg).to.exist;
          expect(reg.active).to.exist;
        });
      }
    });
  });

  it('deve servir recursos estáticos do cache (cache hit)', () => {
    // Acessar página de reset para garantir assets carregados
    cy.visit(resetUrl);
    // Verificar se arquivos JS/CSS principais vieram do cache via logs do SW
    cy.window().then(() => {
      cy.wait(1000); // Dar tempo para logs aparecerem
      cy.task('getConsoleLogs').then((logs) => {
        const cacheHits = logs.filter(l => l.includes('[SW] Cache hit'));
        expect(cacheHits.length).to.be.greaterThan(0);
      });
    });
  });

  it('deve seguir o fluxo real do link de e-mail Supabase até o formulário de nova senha', () => {
    // Simula o clique no link do e-mail (endpoint Supabase)
    const supabaseVerifyUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co/auth/v1/verify?token=MOCK_TOKEN&type=recovery&redirect_to=https://sistema-monitore.com.br/reset-password';
    cy.visit(supabaseVerifyUrl);

    // O Supabase redireciona para /reset-password#access_token=...
    // Simulando esse redirecionamento:
    cy.location('hash', { timeout: 10000 }).should('include', 'access_token=');

    // O formulário de redefinição de senha deve aparecer
    cy.get('[data-cy="reset-password-form"]').should('be.visible');
    cy.get('[data-cy="new-password-input"]').should('be.visible');
    cy.get('[data-cy="confirm-password-input"]').should('be.visible');

    // Preencher e submeter nova senha
    cy.get('[data-cy="new-password-input"]').type('NovaSenha2025!');
    cy.get('[data-cy="confirm-password-input"]').type('NovaSenha2025!');
    cy.get('[data-cy="reset-password-button"]').click();

    // Verificar mensagem de sucesso
    cy.contains(/sucesso|alterada/i, { timeout: 10000 }).should('exist');
  });

  it('deve manter o contexto do token e email após múltiplos refreshes', () => {
    cy.visit(resetUrl);
    cy.get('[data-cy="reset-password-form"]').should('be.visible');
    // Fazer dois refreshes seguidos
    cy.reload();
    cy.reload();
    // Formulário ainda deve estar funcional
    cy.get('[data-cy="reset-password-form"]').should('be.visible');
    cy.get('[data-cy="new-password-input"]').should('exist');
    cy.get('[data-cy="confirm-password-input"]').should('exist');
  });

  it('deve exibir mensagem de erro se a Edge Function falhar', () => {
    cy.intercept('POST', '**/functions/v1/send-password-reset', {
      statusCode: 500,
      body: { error: 'Internal Server Error' }
    }).as('edgeFunctionFail');

    cy.visit('/login');
    cy.get('[data-cy="forgot-password-link"]').click();
    cy.get('input[type="email"]').type(testEmail);
    cy.get('button[type="submit"]').click();

    cy.wait('@edgeFunctionFail');
    cy.contains(/erro|falhou|tente novamente/i).should('exist');
  });

  it('deve exibir mensagem de limite de requisições (429)', () => {
    cy.intercept('POST', '**/auth/v1/recover*', {
      statusCode: 429,
      body: { error: 'For security purposes, you can only request this after 2 seconds.' }
    }).as('rateLimit');

    cy.visit('/login');
    cy.get('[data-cy="forgot-password-link"]').click();
    cy.get('input[type="email"]').type(testEmail);
    cy.get('button[type="submit"]').click();

    cy.wait('@rateLimit');
    cy.contains(/segurança|aguarde|limite|2 segundos/i).should('exist');
  });
}); 