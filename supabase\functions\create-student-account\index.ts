import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  console.log('[CREATE_STUDENT] 🚀 Iniciando processo FALLBACK v6.0 - RPC Approach');

  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders, status: 204 });
  }

  try {
    // PASSO 1: Verificar configuração
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase configuration missing');
    }

    // Criar cliente com service role para bypass RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      db: {
        schema: 'public'
      }
    });

    console.log('[CREATE_STUDENT] ✅ Cliente Supabase configurado');

    // PASSO 2: Parse request data
    const {
      student_name,
      student_email,
      student_cpf,
      student_phone,
      guardian_id,
      guardian_email,
      guardian_name
    } = await req.json();

    console.log('[CREATE_STUDENT] 📝 Dados recebidos:', {
      student_name,
      student_email,
      student_cpf: student_cpf?.substring(0, 3) + '***',
      guardian_id: guardian_id?.substring(0, 8) + '***'
    });

    // PASSO 3: Validação básica
    if (!student_name?.trim() || !student_email?.trim() || !student_cpf?.trim()) {
      throw new Error('Campos obrigatórios ausentes');
    }

    // Clean CPF
    const cleanCpf = student_cpf.replace(/\D/g, '');
    if (cleanCpf.length !== 11) {
      throw new Error('CPF deve ter 11 dígitos válidos');
    }

    // PASSO 4: Usar a RPC para criar estudante (MELHOR ABORDAGEM)
    console.log('[CREATE_STUDENT] 🔄 Usando RPC create_student_invitation_for_fallback...');

    const { data: rpcResult, error: rpcError } = await supabase.rpc(
      'create_student_invitation_for_fallback', 
      {
        p_student_name: student_name.trim(),
        p_student_email: student_email.trim(),
        p_student_cpf: cleanCpf,
        p_student_phone: student_phone?.trim() || null,
        p_guardian_id: guardian_id
      }
    );

    console.log('[CREATE_STUDENT] 📊 Resultado RPC:', rpcResult);

    if (rpcError) {
      console.error('[CREATE_STUDENT] ❌ Erro na RPC:', rpcError);
      throw new Error(`RPC Error: ${rpcError.message}`);
    }

    if (!rpcResult || rpcResult.length === 0) {
      throw new Error('RPC não retornou dados válidos');
    }

    const result = rpcResult[0];
    
    if (!result.success) {
      console.error('[CREATE_STUDENT] ❌ RPC retornou falha:', result.message);
      throw new Error(result.message);
    }

    console.log('[CREATE_STUDENT] ✅ RPC bem-sucedida, criando usuário auth...');

    // PASSO 5: Criar usuário usando Admin API
    const { data: newUser, error: createUserError } = await supabase.auth.admin.createUser({
      email: student_email.trim(),
      password: result.temp_password,
      email_confirm: true,
      user_metadata: {
        full_name: student_name.trim(),
        user_type: 'student',
        created_by: 'guardian_invitation_fallback'
      }
    });

    if (createUserError || !newUser.user) {
      console.error('[CREATE_STUDENT] ❌ Erro ao criar usuário:', createUserError);
      throw new Error(`Erro ao criar usuário: ${createUserError?.message}`);
    }

    console.log('[CREATE_STUDENT] ✅ Usuário criado:', newUser.user.id);

    // PASSO 6: Completar conta usando RPC
    console.log('[CREATE_STUDENT] 🔄 Completando conta via RPC...');
    
    const { data: completeResult, error: completeError } = await supabase.rpc(
      'complete_student_account_creation',
      {
        p_invitation_id: result.invitation_id,
        p_auth_user_id: newUser.user.id
      }
    );

    if (completeError || !completeResult?.[0]?.success) {
      console.error('[CREATE_STUDENT] ❌ Erro ao completar conta:', completeError || 'RPC returned failure');
      // Cleanup - remover usuário criado
      await supabase.auth.admin.deleteUser(newUser.user.id);
      throw new Error(`Erro ao completar conta: ${completeError?.message || 'Operação falhou'}`);
    }

    console.log('[CREATE_STUDENT] ✅ Conta completada via RPC');

    // PASSO 7: Enviar email com credenciais (opcional)
    console.log('[CREATE_STUDENT] 📧 Enviando email com credenciais...');
    
    try {
      const { data: emailResult, error: emailError } = await supabase.functions.invoke('send-student-credentials', {
        body: {
          student_name,
          student_email,
          guardian_name: guardian_name || 'Responsável',
          guardian_email,
          temp_password: result.temp_password,
          activation_token: result.activation_token,
          invitation_id: result.invitation_id,
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        }
      });

      if (emailError) {
        console.warn('[CREATE_STUDENT] ⚠️ Email falhou (não crítico):', emailError);
      } else {
        console.log('[CREATE_STUDENT] ✅ Email enviado com sucesso:', emailResult?.success || 'sent');
      }
    } catch (emailErr) {
      console.warn('[CREATE_STUDENT] ⚠️ Email exception (não crítico):', emailErr);
    }

    // PASSO 8: Retornar resultado final
    console.log('[CREATE_STUDENT] 🎉 PROCESSO COMPLETO! Usuário criado:', newUser.user.id);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Conta de ${student_name} criada com sucesso`,
        student_id: newUser.user.id,
        activation_token: result.activation_token,
        email_sent: true,
        type: 'new_student_created_fallback'
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
        status: 200 
      }
    );

  } catch (error) {
    console.error('[CREATE_STUDENT] 💥 ERRO FATAL:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage,
        details: error instanceof Error ? error.stack : 'Unknown error'
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
        status: 500 
      }
    );
  }
});
