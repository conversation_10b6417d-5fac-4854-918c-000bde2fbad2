import { useEffect, useState } from 'react';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  placeholderClassName?: string;
}

export default function OptimizedImage({
  src,
  alt,
  placeholderClassName = 'bg-muted animate-pulse',
  ...props
}: OptimizedImageProps) {
  const [imageSrc, setImageSrc] = useState<string | null>(null);

  useEffect(() => {
    if (!src) return;
    const img = new Image();
    img.onload = () => setImageSrc(src);
    img.src = src;
  }, [src]);

  if (!src) return null;

  return imageSrc ? (
    <img src={imageSrc} alt={alt} loading="lazy" {...props} />
  ) : (
    <div
      className={placeholderClassName}
      style={{ width: props.width, height: props.height }}
    />
  );
}
