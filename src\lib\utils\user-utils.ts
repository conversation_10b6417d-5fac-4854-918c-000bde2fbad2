// User type definitions
interface UserMetadata {
  user_type?: 'student' | 'parent' | 'teacher';
  [key: string]: unknown;
}

interface UserProfile {
  user_type?: 'student' | 'parent' | 'teacher';
  user_metadata?: UserMetadata;
  [key: string]: unknown;
}

export function getUserType(user: UserProfile | null | undefined): 'student' | 'parent' | 'teacher' | 'unknown' {
  if (!user) return 'unknown';
  return user.user_type || user.user_metadata?.user_type || 'unknown';
}

export function isStudent(user: UserProfile | null | undefined): boolean {
  return getUserType(user) === 'student';
}

export function isParent(user: UserProfile | null | undefined): boolean {
  return getUserType(user) === 'parent';
}

export function hasPermission(user: UserProfile | null | undefined, requiredType: string | null): boolean {
  if (!requiredType) return true;
  return getUserType(user) === requiredType;
} 