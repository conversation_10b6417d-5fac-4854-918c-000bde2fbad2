
-- CORREÇÃO URGENTE: <PERSON><PERSON><PERSON><PERSON> políticas RLS para restaurar login de usuários
-- Vers<PERSON> corrigida que resolve o conflito de função

-- 1. RECRIAR POLÍTICAS RLS PARA A TABELA PROFILES
-- Remover políticas existentes que podem estar causando conflito
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can view any profile" ON public.profiles;
DROP POLICY IF EXISTS "Allow authenticated users to view profiles" ON public.profiles;
DROP POLICY IF EXISTS "Allow authenticated users to update own profile" ON public.profiles;

-- Criar políticas básicas e funcionais para profiles
CREATE POLICY "authenticated_users_can_view_own_profile" 
ON public.profiles 
FOR SELECT 
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "authenticated_users_can_update_own_profile" 
ON public.profiles 
FOR UPDATE 
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "authenticated_users_can_insert_own_profile" 
ON public.profiles 
FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Política especial para permitir que o sistema Supabase acesse profiles durante login
CREATE POLICY "system_can_access_profiles_for_auth" 
ON public.profiles 
FOR SELECT 
TO service_role
USING (true);

-- 2. RECRIAR POLÍTICAS RLS PARA A TABELA AUTH_LOGS
-- Remover políticas existentes
DROP POLICY IF EXISTS "Users can view their own logs" ON public.auth_logs;
DROP POLICY IF EXISTS "Users can insert their own logs" ON public.auth_logs;

-- Criar políticas para auth_logs
CREATE POLICY "authenticated_users_can_view_own_logs" 
ON public.auth_logs 
FOR SELECT 
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "authenticated_users_can_insert_logs" 
ON public.auth_logs 
FOR INSERT 
TO authenticated
WITH CHECK (user_id = auth.uid() OR user_id IS NULL);

-- Política para permitir que o sistema insira logs sem user_id
CREATE POLICY "system_can_insert_auth_logs" 
ON public.auth_logs 
FOR INSERT 
TO anon, service_role
WITH CHECK (true);

-- 3. GARANTIR PERMISSÕES ADEQUADAS PARA AS TABELAS
-- Conceder permissões básicas necessárias
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT, INSERT ON public.auth_logs TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Conceder permissões para usuários anônimos acessarem profiles durante o processo de login
GRANT SELECT ON public.profiles TO anon;

-- 4. VERIFICAR E CORRIGIR TABELA STUDENTS SE NECESSÁRIO
-- Criar políticas básicas para students se não existirem
DROP POLICY IF EXISTS "Students can view own data" ON public.students;
DROP POLICY IF EXISTS "Guardians can view their students" ON public.students;

CREATE POLICY "students_can_view_own_data" 
ON public.students 
FOR SELECT 
TO authenticated
USING (guardian_id = auth.uid());

CREATE POLICY "authenticated_users_can_view_students" 
ON public.students 
FOR SELECT 
TO authenticated
USING (
    guardian_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.student_guardian_relationships sgr 
        WHERE sgr.student_id = students.id AND sgr.guardian_id = auth.uid()
    )
);

-- 5. REMOVER FUNÇÃO CONFLITANTE E RECRIAR (CORRIGIDO)
-- Primeiro, remover todas as versões da função
DROP FUNCTION IF EXISTS public.simple_update_profile;
DROP FUNCTION IF EXISTS public.simple_update_profile(TEXT);
DROP FUNCTION IF EXISTS public.simple_update_profile(TEXT, TEXT);
DROP FUNCTION IF EXISTS public.simple_update_profile(TEXT, TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS public.simple_update_profile(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT);

-- Recriar função com nome único para evitar conflitos
CREATE OR REPLACE FUNCTION public.update_profile_basic(
    p_full_name TEXT DEFAULT NULL,
    p_phone TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        full_name = COALESCE(p_full_name, full_name),
        phone = COALESCE(p_phone, phone),
        updated_at = NOW()
    WHERE user_id = auth.uid();
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Conceder permissão para executar a nova função
GRANT EXECUTE ON FUNCTION public.update_profile_basic TO authenticated;

-- 6. LOG DA CORREÇÃO
INSERT INTO public.auth_logs (
    event_type,
    metadata,
    occurred_at
) VALUES (
    'urgent_rls_policies_fix_applied_v2',
    jsonb_build_object(
        'description', 'Correção urgente das políticas RLS para restaurar login - versão corrigida',
        'timestamp', NOW(),
        'tables_affected', ARRAY['profiles', 'auth_logs', 'students'],
        'policies_recreated', true,
        'function_conflict_resolved', true
    ),
    NOW()
);
