/**
 * Production-specific debugging utilities for Mapbox GL integration
 * Helps identify timing issues and DOM readiness problems in production builds
 */

interface MapboxDebugInfo {
  timestamp: number;
  environment: 'development' | 'production';
  containerInfo: {
    exists: boolean;
    hasParent: boolean;
    isVisible: boolean;
    dimensions: { width: number; height: number };
    inDocument: boolean;
  };
  mapInfo: {
    isLoaded: boolean;
    hasStyle: boolean;
    center?: [number, number];
    zoom?: number;
  };
  performanceInfo: {
    domContentLoaded: boolean;
    windowLoaded: boolean;
    timeSincePageLoad: number;
  };
}

class MapboxProductionDebugger {
  private static instance: MapboxProductionDebugger;
  private debugLogs: MapboxDebugInfo[] = [];
  private maxLogs = 50; // Keep last 50 debug entries

  static getInstance(): MapboxProductionDebugger {
    if (!MapboxProductionDebugger.instance) {
      MapboxProductionDebugger.instance = new MapboxProductionDebugger();
    }
    return MapboxProductionDebugger.instance;
  }

  /**
   * Capture comprehensive debug information about map state
   */
  captureDebugInfo(map?: mapboxgl.Map, container?: HTMLElement): MapboxDebugInfo {
    const now = Date.now();
    const isProduction = import.meta.env.PROD;
    
    // Container information
    const containerInfo = {
      exists: !!container,
      hasParent: !!(container?.parentNode),
      isVisible: !!(container && container.offsetParent !== null),
      dimensions: {
        width: container?.clientWidth || 0,
        height: container?.clientHeight || 0
      },
      inDocument: !!(container && document.contains(container))
    };

    // Map information
    const mapInfo = {
      isLoaded: !!(map && map.loaded()),
      hasStyle: !!(map && map.getStyle()),
      center: map?.getCenter() ? [map.getCenter().lng, map.getCenter().lat] as [number, number] : undefined,
      zoom: map?.getZoom()
    };

    // Performance information
    const performanceInfo = {
      domContentLoaded: document.readyState !== 'loading',
      windowLoaded: document.readyState === 'complete',
      timeSincePageLoad: now - (performance.timing?.navigationStart || now)
    };

    const debugInfo: MapboxDebugInfo = {
      timestamp: now,
      environment: isProduction ? 'production' : 'development',
      containerInfo,
      mapInfo,
      performanceInfo
    };

    // Store debug info
    this.debugLogs.push(debugInfo);
    if (this.debugLogs.length > this.maxLogs) {
      this.debugLogs.shift();
    }

    return debugInfo;
  }

  /**
   * Log detailed container readiness information
   */
  logContainerReadiness(container: HTMLElement | null, context: string): boolean {
    if (!container) {
      console.warn(`[MapboxDebug] ${context}: Container is null`);
      return false;
    }

    const debugInfo = this.captureDebugInfo(undefined, container);
    const isReady = debugInfo.containerInfo.exists && 
                   debugInfo.containerInfo.hasParent && 
                   debugInfo.containerInfo.isVisible && 
                   debugInfo.containerInfo.inDocument &&
                   debugInfo.containerInfo.dimensions.width > 0 &&
                   debugInfo.containerInfo.dimensions.height > 0;

    const logLevel = isReady ? 'log' : 'warn';
    console[logLevel](`[MapboxDebug] ${context}:`, {
      ready: isReady,
      container: debugInfo.containerInfo,
      performance: debugInfo.performanceInfo,
      environment: debugInfo.environment
    });

    return isReady;
  }

  /**
   * Log marker addition attempt with context
   */
  logMarkerAddition(map: mapboxgl.Map | null, marker: mapboxgl.Marker, context: string): void {
    const container = map?.getContainer();
    const debugInfo = this.captureDebugInfo(map, container || undefined);
    
    console.log(`[MapboxDebug] ${context}:`, {
      markerExists: !!marker,
      mapReady: debugInfo.mapInfo.isLoaded,
      containerReady: debugInfo.containerInfo.exists && 
                     debugInfo.containerInfo.hasParent && 
                     debugInfo.containerInfo.isVisible,
      environment: debugInfo.environment,
      timeSincePageLoad: debugInfo.performanceInfo.timeSincePageLoad
    });
  }

  /**
   * Get production-specific timing recommendations
   */
  getProductionTimingRecommendations(): { containerDelay: number; markerDelay: number; retryDelay: number } {
    const isProduction = import.meta.env.PROD;
    const recentLogs = this.debugLogs.slice(-10);
    const avgLoadTime = recentLogs.length > 0 
      ? recentLogs.reduce((sum, log) => sum + log.performanceInfo.timeSincePageLoad, 0) / recentLogs.length
      : 1000;

    if (isProduction) {
      // Production builds need longer delays due to bundle optimization
      return {
        containerDelay: Math.max(200, avgLoadTime * 0.1),
        markerDelay: Math.max(300, avgLoadTime * 0.15),
        retryDelay: Math.max(500, avgLoadTime * 0.2)
      };
    } else {
      // Development can use shorter delays
      return {
        containerDelay: 50,
        markerDelay: 100,
        retryDelay: 200
      };
    }
  }

  /**
   * Export debug logs for analysis
   */
  exportDebugLogs(): MapboxDebugInfo[] {
    return [...this.debugLogs];
  }

  /**
   * Clear debug logs
   */
  clearDebugLogs(): void {
    this.debugLogs = [];
  }

  /**
   * Check if we're in a problematic production environment
   */
  isProblematicEnvironment(): boolean {
    const isProduction = import.meta.env.PROD;
    const recentFailures = this.debugLogs.slice(-5).filter(log => 
      !log.containerInfo.isVisible || 
      !log.containerInfo.inDocument ||
      log.containerInfo.dimensions.width === 0
    );

    return isProduction && recentFailures.length > 2;
  }
}

// Export singleton instance
export const mapboxDebugger = MapboxProductionDebugger.getInstance();

// Export types for external use
export type { MapboxDebugInfo };
