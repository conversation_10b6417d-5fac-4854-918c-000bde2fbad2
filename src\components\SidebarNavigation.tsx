
import React from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Home, Map, User, Book, LogOut, Shield, Settings, Activity, Database, Users } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SidebarNavigationProps {
  userType: string;
  dashboardLink: string;
  deviceType: string;
  onLogout: () => void;
}

export const SidebarNavigation = ({
  userType,
  dashboardLink,
  deviceType,
  onLogout
}: SidebarNavigationProps) => {
  // Adjust the width of the sidebar based on the device
  const { t } = useTranslation();
  const getSidebarWidth = () => {
    if (deviceType === 'tablet') {
      return 'w-48';
    }
    return 'w-56 lg:w-64';
  };

  return (
    <aside className={`${getSidebarWidth()} bg-white border-r shadow-sm hidden md:block`}>
      <nav className="p-3 lg:p-4 space-y-1 lg:space-y-2">
        <Link
          to={dashboardLink}
          className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
        >
          <Home className="h-4 w-4 lg:h-5 lg:w-5" />
          {t('common.dashboard')}
        </Link>
        
        {userType === "student" && (
          <>
            <Link
              to="/student-map"
              className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
            >
              <Map className="h-4 w-4 lg:h-5 lg:w-5" />
              {t('navigation.myMap')}
            </Link>
            <Link
              to="/guardians"
              className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
            >
              <User className="h-4 w-4 lg:h-5 lg:w-5" />
              {t('navigation.myGuardians')}
            </Link>
          </>
        )}

        {userType === "parent" && (
          <>
            <Link
              to="/student-map"
              className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
            >
              <Map className="h-4 w-4 lg:h-5 lg:w-5" />
              {t('navigation.studentMap')}
            </Link>
          </>
        )}

        {userType === "developer" && (
          <>
            <Link
              to="/dev/cypress"
              className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
            >
              <Activity className="h-4 w-4 lg:h-5 lg:w-5" />
              Cypress Tests
            </Link>
            <Link
              to="/dev/api-docs"
              className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
            >
              <Book className="h-4 w-4 lg:h-5 lg:w-5" />
              API Docs
            </Link>
          </>
        )}

        {userType === "admin" && (
          <>
            <Link
              to="/admin/users"
              className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
            >
              <Users className="h-4 w-4 lg:h-5 lg:w-5" />
              Gerenciar Usuários
            </Link>
            <Link
              to="/admin/system"
              className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
            >
              <Settings className="h-4 w-4 lg:h-5 lg:w-5" />
              Sistema
            </Link>
            <Link
              to="/admin/database"
              className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
            >
              <Database className="h-4 w-4 lg:h-5 lg:w-5" />
              Banco de Dados
            </Link>
          </>
        )}

        {/* Diagnostics for developers and admins */}
        {(userType === "developer" || userType === "admin") && (
          <Link
            to="/diagnostic"
            className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
          >
            <Activity className="h-4 w-4 lg:h-5 lg:w-5" />
            Diagnostics
          </Link>
        )}

        <Link
          to="/api-docs"
          className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
        >
          <Book className="h-4 w-4 lg:h-5 lg:w-5" />
          API Docs
        </Link>
        
        {/* LGPD Settings */}
        <Link
          to="/lgpd-settings"
          className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium text-sm lg:text-base"
        >
          <Shield className="h-4 w-4 lg:h-5 lg:w-5" />
          {t('profile.tabs.privacy')}
        </Link>
        
        {/* Logout Button */}
        <Button
          variant="ghost"
          className="w-full flex items-center gap-2 px-3 py-2 rounded-md hover:bg-red-50 text-red-600 font-medium text-sm lg:text-base justify-start"
          onClick={onLogout}
        >
          <LogOut className="h-4 w-4 lg:h-5 lg:w-5" />
          {t('common.logout')}
        </Button>
      </nav>
    </aside>
  );
};
