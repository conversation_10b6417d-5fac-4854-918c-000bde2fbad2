
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';

  return {
  server: {
    host: "::",
    port: 8081, // Use 8081 directly since 8080 is occupied
    strictPort: false, // Allow automatic port switching if needed
    hmr: {
      host: 'localhost', // Ensure WebSocket uses localhost
    },
    watch: {
      usePolling: false, // Use native file watching for better performance
    },
    proxy: {
      '/api': {
        target: 'http://localhost:4001',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    chunkSizeWarningLimit: 1000, // Adjusted for map vendor chunk
    sourcemap: !isProduction, // Enable sourcemaps only in development
    minify: isProduction ? 'terser' : false, // Use terser only in production
    terserOptions: isProduction ? {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info'], // Remove specific console methods
      },
    } : {},
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Core React libraries
          if (id.includes('react') || id.includes('react-dom')) {
            return 'react-vendor';
          }

          // Routing libraries
          if (id.includes('react-router')) {
            return 'router-vendor';
          }

          // Supabase and database
          if (id.includes('@supabase') || id.includes('supabase-js')) {
            return 'supabase-vendor';
          }

          // UI libraries (Radix, Lucide, etc.)
          if (id.includes('@radix-ui') || id.includes('lucide-react') || id.includes('@headlessui')) {
            return 'ui-vendor';
          }

          // Map libraries (Mapbox)
          if (id.includes('mapbox') || id.includes('maplibre')) {
            return 'map-vendor';
          }

          // Form and validation libraries
          if (id.includes('react-hook-form') || id.includes('zod') || id.includes('@hookform')) {
            return 'form-vendor';
          }

          // Date and utility libraries
          if (id.includes('date-fns') || id.includes('lodash') || id.includes('axios')) {
            return 'utils-vendor';
          }

          // Animation and styling
          if (id.includes('framer-motion') || id.includes('tailwind') || id.includes('clsx')) {
            return 'style-vendor';
          }

          // Query and state management
          if (id.includes('@tanstack') || id.includes('react-query')) {
            return 'query-vendor';
          }

          // Other large node_modules
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        }
      }
    }
  },
}});
