
import { useEffect, useRef } from 'react';
import { NavigateFunction } from 'react-router-dom';

interface ToastFunction {
  (options: {
    variant?: "destructive" | "default";
    title: string;
    description: string;
  }): void;
}

export const useAuthRedirect = (
  isAuthenticated: boolean,
  loading: boolean,
  navigate: NavigateFunction,
  toast: ToastFunction
) => {
  const redirectedRef = useRef(false);

  useEffect(() => {
    // Avoid multiple redirects and wait for loading to complete
    if (loading || redirectedRef.current) return;

    if (!isAuthenticated) {
      console.log('[LAYOUT] User not authenticated, redirecting to login');
      redirectedRef.current = true;
      
      toast({
        variant: "destructive",
        title: "Acesso negado",
        description: "Faça login para acessar esta página",
      });
      
      navigate("/login");
    }
  }, [isAuthenticated, loading, navigate, toast]);

  // Reset redirect flag when authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      redirectedRef.current = false;
    }
  }, [isAuthenticated]);
};
