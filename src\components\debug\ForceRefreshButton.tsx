import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const ForceRefreshButton: React.FC = () => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  const forceRefresh = async () => {
    setIsRefreshing(true);
    
    try {
      console.log('🔄 FORCE REFRESH - Limpando cache e verificando autenticação...');
      
      // 1. Verificar autenticação atual
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        console.error('❌ Erro de autenticação:', authError);
        toast({
          title: "❌ Erro de Autenticação",
          description: `${authError.message}`,
          variant: "destructive",
        });
        return;
      }
      
      if (!user) {
        console.error('❌ Usuário não autenticado');
        toast({
          title: "❌ Não Autenticado", 
          description: "Você precisa estar logado para usar esta funcionalidade",
          variant: "destructive",
        });
        return;
      }
      
      console.log('✅ Usuário autenticado:', user.id);
      
      // 2. Testar acesso à tabela locations
      const { data: locData, error: locError } = await supabase
        .from('locations')
        .select('count(*)', { count: 'exact', head: true });
      
      if (locError) {
        console.error('❌ Erro ao acessar locations:', locError);
        toast({
          title: "❌ Erro na Tabela Locations",
          description: `${locError.message}`,
          variant: "destructive",
        });
        return;
      }
      
      console.log('✅ Tabela locations acessível');
      
      // 3. Testar função RPC
      const { data: rpcData, error: rpcError } = await supabase.rpc('save_student_location', {
        p_latitude: -23.5505,
        p_longitude: -46.6333,
        p_shared_with_guardians: true
      });
      
      if (rpcError) {
        if (rpcError.code === 'P0001') {
          console.log('✅ Função RPC funciona (erro P0001 é esperado para teste)');
        } else {
          console.error('❌ Erro na RPC:', rpcError);
          toast({
            title: "❌ Erro na Função RPC",
            description: `${rpcError.message}`,
            variant: "destructive",
          });
          return;
        }
      } else {
        console.log('✅ Função RPC funcionou, ID:', rpcData);
      }
      
      // 4. Forçar reload da página para limpar cache
      console.log('🔄 Forçando reload da página...');
      
      toast({
        title: "✅ Cache Verificado",
        description: "Autenticação OK, banco acessível. Recarregando página...",
        variant: "default",
      });
      
      // Aguardar 2s para o toast ser visto, depois recarregar
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      
    } catch (error: any) {
      console.error('❌ Erro durante force refresh:', error);
      toast({
        title: "❌ Erro no Force Refresh",
        description: error.message || "Erro desconhecido",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Button 
      onClick={forceRefresh}
      disabled={isRefreshing}
      variant="outline"
      size="sm"
      className="bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100"
    >
      {isRefreshing ? '🔄 Refreshing...' : '🔄 Force Refresh Cache'}
    </Button>
  );
}; 