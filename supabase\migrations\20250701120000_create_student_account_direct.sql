-- Migration: create direct student account function
CREATE OR REPLACE FUNCTION public.create_student_account_direct(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_student_phone TEXT DEFAULT NULL
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT,
  student_id UUID,
  temp_password TEXT,
  activation_token TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_guardian_id UUID := auth.uid();
  v_clean_cpf TEXT;
  v_temp_password TEXT;
  v_activation_token TEXT;
  v_new_user UUID;
BEGIN
  IF v_guardian_id IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Usuário não autenticado', NULL, NULL, NULL;
    RETURN;
  END IF;

  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  IF char_length(v_clean_cpf) <> 11 THEN
    RETURN QUERY SELECT FALSE, 'CPF inválido', NULL, NULL, NULL;
    RETURN;
  END IF;

  v_temp_password := substr(md5(random()::text), 1, 8) || '!';
  v_activation_token := encode(gen_random_bytes(24), 'base64url');

  SELECT uid INTO v_new_user FROM auth.create_user(
    email := p_student_email,
    password := v_temp_password,
    email_confirm := true
  );

  IF v_new_user IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Falha ao criar usuário', NULL, NULL, NULL;
    RETURN;
  END IF;

  INSERT INTO public.profiles(
    user_id, full_name, email, cpf, phone, user_type,
    account_creation_method, created_by_user_id,
    requires_password_change, activation_token,
    activation_expires_at, status
  ) VALUES (
    v_new_user, p_student_name, p_student_email, v_clean_cpf,
    p_student_phone, 'student', 'direct_db', v_guardian_id,
    TRUE, v_activation_token, NOW() + interval '7 days', 'pending_activation'
  );

  INSERT INTO public.student_guardian_relationships(
    student_id, guardian_id, relationship_type, is_primary
  ) VALUES (
    v_new_user, v_guardian_id, 'parent', TRUE
  );

  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_created_direct', v_new_user,
          jsonb_build_object('guardian_id', v_guardian_id, 'email', p_student_email),
          NOW());

  RETURN QUERY SELECT TRUE, 'Estudante criado com sucesso', v_new_user, v_temp_password, v_activation_token;
END;
$$;

GRANT EXECUTE ON FUNCTION public.create_student_account_direct(TEXT, TEXT, TEXT, TEXT) TO authenticated, service_role;
