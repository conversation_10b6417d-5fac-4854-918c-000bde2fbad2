
-- <PERSON><PERSON><PERSON><PERSON> que service_role tenha acesso total à tabela profiles
-- <PERSON><PERSON> resolve o erro "permission denied for table profiles"

-- Remover políticas conflitantes que podem estar bloqueando o service_role
DROP POLICY IF EXISTS "service_role_full_access_profiles" ON public.profiles;
DROP POLICY IF EXISTS "service_role_can_insert_profiles" ON public.profiles;

-- <PERSON><PERSON>r política específica para service_role com acesso total
CREATE POLICY "service_role_bypass_rls_profiles" 
ON public.profiles 
FOR ALL 
TO service_role 
USING (true) 
WITH CHECK (true);

-- Garan<PERSON>r que service_role tenha acesso à tabela student_guardian_relationships
DROP POLICY IF EXISTS "service_role_relationships_access" ON public.student_guardian_relationships;

CREATE POLICY "service_role_bypass_rls_relationships" 
ON public.student_guardian_relationships 
FOR ALL 
TO service_role 
USING (true) 
WITH CHECK (true);

-- Verificar se as políticas foram criadas
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE tablename IN ('profiles', 'student_guardian_relationships')
AND 'service_role' = ANY(roles);

-- <PERSON><PERSON> <PERSON> correção
INSERT INTO public.auth_logs (event_type, metadata, occurred_at)
VALUES (
  'service_role_permissions_fixed', 
  jsonb_build_object(
    'migration', '20250630193000-fix-service-role-permissions.sql',
    'description', 'Correção definitiva para acesso do service_role às tabelas críticas'
  ), 
  now()
);
