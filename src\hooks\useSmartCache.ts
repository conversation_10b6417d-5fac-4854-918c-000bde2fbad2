
/**
 * Hook for intelligent cache management
 * Integrates with existing authentication and network status
 */

import { useState, useEffect, useCallback } from 'react';
import { smartCache, CachedLocation, CachedProfile } from '@/lib/offline/smart-cache';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { useUser } from '@/contexts/UnifiedAuthContext';

interface CacheStats {
  stores: Record<string, number>;
  totalItems: number;
  lastSync?: Date;
}

interface SmartCacheHook {
  // Cache status
  isInitialized: boolean;
  stats: CacheStats | null;
  lastSync: Date | null;
  
  // Location cache
  lastKnownLocation: CachedLocation | null;
  locationHistory: CachedLocation[];
  
  // Profile cache
  userProfile: CachedProfile | null;
  
  // Actions
  refreshCache: () => Promise<void>;
  syncWithServer: () => Promise<boolean>;
  clearCache: () => Promise<void>;
  
  // Status
  isSyncing: boolean;
  error: string | null;
}

export function useSmartCache(): SmartCacheHook {
  const { user } = useUser();
  const { isOnline } = useNetworkStatus();
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [stats, setStats] = useState<CacheStats | null>(null);
  const [lastSync, setLastSync] = useState<Date | null>(null);
  const [lastKnownLocation, setLastKnownLocation] = useState<CachedLocation | null>(null);
  const [locationHistory, setLocationHistory] = useState<CachedLocation[]>([]);
  const [userProfile, setUserProfile] = useState<CachedProfile | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize cache
  useEffect(() => {
    const initCache = async () => {
      try {
        await smartCache.init();
        setIsInitialized(true);
        console.log('[useSmartCache] Cache initialized');
      } catch (err) {
        console.error('[useSmartCache] Failed to initialize:', err);
        setError('Failed to initialize cache');
      }
    };

    initCache();
  }, []);

  // Load cached data when user changes
  useEffect(() => {
    if (!isInitialized || !user?.id) return;

    const loadCachedData = async () => {
      try {
        console.log('[useSmartCache] Loading cached data for user:', user.id);
        
        // Load last known location
        const location = await smartCache.getLastKnownLocation(user.id);
        setLastKnownLocation(location);
        
        // Load location history
        const history = await smartCache.getLocationHistory(user.id);
        setLocationHistory(history);
        
        // Load user profile
        const profile = await smartCache.getUserProfile(user.id);
        setUserProfile(profile);
        
        // Update stats
        const cacheStats = await smartCache.getStats();
        setStats(cacheStats);
        
        console.log('[useSmartCache] Cached data loaded');
      } catch (err) {
        console.error('[useSmartCache] Failed to load cached data:', err);
        setError('Failed to load cached data');
      }
    };

    loadCachedData();
  }, [isInitialized, user?.id]);

  // Auto-sync when coming online
  useEffect(() => {
    if (!isInitialized || !user?.id || !isOnline) return;
    
    const autoSync = async () => {
      console.log('[useSmartCache] Auto-syncing cache...');
      await syncWithServer();
    };

    // Delay to ensure connection is stable
    const timer = setTimeout(autoSync, 2000);
    return () => clearTimeout(timer);
  }, [isOnline, isInitialized, user?.id]);

  const refreshCache = useCallback(async () => {
    if (!isInitialized) return;
    
    try {
      const cacheStats = await smartCache.getStats();
      setStats(cacheStats);
      console.log('[useSmartCache] Cache stats refreshed');
    } catch (err) {
      console.error('[useSmartCache] Failed to refresh cache:', err);
      setError('Failed to refresh cache');
    }
  }, [isInitialized]);

  const syncWithServer = useCallback(async (): Promise<boolean> => {
    if (!isInitialized || !user?.id || !isOnline) {
      console.log('[useSmartCache] Cannot sync: not initialized, no user, or offline');
      return false;
    }

    setIsSyncing(true);
    setError(null);
    
    try {
      console.log('[useSmartCache] Starting sync with server...');
      const result = await smartCache.syncWithSupabase(user.id);
      
      if (result.success) {
        // Reload cached data
        const location = await smartCache.getLastKnownLocation(user.id);
        setLastKnownLocation(location);
        
        const history = await smartCache.getLocationHistory(user.id);
        setLocationHistory(history);
        
        const profile = await smartCache.getUserProfile(user.id);
        setUserProfile(profile);
        
        setLastSync(new Date());
        await refreshCache();
        
        console.log('[useSmartCache] Sync completed successfully, synced:', result.synced);
        return true;
      } else {
        setError('Sync failed');
        return false;
      }
    } catch (err) {
      console.error('[useSmartCache] Sync failed:', err);
      setError('Sync failed with error');
      return false;
    } finally {
      setIsSyncing(false);
    }
  }, [isInitialized, user?.id, isOnline, refreshCache]);

  const clearCache = useCallback(async () => {
    if (!isInitialized) return;
    
    try {
      console.log('[useSmartCache] Clearing cache...');
      // Clear all stores
      const stores = ['locations', 'profiles', 'guardians', 'settings', 'api_cache'];
      for (const store of stores) {
        await smartCache.cleanup();
      }
      
      // Reset state
      setLastKnownLocation(null);
      setLocationHistory([]);
      setUserProfile(null);
      setLastSync(null);
      
      await refreshCache();
      console.log('[useSmartCache] Cache cleared successfully');
    } catch (err) {
      console.error('[useSmartCache] Failed to clear cache:', err);
      setError('Failed to clear cache');
    }
  }, [isInitialized, refreshCache]);

  return {
    isInitialized,
    stats,
    lastSync,
    lastKnownLocation,
    locationHistory,
    userProfile,
    refreshCache,
    syncWithServer,
    clearCache,
    isSyncing,
    error
  };
}
