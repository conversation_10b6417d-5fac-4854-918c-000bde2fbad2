# Protocolo de Encerramento Diário

**Data de Criação:** 2025-06-02  
**Versão:** 1.0.0  
**Projeto:** Locate-Family-Connect

## Objetivo

Este documento define o protocolo padrão para encerramento seguro de sessões de desenvolvimento do projeto Locate-Family-Connect, garantindo a segurança de dados sensíveis, a integridade do ambiente Docker e a preservação do estado de trabalho.

## Etapas do Protocolo

### 1. Encerramento de Serviços Docker

```bash
# Parar todos os contêineres relacionados ao projeto
docker compose -f docker-compose.yml down

# Verificar que nenhum contêiner permanece em execução
docker ps -a

# Opcionalmente, para limpeza completa do ambiente:
# docker system prune -a --volumes
```

### 2. Backup de Arquivos Sensíveis

```bash
# Criar diretório de backup se não existir
mkdir -p .backup

# Backup de arquivos de configuração com timestamp
copy .env .backup\.env.backup-$(Get-Date -Format "yyyyMMdd")
copy .gitignore .backup\.gitignore.backup-$(Get-Date -Format "yyyyMMdd")
```

### 3. Verificação de Segurança

- Confirmar que arquivos sensíveis estão no `.gitignore`:
  - `.env` e variantes
  - `mcp_config.json`
  - Tokens e chaves de API
  - Arquivos temporários

- Verificar se não há tokens expostos em:
  - Código-fonte
  - Arquivos de configuração versionados
  - Logs e outputs de depuração

### 4. Registro de Estado

- Documentar o estado atual do trabalho
- Listar tarefas pendentes para a próxima sessão
- Registrar problemas conhecidos e soluções parciais

### 5. Preparação para a Próxima Sessão

```bash
# Registrar no arquivo de plano (opcional)
echo "## Plano para $(Get-Date -Format "yyyy-MM-dd" -AddDays 1)" >> docs/plano-execucao.md
echo "1. Continuar testes do container Docker" >> docs/plano-execucao.md
echo "2. Verificar ícones do manifesto PWA" >> docs/plano-execucao.md
echo "3. Implementar recomendações do relatório final" >> docs/plano-execucao.md
```

## Diagnóstico e Verificação

Antes de encerrar completamente, execute estas verificações:

1. **Integridade do Docker**
   - Nenhum contêiner em estado zumbi
   - Sem conflitos de portas pendentes
   - Volumes desnecessários removidos

2. **Estado do Código**
   - Não há alterações não commitadas importantes
   - Branches estão em estado consistente
   - Não há conflitos de merge pendentes

3. **Segurança de Dados**
   - Tokens estão protegidos no `.gitignore`
   - Backups estão seguros e atualizados
   - Nenhum dado sensível exposto

## Protocolo de Retomada

Para retomar o trabalho na próxima sessão:

1. Verificar a integridade do ambiente:
   ```bash
   docker system df
   ```

2. Restaurar configuração se necessário:
   ```bash
   # Apenas se necessário
   # copy .backup\.env.backup-[DATA] .env
   ```

3. Limpar recursos não utilizados:
   ```bash
   docker system prune -f
   ```

4. Iniciar serviços conforme necessário:
   ```bash
   # Iniciar Supabase local primeiro (se necessário)
   # npx supabase start
   
   # Depois iniciar a aplicação
   docker compose -f docker-compose.yml up -d --build
   ```

## Considerações de Segurança

- **Nunca commitar** arquivos com tokens ou credenciais
- **Sempre verificar** o `.gitignore` antes de commits
- **Manter backups** de arquivos de configuração essenciais
- **Rotacionar tokens** regularmente, especialmente após exposição
- **Validar variáveis de ambiente** após restauração

---

*Este protocolo deve ser seguido no encerramento de cada sessão de desenvolvimento para garantir a segurança, integridade e continuidade do projeto Locate-Family-Connect.*
