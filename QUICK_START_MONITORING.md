# ⚡ Guia Rápido - Monitoramento e Analytics

> **Setup em 5 minutos** para começar a usar Sentry, PostHog, TestCafe e Redis no EduConnect

## 🚀 **Instalação Automática (Recomendado)**

### **Windows (PowerShell)**
```powershell
# Instalar tudo automaticamente
.\scripts\setup-monitoring.ps1

# Ou apenas configurar (se dependências já instaladas)
.\scripts\setup-monitoring.ps1 -ConfigOnly
```

### **Linux/macOS (Bash)**
```bash
# Tornar executável e executar
chmod +x scripts/setup-monitoring.sh
./scripts/setup-monitoring.sh
```

---

## 📝 **Instalação Manual (5 passos)**

### **1. Instalar Dependências**
```bash
# Monitoramento de erros
npm install @sentry/react @sentry/tracing

# Analytics de comportamento  
npm install posthog-js

# Testes cross-browser
npm install --save-dev testcafe @types/testcafe

# Cache Redis
npm install ioredis
npm install --save-dev @types/ioredis
```

### **2. Configurar Variáveis de Ambiente**
```bash
# Executar assistente de configuração
npm run monitoring:setup

# Ou adicionar manualmente ao .env:
echo "VITE_SENTRY_DSN=https://<EMAIL>/project-id" >> .env
echo "VITE_POSTHOG_KEY=phc_your-project-key" >> .env  
echo "VITE_POSTHOG_HOST=https://app.posthog.com" >> .env
echo "REDIS_PASSWORD=educonnect123" >> .env
```

### **3. Iniciar Redis**
```bash
# Com Docker
npm run redis:start

# Ou manualmente
docker run -d -p 6379:6379 --name redis redis:7-alpine
```

### **4. Verificar Saúde do Sistema**
```bash
npm run health-check
```

### **5. Executar Testes**
```bash
# Testes básicos
npm run test:e2e:chrome

# Testes mobile
npm run test:e2e:mobile
```

---

## 🔧 **Configuração das Contas**

### **Sentry (Monitoramento de Erros)**
1. Acesse [sentry.io](https://sentry.io) e crie uma conta
2. Crie um novo projeto React
3. Copie o DSN fornecido
4. Adicione ao `.env`: `VITE_SENTRY_DSN=seu-dsn-aqui`

### **PostHog (Analytics)**
1. Acesse [app.posthog.com](https://app.posthog.com) e crie uma conta
2. Copie o Project API Key
3. Adicione ao `.env`: `VITE_POSTHOG_KEY=seu-key-aqui`

---

## 📊 **Uso Prático**

### **Tracking de Eventos (PostHog)**
```typescript
import { trackLocationShare, trackAuthEvent } from '@/lib/analytics/posthog-config';

// Rastrear compartilhamento de localização
const handleLocationShare = () => {
  trackLocationShare('manual', studentId);
  // sua lógica aqui...
};

// Rastrear login
const handleLogin = () => {
  trackAuthEvent('login', 'student');
  // sua lógica aqui...
};
```

### **Captura de Erros (Sentry)**
```typescript
import { captureError } from '@/lib/monitoring/sentry-config';

try {
  // código que pode falhar
} catch (error) {
  captureError(error, {
    component: 'LocationService',
    userId: currentUserId
  });
}
```

### **Cache Redis**
```typescript
import { redisManager } from '@/lib/cache/redis-manager';

// Cache de localização
await redisManager.cacheLocation(studentId, locationData);
const cached = await redisManager.getLatestLocation(studentId);
```

---

## 🧪 **Executar Testes**

### **Comandos Disponíveis**
```bash
# Todos os navegadores
npm run test:e2e

# Apenas Chrome
npm run test:e2e:chrome

# Dispositivos móveis
npm run test:e2e:mobile

# Modo headless (CI/CD)
npm run test:e2e:headless
```

### **Exemplo de Teste**
```typescript
// tests/e2e/testcafe/login.test.ts
import { Selector, t } from 'testcafe';

fixture('Login Flow')
  .page('http://localhost:4000/login');

test('Should login successfully', async t => {
  await t
    .typeText('[data-testid="email"]', '<EMAIL>')
    .typeText('[data-testid="password"]', 'password123')
    .click('[data-testid="login-button"]')
    .expect(Selector('[data-testid="dashboard"]').exists).ok();
});
```

---

## 🏥 **Monitoramento da Saúde**

### **Health Check Manual**
```bash
npm run health-check
```

### **Resultado Esperado**
```
🏥 Verificando saúde do sistema...

📊 Resultados:
App (localhost:4000): ✅
Redis: ✅  
Sentry: ✅
PostHog: ✅

🎯 Status geral: ✅ SAUDÁVEL
```

---

## 🚨 **Troubleshooting**

### **Problemas Comuns**

#### **Redis não conecta**
```bash
# Verificar se Docker está rodando
docker ps

# Reiniciar Redis
npm run redis:stop
npm run redis:start
```

#### **Testes falham**
```bash
# Verificar se app está rodando
npm run dev

# Verificar se Chrome está instalado
npx testcafe --list-browsers
```

#### **Sentry não recebe erros**
```bash
# Verificar DSN no .env
cat .env | grep SENTRY

# Testar erro manual
import { captureMessage } from '@/lib/monitoring/sentry-config';
captureMessage('Teste de erro', 'error');
```

#### **PostHog não rastreia**
```bash
# Verificar key no .env  
cat .env | grep POSTHOG

# Verificar console do navegador
# Deve aparecer: [PostHog] Analytics inicializado
```

---

## 📈 **Dashboards e Relatórios**

### **Acessar Dashboards**
- **Sentry**: [sentry.io/projects](https://sentry.io/projects)
- **PostHog**: [app.posthog.com](https://app.posthog.com)

### **Métricas Importantes**
- 🔍 **Sentry**: Taxa de erro, performance, releases
- 📊 **PostHog**: Usuários ativos, funil de conversão, sessões
- 🧪 **TestCafe**: Cobertura de testes, tempo de execução
- 🔄 **Redis**: Hit rate, latência, uso de memória

---

## 🎯 **Próximas Ações**

1. ✅ Configure as contas (Sentry + PostHog)
2. ✅ Execute: `npm run monitoring:setup`  
3. ✅ Inicie Redis: `npm run redis:start`
4. ✅ Teste: `npm run health-check`
5. ✅ Execute testes: `npm run test:e2e:chrome`
6. 📊 Configure alertas nos dashboards
7. 🔧 Customize métricas conforme necessário

---

## 📚 **Documentação Completa**

- 📖 [Guia Completo de Integração](./MONITORING_ANALYTICS_INTEGRATION_GUIDE.md)
- 🔍 [Documentação Sentry](https://docs.sentry.io/platforms/javascript/guides/react/)
- 📊 [Documentação PostHog](https://posthog.com/docs/libraries/js)
- 🧪 [Documentação TestCafe](https://testcafe.io/documentation/402635/guides)

---

**💡 Dica**: Execute `npm run health-check` regularmente para garantir que tudo está funcionando corretamente! 