
export interface LocationData {
  id: string;
  user_id: string;
  latitude: number;
  longitude: number;
  timestamp: string;
  address?: string | null;
  shared_with_guardians: boolean;
  student_name: string;
  student_email: string;
  created_at: string;
  user?: {
    full_name: string;
    user_type: string;
  };
}

export interface GuardianData {
  id: string;
  student_id: string | null;
  guardian_id: string | null;
  email: string;
  full_name: string;
  phone?: string;
  cpf?: string | null;
  birth_date?: string | null;
  is_active: boolean;
  created_at: string;
  relationship_type?: string | null;
  status: 'active' | 'inactive' | 'pending';
}

export interface StudentGuardianResponse {
  id: string;
  student_id: string;
  email: string;
  full_name: string;
  phone?: string;
  is_active: boolean;
  created_at: string;
}

export interface UserProfile {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  phone?: string;
  user_type: string;
  created_at: string;
  updated_at: string;
}
