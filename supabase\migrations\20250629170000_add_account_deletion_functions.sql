-- Add missing guardian account deletion functions

-- R<PERSON> to fetch deletion requests for the authenticated guardian
CREATE OR REPLACE FUNCTION public.get_guardian_deletion_requests()
RETURNS TABLE (
    id UUID,
    student_id UUID,
    student_email TEXT,
    student_name TEXT,
    reason TEXT,
    status TEXT,
    requested_at TIMESTAMPTZ,
    processed_at TIMESTAMPTZ,
    guardian_notes TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        adr.id,
        adr.student_id,
        adr.student_email,
        adr.student_name,
        adr.reason,
        adr.status,
        adr.requested_at,
        adr.processed_at,
        adr.guardian_notes,
        adr.created_at,
        adr.updated_at
    FROM public.account_deletion_requests adr
    WHERE adr.student_id IN (
        SELECT g.student_id
        FROM public.guardians g
        WHERE g.guardian_id = auth.uid()
    )
    ORDER BY adr.requested_at DESC;
END;
$$;

-- RPC to process a specific deletion request
CREATE OR REPLACE FUNCTION public.process_account_deletion_request(
    p_request_id UUID,
    p_action TEXT,
    p_guardian_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    guardian_email TEXT;
    request_student_id UUID;
BEGIN
    SELECT student_id INTO request_student_id
    FROM public.account_deletion_requests
    WHERE id = p_request_id;

    IF request_student_id IS NULL THEN
        RAISE EXCEPTION 'Request not found';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM public.guardians g
        WHERE g.guardian_id = auth.uid()
        AND g.student_id = request_student_id
    ) THEN
        RAISE EXCEPTION 'Unauthorized: You do not have permission to process this request';
    END IF;

    SELECT email INTO guardian_email
    FROM public.profiles
    WHERE id = auth.uid();

    UPDATE public.account_deletion_requests
    SET
        status = CASE
            WHEN p_action = 'approve' THEN 'approved'
            WHEN p_action = 'reject' THEN 'rejected'
            ELSE status
        END,
        processed_at = now(),
        processed_by_guardian_email = guardian_email,
        guardian_notes = p_guardian_notes
    WHERE id = p_request_id;

    IF p_action = 'approve' THEN
        UPDATE public.profiles
        SET
            updated_at = now(),
            user_metadata = COALESCE(user_metadata, '{}')::jsonb || '{"deletion_approved": true}'::jsonb
        WHERE id = request_student_id;
    END IF;

    RETURN TRUE;
END;
$$;

GRANT EXECUTE ON FUNCTION public.get_guardian_deletion_requests() TO authenticated;
GRANT EXECUTE ON FUNCTION public.process_account_deletion_request(UUID, TEXT, TEXT) TO authenticated;
