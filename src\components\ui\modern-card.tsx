import React from 'react';
import { cn } from '@/lib/utils';

interface ModernCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'professional' | 'gradient';
  children: React.ReactNode;
}

export const ModernCard = React.forwardRef<HTMLDivElement, ModernCardProps>(
  ({ className, variant = 'default', children, ...props }, ref) => {
    const variants = {
      default: 'bg-transparent border-0 shadow-none',
      glass: 'bg-transparent border-0 shadow-none',
      professional: 'bg-transparent border-0 shadow-none',
      gradient: 'bg-gradient-card border border-border shadow-medium'
    };

    return (
      <div
        ref={ref}
        className={cn(
          'rounded-xl p-6 transition-all duration-300 hover:shadow-large',
          variants[variant],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

ModernCard.displayName = 'ModernCard';

interface ModernCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const ModernCardHeader = React.forwardRef<HTMLDivElement, ModernCardHeaderProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex flex-col space-y-1.5 pb-4 border-b border-border/50', className)}
      {...props}
    >
      {children}
    </div>
  )
);

ModernCardHeader.displayName = 'ModernCardHeader';

interface ModernCardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
}

export const ModernCardTitle = React.forwardRef<HTMLParagraphElement, ModernCardTitleProps>(
  ({ className, children, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn(
        'text-xl font-semibold leading-none tracking-tight bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent',
        className
      )}
      {...props}
    >
      {children}
    </h3>
  )
);

ModernCardTitle.displayName = 'ModernCardTitle';

interface ModernCardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const ModernCardContent = React.forwardRef<HTMLDivElement, ModernCardContentProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('pt-4', className)}
      {...props}
    >
      {children}
    </div>
  )
);

ModernCardContent.displayName = 'ModernCardContent';