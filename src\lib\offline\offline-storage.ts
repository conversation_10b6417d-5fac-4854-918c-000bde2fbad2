import { offlineDB } from './indexed-db-manager';

export async function setOfflineData<T>(store: string, key: string, data: T, ttl?: number): Promise<void> {
  await offlineDB.set(store, key, data, ttl);
}

export async function getOfflineData<T>(store: string, key: string): Promise<T | null> {
  return offlineDB.get<T>(store, key);
}

export async function removeOfflineData(store: string, key: string): Promise<void> {
  await offlineDB.delete(store, key);
}

export async function clearOfflineStore(store: string): Promise<void> {
  await offlineDB.clear(store);
}

export async function cleanExpired(store: string): Promise<number> {
  return offlineDB.cleanExpired(store);
}
