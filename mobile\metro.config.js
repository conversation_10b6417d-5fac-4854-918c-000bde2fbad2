// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Adicione esta configuração para resolver problemas com assets
config.resolver.assetExts.push('png', 'jpg', 'jpeg', 'gif', 'bmp', 'ttf', 'otf');

// Configuração para resolver módulos personalizados
config.resolver.extraNodeModules = {
  'missing-asset-registry-path': path.resolve(__dirname, 'missing-asset-registry-path.js'),
  // Resolver workspace shared
  'shared': path.resolve(__dirname, '../shared'),
  // Resolver App.tsx local
  'App': path.resolve(__dirname, './App.tsx'),
};

// Configuração adicional para resolver problemas de entrada
config.resolver.alias = {
  'App': path.resolve(__dirname, './App.tsx'),
};

module.exports = config;