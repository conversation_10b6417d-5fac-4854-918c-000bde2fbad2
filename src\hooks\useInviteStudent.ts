import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';
import type { User } from '@supabase/supabase-js';

export interface InviteStudentFormData {
  studentName: string;
  studentEmail: string;
  studentCpf: string;
  studentPhone?: string;
}

export const useInviteStudent = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useUser();

  interface InviteStudentResult {
    success: boolean;
    type?: string;
    studentId?: string;
    activationToken?: string;
    error?: string;
  }

  const inviteStudent = async (data: InviteStudentFormData): Promise<InviteStudentResult> => {
    setIsLoading(true);
    
    try {
      console.log('[INVITE_STUDENT] 🚀 Iniciando processo OTIMIZADO v3:', {
        name: data.studentName,
        email: data.studentEmail,
        cpf: data.studentCpf?.substring(0, 3) + '***'
      });

      // Verificar autenticação
      if (!user) {
        throw new Error('Usuário não autenticado');
      }

      const authUser = user as User;

      // Obter perfil do responsável
      const { data: guardianProfile, error: guardianError } = await supabase
        .from('profiles')
        .select('full_name, email')
        .eq('user_id', authUser.id)
        .single();

      if (guardianError) {
        console.warn('[INVITE_STUDENT] Perfil do responsável não encontrado:', guardianError);
      }

      const guardianName = guardianProfile?.full_name || authUser.user_metadata?.full_name || 'Responsável';
      const guardianEmail = guardianProfile?.email || authUser.email || '';

      // Limpar CPF
      const cleanCpf = data.studentCpf.replace(/\D/g, '');
      
      // VALIDAÇÃO CLIENT-SIDE
      if (cleanCpf.length !== 11) {
        throw new Error('CPF deve ter 11 dígitos válidos');
      }

      if (!data.studentEmail || !data.studentEmail.includes('@')) {
        throw new Error('Email deve ter um formato válido');
      }

      if (!data.studentName || data.studentName.trim().length < 3) {
        throw new Error('Nome deve ter pelo menos 3 caracteres');
      }

      // FLUXO PRINCIPAL: Nova Edge Function Completa (baseada em RPC direto)
      console.log('[INVITE_STUDENT] 🎯 Criando via Nova Edge Function Completa...');
      
      try {
        const { data: edgeResult, error: edgeError } = await supabase.functions.invoke('create-student-complete', {
          body: {
            student_name: data.studentName,
            student_email: data.studentEmail,
            student_cpf: cleanCpf,
            student_phone: data.studentPhone || null,
            guardian_id: authUser.id,
            guardian_email: guardianEmail,
            guardian_name: guardianName
          }
        });

        if (!edgeError && edgeResult?.success) {
          console.log('[INVITE_STUDENT] ✅ Nova Edge Function SUCESSO:', edgeResult);

          if (edgeResult.type === 'existing_student_linked') {
            toast({
              title: 'Estudante vinculado!',
              description: `${data.studentName} foi vinculado à sua conta com sucesso.`,
            });
          } else {
            toast({
              title: 'Conta criada com sucesso!',
              description: `A conta de ${data.studentName} foi criada e as credenciais foram enviadas por email.`,
            });
          }

          return {
            success: true,
            type: edgeResult.type,
            studentId: edgeResult.student_id,
            activationToken: edgeResult.activation_token
          };
        } else {
          console.warn('[INVITE_STUDENT] ⚠️ Nova Edge Function retornou erro:', edgeError);
          throw new Error(edgeError?.message || 'Erro na Edge Function');
        }
      } catch (edgeErr: any) {
        console.warn('[INVITE_STUDENT] ⚠️ Nova Edge Function ("create-student-complete") falhou, tentando fallback...');
        
        // FALLBACK: Tentar Edge Function original
        try {
          console.log('[INVITE_STUDENT] 🔄 Tentando Edge Function original como fallback...');
          
          const { data: fallbackResult, error: fallbackError } = await supabase.functions.invoke('create-student-account', {
            body: {
              student_name: data.studentName,
              student_email: data.studentEmail,
              student_cpf: cleanCpf,
              student_phone: data.studentPhone || null,
              guardian_id: authUser.id,
              guardian_email: guardianEmail,
              guardian_name: guardianName
            }
          });

          if (!fallbackError && fallbackResult?.success) {
            console.log('[INVITE_STUDENT] ✅ Edge Function Fallback SUCESSO:', fallbackResult);
            
            toast({
              title: 'Conta criada com sucesso!',
              description: `A conta de ${data.studentName} foi criada via fallback.`,
            });

            return {
              success: true,
              type: fallbackResult.type,
              studentId: fallbackResult.student_id,
              activationToken: fallbackResult.activation_token
            };
          } else {
            console.error('[INVITE_STUDENT] ❌ Fallback também falhou:', fallbackError);
            throw new Error(fallbackError?.message || 'Ambas as Edge Functions falharam');
          }
        } catch (fallbackErr: any) {
          console.error('[INVITE_STUDENT] ❌ Fallback Error:', fallbackErr);
          throw new Error(fallbackErr?.message || 'Erro ao criar conta do estudante após tentar fallback.');
        }
      }

    } catch (error: unknown) {
      console.error('[INVITE_STUDENT] 💥 Erro final no processo de convite:', error);
      
      // Mensagens de erro mais amigáveis
      let errorMessage = 'Erro desconhecido ao processar solicitação';

      if (error instanceof Error) {
        const msg = error.message.toLowerCase();
        if (msg.includes('campos obrigatórios') || msg.includes('obrigatório')) {
          errorMessage = 'Todos os campos obrigatórios devem ser preenchidos';
        } else if (msg.includes('cpf') && msg.includes('dígitos')) {
          errorMessage = 'CPF deve ter 11 dígitos válidos';
        } else if (msg.includes('email') && msg.includes('inválido')) {
          errorMessage = 'Email deve ter um formato válido';
        } else if (msg.includes('já está vinculado')) {
          errorMessage = 'Este estudante já está vinculado à sua conta';
        } else if (msg.includes('já está sendo usado')) {
          errorMessage = 'Este email já está sendo usado por outro usuário';
        } else if (msg.includes('não autenticado')) {
          errorMessage = 'Sessão expirada. Faça login novamente.';
        } else if (error.message) {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: 'Erro ao criar conta',
        description: errorMessage,
        variant: 'destructive',
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    inviteStudent,
    isLoading
  };
};
