{"version": "5", "redirects": {"https://deno.land/x/supabase/mod.ts": "https://deno.land/x/supabase@v2.44.2/mod.ts", "https://esm.sh/@supabase/functions-js@^2.1.5?target=denonext": "https://esm.sh/@supabase/functions-js@2.4.4?target=denonext", "https://esm.sh/@supabase/gotrue-js@^2.60.0?target=denonext": "https://esm.sh/@supabase/gotrue-js@2.70.0?target=denonext", "https://esm.sh/@supabase/node-fetch@^2.6.13?target=denonext": "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext", "https://esm.sh/@supabase/node-fetch@^2.6.14?target=denonext": "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext", "https://esm.sh/@supabase/postgrest-js@^1.9.0?target=denonext": "https://esm.sh/@supabase/postgrest-js@1.19.4?target=denonext", "https://esm.sh/@supabase/realtime-js@^2.9.3?target=denonext": "https://esm.sh/@supabase/realtime-js@2.11.15?target=denonext", "https://esm.sh/@supabase/storage-js@^2.5.4?target=denonext": "https://esm.sh/@supabase/storage-js@2.7.3?target=denonext", "https://esm.sh/@types/ws@~8.18.1/index.d.mts": "https://esm.sh/@types/ws@8.18.1/index.d.mts", "https://esm.sh/bufferutil@^4.0.1?target=denonext": "https://esm.sh/bufferutil@4.0.9?target=denonext", "https://esm.sh/isows@^1.0.7?target=denonext": "https://esm.sh/isows@1.0.7?target=denonext", "https://esm.sh/node-gyp-build@^4.3.0?target=denonext": "https://esm.sh/node-gyp-build@4.8.4?target=denonext", "https://esm.sh/tr46@~0.0.3?target=denonext": "https://esm.sh/tr46@0.0.3?target=denonext", "https://esm.sh/utf-8-validate@%3E=5.0.2?target=denonext": "https://esm.sh/utf-8-validate@6.0.5?target=denonext", "https://esm.sh/webidl-conversions@^3.0.0?target=denonext": "https://esm.sh/webidl-conversions@3.0.1?target=denonext", "https://esm.sh/whatwg-url@^5.0.0?target=denonext": "https://esm.sh/whatwg-url@5.0.0?target=denonext", "https://esm.sh/ws@^8.14.2?target=denonext": "https://esm.sh/ws@8.18.3?target=denonext"}, "remote": {"https://deno.land/std@0.168.0/async/abortable.ts": "80b2ac399f142cc528f95a037a7d0e653296352d95c681e284533765961de409", "https://deno.land/std@0.168.0/async/deadline.ts": "2c2deb53c7c28ca1dda7a3ad81e70508b1ebc25db52559de6b8636c9278fd41f", "https://deno.land/std@0.168.0/async/debounce.ts": "60301ffb37e730cd2d6f9dadfd0ecb2a38857681bd7aaf6b0a106b06e5210a98", "https://deno.land/std@0.168.0/async/deferred.ts": "77d3f84255c3627f1cc88699d8472b664d7635990d5358c4351623e098e917d6", "https://deno.land/std@0.168.0/async/delay.ts": "5a9bfba8de38840308a7a33786a0155a7f6c1f7a859558ddcec5fe06e16daf57", "https://deno.land/std@0.168.0/async/mod.ts": "7809ad4bb223e40f5fdc043e5c7ca04e0e25eed35c32c3c32e28697c553fa6d9", "https://deno.land/std@0.168.0/async/mux_async_iterator.ts": "770a0ff26c59f8bbbda6b703a2235f04e379f73238e8d66a087edc68c2a2c35f", "https://deno.land/std@0.168.0/async/pool.ts": "6854d8cd675a74c73391c82005cbbe4cc58183bddcd1fbbd7c2bcda42b61cf69", "https://deno.land/std@0.168.0/async/retry.ts": "e8e5173623915bbc0ddc537698fa418cf875456c347eda1ed453528645b42e67", "https://deno.land/std@0.168.0/async/tee.ts": "3a47cc4e9a940904fd4341f0224907e199121c80b831faa5ec2b054c6d2eff5e", "https://deno.land/std@0.168.0/http/server.ts": "e99c1bee8a3f6571ee4cdeb2966efad465b8f6fe62bec1bdb59c1f007cc4d155", "https://deno.land/std@0.177.0/async/abortable.ts": "73acfb3ed7261ce0d930dbe89e43db8d34e017b063cf0eaa7d215477bf53442e", "https://deno.land/std@0.177.0/async/deadline.ts": "c5facb0b404eede83e38bd2717ea8ab34faa2ffb20ef87fd261fcba32ba307aa", "https://deno.land/std@0.177.0/async/debounce.ts": "adab11d04ca38d699444ac8a9d9856b4155e8dda2afd07ce78276c01ea5a4332", "https://deno.land/std@0.177.0/async/deferred.ts": "42790112f36a75a57db4a96d33974a936deb7b04d25c6084a9fa8a49f135def8", "https://deno.land/std@0.177.0/async/delay.ts": "73aa04cec034c84fc748c7be49bb15cac3dd43a57174bfdb7a4aec22c248f0dd", "https://deno.land/std@0.177.0/async/mod.ts": "f04344fa21738e5ad6bea37a6bfffd57c617c2d372bb9f9dcfd118a1b622e576", "https://deno.land/std@0.177.0/async/mux_async_iterator.ts": "70c7f2ee4e9466161350473ad61cac0b9f115cff4c552eaa7ef9d50c4cbb4cc9", "https://deno.land/std@0.177.0/async/pool.ts": "fd082bd4aaf26445909889435a5c74334c017847842ec035739b4ae637ae8260", "https://deno.land/std@0.177.0/async/retry.ts": "5efa3ba450ac0c07a40a82e2df296287b5013755d232049efd7ea2244f15b20f", "https://deno.land/std@0.177.0/async/tee.ts": "47e42d35f622650b02234d43803d0383a89eb4387e1b83b5a40106d18ae36757", "https://deno.land/std@0.177.0/http/server.ts": "cbb17b594651215ba95c01a395700684e569c165a567e4e04bba327f41197433", "https://esm.sh/@supabase/auth-js@2.64.4/denonext/auth-js.mjs": "a72d41796602ae950f8b7b980a629ccb4ac45376f0e510503c7a5e16019f4362", "https://esm.sh/@supabase/functions-js@2.4.1/denonext/functions-js.mjs": "0dfa81270bba8ae5af371b273a564203b4625b3a18e70774235b0b46ef40d940", "https://esm.sh/@supabase/functions-js@2.4.4/denonext/functions-js.mjs": "7adeb257410ef3c4a8a1eb9b4ff416c0075d1c32860ca04913c8a9dace1de6a6", "https://esm.sh/@supabase/functions-js@2.4.4?target=denonext": "e2476c61b8afb50cd0987ff03900f48daa646706d6bad319c347e38870b9e72b", "https://esm.sh/@supabase/gotrue-js@2.70.0/denonext/gotrue-js.mjs": "98f35291588b46acaf1a109d92b2f4423609595aba9a2ef8be611b413df03201", "https://esm.sh/@supabase/gotrue-js@2.70.0?target=denonext": "05513b9349789ef1cf6284457abbe0a864601da43ce79986ca028c1699489020", "https://esm.sh/@supabase/node-fetch@2.6.15/denonext/node-fetch.mjs": "0bae9052231f4f6dbccc7234d05ea96923dbf967be12f402764580b6bf9f713d", "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext": "4d28c4ad97328403184353f68434f2b6973971507919e9150297413664919cf3", "https://esm.sh/@supabase/postgrest-js@1.15.8/denonext/postgrest-js.mjs": "e38ab1fbd1d7ed325e3ddc20c72001d3653151c3d3be3455d682b141321a345d", "https://esm.sh/@supabase/postgrest-js@1.19.4/denonext/postgrest-js.mjs": "2073b5552ba10c7a8302bffffae771e3aede1daf833382355dae239fb0ab2576", "https://esm.sh/@supabase/postgrest-js@1.19.4?target=denonext": "b526be6f497dc0c461937d1f877cf2e2ebf5dbd92cf2af098fc22a6362a9a569", "https://esm.sh/@supabase/realtime-js@2.10.2/denonext/realtime-js.mjs": "3a5a350e881736135ec1bd8c4361c96edeb647d3c56be7cf3a68eddfaf3ce242", "https://esm.sh/@supabase/realtime-js@2.11.15/denonext/realtime-js.mjs": "3b7511415165a1b503eae1dfbc10814ef3d0c1c866c97fdec4f6714bfa34a847", "https://esm.sh/@supabase/realtime-js@2.11.15?target=denonext": "ac57c6133b860e3d9b15c811d46d72aec5c21d9eb2e96966eae712b966e0b7f4", "https://esm.sh/@supabase/storage-js@2.6.0/denonext/storage-js.mjs": "d3f8b6f5f538cd0d8f88ed0248c3b5dc845d9ca41500589e19fbfc734c8952de", "https://esm.sh/@supabase/storage-js@2.7.3/denonext/storage-js.mjs": "566d6e1c95b4747ed1dd39a9d49397b011bda21167f946772665e5e0abcff69c", "https://esm.sh/@supabase/storage-js@2.7.3?target=denonext": "c090cd9da74382e43b4af17d607ba2723ce7616a0bdab6f4439778ac03e10e13", "https://esm.sh/@supabase/supabase-js@2.39.3": "832d7714491bd1c8a9008a332b35cb099f24b451ad79e755ecf4d71985fd6610", "https://esm.sh/@supabase/supabase-js@2.39.3/denonext/supabase-js.mjs": "054f4cd1560fa814cdc10d06c0809b00d0817dc06a072fa983e4c023e4346439", "https://esm.sh/@supabase/supabase-js@2.45.0": "36ae57c3f2dc3f94fd122a846b40a87114d29bb2f47e910cb46bee5575febf2e", "https://esm.sh/@supabase/supabase-js@2.45.0/denonext/supabase-js.mjs": "59c4845d0798118ff097b3295b5a6f3ef63d18ff4a31bf8125e91b467e0be7a6", "https://esm.sh/bufferutil@4.0.9/denonext/bufferutil.mjs": "13dca4d5bb2c68cbe119f880fa3bd785b9a81a8e02e0834dae604b4b85295cd8", "https://esm.sh/bufferutil@4.0.9?target=denonext": "e32574569ab438facfcc3f412c659b0719bbf05477136ca176938c9a3ac45125", "https://esm.sh/isows@1.0.7/denonext/isows.mjs": "be1812ebbf28737b43588bbcbd89bf43345e3d58d32be13def331d12361045b4", "https://esm.sh/isows@1.0.7?target=denonext": "31b7306eada995ba18e29841d688a42bcc0166caf302e1b183b6d2a7c649f262", "https://esm.sh/node-gyp-build@4.8.4/denonext/node-gyp-build.mjs": "9a86f2d044fc77bd60aaa3d697c2ba1b818da5fb1b9aaeedec59a40b8e908803", "https://esm.sh/node-gyp-build@4.8.4?target=denonext": "261a6cedf1fdbf159798141ba1e2311ac1510682c5c8b55dacc8cf5fdee4aa06", "https://esm.sh/tr46@0.0.3/denonext/tr46.mjs": "5753ec0a99414f4055f0c1f97691100f13d88e48a8443b00aebb90a512785fa2", "https://esm.sh/tr46@0.0.3?target=denonext": "19cb9be0f0d418a0c3abb81f2df31f080e9540a04e43b0f699bce1149cba0cbb", "https://esm.sh/utf-8-validate@6.0.5/denonext/utf-8-validate.mjs": "66b8ea532a0c745068f5b96ddb1bae332c3036703243541d2e89e66331974d98", "https://esm.sh/utf-8-validate@6.0.5?target=denonext": "071bc33ba1a58297e23a34d69dd589fd06df04b0f373b382ff5da544a623f271", "https://esm.sh/webidl-conversions@3.0.1/denonext/webidl-conversions.mjs": "54b5c2d50a294853c4ccebf9d5ed8988c94f4e24e463d84ec859a866ea5fafec", "https://esm.sh/webidl-conversions@3.0.1?target=denonext": "4e20318d50528084616c79d7b3f6e7f0fe7b6d09013bd01b3974d7448d767e29", "https://esm.sh/whatwg-url@5.0.0/denonext/whatwg-url.mjs": "29b16d74ee72624c915745bbd25b617cfd2248c6af0f5120d131e232a9a9af79", "https://esm.sh/whatwg-url@5.0.0?target=denonext": "f001a2cadf81312d214ca330033f474e74d81a003e21e8c5d70a1f46dc97b02d", "https://esm.sh/ws@8.18.3/denonext/ws.mjs": "063ab2c92e0908d060e4de414b74e941fbd1b3ccedfce35cc965703a8a3f86c1", "https://esm.sh/ws@8.18.3?target=denonext": "a5ca9d7990fc419fd8b8d06d0855bb64688769e6bb887ba07d36c13172f882b5"}, "workspace": {"packageJson": {"dependencies": ["npm:@agentdeskai/browser-tools-server@^1.2.0", "npm:@eslint/js@^9.9.0", "npm:@hookform/resolvers@^5.0.1", "npm:@radix-ui/react-accordion@^1.2.8", "npm:@radix-ui/react-alert-dialog@^1.1.11", "npm:@radix-ui/react-aspect-ratio@^1.1.4", "npm:@radix-ui/react-avatar@^1.1.7", "npm:@radix-ui/react-checkbox@^1.2.3", "npm:@radix-ui/react-context-menu@^2.2.12", "npm:@radix-ui/react-dialog@^1.1.14", "npm:@radix-ui/react-dropdown-menu@^2.1.12", "npm:@radix-ui/react-hover-card@^1.1.11", "npm:@radix-ui/react-label@^2.1.4", "npm:@radix-ui/react-menubar@^1.1.12", "npm:@radix-ui/react-navigation-menu@^1.2.10", "npm:@radix-ui/react-popover@^1.1.11", "npm:@radix-ui/react-progress@^1.1.4", "npm:@radix-ui/react-radio-group@^1.3.4", "npm:@radix-ui/react-scroll-area@^1.2.6", "npm:@radix-ui/react-select@^2.2.2", "npm:@radix-ui/react-separator@^1.1.4", "npm:@radix-ui/react-slider@^1.3.2", "npm:@radix-ui/react-slot@^1.2.0", "npm:@radix-ui/react-switch@^1.2.2", "npm:@radix-ui/react-tabs@^1.1.9", "npm:@radix-ui/react-toast@^1.2.11", "npm:@radix-ui/react-toggle-group@^1.1.7", "npm:@radix-ui/react-toggle@^1.1.6", "npm:@radix-ui/react-tooltip@^1.2.4", "npm:@sentry/react@^9.33.0", "npm:@sentry/tracing@^7.120.3", "npm:@supabase/auth-helpers-react@0.5", "npm:@supabase/supabase-js@^2.50.2", "npm:@tailwindcss/typography@~0.5.15", "npm:@tanstack/react-query@^5.74.4", "npm:@testing-library/jest-dom@^6.6.3", "npm:@types/bcryptjs@^2.4.6", "npm:@types/ioredis@^4.28.10", "npm:@types/jest@^29.5.14", "npm:@types/mapbox-gl@^3.4.1", "npm:@types/node@^22.14.1", "npm:@types/pg@^8.11.13", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.3", "npm:@types/swagger-ui-react@^4.18.3", "npm:@vitejs/plugin-react-swc@^3.5.0", "npm:autoprefixer@^10.4.20", "npm:bcryptjs@^3.0.2", "npm:class-variance-authority@~0.7.1", "npm:cmdk@^1.1.1", "npm:cypress@^14.3.2", "npm:date-fns@^3.6.0", "npm:dotenv@^16.5.0", "npm:drizzle-kit@0.31", "npm:drizzle-orm@~0.43.1", "npm:embla-carousel-react@^8.6.0", "npm:eslint-plugin-react-hooks@^5.1.0-rc.0", "npm:eslint-plugin-react-refresh@~0.4.9", "npm:eslint@^9.9.0", "npm:globals@^15.9.0", "npm:input-otp@^1.4.2", "npm:i<PERSON>is@^5.6.1", "npm:jest-environment-jsdom@^30.0.2", "npm:lovable-tagger@^1.1.7", "npm:lucide-react@0.503", "npm:mapbox-gl@^3.11.1", "npm:next-themes@~0.4.6", "npm:pg@^8.14.1", "npm:postcss@^8.4.47", "npm:postgres@^3.4.5", "npm:posthog-js@^1.255.1", "npm:react-day-picker@^8.10.1", "npm:react-dom@^18.2.0", "npm:react-hook-form@^7.56.1", "npm:react-resizable-panels@^2.1.8", "npm:react-router-dom@^6.22.3", "npm:react@^18.2.0", "npm:recharts@^2.15.3", "npm:redis@^5.1.1", "npm:resend@^4.5.0", "npm:serve@^14.2.0", "npm:sonner@^2.0.3", "npm:supabase@^2.26.9", "npm:swagger-ui-react@^5.21.0", "npm:tailwind-merge@^3.2.0", "npm:tailwindcss-animate@^1.0.7", "npm:tailwindcss@^3.4.11", "npm:task-master-ai@~0.12.1", "npm:testcafe@^3.7.2", "npm:ts-jest@^29.4.0", "npm:ts-node@^10.9.2", "npm:typescript-eslint@^8.0.1", "npm:typescript@^5.8.3", "npm:vaul@^1.1.2", "npm:vite@^5.4.18", "npm:zod@^3.24.3"]}}}