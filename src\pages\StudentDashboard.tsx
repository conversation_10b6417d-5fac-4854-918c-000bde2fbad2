import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useUser } from '@/contexts/UnifiedAuthContext';
import StudentInfoSection from '@/components/student/dashboard/StudentInfoSection';
import StudentLocationSection from '@/components/student/dashboard/StudentLocationSection';
import GuardianSection from '@/components/student/dashboard/GuardianSection';
import StudentDashboardHeader from '@/components/student/dashboard/StudentDashboardHeader';
import LocationSharingModal from '@/components/LocationSharingModal';
import LocationSharingProgress from '@/components/LocationSharingProgress';
import { useGuardianData } from '@/hooks/useGuardianData';
import { apiService } from '@/lib/api/api-service';
import { Guardian } from '@/types/auth';
import { LocationData } from '@/types/database';
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import { SharedLocationAlert } from '@/components/ui/shared-location-alert';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useIsMobile } from '@/hooks/use-mobile';
import { fetchIpLocation } from '@/lib/utils/geolocation';
import LocationActions from '@/components/location/LocationActions';
import { apiCache } from '@/utils/apiAvailabilityCache';

// Desired accuracy in meters for a "precise" location
const ACCURACY_GOAL = 20;
const DUPLICATE_DISTANCE_THRESHOLD = 0.0001; // ~11m
const DUPLICATE_TIME_WINDOW_MS = 60000; // 1 minute

const StudentDashboard: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();
  const [sharingStatus, setSharingStatus] = useState<Record<string, string>>({});
  const [isSendingAll, setIsSendingAll] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [mapForceUpdate, setMapForceUpdate] = useState(0);
  const [actionsStatus, setActionsStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  
  // New state for student locations
  const [studentLocations, setStudentLocations] = useState<LocationData[]>([]);
  const [isLoadingLocations, setIsLoadingLocations] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  
  // Progress tracking states
  const [showProgress, setShowProgress] = useState(false);
  const [progressData, setProgressData] = useState({
    total: 0,
    success: 0,
    error: 0,
    complete: false
  });
  
  // Estados para o alerta de compartilhamento (especialmente para mobile)
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertSuccess, setAlertSuccess] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertDetails, setAlertDetails] = useState('');
  
  // Detectar dispositivo mobile
  const isMobile = useIsMobile();

  // Memoizar informações do usuário para evitar re-computação
  const userInfo = useMemo(() => ({
    fullName:
      (user as any)?.full_name ||
      user?.user_metadata?.full_name ||
      user?.email?.split('@')[0] ||
      'User',
    phone:
      (user as any)?.phone ||
      user?.user_metadata?.phone ||
      t('common.notProvided'),
    id: user?.id,
    email: user?.email,
  }), [user]);

  // Guardian data management using our custom hook
  const { 
    loading: isLoadingGuardians, 
    error, 
    guardians, 
    fetchGuardians,
    addGuardian, 
    removeGuardian
  } = useGuardianData();

  // Fetch guardians when user is available - CORRIGIDO
  useEffect(() => {
    if (userInfo.id) {
      console.log('[StudentDashboard] Carregando responsáveis para:', userInfo.id);
      fetchGuardians(userInfo.id);
    }
  }, [userInfo.id]); // REMOVIDO fetchGuardians das dependências

  // Load student's own locations - CORRIGIDO PARA EVITAR LOOPS
  const loadStudentLocations = useCallback(async () => {
    if (!userInfo.id) return;

    console.log('[StudentDashboard] Carregando localizações do estudante');
    setIsLoadingLocations(true);
    setLocationError(null);

    try {
      // Use direct query for student's own locations
      const { data: locationsData, error: locationsError } = await supabase
        .from('locations')
        .select('*')
        .eq('user_id', userInfo.id)
        .order('timestamp', { ascending: false })
        .limit(10);

      if (locationsError) throw locationsError;

      // Get user profile separately to avoid JOIN issues
      const { data: profileData } = await supabase
        .from('profiles')
        .select('full_name, email')
        .eq('user_id', userInfo.id)
        .single();

      const formattedLocations: LocationData[] = locationsData?.map(loc => ({
        id: loc.id,
        user_id: loc.user_id,
        latitude: loc.latitude,
        longitude: loc.longitude,
        timestamp: loc.timestamp,
        shared_with_guardians: loc.shared_with_guardians,
        address: loc.address,
        student_name: profileData?.full_name || userInfo.fullName,
        student_email: profileData?.email || userInfo.email || '',
        created_at: loc.created_at || loc.timestamp,
        user: profileData ? { 
          full_name: profileData.full_name,
          user_type: 'student'
        } : undefined
      })) || [];

      setStudentLocations(formattedLocations);
      console.log('[StudentDashboard] Localizações carregadas:', formattedLocations.length);
      
      // Forçar atualização do mapa para focar na localização mais recente
      if (formattedLocations.length > 0) {
        console.log('[StudentDashboard] Forçando foco na localização mais recente no mapa');
        setMapForceUpdate(prev => prev + 1);
      }
    } catch (error: unknown) {
      console.error('[StudentDashboard] Erro ao carregar localizações:', error);
      setLocationError(t('studentDashboard.loadError'));
    } finally {
      setIsLoadingLocations(false);
    }
  }, [userInfo.id, userInfo.fullName, userInfo.email]); // Dependências estáveis

  // Função para salvar a localização no banco de dados com PRECISÃO - MELHORADA
  const saveLocationToDatabase = useCallback(async (
    latitude: number,
    longitude: number,
    accuracy?: number
  ): Promise<boolean> => {
    if (!userInfo.id) return false;

    // Check for recent duplicate location
    try {
      const { data: last, error: lastErr } = await supabase
        .from('locations')
        .select('latitude, longitude, timestamp')
        .eq('user_id', userInfo.id)
        .order('timestamp', { ascending: false })
        .limit(1)
        .maybeSingle();
      if (!lastErr && last) {
        const dist = Math.sqrt(
          Math.pow(latitude - last.latitude, 2) +
            Math.pow(longitude - last.longitude, 2)
        );
        const timeDiff = Math.abs(Date.now() - new Date(last.timestamp).getTime());
        if (dist < DUPLICATE_DISTANCE_THRESHOLD && timeDiff < DUPLICATE_TIME_WINDOW_MS) {
          console.log('[StudentDashboard] Ignorando localização duplicada');
          return true;
        }
      }
    } catch (dupErr) {
      console.warn('[StudentDashboard] Erro ao verificar duplicidade:', dupErr);
    }

    try {
      console.log(`[StudentDashboard] 💾 Salvando localização PRECISA no banco:`, {
        latitude,
        longitude,
        accuracy: accuracy ? `${accuracy}m` : 'desconhecida' 
      });
      
      // Usar função RPC específica (recomendado)
      const { data, error } = await supabase.rpc('save_student_location', {
        p_latitude: latitude,
        p_longitude: longitude,
        p_shared_with_guardians: true,
        p_accuracy: accuracy ?? null,
        p_address: null
      });
      
      if (error) {
        console.error('[StudentDashboard] Erro ao salvar localização via RPC:', error);
        
        // Fallback para inserção direta na tabela locations
        const { error: insertError } = await supabase
          .from('locations')
          .insert([{
            user_id: userInfo.id,
            latitude,
            longitude,
            shared_with_guardians: true
          }]);
        
        if (insertError) {
          console.error('[StudentDashboard] Erro no fallback de inserção direta:', insertError);
          return false;
        }
      }
      
      // Reload locations after saving e atualizar o mapa imediatamente
      await loadStudentLocations();
      setMapForceUpdate(prev => prev + 1);
      
      console.log('✅ Localização salva com sucesso no banco de dados');
      return true;
    } catch (error) {
      console.error('[StudentDashboard] Exceção ao salvar localização:', error);
      return false;
    }
  }, [userInfo.id]); // REMOVIDO loadStudentLocations das dependências

  const startWatchPosition = useCallback(
    (currentAccuracy: number | undefined) => {
      console.log(
        `[StudentDashboard] 🔄 Precisão >${ACCURACY_GOAL}m, iniciando watchPosition para refinar`
      );
      let bestAccuracy = currentAccuracy ?? Number.MAX_SAFE_INTEGER;
      const watchId = navigator.geolocation.watchPosition(
        async pos => {
          console.log(
            `[StudentDashboard] ▶️ watchPosition atualização: ${pos.coords.accuracy} m`
          );
          if (pos.coords.accuracy < bestAccuracy) {
            bestAccuracy = pos.coords.accuracy;
            await saveLocationToDatabase(
              pos.coords.latitude,
              pos.coords.longitude,
              pos.coords.accuracy
            );
            if (bestAccuracy <= ACCURACY_GOAL) {
              navigator.geolocation.clearWatch(watchId);
            }
          }
        },
        err => {
          console.error('[StudentDashboard] watchPosition erro:', err);
          navigator.geolocation.clearWatch(watchId);
        },
        { enableHighAccuracy: true }
      );
      setTimeout(() => navigator.geolocation.clearWatch(watchId), 45000);
    },
    [saveLocationToDatabase]
  );

  const resolveLocationViaServer = useCallback(async (
    lat?: number,
    lon?: number
  ): Promise<{ latitude: number; longitude: number; accuracy?: number; source: string } | null> => {
    const endpoint = '/api/resolve-location';

    console.log(`[StudentDashboard] 🎯 resolveLocationViaServer called with lat=${lat}, lon=${lon}`);
    apiCache.logCacheState('before resolveLocationViaServer');

    // Check if API is available before making the call
    const { shouldCall, reason } = apiCache.shouldAttemptCall(endpoint);
    if (!shouldCall) {
      console.log(`[StudentDashboard] 🚫 Skipping API call: ${reason}`);
      apiCache.logCacheState('after shouldAttemptCall (blocked)');
      // Use fallback immediately
      if (lat !== undefined && lon !== undefined) {
        return { latitude: lat, longitude: lon, accuracy: 1000, source: 'fallback' };
      }
      // Default to São Paulo coordinates if no coordinates provided
      return { latitude: -23.5489, longitude: -46.6388, accuracy: 5000, source: 'default' };
    }

    console.log(`[StudentDashboard] ✅ Proceeding with API call: ${reason}`);

    try {
      const res = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body:
          lat !== undefined && lon !== undefined
            ? JSON.stringify({ latitude: lat, longitude: lon })
            : undefined,
      });

      // Handle 404 errors gracefully (API server not running)
      if (res.status === 404) {
        apiCache.markApiFailure(endpoint, 404);
        console.warn('[StudentDashboard] API server not available, using fallback location');
        // Return a fallback location or the provided coordinates
        if (lat !== undefined && lon !== undefined) {
          return { latitude: lat, longitude: lon, accuracy: 1000, source: 'fallback' };
        }
        // Default to São Paulo coordinates if no coordinates provided
        return { latitude: -23.5489, longitude: -46.6388, accuracy: 5000, source: 'default' };
      }

      const type = res.headers.get('content-type') || '';
      if (!res.ok || !type.includes('application/json')) {
        apiCache.markApiFailure(endpoint, res.status);
        throw new Error('invalid response');
      }

      const data = await res.json();
      if (typeof data.latitude === 'number' && typeof data.longitude === 'number') {
        apiCache.markApiSuccess(endpoint);
        return {
          latitude: data.latitude,
          longitude: data.longitude,
          accuracy: data.accuracy ?? undefined,
          source: data.source ?? 'ipinfo',
        };
      }

      apiCache.markApiFailure(endpoint);
      throw new Error('invalid data');
    } catch (err) {
      // Only mark as failure if it's not already cached as unavailable
      if (apiCache.isApiAvailable(endpoint)) {
        apiCache.markApiFailure(endpoint);
      }

      console.warn('[StudentDashboard] Backend location failed, trying IP fallback', err);
      try {
        const ip = await fetchIpLocation();
        return { latitude: ip.lat, longitude: ip.lon, accuracy: undefined, source: 'ipinfo' };
      } catch (ipErr) {
        console.error('[StudentDashboard] IP fallback failed:', ipErr);
        return null;
      }
    }
  }, []);

  // Obter a posição atual com fallback AWS imediato em desktop/laptop
  const getCurrentPositionAccurate = useCallback(async (): Promise<{latitude: number, longitude: number, accuracy?: number, source?: string} | null> => {
    const isMobileDevice = isMobile;
    console.log('[StudentDashboard] 🎯 Obtendo localização de ALTA PRECISÃO');
    try {
      // Desktop/laptop: tentar GPS uma vez, se precisão ruim (>200m) ou erro, acionar fallback
      if (!isMobileDevice) {
        try {
          const position = await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 0
            });
          });
          const { latitude, longitude, accuracy } = position.coords;
          if (accuracy && accuracy <= 200) {
            return { latitude, longitude, accuracy, source: 'gps' };
          }
          const fallback = await resolveLocationViaServer(latitude, longitude);
          if (fallback) return fallback;
          throw new Error('Não foi possível obter localização via IP');
        } catch (gpsError) {
          // Falha total no GPS, tentar backend puro
          const fallback = await resolveLocationViaServer();
          if (fallback) return fallback;
          throw new Error('Não foi possível obter localização nem via GPS nem IP');
        }
      }
      // Mobile: manter tentativas múltiplas e watchPosition
      let bestPosition: GeolocationPosition | null = null;
      let attempts = 0;
      const maxAttempts = 3;
      while (attempts < maxAttempts && (!bestPosition || bestPosition.coords.accuracy > ACCURACY_GOAL)) {
        attempts++;
        try {
          const position = await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 30000,
              maximumAge: 0
            });
          });
          if (!bestPosition || position.coords.accuracy < bestPosition.coords.accuracy) {
            bestPosition = position;
          }
          if (position.coords.accuracy <= 10) {
            break;
          }
        } catch (attemptError) {
          if (attempts === maxAttempts && !bestPosition) {
            throw attemptError;
          }
        }
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
      if (!bestPosition) {
        throw new Error(t('studentDashboard.locationAttemptsError'));
      }
      return {
        latitude: bestPosition.coords.latitude,
        longitude: bestPosition.coords.longitude,
        accuracy: bestPosition.coords.accuracy,
        source: 'gps'
      };
    } catch (error) {
      console.error('[StudentDashboard] ❌ Erro ao obter posição:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível obter sua localização',
        variant: 'destructive',
      });
      return null;
    }
  }, [isMobile, toast, resolveLocationViaServer]);

  // Get current location for LocationActions component com FEEDBACK DETALHADO
  const getCurrentLocation = useCallback(async () => {
    if (!userInfo.id) {
      toast({
        title: t('common.error'),
        description: t('studentDashboard.userIdNotFound'),
        variant: 'destructive'
      });
      console.error('[StudentDashboard] Erro: Usuário não encontrado');
      return;
    }

    setIsGettingLocation(true);
    let position: any = null;
    let error: any = null;
    try {
      position = await getCurrentPositionAccurate();
      if (!position) {
        throw new Error(t('studentDashboard.noCurrentLocation'));
      }
      const { latitude, longitude, accuracy, source } = position;
      // Salvar no banco de dados com precisão
      const savedToDb = await saveLocationToDatabase(latitude, longitude, accuracy);
      if (savedToDb) {
        // Feedback diferenciado baseado na precisão
        let precisionMessage = t('studentDashboard.locationRecorded');
        let precisionVariant: "default" | "destructive" = "default";
        if (accuracy && accuracy <= 10) {
          precisionMessage += ` com EXCELENTE precisão (±${Math.round(accuracy)}m)`;
        } else if (accuracy && accuracy <= 50) {
          precisionMessage += ` com BOA precisão (±${Math.round(accuracy)}m)`;
        } else if (accuracy && accuracy <= 100) {
          precisionMessage += ` com precisão MODERADA (±${Math.round(accuracy)}m)`;
        } else if (accuracy) {
          precisionMessage += ` com baixa precisão (±${Math.round(accuracy)}m)`;
          precisionVariant = "destructive";
        }
        toast({
          title: t('studentDashboard.locationUpdated'),
          description: precisionMessage,
          variant: precisionVariant,
        });
        if (!accuracy || accuracy > ACCURACY_GOAL) {
          startWatchPosition(accuracy);
        }
      }
    } catch (err) {
      error = err;
      console.error('[StudentDashboard] Erro ao obter localização:', err);
      toast({
        title: t('studentDashboard.locationErrorTitle'),
        description: err instanceof Error ? err.message : t('studentDashboard.unknownLocationError'),
        variant: 'destructive',
      });
    } finally {
      setIsGettingLocation(false);
      // Log apenas no console
      console.log('[StudentDashboard] Resultado da operação de localização:', {
        success: !!position,
        position,
        error: error?.message || error
      });
    }
  }, [userInfo.id, getCurrentPositionAccurate, saveLocationToDatabase, startWatchPosition, toast]);

  // Load student locations on mount - APENAS UMA VEZ
  useEffect(() => {
    console.log('[StudentDashboard] useEffect para carregar localizações');
    loadStudentLocations();
  }, [userInfo.id]); // APENAS userInfo.id

  // Obtain current location once when dashboard loads
  useEffect(() => {
    if (userInfo.id) {
      console.log('[StudentDashboard] Obtendo localização inicial do usuário');
      getCurrentLocation();
    }
  }, [userInfo.id]);

  // Check if user has already consented to location sharing
  const hasLocationConsent = localStorage.getItem('lgpd-location-consent') === 'true';

  // Lock atômico para impedir múltiplos envios concorrentes
  let shareAllLock = false;

  // Handle share location to all guardians - CORRIGIDO PARA FUNCIONAR AUTOMATICAMENTE
  const handleShareAll = useCallback(async () => {
    if (shareAllLock) {
      console.warn('[StudentDashboard] Envio já em andamento (lock atômico)');
      return;
    }
    shareAllLock = true;
    try {
      console.log('[StudentDashboard] 🚀 BOTÃO COMPARTILHAR CLICADO!');
      console.log('[StudentDashboard] Responsáveis cadastrados:', guardians.length);
      
      // Se há responsáveis cadastrados, enviar automaticamente
      if (guardians.length > 0) {
        console.log('[StudentDashboard] Enviando automaticamente para responsáveis cadastrados');
        await executeShareAll();
        return;
      }
      
      // Se não há responsáveis, mostrar mensagem
      toast({
        title: t('studentDashboard.noGuardians'),
        description: t('studentDashboard.addGuardiansWarning'),
        variant: 'destructive',
      });
    } finally {
      shareAllLock = false;
    }
  }, [guardians.length, toast]); // Dependências corretas

  const executeShareAll = useCallback(async () => {
    console.log('[StudentDashboard] Executando compartilhamento para todos os responsáveis');
    
    if (guardians.length === 0) {
      toast({
        title: t('studentDashboard.noGuardiansFound'),
        description: t('studentDashboard.noGuardiansToShare'),
        variant: 'destructive',
      });
      return;
    }

    setIsSendingAll(true);
    setShowProgress(true);
    setProgressData({
      total: guardians.length,
      success: 0,
      error: 0,
      complete: false
    });

    try {
      // Obter a posição atual
      const position = await getCurrentPositionAccurate();
      
      if (!position) {
        throw new Error(t('studentDashboard.noCurrentLocation'));
      }
      
      const { latitude, longitude, accuracy } = position;
      console.log('[StudentDashboard] 🎯 Localização obtida para compartilhamento:', { latitude, longitude, accuracy: accuracy ? `${accuracy}m` : 'desconhecida' });
      
      // Salvar a localização no banco de dados com precisão
      let savedToDb = false;
      try {
        savedToDb = await saveLocationToDatabase(latitude, longitude, accuracy);
      } catch (err: any) {
        // Se erro for de duplicidade, mostrar toast específico
        if (err?.message?.toLowerCase().includes('duplicate') || err?.message?.toLowerCase().includes('já existe')) {
          toast({
            title: t('studentDashboard.duplicateLocationTitle', 'Localização já enviada'),
            description: t('studentDashboard.duplicateLocationDesc', 'Você já enviou esta localização recentemente. Aguarde antes de tentar novamente.'),
            variant: 'destructive',
          });
          setIsSendingAll(false);
          setShowProgress(false);
          return;
        }
        throw err;
      }
      if (!savedToDb) {
        // Se não salvou e não foi duplicidade, mostrar erro genérico
        toast({
          title: t('studentDashboard.locationErrorTitle'),
          description: t('studentDashboard.checkPermissions'),
          variant: 'destructive',
        });
        setIsSendingAll(false);
        setShowProgress(false);
        return;
      }
      
      let successCount = 0;
      let failCount = 0;
      
      console.log('[StudentDashboard] Enviando para responsáveis:', guardians.map(g => g.email));
      
      // Send to each guardian with progress tracking
      for (const guardian of guardians) {
        try {
          console.log(`[StudentDashboard] Enviando para: ${guardian.email}`);
          const result = await apiService.shareLocation(
            guardian.email,
            latitude,
            longitude,
            userInfo.fullName
          );
          
          if (result.success) {
            successCount++;
            setProgressData(prev => ({ ...prev, success: successCount }));
            console.log(`[StudentDashboard] ✅ Sucesso para: ${guardian.email}`);
          } else {
            throw new Error(result.message || 'Falha ao compartilhar');
          }
        } catch (err) {
          failCount++;
          setProgressData(prev => ({ ...prev, error: failCount }));
          console.error(`[StudentDashboard] ❌ Falha para ${guardian.email}:`, err);
        }
      }
      
      // Mark progress as complete
      setProgressData(prev => ({ ...prev, complete: true }));
      
      // Hide progress after delay
      setTimeout(() => {
        setShowProgress(false);
      }, 3000);
      
      // Resumo final do compartilhamento
      const successMessage = successCount > 0
        ? t('studentDashboard.shareSuccess', { count: successCount }) +
          (failCount > 0 ? t('studentDashboard.failSuffix', { fails: failCount }) : '')
        : t('studentDashboard.shareFail');
      
      console.log('[StudentDashboard] Resultado final:', { successCount, failCount, message: successMessage });
      
      if (isMobile) {
        // Em mobile usamos o alerta modal para o resultado final
        setAlertSuccess(successCount > 0);
        setAlertMessage(successMessage);
        setAlertDetails(successCount > 0
          ? t('studentDashboard.sharedByEmail')
          : t('studentDashboard.checkPermissions'));
        setAlertOpen(true);
      } else {
        // Em desktop mantemos o toast
        toast({
          title: t('studentDashboard.shareComplete'),
          description: successMessage,
          variant: successCount > 0 ? 'default' : 'destructive',
        });
      }
      
      // Atualiza o status visual dos botões
      setSharingStatus(prevStatus => {
        const newStatus: Record<string, string> = {};
        for (const guardian of guardians) {
          newStatus[guardian.id] = 'success';
        }
        return newStatus;
      });
      
    } catch (error: unknown) {
      console.error('[StudentDashboard] Erro ao compartilhar localização:', error);
      
      setProgressData(prev => ({ ...prev, complete: true }));
      setTimeout(() => setShowProgress(false), 3000);
      
      // Feedback de erro geral
      if (isMobile) {
        // Em mobile usamos o alerta modal para o erro
        setAlertSuccess(false);
        setAlertMessage(t('studentDashboard.locationErrorTitle'));
        setAlertDetails(t('studentDashboard.checkPermissions'));
        setAlertOpen(true);
      } else {
        // Em desktop mantemos o toast
        toast({
          title: t('studentDashboard.locationErrorTitle'),
          description: t('studentDashboard.checkPermissions'),
          variant: 'destructive',
        });
      }
    } finally {
      setIsSendingAll(false);
    }
  }, [guardians, getCurrentPositionAccurate, saveLocationToDatabase, userInfo.fullName, isMobile, toast]);
  
  // Share location with a specific guardian - SIMPLIFICADO
  const shareLocationToGuardian = useCallback(async (guardian: Guardian, providedLat?: number, providedLong?: number): Promise<void> => {
    // Executar diretamente sem modal LGPD para responsáveis individuais
    await executeShareLocationToGuardian(guardian, providedLat, providedLong);
  }, []);

  const executeShareLocationToGuardian = useCallback(async (guardian: Guardian, providedLat?: number, providedLong?: number): Promise<void> => {
    setSharingStatus(prev => ({ ...prev, [guardian.id]: 'loading' }));
    
    try {
      let latitude: number, longitude: number;
      
      if (providedLat !== undefined && providedLong !== undefined) {
        // Use provided coordinates if available
        latitude = providedLat;
        longitude = providedLong;
      } else {
        // Otherwise, get current position
        const position = await getCurrentPositionAccurate();
        
        if (!position) {
          throw new Error(t('studentDashboard.noCurrentLocation'));
        }
        
        ({ latitude, longitude } = position);
        
        // Salvar no banco de dados com precisão
        await saveLocationToDatabase(latitude, longitude, position.accuracy);
      }
      
      // Compartilhar via email
      const result = await apiService.shareLocation(
        guardian.email,
        latitude,
        longitude,
        userInfo.fullName
      );
      
      if (result.success) {
        setSharingStatus(prev => ({ ...prev, [guardian.id]: 'success' }));
        
        // Em dispositivos móveis usamos o alerta modal em vez do toast
        if (isMobile) {
          setAlertSuccess(true);
          setAlertMessage(t('studentDashboard.shareToGuardianSuccess', { name: guardian.full_name || guardian.email }));
          setAlertDetails(t('studentDashboard.sharedByEmail'));
          setAlertOpen(true);
        } else {
          // Em desktop mantemos o toast
          toast({
            title: t('studentDashboard.locationShared'),
            description: t('studentDashboard.shareToGuardianSuccess', { name: guardian.full_name || guardian.email }),
            variant: 'default',
          });
        }
      } else {
        throw new Error(result.message || 'Falha ao compartilhar localização');
      }
    } catch (error: unknown) {
      console.error('[StudentDashboard] Erro ao compartilhar localização:', error);
      setSharingStatus(prev => ({ ...prev, [guardian.id]: 'error' }));
      
      // Em dispositivos móveis usamos o alerta modal para erros também
      const errorMessage = error instanceof Error ? error.message : t('studentDashboard.unknownError');
      if (isMobile) {
        setAlertSuccess(false);
        setAlertMessage(errorMessage || t('studentDashboard.shareFail'));
        setAlertDetails(t('studentDashboard.checkPermissions'));
        setAlertOpen(true);
      } else {
        // Em desktop mantemos o toast para erros
        toast({
          title: t('studentDashboard.shareErrorTitle'),
          description: errorMessage || t('studentDashboard.shareFail'),
          variant: 'destructive',
        });
      }
    }
  }, [getCurrentPositionAccurate, saveLocationToDatabase, userInfo.fullName, isMobile, toast]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!userInfo.id) {
      navigate('/login');
    }
  }, [userInfo.id, navigate]);

  // Wrapper for addGuardian to convert return type - ESTABILIZADO
  const handleAddGuardian = useCallback(async (guardianData: Partial<Guardian>): Promise<void> => {
    if (userInfo.id) {
      await addGuardian(userInfo.id, guardianData.email || '');
    }
  }, [userInfo.id, addGuardian]);

  // Wrapper for removeGuardian to convert return type - ESTABILIZADO
  const handleRemoveGuardian = useCallback(async (id: string): Promise<void> => {
    const guardian = guardians.find(g => g.id === id);
    if (guardian) {
      await removeGuardian(guardian);
    }
  }, [guardians, removeGuardian]);


  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="content-overlay rounded-xl bg-transparent w-full max-w-4xl mx-auto flex flex-col gap-8 px-4 py-6 md:px-8 md:py-8">
        <StudentDashboardHeader userFullName={userInfo.fullName} className="w-full mb-2" />
        <div className="flex flex-col md:flex-row gap-8 w-full">
          <div className="flex-1 min-w-[280px] max-w-md h-full">
            <div className="p-6 h-full rounded-2xl bg-transparent">
              <StudentInfoSection
                userFullName={userInfo.fullName}
                email={userInfo.email}
                phone={userInfo.phone}
                onGetLocation={getCurrentLocation}
                isGettingLocation={isGettingLocation}
                onShareAll={handleShareAll}
                guardianCount={guardians.length}
                isSendingAll={isSendingAll}
                sharingStatus={actionsStatus}
                hasLocations={studentLocations.length > 0}
                className="h-full bg-transparent"
              />
            </div>
          </div>
          <div className="flex-1 min-w-[280px] max-w-md h-full flex items-stretch">
            <div className="p-6 h-full w-full rounded-2xl bg-transparent">
              <LocationActions
                onGetLocation={getCurrentLocation}
                isGettingLocation={isGettingLocation}
                onShareAll={handleShareAll}
                guardianCount={guardians.length}
                isSendingAll={isSendingAll}
                sharingStatus={actionsStatus}
                hasLocations={studentLocations.length > 0}
                className="h-full [&_.flex-col]:!flex-col bg-transparent"
              />
            </div>
          </div>
        </div>
        <StudentLocationSection
          userId={userInfo.id}
          userName={userInfo.fullName}
          userEmail={userInfo.email || ''}
          locations={studentLocations}
          loading={isLoadingLocations}
          error={locationError}
        />
        {/* Progress tracking component */}
        <LocationSharingProgress
          isVisible={showProgress}
          totalGuardians={progressData.total}
          successCount={progressData.success}
          errorCount={progressData.error}
          isComplete={progressData.complete}
        />
        {/* Alerta modal para dispositivos móveis */}
        <SharedLocationAlert
          isOpen={alertOpen}
          onClose={() => setAlertOpen(false)}
          success={alertSuccess}
          message={alertMessage}
          details={alertDetails}
        />
      </div>
    </div>
  );
};

export default StudentDashboard;
