import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser, ExtendedUser } from '../contexts/UnifiedAuthContext';
import StudentInfoSection from '../components/student/dashboard/StudentInfoSection';
import StudentLocationSection from '../components/student/dashboard/StudentLocationSection';
import StudentDashboardHeader from '../components/student/dashboard/StudentDashboardHeader';
import LocationSharingProgress from '../components/LocationSharingProgress';
import { useGuardianData } from '../hooks/useGuardianData';
import { apiService } from '../lib/api/api-service';
import { LocationData } from '../types/database';
import { useToast } from '../components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import { SharedLocationAlert } from '../components/ui/shared-location-alert';
import { supabase } from '../integrations/supabase/client';
import { useIsMobile } from '../hooks/use-mobile';
import { fetchIpLocation } from '../lib/utils/geolocation';
import LocationActions from '../components/location/LocationActions';
import { apiCache } from '../utils/apiAvailabilityCache';

// Desired accuracy in meters for a "precise" location
const ACCURACY_GOAL = 20;
const DUPLICATE_DISTANCE_THRESHOLD = 0.0001; // ~11m
const DUPLICATE_TIME_WINDOW_MS = 60000; // 1 minute

// Define types for position data
interface PositionData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  source?: string;
}

// Define progress data type
interface ProgressData {
  total: number;
  success: number;
  error: number;
  complete: boolean;
}

const StudentDashboard: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();

  const [isSendingAll, setIsSendingAll] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [actionsStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  
  // New state for student locations
  const [studentLocations, setStudentLocations] = useState<LocationData[]>([]);
  const [isLoadingLocations, setIsLoadingLocations] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  
  // Progress tracking states
  const [showProgress, setShowProgress] = useState(false);
  const [progressData, setProgressData] = useState<ProgressData>({
    total: 0,
    success: 0,
    error: 0,
    complete: false
  });
  
  // Estados para o alerta de compartilhamento (especialmente para mobile)
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertSuccess, setAlertSuccess] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertDetails, setAlertDetails] = useState('');
  
  // Detectar dispositivo mobile
  const isMobile = useIsMobile();

  // Memoizar informações do usuário para evitar re-computação
  const userInfo = useMemo(() => ({
    fullName:
      (user as ExtendedUser)?.full_name ||
      user?.user_metadata?.full_name ||
      user?.email?.split('@')[0] ||
      'User',
    phone:
      (user as ExtendedUser)?.phone ||
      user?.user_metadata?.phone ||
      t('common.notProvided'),
    id: user?.id,
    email: user?.email,
  }), [user, t]);

  // Guardian data management using our custom hook
  const {
    error: _error,
    guardians,
    fetchGuardians
  } = useGuardianData();

  // Fetch guardians when user is available
  useEffect(() => {
    if (userInfo.id) {
      console.log('[StudentDashboard] Carregando responsáveis para:', userInfo.id);
      fetchGuardians(userInfo.id);
    }
  }, [userInfo.id, fetchGuardians]); 

  // Load student's own locations
  const loadStudentLocations = useCallback(async () => {
    if (!userInfo.id) return;

    console.log('[StudentDashboard] Carregando localizações do estudante');
    setIsLoadingLocations(true);
    setLocationError(null);

    try {
      // Use direct query for student's own locations
      const { data: locationsData, error: locationsError } = await supabase
        .from('locations')
        .select('*')
        .eq('user_id', userInfo.id)
        .order('timestamp', { ascending: false })
        .limit(10);

      if (locationsError) throw locationsError;

      // Get user profile separately to avoid JOIN issues
      const { data: profileData } = await supabase
        .from('profiles')
        .select('full_name, email')
        .eq('user_id', userInfo.id)
        .single();

      const formattedLocations: LocationData[] = locationsData?.map(loc => ({
        id: loc.id,
        user_id: loc.user_id,
        latitude: loc.latitude,
        longitude: loc.longitude,
        timestamp: loc.timestamp,
        shared_with_guardians: !!loc.shared_with_guardians,
        address: loc.address,
        student_name: profileData?.full_name || userInfo.fullName,
        student_email: profileData?.email || userInfo.email || '',
        created_at: loc.created_at || loc.timestamp,
        user: profileData ? { 
          full_name: profileData.full_name ?? '',
          user_type: 'student'
        } : undefined
      })) || [];

      setStudentLocations(formattedLocations);
      console.log('[StudentDashboard] Localizações carregadas:', formattedLocations.length);
      
      // Forçar atualização do mapa para focar na localização mais recente
      // Forçar atualização do mapa para focar na localização mais recente
      if (formattedLocations.length > 0) {
        console.log('[StudentDashboard] Forçando foco na localização mais recente no mapa');
        // setMapForceUpdate(prev => prev + 1); // Removido pois não é mais necessário
      }
    } 
    catch (error) {
      console.error('[StudentDashboard] Erro ao carregar localizações:', error);
      setLocationError(t('studentDashboard.loadError'));
    } finally {
      setIsLoadingLocations(false);
    }
  }, [userInfo.id, userInfo.fullName, userInfo.email, t]); 

  // Função para salvar a localização no banco de dados com PRECISÃO
  const saveLocationToDatabase = useCallback(async (
    latitude: number,
    longitude: number,
    accuracy?: number
  ): Promise<boolean> => {
    if (!userInfo.id) return false;

    // Check for recent duplicate location
    try {
      const { data: last, error: lastErr } = await supabase
        .from('locations')
        .select('latitude, longitude, timestamp')
        .eq('user_id', userInfo.id)
        .order('timestamp', { ascending: false })
        .limit(1)
        .maybeSingle();
      if (!lastErr && last) {
        const dist = Math.sqrt(
          Math.pow(latitude - last.latitude, 2) +
            Math.pow(longitude - last.longitude, 2)
        );
        const timeDiff = Math.abs(Date.now() - new Date(last.timestamp).getTime());
        if (dist < DUPLICATE_DISTANCE_THRESHOLD && timeDiff < DUPLICATE_TIME_WINDOW_MS) {
          console.log('[StudentDashboard] Ignorando localização duplicada');
          return true;
        }
      }
    } catch (dupErr) {
      console.warn('[StudentDashboard] Erro ao verificar duplicidade:', dupErr);
    }

    try {
      console.log(`[StudentDashboard] 💾 Salvando localização PRECISA no banco:`, {
        latitude,
        longitude,
        accuracy: accuracy ? `${accuracy}m` : 'desconhecida' 
      });
      
      // Try multiple RPC function signatures to handle overloading issues
      let rpcSuccess = false;
      let rpcError: any = null;

      // Try the newer function signature first (with accuracy and address)
      try {
        const { data, error } = await supabase.rpc('save_student_location', {
          p_latitude: latitude,
          p_longitude: longitude,
          p_shared_with_guardians: true,
          p_accuracy: accuracy,
          p_address: undefined
        });

        if (error) {
          throw error;
        }

        // Check if function returns TABLE format
        if (data && Array.isArray(data) && data.length > 0) {
          const result = data[0];
          if (result.success === false) {
            throw new Error(result.message || 'RPC returned failure');
          }
          console.log('[StudentDashboard] RPC executada com sucesso (TABLE format):', result.message);
        } else {
          console.log('[StudentDashboard] RPC executada com sucesso (UUID format)');
        }

        rpcSuccess = true;
      } catch (error: any) {
        console.warn('[StudentDashboard] Primeira tentativa RPC falhou:', error);
        rpcError = error;

        // Try the simpler function signature (without accuracy and address)
        try {
          const { error: error2 } = await supabase.rpc('save_student_location', {
            p_latitude: latitude,
            p_longitude: longitude,
            p_shared_with_guardians: true
          });

          if (error2) {
            throw error2;
          }

          console.log('[StudentDashboard] RPC executada com sucesso (assinatura simples)');
          rpcSuccess = true;
        } catch (error2: any) {
          console.warn('[StudentDashboard] Segunda tentativa RPC falhou:', error2);
          rpcError = error2;
        }
      }

      // If both RPC attempts failed, use direct table insertion
      if (!rpcSuccess) {
        console.error('[StudentDashboard] Todas as tentativas RPC falharam, usando inserção direta:', rpcError);

        const { error: insertError } = await supabase
          .from('locations')
          .insert([{
            user_id: userInfo.id,
            latitude,
            longitude,
            shared_with_guardians: true,
            accuracy,
            source: 'gps',
            timestamp: new Date().toISOString()
          }]);

        if (insertError) {
          console.error('[StudentDashboard] Erro no fallback de inserção direta:', insertError);
          return false;
        }

        console.log('[StudentDashboard] Localização salva via inserção direta');
      }
      
      // Reload locations after saving e atualizar o mapa imediatamente
      await loadStudentLocations();

      console.log('✅ Localização salva com sucesso no banco de dados');
      return true;
    } catch (saveError) {
      console.error('[StudentDashboard] Erro ao salvar localização:', saveError);
      return false;
    }
  }, [userInfo.id, loadStudentLocations]);

  const startWatchPosition = useCallback(
    (currentAccuracy: number | undefined) => {
      console.log(
        `[StudentDashboard] 🔄 Precisão >${ACCURACY_GOAL}m, iniciando watchPosition para refinar`
      );
      let bestAccuracy = currentAccuracy ?? Number.MAX_SAFE_INTEGER;
      const watchId = navigator.geolocation.watchPosition(
        async (pos: GeolocationPosition) => {
          console.log(
            `[StudentDashboard] ▶️ watchPosition atualização: ${pos.coords.accuracy} m`
          );
          if (pos.coords.accuracy < bestAccuracy) {
            bestAccuracy = pos.coords.accuracy;
            await saveLocationToDatabase(
              pos.coords.latitude,
              pos.coords.longitude,
              pos.coords.accuracy
            );
            if (bestAccuracy <= ACCURACY_GOAL) {
              navigator.geolocation.clearWatch(watchId);
            }
          }
        },
        (err: GeolocationPositionError) => {
          console.error('[StudentDashboard] watchPosition erro:', err);
          navigator.geolocation.clearWatch(watchId);
        },
        { enableHighAccuracy: true }
      );
      setTimeout(() => navigator.geolocation.clearWatch(watchId), 45000);
    },
    [saveLocationToDatabase]
  );

  const resolveLocationViaServer = useCallback(async (
    lat?: number,
    lon?: number
  ): Promise<PositionData | null> => {
    // Try multiple endpoints with robust fallback
    const endpoints = [
      'http://localhost:4001/api/resolve-location', // Express server
      '/api/resolve-location' // Vite proxy fallback
    ];

    console.log(`[StudentDashboard] 🎯 resolveLocationViaServer called with lat=${lat}, lon=${lon}`);
    apiCache.logCacheState('before resolveLocationViaServer');

    // Try each endpoint until one works
    for (let i = 0; i < endpoints.length; i++) {
      const endpoint = endpoints[i];

      try {
        // Check if API is available before making the call
        const { shouldCall, reason } = apiCache.shouldAttemptCall(endpoint);
        if (!shouldCall) {
          console.log(`[StudentDashboard] 🚫 Skipping ${endpoint}: ${reason}`);
          continue; // Try next endpoint
        }

        console.log(`[StudentDashboard] ✅ Trying ${endpoint}: ${reason}`);

        // Add timeout to the fetch request to prevent long hanging requests
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout per endpoint

        const res = await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body:
            lat !== undefined && lon !== undefined
              ? JSON.stringify({ latitude: lat, longitude: lon })
              : undefined,
          signal: controller.signal
        }).finally(() => clearTimeout(timeoutId));

        // Handle 404 errors gracefully (API server not running)
        if (res.status === 404) {
          apiCache.markApiFailure(endpoint, 404);
          console.warn(`[StudentDashboard] ${endpoint} not available (404)`);
          continue; // Try next endpoint
        }

        const type = res.headers.get('content-type') || '';
        if (!res.ok || !type.includes('application/json')) {
          apiCache.markApiFailure(endpoint, res.status);
          console.warn(`[StudentDashboard] ${endpoint} returned invalid response (${res.status})`);
          continue; // Try next endpoint
        }

        const data = await res.json();
        if (typeof data.latitude === 'number' && typeof data.longitude === 'number') {
          apiCache.markApiSuccess(endpoint);
          console.log(`[StudentDashboard] ✅ Success with ${endpoint}`);
          return {
            latitude: data.latitude,
            longitude: data.longitude,
            accuracy: data.accuracy ?? undefined,
            source: data.source ?? 'ipinfo',
          };
        }

        apiCache.markApiFailure(endpoint);
        console.warn(`[StudentDashboard] ${endpoint} returned invalid data`);
        continue; // Try next endpoint

      } catch (err) {
        // Handle specific error types
        if (err instanceof DOMException && err.name === 'AbortError') {
          console.warn(`[StudentDashboard] ${endpoint} request timed out`);
          apiCache.markApiFailure(endpoint, 408);
        } else if (err instanceof TypeError && err.message.includes('Failed to fetch')) {
          console.warn(`[StudentDashboard] Connection refused to ${endpoint}`);
          apiCache.markApiFailure(endpoint, 0);
        } else {
          console.warn(`[StudentDashboard] ${endpoint} failed:`, err);
          apiCache.markApiFailure(endpoint);
        }
        continue; // Try next endpoint
      }
    }

    // All endpoints failed, use IP fallback
    console.warn('[StudentDashboard] All API endpoints failed, trying IP fallback');
    try {
      const ip = await fetchIpLocation();
      return { latitude: ip.lat, longitude: ip.lon, accuracy: undefined, source: 'ipinfo' };
    } catch (ipErr) {
      console.error('[StudentDashboard] IP fallback failed:', ipErr);

      // Last resort fallback - return provided coordinates or default
      if (lat !== undefined && lon !== undefined) {
        return { latitude: lat, longitude: lon, accuracy: 1000, source: 'last-resort' };
      }
      return { latitude: -23.5489, longitude: -46.6388, accuracy: 5000, source: 'default' };
    }
  }, []);

  // Obter a posição atual com fallback AWS imediato em desktop/laptop
  const getCurrentPositionAccurate = useCallback(async (): Promise<PositionData | null> => {
    const isMobileDevice = isMobile;
    console.log('[StudentDashboard] 🎯 Obtendo localização de ALTA PRECISÃO');
    try {
      // Desktop/laptop: tentar GPS uma vez, se precisão ruim (>200m) ou erro, acionar fallback
      if (!isMobileDevice) {
        try {
          const position = await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 0
            });
          });
          const { latitude, longitude, accuracy } = position.coords;
          if (accuracy && accuracy <= 200) {
            return { latitude, longitude, accuracy, source: 'gps' };
          }
          const fallback = await resolveLocationViaServer(latitude, longitude);
          if (fallback) return fallback;
          throw new Error('Não foi possível obter localização via IP');
        } catch (gpsError) {
          // Falha total no GPS, tentar backend puro
          const fallback = await resolveLocationViaServer();
          if (fallback) return fallback;
          throw new Error('Não foi possível obter localização nem via GPS nem IP');
        }
      }
      // Mobile: manter tentativas múltiplas e watchPosition
      let bestPosition: GeolocationPosition | null = null;
      let attempts = 0;
      const maxAttempts = 3;
      while (attempts < maxAttempts && (!bestPosition || bestPosition.coords.accuracy > ACCURACY_GOAL)) {
        attempts++;
        try {
          const position = await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 30000,
              maximumAge: 0
            });
          });
          if (!bestPosition || position.coords.accuracy < bestPosition.coords.accuracy) {
            bestPosition = position;
          }
          if (position.coords.accuracy <= 10) {
            break;
          }
        } catch (attemptError) {
          if (attempts === maxAttempts && !bestPosition) {
            throw attemptError;
          }
        }
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
      if (!bestPosition) {
        throw new Error(t('studentDashboard.locationAttemptsError'));
      }
      return {
        latitude: bestPosition.coords.latitude,
        longitude: bestPosition.coords.longitude,
        accuracy: bestPosition.coords.accuracy,
        source: 'gps'
      };
    } catch (error) {
      console.error('[StudentDashboard] ❌ Erro ao obter posição:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível obter sua localização',
        variant: 'destructive',
      });
      return null;
    }
  }, [isMobile, toast, resolveLocationViaServer, t]);

  // Get current location for LocationActions component com FEEDBACK DETALHADO
  const getCurrentLocation = useCallback(async () => {
    if (!userInfo.id) {
      toast({
        title: t('common.error'),
        description: t('studentDashboard.userIdNotFound'),
        variant: 'destructive'
      });
      console.error('[StudentDashboard] Erro: Usuário não encontrado');
      return;
    }

    setIsGettingLocation(true);
    let position: PositionData | null = null;
    let error: unknown = null;
    try {
      position = await getCurrentPositionAccurate();
      if (!position) {
        throw new Error(t('studentDashboard.noCurrentLocation'));
      }
      const { latitude, longitude, accuracy, source: _source } = position;
      // Salvar no banco de dados com precisão
      const savedToDb = await saveLocationToDatabase(latitude, longitude, accuracy);
      if (savedToDb) {
        // Feedback diferenciado baseado na precisão
        let precisionMessage = t('studentDashboard.locationRecorded');
        let precisionVariant: "default" | "destructive" = "default";
        if (accuracy && accuracy <= 10) {
          precisionMessage += ` com EXCELENTE precisão (±${Math.round(accuracy)}m)`;
        } else if (accuracy && accuracy <= 50) {
          precisionMessage += ` com BOA precisão (±${Math.round(accuracy)}m)`;
        } else if (accuracy && accuracy <= 100) {
          precisionMessage += ` com precisão MODERADA (±${Math.round(accuracy)}m)`;
        } else if (accuracy) {
          precisionMessage += ` com baixa precisão (±${Math.round(accuracy)}m)`;
          precisionVariant = "destructive";
        }
        toast({
          title: t('studentDashboard.locationUpdated'),
          description: precisionMessage,
          variant: precisionVariant,
        });
        if (!accuracy || accuracy > ACCURACY_GOAL) {
          startWatchPosition(accuracy);
        }
      }
    } catch (err) {
      error = err;
      console.error('[StudentDashboard] Erro ao obter localização:', err);
      toast({
        title: t('studentDashboard.locationErrorTitle'),
        description: err instanceof Error ? err.message : t('studentDashboard.unknownLocationError'),
        variant: 'destructive',
      });
    } finally {
      setIsGettingLocation(false);
      // Log apenas no console
      console.log('[StudentDashboard] Resultado da operação de localização:', {
        success: !!position,
        position,
        error: error instanceof Error ? error.message : error
      });
    }
  }, [userInfo.id, getCurrentPositionAccurate, saveLocationToDatabase, startWatchPosition, toast, t]);

  // Load student locations on mount - APENAS UMA VEZ
  useEffect(() => {
    console.log('[StudentDashboard] useEffect para carregar localizações');
    loadStudentLocations();
  }, [userInfo.id, loadStudentLocations]); 

  // Obtain current location once when dashboard loads
  useEffect(() => {
    if (userInfo.id) {
      console.log('[StudentDashboard] Obtendo localização inicial do usuário');
      getCurrentLocation();
    }
  }, [userInfo.id, getCurrentLocation]);

  // Lock atômico para impedir múltiplos envios concorrentes
  let shareAllLock = false;

  // Mover executeShareAll acima de handleShareAll para evitar uso antes da declaração
  const executeShareAll = useCallback(async () => {
    console.log('[StudentDashboard] Executando compartilhamento para todos os responsáveis');
    
    if (guardians.length === 0) {
      toast({
        title: t('studentDashboard.noGuardiansFound'),
        description: t('studentDashboard.noGuardiansToShare'),
        variant: 'destructive',
      });
      return;
    }

    setIsSendingAll(true);
    setShowProgress(true);
    setProgressData({
      total: guardians.length,
      success: 0,
      error: 0,
      complete: false
    });

    try {
      // Obter a posição atual
      const position = await getCurrentPositionAccurate();
      
      if (!position) {
        throw new Error(t('studentDashboard.noCurrentLocation'));
      }
      
      const { latitude, longitude, accuracy } = position;
      console.log('[StudentDashboard] 🎯 Localização obtida para compartilhamento:', { latitude, longitude, accuracy: accuracy ? `${accuracy}m` : 'desconhecida' });
      
      // Salvar a localização no banco de dados com precisão
      let savedToDb = false;
      try {
        savedToDb = await saveLocationToDatabase(latitude, longitude, accuracy);
      } catch (err: unknown) {
        // Se erro for de duplicidade, mostrar toast específico
        const errorMessage = err instanceof Error ? err.message : String(err);
        if (errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('já existe')) {
          toast({
            title: t('studentDashboard.duplicateLocationTitle', 'Localização já enviada'),
            description: t('studentDashboard.duplicateLocationDesc', 'Você já enviou esta localização recentemente. Aguarde antes de tentar novamente.'),
            variant: 'destructive',
          });
          setIsSendingAll(false);
          setShowProgress(false);
          return;
        }
        throw err;
      }
      if (!savedToDb) {
        // Se não salvou e não foi duplicidade, mostrar erro genérico
        toast({
          title: t('studentDashboard.locationErrorTitle'),
          description: t('studentDashboard.checkPermissions'),
          variant: 'destructive',
        });
        setIsSendingAll(false);
        setShowProgress(false);
        return;
      }
      
      let successCount = 0;
      let failCount = 0;
      
      console.log('[StudentDashboard] Enviando para responsáveis:', guardians.map(g => g.email));
      
      // Send to each guardian with progress tracking
      for (const guardian of guardians) {
        try {
          console.log(`[StudentDashboard] Enviando para: ${guardian.email}`);
          const result = await apiService.shareLocation(
            guardian.email,
            latitude,
            longitude,
            userInfo.fullName
          );
          
          if (result.success) {
            successCount++;
            setProgressData(prev => ({ ...prev, success: successCount }));
            console.log(`[StudentDashboard] ✅ Sucesso para: ${guardian.email}`);
          } else {
            throw new Error(result.message || 'Falha ao compartilhar');
          }
        } catch (err) {
          failCount++;
          setProgressData(prev => ({ ...prev, error: failCount }));
          console.error(`[StudentDashboard] ❌ Falha para ${guardian.email}:`, err);
        }
      }
      
      // Mark progress as complete
      setProgressData(prev => ({ ...prev, complete: true }));
      
      // Hide progress after delay
      setTimeout(() => {
        setShowProgress(false);
      }, 3000);
      
      // Resumo final do compartilhamento
      const successMessage = successCount > 0
        ? t('studentDashboard.shareSuccess', { count: successCount }) +
          (failCount > 0 ? t('studentDashboard.failSuffix', { fails: failCount }) : '')
        : t('studentDashboard.shareFail');
      
      console.log('[StudentDashboard] Resultado final:', { successCount, failCount, message: successMessage });
      
      if (isMobile) {
        // Em mobile usamos o alerta modal para o resultado final
        setAlertSuccess(successCount > 0);
        setAlertMessage(successMessage);
        setAlertDetails(successCount > 0
          ? t('studentDashboard.sharedByEmail')
          : t('studentDashboard.checkPermissions'));
        setAlertOpen(true);
      } else {
        // Em desktop mantemos o toast
        toast({
          title: t('studentDashboard.shareComplete'),
          description: successMessage,
          variant: successCount > 0 ? 'default' : 'destructive',
        });
      }
      
      // Status visual dos botões será atualizado pelos componentes individuais
      
    } catch (error: unknown) {
      console.error('[StudentDashboard] Erro ao compartilhar localização:', error);
      
      setProgressData(prev => ({ ...prev, complete: true }));
      setTimeout(() => setShowProgress(false), 3000);
      
      // Feedback de erro geral
      if (isMobile) {
        // Em mobile usamos o alerta modal para o erro
        setAlertSuccess(false);
        setAlertMessage(t('studentDashboard.locationErrorTitle'));
        setAlertDetails(t('studentDashboard.checkPermissions'));
        setAlertOpen(true);
      } else {
        // Em desktop mantemos o toast
        toast({
          title: t('studentDashboard.locationErrorTitle'),
          description: t('studentDashboard.checkPermissions'),
          variant: 'destructive',
        });
      }
    } finally {
      setIsSendingAll(false);
    }
  }, [guardians, getCurrentPositionAccurate, saveLocationToDatabase, userInfo.fullName, isMobile, toast, t]);

  // Handle share location to all guardians - CORRIGIDO PARA FUNCIONAR AUTOMATICAMENTE
  const handleShareAll = useCallback(async () => {
    if (shareAllLock) {
      console.warn('[StudentDashboard] Envio já em andamento (lock atômico)');
      return;
    }
    shareAllLock = true;
    try {
      console.log('[StudentDashboard] 🚀 BOTÃO COMPARTILHAR CLICADO!');
      console.log('[StudentDashboard] Responsáveis cadastrados:', guardians.length);
      
      // Se há responsáveis cadastrados, enviar automaticamente
      if (guardians.length > 0) {
        console.log('[StudentDashboard] Enviando automaticamente para responsáveis cadastrados');
        await executeShareAll();
        return;
      }
      
      // Se não há responsáveis, mostrar mensagem
      toast({
        title: t('studentDashboard.noGuardians'),
        description: t('studentDashboard.addGuardiansWarning'),
        variant: 'destructive',
      });
    } finally {
      shareAllLock = false;
    }
  }, [guardians.length, toast, t, executeShareAll]); 


  // Redirect to login if not authenticated
  useEffect(() => {
    if (!userInfo.id) {
      navigate('/login');
    }
  }, [userInfo.id, navigate]);




  return (
    <div className={`min-h-screen flex flex-col student-dashboard-container ${isMobile ? 'student-dashboard-mobile ios-safe-area' : ''}`}>
      {/* Header fixo no topo */}
      <div className="flex-shrink-0 px-2 py-2 md:px-8 md:py-4">
        <StudentDashboardHeader userFullName={userInfo.fullName} className="w-full" />
      </div>

      {/* Conteúdo principal com layout otimizado para mobile */}
      <div className="flex-1 flex flex-col lg:flex-row gap-2 md:gap-4 px-1 md:px-4 pb-2 md:pb-4">
        {/* Seção de informações - compacta em mobile */}
        <div className={`flex flex-col sm:flex-row lg:flex-col gap-2 md:gap-4 lg:w-80 lg:flex-shrink-0 ${isMobile ? 'student-info-mobile' : ''}`}>
          <div className="flex-1 lg:flex-none">
            <div className="p-2 md:p-6 rounded-xl md:rounded-2xl bg-transparent">
              <StudentInfoSection
                userFullName={userInfo.fullName}
                email={userInfo.email ?? null}
                phone={userInfo.phone}
                onGetLocation={getCurrentLocation}
                isGettingLocation={isGettingLocation}
                onShareAll={handleShareAll}
                guardianCount={guardians.length}
                isSendingAll={isSendingAll}
                sharingStatus={actionsStatus}
                hasLocations={studentLocations.length > 0}
                className="h-full bg-transparent"
              />
            </div>
          </div>
          <div className="flex-1 lg:flex-none">
            <div className="p-2 md:p-6 rounded-xl md:rounded-2xl bg-transparent">
              <LocationActions
                onGetLocation={getCurrentLocation}
                isGettingLocation={isGettingLocation}
                onShareAll={handleShareAll}
                guardianCount={guardians.length}
                isSendingAll={isSendingAll}
                sharingStatus={actionsStatus}
                hasLocations={studentLocations.length > 0}
                className="h-full [&_.flex-col]:!flex-col bg-transparent"
              />
            </div>
          </div>
        </div>

        {/* Seção do mapa - ocupa máximo espaço disponível com otimizações iOS */}
        <div className={`flex-1 min-h-0 ${isMobile ? 'map-container-ios' : ''}`}>
          <StudentLocationSection
            userId={userInfo.id}
            userName={userInfo.fullName}
            userEmail={userInfo.email || ''}
            locations={studentLocations}
            loading={isLoadingLocations}
            error={locationError}
            className={`h-full ${isMobile ? 'ios-tabs-container' : ''}`}
          />
        </div>
      </div>

      {/* Componentes de overlay */}
      <LocationSharingProgress
        isVisible={showProgress}
        totalGuardians={progressData.total}
        successCount={progressData.success}
        errorCount={progressData.error}
        isComplete={progressData.complete}
      />
      <SharedLocationAlert
        isOpen={alertOpen}
        onClose={() => setAlertOpen(false)}
        success={alertSuccess}
        message={alertMessage}
        details={alertDetails}
      />
    </div>
  );
};

export default StudentDashboard;