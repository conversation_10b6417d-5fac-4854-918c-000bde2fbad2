import { setOfflineData, getOfflineData, removeOfflineData, clearOfflineStore } from '../offline-storage';

jest.mock('../indexed-db-manager', () => {
  const store: Record<string, Record<string, any>> = {};
  return {
    offlineDB: {
      set: jest.fn(async (storeName: string, key: string, data: any) => {
        if (!store[storeName]) store[storeName] = {};
        store[storeName][key] = data;
      }),
      get: jest.fn(async (storeName: string, key: string) => {
        return store[storeName]?.[key] ?? null;
      }),
      delete: jest.fn(async (storeName: string, key: string) => {
        if (store[storeName]) delete store[storeName][key];
      }),
      clear: jest.fn(async (storeName: string) => {
        store[storeName] = {};
      }),
      cleanExpired: jest.fn(async () => 0),
    }
  };
});

describe('offline-storage', () => {
  const STORE = 'test';

  beforeEach(async () => {
    await clearOfflineStore(STORE);
  });

  test('set and get data', async () => {
    await setOfflineData(STORE, 'a', { ok: true });
    const value = await getOfflineData(STORE, 'a');
    expect(value).toEqual({ ok: true });
  });

  test('remove data', async () => {
    await setOfflineData(STORE, 'b', 123);
    await removeOfflineData(STORE, 'b');
    const value = await getOfflineData(STORE, 'b');
    expect(value).toBeNull();
  });
});
