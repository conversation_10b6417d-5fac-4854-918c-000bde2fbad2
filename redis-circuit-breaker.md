# 🛡️ Redis Circuit Breaker & Monitoramento

## Visão Geral

Este documento descreve a implementação do padrão Circuit Breaker para o Redis no projeto Locate-Family-Connect, conforme o Anti-Break Protocol. O sistema foi projetado para garantir alta resiliência, performance e estabilidade mesmo em cenários de falha do Redis.

## 🧩 Componentes Principais

### 1. Circuit Breaker
Localizado em `src/lib/utils/circuit-breaker.ts`, implementa:
- Estados: `CLOSED` (normal), `OPEN` (bloqueando operações), `HALF-OPEN` (testando recuperação)
- Métricas de falhas e sucesso
- Timeout de recuperação automática
- Failover transparente

### 2. Integração com Redis Service
Em `src/lib/services/redis/index.ts`, o Circuit Breaker protege:
- `getCachedData`: Fallback para consulta direta ao banco
- `cacheData`: Operação silenciosa em caso de falha
- `invalidateCache`: Proteção contra cascata de falhas
- `publishNotification`: Garantia de entrega

### 3. Sistema de Monitoramento
Em `src/lib/monitoring/health-check.ts`:
- Verificação de saúde do Redis
- Verificação de conexão com Supabase
- Consolidação de saúde do sistema
- Métricas do circuit breaker

### 4. Scripts de Diagnóstico
- `scripts/test-circuit-breaker.mjs`: Teste do padrão circuit breaker
- `scripts/redis-health-check.mjs`: Diagnóstico completo do Redis

## 📊 Métricas e Monitoramento

### Métricas Coletadas
- **Circuit Breaker**: 
  - Estado atual (CLOSED/OPEN/HALF-OPEN)
  - Contagem de falhas consecutivas
  - Tempo desde última falha/recuperação
  - Tentativas de recuperação

- **Redis Performance**:
  - Latência de operações (GET/SET/PUB-SUB)
  - Taxa de acertos do cache (hit rate)
  - Uso de memória
  - Clientes conectados

### Alertas Configurados
- Circuit breaker aberto por mais de 5 minutos
- Taxa de acertos de cache abaixo de 50%
- Latência média acima de 100ms

## 🚨 Estratégias de Fallback

### Quando Redis Indisponível:
1. **Dados de Localização**: Consulta direta ao banco Supabase
2. **Notificações**: Fila em memória com retry automático
3. **Cache**: Desabilitação temporária com log de aviso

## 🧪 Teste e Validação

### Script de Teste do Circuit Breaker
```bash
# Teste completo do circuit breaker
node scripts/test-circuit-breaker.mjs

# Verificação detalhada com métricas
node scripts/redis-health-check.mjs --verbose

# Relatório de saúde
node scripts/redis-health-check.mjs --report

# Monitoramento contínuo
node scripts/redis-health-check.mjs --monitor
```

### Cenários de Teste
1. **Operação Normal**: Verifica funcionalidade básica
2. **Falhas Sequenciais**: Verifica transição para estado OPEN
3. **Circuit Aberto**: Verifica rejeição de operações
4. **Recuperação**: Verifica transição para HALF-OPEN e CLOSED
5. **Health Check**: Verifica relatório de saúde

## 🔄 Integração com Anti-Break Protocol

### Verificações Críticas
```bash
# Verificar saúde do Redis
npm run health-check -- --component=redis

# Testar resiliência do circuit breaker
npm run critical-test -- --test=redis-resilience

# Verificar performance antes de deploy
npm run performance-baseline -- --component=redis
```

### Rollback Plan
Em caso de problemas após deploy:

1. Reverter código do circuit breaker:
```bash
git checkout [commit_anterior] -- src/lib/utils/circuit-breaker.ts src/lib/services/redis/index.ts
```

2. Reiniciar conexão Redis:
```bash
node scripts/redis-health-check.mjs --fix
```

3. Verificar recuperação:
```bash
npm run critical-test -- --component=redis
```

## 🛠️ Diagnóstico e Solução de Problemas

### Problemas Comuns e Soluções

1. **Circuit Breaker Sempre Aberto**
   - Verifique se o Redis está acessível: `redis-cli ping`
   - Verifique configurações de conexão no `.env`
   - Resetar manualmente: `node scripts/redis-health-check.mjs --fix`

2. **Alta Latência em Operações**
   - Verifique carga do servidor: `redis-cli info | grep used_memory`
   - Considere ajustar `maxmemory` no Redis
   - Verifique rede entre aplicação e Redis

3. **Falhas em Notificações**
   - Verifique canais subscritos: `redis-cli pubsub channels`
   - Teste publicação manual: `redis-cli publish canal mensagem`
   - Verifique logs de erro no cliente Redis

## 📝 Referências

- [Circuit Breaker Pattern - Martin Fowler](https://martinfowler.com/bliki/CircuitBreaker.html)
- [Redis Oficial - Documentação](https://redis.io/documentation)
- [Redis Pub/Sub - Guia](https://redis.io/topics/pubsub)
- [Padrões de Resiliência em Sistemas Distribuídos](https://docs.microsoft.com/en-us/azure/architecture/patterns/circuit-breaker)
