-- ==========================================
-- ANÁLISE DE TABELAS - VERSÃO CORRIGIDA
-- Data: 24/06/2025
-- Objetivo: Identificar tabelas e estrutura do banco (funciona mesmo sem tabelas)
-- ==========================================

-- 1. VERIFICAR SE HÁ TABELAS NO SCHEMA PUBLIC
SELECT 
    'VERIFICACAO_TABELAS' as info,
    COUNT(*) as total_tabelas,
    CASE 
        WHEN COUNT(*) = 0 THEN 'NENHUMA_TABELA_ENCONTRADA'
        ELSE 'TABELAS_EXISTEM'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE';

-- 2. LIS<PERSON>R TODAS AS TABELAS DO SCHEMA PUBLIC (se existirem)
SELECT 
    'LISTA_TABELAS' as tipo,
    table_name,
    table_type,
    'public' as schema_name
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- 3. VERIFICAR TODOS OS SCHEMAS DISPONÍVEIS
SELECT 
    'SCHEMAS_DISPONIVEIS' as info,
    schema_name,
    CASE 
        WHEN schema_name = 'public' THEN 'SCHEMA_PRINCIPAL'
        WHEN schema_name = 'auth' THEN 'SCHEMA_AUTENTICACAO'
        WHEN schema_name LIKE 'pg_%' THEN 'SCHEMA_SISTEMA'
        ELSE 'SCHEMA_OUTRO'
    END as tipo_schema
FROM information_schema.schemata
ORDER BY schema_name;

-- 4. VERIFICAR TABELAS EM TODOS OS SCHEMAS (não só public)
SELECT 
    'TABELAS_TODOS_SCHEMAS' as info,
    table_schema,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema NOT LIKE 'pg_%' 
  AND table_schema != 'information_schema'
ORDER BY table_schema, table_name;

-- 5. VERIFICAR SE TABELAS CRÍTICAS EXISTEM
SELECT 
    'VERIFICACAO_TABELAS_CRITICAS' as info,
    'profiles' as tabela_esperada,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = 'profiles'
        ) THEN 'EXISTE'
        ELSE 'NAO_EXISTE'
    END as status;

SELECT 
    'VERIFICACAO_TABELAS_CRITICAS' as info,
    'guardians' as tabela_esperada,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = 'guardians'
        ) THEN 'EXISTE'
        ELSE 'NAO_EXISTE'
    END as status;

SELECT 
    'VERIFICACAO_TABELAS_CRITICAS' as info,
    'account_deletion_requests' as tabela_esperada,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = 'account_deletion_requests'
        ) THEN 'EXISTE'
        ELSE 'NAO_EXISTE'
    END as status;

-- 6. VERIFICAR FUNÇÕES RPC DISPONÍVEIS
SELECT 
    'FUNCOES_RPC' as tipo,
    routine_schema,
    routine_name as nome_funcao,
    routine_type as tipo_funcao
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_type = 'FUNCTION'
ORDER BY routine_name;

-- 7. VERIFICAR USUÁRIOS AUTH (se tabela auth.users existir)
SELECT 
    'USUARIOS_AUTH' as info,
    COUNT(*) as total_usuarios
FROM information_schema.tables 
WHERE table_schema = 'auth' 
  AND table_name = 'users';

-- 8. VERIFICAR SE O BANCO ESTÁ COMPLETAMENTE VAZIO
SELECT 
    'DIAGNOSTICO_BANCO' as tipo,
    CASE 
        WHEN (
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
              AND table_type = 'BASE TABLE'
        ) = 0 THEN 'BANCO_VAZIO_PUBLIC_SCHEMA'
        ELSE 'BANCO_TEM_TABELAS'
    END as status_public,
    CASE 
        WHEN (
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema NOT LIKE 'pg_%' 
              AND table_schema != 'information_schema'
        ) = 0 THEN 'BANCO_COMPLETAMENTE_VAZIO'
        ELSE 'BANCO_TEM_DADOS'
    END as status_geral;

-- 9. CONTAR REGISTROS SE TABELAS EXISTIREM
DO $$
DECLARE
    tabela_existe BOOLEAN;
BEGIN
    -- Verificar se pg_stat_user_tables tem dados
    SELECT EXISTS (
        SELECT 1 FROM pg_stat_user_tables 
        WHERE schemaname = 'public'
        LIMIT 1
    ) INTO tabela_existe;
    
    IF tabela_existe THEN
        RAISE NOTICE 'ENCONTRADAS TABELAS COM ESTATÍSTICAS - executando contagem';
        
        -- Se chegou aqui, podemos executar a query original
        PERFORM * FROM (
            SELECT 
                'CONTAGEM_REGISTROS' as tipo,
                schemaname,
                tablename,
                n_tup_ins as total_inserts,
                n_tup_upd as total_updates,
                n_tup_del as total_deletes,
                n_live_tup as registros_atuais,
                n_dead_tup as registros_mortos
            FROM pg_stat_user_tables 
            WHERE schemaname = 'public'
            ORDER BY n_live_tup DESC
        ) AS contagem;
        
    ELSE
        RAISE NOTICE 'NENHUMA TABELA ENCONTRADA NO SCHEMA PUBLIC';
    END IF;
END $$;

-- 10. DIAGNÓSTICO FINAL
SELECT 
    'DIAGNOSTICO_FINAL' as resultado,
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public') = 0 
        THEN 'PROBLEMA: Schema public está vazio - migrações não foram aplicadas'
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'profiles')
        THEN 'PROBLEMA: Tabela profiles não existe - sistema não foi inicializado'
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'guardians')
        THEN 'PROBLEMA: Tabela guardians não existe - funcionalidades de responsável não funcionarão'
        ELSE 'OK: Tabelas principais existem'
    END as diagnostico;

-- 11. RECOMENDAÇÕES
SELECT 
    'RECOMENDACOES' as tipo,
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public') = 0 
        THEN 'ACAO_NECESSARIA: Executar migrações básicas do sistema para criar tabelas fundamentais'
        ELSE 'ACAO_POSSIVEL: Banco tem estrutura básica, pode prosseguir com migração de account_deletion_requests'
    END as acao_recomendada; 