
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, RotateCcw, User, MapPin } from 'lucide-react';
import StudentMapSection from '@/components/student/StudentMapSection';
import LocationHistoryList from '@/components/student/LocationHistoryList';
import { LocationData } from '@/types/database';
import { Student } from '@/types/auth';
import { useTranslation } from 'react-i18next';

interface StudentMapTabsProps {
  selectedStudent: Student | null;
  locations: LocationData[];
  isLoadingLocations: boolean;
  locationError: string | null;
  selectedLocation: LocationData | null;
  activeTab: string;
  userName?: string;
  onTabChange: (value: string) => void;
  onLocationSelect: (location: LocationData) => void;
  onClearSelection: () => void;
}

const LoadingSkeleton = () => (
  <div className="space-y-4">
    <div className="flex items-center gap-3 p-4 border rounded-lg">
      <Skeleton className="w-12 h-12 rounded-full" />
      <div className="flex-1">
        <Skeleton className="h-4 w-32 mb-2" />
        <Skeleton className="h-3 w-24" />
      </div>
    </div>
    <div className="h-[600px] bg-muted rounded-lg flex items-center justify-center">
      <div className="text-center">
        <MapPin className="h-8 w-8 mx-auto mb-2 text-muted-foreground animate-pulse" />
        <p className="text-sm text-muted-foreground">Carregando mapa...</p>
      </div>
    </div>
  </div>
);

export const StudentMapTabs: React.FC<StudentMapTabsProps> = ({
  selectedStudent,
  locations,
  isLoadingLocations,
  locationError,
  selectedLocation,
  activeTab,
  userName,
  onTabChange,
  onLocationSelect,
  onClearSelection
}) => {
  const { t } = useTranslation();
  return (
    <Card className="lg:col-span-2">
      <CardContent>
        <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
          <TabsList className="w-full">
            <TabsTrigger value="map" className="flex-1">
              {t('dashboard.tabs.map', 'Map')}
              {selectedLocation && (
                <span className="ml-2 text-xs bg-blue-500 text-white px-2 py-1 rounded">
                  Selecionada
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="history" className="flex-1">
              {t('dashboard.tabs.history', 'History')}
              {locations.length > 0 && (
                <span className="ml-2 text-xs bg-green-500 text-white px-2 py-1 rounded">
                  {locations.length}
                </span>
              )}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="map">
            {!selectedStudent ? (
              <Card className="mb-4 border-amber-200 bg-amber-50">
                <CardContent className="p-4 flex items-center gap-3">
                  <AlertCircle className="text-amber-500" />
                  <div>
                    <p className="font-medium">{t('dashboard.parent.selectStudentPrompt', 'Select a student')}</p>
                    <p className="text-sm text-muted-foreground">{t('dashboard.parent.selectStudentInstruction', 'Click on a student in the list to view their location.')}</p>
                  </div>
                </CardContent>
              </Card>
            ) : null}
            
            {selectedLocation && (
              <div className="mb-4 flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onClearSelection}
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  {t('dashboard.parent.viewAllLocations', 'View all locations')}
                </Button>
                <span className="text-sm text-muted-foreground">
                  {t('dashboard.parent.showing', 'Showing')}: {new Date(selectedLocation.timestamp).toLocaleString()}
                </span>
              </div>
            )}

            {/* Mostrar skeleton durante loading */}
            {selectedStudent && isLoadingLocations ? (
              <LoadingSkeleton />
            ) : (
              <StudentMapSection
                title={t('dashboard.parent.studentLocationTitle', { name: selectedStudent?.name || t('common.student','Student'), defaultValue: `Location of ${selectedStudent?.name || 'Student'}` })}
                selectedUserId={selectedStudent?.id}
                showControls={true}
                locations={locations}
                userType="parent"
                studentDetails={selectedStudent ? {
                  name: selectedStudent.name,
                  email: selectedStudent.email
                } : null}
                loading={isLoadingLocations}
                error={locationError}
                senderName="Responsável"
                selectedLocation={selectedLocation}
                noDataContent={selectedStudent ? null : (
                  <div className="p-4 text-center">
                    <User className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="font-medium text-lg">{t('dashboard.parent.selectStudentPromptDetailed', 'Select a student to see their location')}</p>
                  </div>
                )}
              />
            )}
          </TabsContent>

          <TabsContent value="history">
            <LocationHistoryList
              locationData={locations}
              loading={isLoadingLocations}
              error={locationError}
              userType="parent"
              studentDetails={selectedStudent ? {
                id: selectedStudent.id,
                name: selectedStudent.name,
                email: selectedStudent.email
              } : null}
              senderName={userName}
              onLocationSelect={onLocationSelect}
              selectedLocationId={selectedLocation?.id}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
