
#!/bin/bash

# 🛡️ Health Check - Verificação de Saúde do Sistema
# Protocolo Anti-Quebra - Fase 1: Verificação Básica

echo "🔍 [HEALTH-CHECK] Iniciando verificação de saúde do sistema..."
echo "📅 Data: $(date)"
echo "🏠 Diretório: $(pwd)"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Contadores
CHECKS_PASSED=0
CHECKS_FAILED=0
WARNINGS=0

# Função para log
log_check() {
    local status=$1
    local message=$2
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $message"
        ((CHECKS_PASSED++))
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ FAIL${NC}: $message"
        ((CHECKS_FAILED++))
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  WARN${NC}: $message"
        ((WARNINGS++))
    fi
}

# 1. Verificar dependências críticas
echo ""
echo "📦 Verificando dependências..."

if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    log_check "PASS" "Node.js disponível: $NODE_VERSION"
else
    log_check "FAIL" "Node.js não encontrado"
fi

if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    log_check "PASS" "npm disponível: $NPM_VERSION"
else
    log_check "FAIL" "npm não encontrado"
fi

# 2. Verificar arquivos críticos
echo ""
echo "📄 Verificando arquivos críticos..."

CRITICAL_FILES=(
    "package.json"
    "src/main.tsx"
    "src/App.tsx"
    "src/contexts/UnifiedAuthContext.tsx"
    "src/lib/supabase.ts"
    "vite.config.ts"
    "index.html"
)

for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_check "PASS" "Arquivo crítico existe: $file"
    else
        log_check "FAIL" "Arquivo crítico ausente: $file"
    fi
done

# 3. Verificar dependências do projeto
echo ""
echo "🔗 Verificando dependências do projeto..."

if [ -f "package.json" ]; then
    if npm list --depth=0 &> /dev/null; then
        log_check "PASS" "Todas as dependências instaladas"
    else
        log_check "WARN" "Algumas dependências podem estar ausentes"
        echo "💡 Execute: npm install"
    fi
else
    log_check "FAIL" "package.json não encontrado"
fi

# 4. Verificar build
echo ""
echo "🏗️  Verificando build..."

if npm run build &> /dev/null; then
    log_check "PASS" "Build executado com sucesso"
    
    # Verificar se dist foi criado
    if [ -d "dist" ]; then
        log_check "PASS" "Diretório dist criado"
        
        # Verificar tamanho do bundle
        BUNDLE_SIZE=$(du -sh dist 2>/dev/null | cut -f1)
        if [ ! -z "$BUNDLE_SIZE" ]; then
            log_check "PASS" "Bundle gerado: $BUNDLE_SIZE"
        fi
    else
        log_check "WARN" "Diretório dist não encontrado"
    fi
else
    log_check "FAIL" "Build falhou - PROJETO QUEBRADO!"
    echo "💥 Build com erro - verifique os logs acima"
fi

# 5. Verificar linting
echo ""
echo "🧹 Verificando linting..."

if npm run lint &> /dev/null; then
    log_check "PASS" "Linting passou sem erros"
else
    log_check "WARN" "Linting encontrou problemas"
    echo "💡 Execute: npm run lint para detalhes"
fi

# 6. Verificar testes
echo ""
echo "🧪 Verificando testes..."

if [ -f "jest.config.cjs" ]; then
    if npm test -- --passWithNoTests &> /dev/null; then
        log_check "PASS" "Testes executados com sucesso"
    else
        log_check "WARN" "Alguns testes falharam"
        echo "💡 Execute: npm test para detalhes"
    fi
else
    log_check "WARN" "Configuração de testes não encontrada"
fi

# 7. Verificar servidor de desenvolvimento
echo ""
echo "🚀 Verificando servidor de desenvolvimento..."

# Verificar se já está rodando
if lsof -ti:8080 &> /dev/null; then
    log_check "PASS" "Servidor já está rodando na porta 8080"
elif lsof -ti:5173 &> /dev/null; then
    log_check "PASS" "Servidor já está rodando na porta 5173"
else
    log_check "WARN" "Servidor de desenvolvimento não está rodando"
    echo "💡 Execute: npm run dev"
fi

# 8. Verificar Git
echo ""
echo "📋 Verificando Git..."

if command -v git &> /dev/null; then
    if git status &> /dev/null; then
        log_check "PASS" "Repositório Git válido"
        
        # Verificar mudanças não commitadas
        if [ -z "$(git status --porcelain)" ]; then
            log_check "PASS" "Working directory limpo"
        else
            log_check "WARN" "Há mudanças não commitadas"
            echo "💡 Execute: git status para detalhes"
        fi
    else
        log_check "WARN" "Não é um repositório Git"
    fi
else
    log_check "WARN" "Git não instalado"
fi

# Resumo Final
echo ""
echo "📊 RESUMO DA VERIFICAÇÃO"
echo "========================"
echo -e "${GREEN}✅ Verificações passou: $CHECKS_PASSED${NC}"
echo -e "${YELLOW}⚠️  Avisos: $WARNINGS${NC}"
echo -e "${RED}❌ Verificações falharam: $CHECKS_FAILED${NC}"

# Determinar status final
if [ $CHECKS_FAILED -eq 0 ]; then
    if [ $WARNINGS -eq 0 ]; then
        echo -e "\n${GREEN}🎉 SISTEMA SAUDÁVEL - Tudo funcionando perfeitamente!${NC}"
        exit 0
    else
        echo -e "\n${YELLOW}⚠️  SISTEMA ESTÁVEL - Alguns avisos, mas funcional${NC}"
        exit 0
    fi
else
    echo -e "\n${RED}🚨 SISTEMA COM PROBLEMAS - Ação imediata necessária!${NC}"
    echo "💡 Corrija os problemas antes de fazer mudanças"
    exit 1
fi
