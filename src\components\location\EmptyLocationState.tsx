
import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, Navigation, Loader2 } from "lucide-react";

interface EmptyLocationStateProps {
  onGetLocation: () => void;
  isGettingLocation: boolean;
}

const EmptyLocationState: React.FC<EmptyLocationStateProps> = ({
  onGetLocation,
  isGettingLocation
}) => {
  return (
    <div className="w-full h-full bg-gray-100 flex flex-col items-center justify-center border rounded">
      <div className="text-center p-6">
        <MapPin className="h-12 w-12 mx-auto mb-4 text-gray-400" />
        <h3 className="font-medium text-lg mb-2">Nenhuma localização registrada</h3>
        <p className="text-gray-600 mb-4">
          Clique no botão abaixo para obter sua localização atual
        </p>
        <Button 
          onClick={onGetLocation}
          disabled={isGettingLocation}
          className="flex items-center gap-2"
        >
          {isGettingLocation ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Navigation className="h-4 w-4" />
          )}
          {isGettingLocation ? 'Obtendo localização...' : 'Obter Minha Localização'}
        </Button>
      </div>
    </div>
  );
};

export default EmptyLocationState;
