-- Criar função RPC para salvar localização do estudante
CREATE OR REPLACE FUNCTION public.save_student_location(
  p_latitude DOUBLE PRECISION,
  p_longitude DOUBLE PRECISION,
  p_shared_with_guardians BOOLEAN DEFAULT true,
  p_accuracy NUMERIC DEFAULT NULL,
  p_address TEXT DEFAULT NULL
)
RETURNS TABLE(id UUID, success BOOLEAN, message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_location_id UUID;
  v_user_id UUID;
BEGIN
  -- Obter ID do usuário autenticado
  v_user_id := auth.uid();
  
  IF v_user_id IS NULL THEN
    RETURN QUERY SELECT NULL::UUID, FALSE, 'Usuário não autenticado'::TEXT;
    RETURN;
  END IF;
  
  -- Inserir nova localização
  INSERT INTO public.locations (
    user_id,
    latitude,
    longitude,
    shared_with_guardians,
    accuracy,
    address,
    source,
    timestamp
  ) VALUES (
    v_user_id,
    p_latitude,
    p_longitude,
    p_shared_with_guardians,
    p_accuracy,
    p_address,
    'gps',
    NOW()
  ) RETURNING locations.id INTO v_location_id;
  
  -- Log da operação
  INSERT INTO public.auth_logs (
    event_type,
    user_id,
    metadata,
    occurred_at
  ) VALUES (
    'location_saved_via_rpc',
    v_user_id,
    jsonb_build_object(
      'location_id', v_location_id,
      'latitude', p_latitude,
      'longitude', p_longitude,
      'accuracy', p_accuracy,
      'shared_with_guardians', p_shared_with_guardians
    ),
    NOW()
  );
  
  RETURN QUERY SELECT 
    v_location_id,
    TRUE,
    'Localização salva com sucesso'::TEXT;
END;
$$;