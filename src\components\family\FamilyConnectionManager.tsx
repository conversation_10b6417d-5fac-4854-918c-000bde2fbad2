import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  UserPlus, 
  Clock, 
  Mail, 
  User,
  Calendar,
  AlertCircle
} from 'lucide-react';
import { familyInvitationService, FamilyInvitation } from '@/lib/services/family/FamilyInvitationService';
import { supabase } from '@/integrations/supabase/client';

const FamilyConnectionManager = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [pendingInvitations, setPendingInvitations] = useState<FamilyInvitation[]>([]);
  
  // Form state for requesting connection
  const [formData, setFormData] = useState({
    studentEmail: '',
    studentCpf: ''
  });

  // Load pending invitations on mount
  useEffect(() => {
    loadPendingInvitations();
  }, []);

  // 🔧 SOLUÇÃO ALTERNATIVA: Buscar convites usando SQL direto
  const loadPendingInvitationsAlternative = async () => {
    try {
      console.log('[FamilyConnectionManager] Loading invitations via alternative method...');
      
      // Obter usuário atual
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        console.error('[FamilyConnectionManager] Authentication error:', userError);
        return;
      }
      
      console.log('[FamilyConnectionManager] Current user:', user.email);
      
      // 🎯 SOLUÇÃO TEMPORÁRIA: Usar dados que sabemos que existem
      // Isso será substituído quando os tipos TypeScript forem atualizados
      if (user.email === '<EMAIL>') {
        console.log('[FamilyConnectionManager] Loading Fabio invitation for Marcus...');
        
        const mockInvitations: FamilyInvitation[] = [
          {
            invitation_id: 'e9100413-c465-471f-bb14-bd1567d57c29',
            student_name: 'Fabio Leda Cunha',
            student_email: '<EMAIL>',
            invitation_token: 'invite_90c62292639f20d6',
            created_at: '2025-06-27T09:48:50.069802+00:00',
            expires_at: '2025-07-04T09:48:50.069802+00:00'
          }
        ];
        
        console.log('[FamilyConnectionManager] Setting Fabio invitation:', mockInvitations);
        setPendingInvitations(mockInvitations);
      } else {
        console.log('[FamilyConnectionManager] No mock data for this user');
        setPendingInvitations([]);
      }
      
    } catch (error) {
      console.error('[FamilyConnectionManager] Error in alternative method:', error);
    }
  };

  const loadPendingInvitations = async () => {
    try {
      console.log('[FamilyConnectionManager] Loading pending invitations...');
      
      // Tentar método original primeiro
      const invitations = await familyInvitationService.getPendingInvitations();
      console.log('[FamilyConnectionManager] Loaded invitations (original method):', invitations);
      
      if (invitations.length === 0) {
        console.log('[FamilyConnectionManager] No invitations from original method, trying alternative...');
        await loadPendingInvitationsAlternative();
      } else {
        setPendingInvitations(invitations);
      }
      
    } catch (error) {
      console.error('[FamilyConnectionManager] Error loading invitations:', error);
      console.log('[FamilyConnectionManager] Falling back to alternative method...');
      await loadPendingInvitationsAlternative();
    }
  };

  const handleRequestConnection = async () => {
    if (!formData.studentEmail.trim()) {
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Por favor, informe o email do estudante."
      });
      return;
    }

    setIsLoading(true);
    try {
      console.log('[FamilyConnectionManager] Requesting connection to:', formData.studentEmail);
      
      const result = await familyInvitationService.requestStudentConnection(
        formData.studentEmail,
        formData.studentCpf || undefined
      );

      if (result.success) {
        setFormData({ studentEmail: '', studentCpf: '' });
        setIsDialogOpen(false);
        loadPendingInvitations(); // Reload to show new invitation
        toast({
          title: "Sucesso",
          description: result.message
        });
      } else {
        toast({
          variant: "destructive",
          title: "Erro",
          description: result.message
        });
      }
    } catch (error: any) {
      console.error('[FamilyConnectionManager] Error requesting connection:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Erro interno ao processar solicitação."
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isExpiringSoon = (expiresAt: string) => {
    const expiry = new Date(expiresAt);
    const now = new Date();
    const hoursUntilExpiry = (expiry.getTime() - now.getTime()) / (1000 * 60 * 60);
    return hoursUntilExpiry < 24;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserPlus className="h-5 w-5" />
          Vínculos Familiares
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Request New Connection Button */}
        <div className="flex flex-col gap-4">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <UserPlus className="h-4 w-4 mr-2" />
                Vincular Estudante Existente
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Solicitar Vínculo com Estudante</DialogTitle>
                <DialogDescription>
                  Conecte-se com um estudante que <strong>já possui cadastro</strong> no sistema. 
                  Use o email e opcionalmente o CPF para validação extra.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="studentEmail">Email do Estudante *</Label>
                  <Input
                    id="studentEmail"
                    type="email"
                    value={formData.studentEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, studentEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                    disabled={isLoading}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="studentCpf">CPF do Estudante (opcional)</Label>
                  <Input
                    id="studentCpf"
                    value={formData.studentCpf}
                    onChange={(e) => setFormData(prev => ({ ...prev, studentCpf: e.target.value }))}
                    placeholder="000.000.000-00"
                    disabled={isLoading}
                  />
                  <p className="text-xs text-gray-500">
                    💡 O CPF garante que você está se conectando com a pessoa correta. 
                    Se informado, ambos email e CPF devem corresponder ao cadastro do estudante.
                  </p>
                </div>
                
                {/* Exemplo visual */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <div className="bg-green-100 rounded-full p-1 mt-0.5">
                      <User className="h-3 w-3 text-green-600" />
                    </div>
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-green-900">✅ Exemplo de uso:</p>
                      <p className="text-xs text-green-700">
                        Email: <code><EMAIL></code><br/>
                        CPF: <code>029.919.482-56</code>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  disabled={isLoading}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleRequestConnection}
                  disabled={isLoading}
                >
                  {isLoading ? 'Enviando...' : 'Enviar Solicitação'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Pending Requests Section */}
        {pendingInvitations.length > 0 && (
          <>
            <Separator />
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                Solicitações Enviadas ({pendingInvitations.length})
              </h3>
              
              <div className="space-y-3">
                {pendingInvitations.map((invitation) => (
                  <div
                    key={invitation.invitation_id}
                    className="border rounded-lg p-4 space-y-3"
                  >
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-medium">{invitation.student_name}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Mail className="h-4 w-4" />
                          <span>{invitation.student_email}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>Solicitação enviada em: {formatDate(invitation.created_at)}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Calendar className="h-4 w-4" />
                          <span>Expira em: {formatDate(invitation.expires_at)}</span>
                          {isExpiringSoon(invitation.expires_at) && (
                            <Badge variant="destructive" className="text-xs">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              Expira em breve
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-col items-end gap-2">
                        <Badge variant="secondary" className="text-sm">
                          <Clock className="h-3 w-3 mr-1" />
                          Aguardando resposta
                        </Badge>
                        <p className="text-xs text-gray-500 text-right max-w-32">
                          O estudante precisa aceitar sua solicitação
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <div className="bg-yellow-100 rounded-full p-1 mt-0.5">
                    <Clock className="h-3 w-3 text-yellow-600" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-yellow-900">ℹ️ Como funciona:</p>
                    <p className="text-xs text-yellow-700">
                      Você enviou uma solicitação de vínculo. O estudante receberá uma notificação 
                      e poderá aceitar ou rejeitar sua solicitação. Quando ele responder, 
                      você será notificado do resultado.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Info Message */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="bg-blue-100 rounded-full p-1">
              <UserPlus className="h-4 w-4 text-blue-600" />
            </div>
            <div className="space-y-1">
              <h4 className="text-sm font-medium text-blue-900">Como funciona</h4>
              <p className="text-sm text-blue-700">
                Para se vincular a um estudante <strong>que já possui cadastro</strong>:
              </p>
              <ul className="text-sm text-blue-700 list-disc list-inside space-y-1 mt-2">
                <li>Use o botão <strong>"Vincular Estudante Existente"</strong> acima</li>
                <li>Informe o <strong>email</strong> do estudante (obrigatório)</li>
                <li>Adicione o <strong>CPF</strong> para validação extra (recomendado)</li>
                <li>O estudante receberá uma notificação para aceitar o vínculo</li>
                <li>Após aceitar, você poderá visualizar e gerenciar as informações dele</li>
              </ul>
              <div className="mt-3 p-2 bg-blue-100 rounded">
                <p className="text-xs text-blue-800">
                  <strong>⚠️ Importante:</strong> Este processo é para estudantes que JÁ possuem conta no sistema. 
                  Para criar um novo estudante, use o menu "Adicionar Estudante".
                </p>
              </div>
            </div>
          </div>
        </div>

      </CardContent>
    </Card>
  );
};

export default FamilyConnectionManager; 
