
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';

export interface CommunicationPreferences {
  user_id: string;
  email_enabled: boolean;
  sms_enabled: boolean;
  whatsapp_enabled: boolean;
  phone_number: string | null;
  preferred_method: string;
}

export interface CommunicationPreferencesInput {
  email_enabled: boolean;
  sms_enabled: boolean;
  whatsapp_enabled: boolean;
  phone_number: string;
  preferred_method: string;
}

export const useCommunicationPreferences = () => {
  const { user } = useUser();
  const [preferences, setPreferences] = useState<CommunicationPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchPreferences = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase.rpc('get_user_communication_preferences');
      
      if (error) {
        console.error('Erro ao buscar preferências:', error);
        return;
      }

      if (data && data.length > 0) {
        setPreferences(data[0]);
      } else {
        // Criar preferências padrão se não existir
        setPreferences({
          user_id: user.id,
          email_enabled: true,
          sms_enabled: false,
          whatsapp_enabled: false,
          phone_number: null,
          preferred_method: 'email'
        });
      }
    } catch (error) {
      console.error('Erro ao buscar preferências:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updatePreferences = async (newPreferences: CommunicationPreferencesInput): Promise<boolean> => {
    if (!user) return false;

    try {
      const { data, error } = await supabase.rpc('save_communication_preferences', {
        p_email_enabled: newPreferences.email_enabled,
        p_sms_enabled: newPreferences.sms_enabled,
        p_whatsapp_enabled: newPreferences.whatsapp_enabled,
        p_phone_number: newPreferences.phone_number || null,
        p_preferred_method: newPreferences.preferred_method
      });

      if (error) {
        console.error('Erro ao salvar preferências:', error);
        return false;
      }

      // Atualizar o estado local
      await fetchPreferences();
      return true;
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
      return false;
    }
  };

  useEffect(() => {
    fetchPreferences();
  }, [user]);

  return {
    preferences,
    isLoading,
    updatePreferences,
    refetch: fetchPreferences
  };
};
