/**
 * Utility to detect whether the current device is a mobile device
 * based on the user agent and screen width.
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') {
    return false; // SSR check
  }
  
  // User agent detection (most common)
  const userAgent = navigator.userAgent.toLowerCase();
  
  // Screen width check as a fallback
  const isMobileWidth = window.innerWidth <= 768;
  
  return (
    /android|webos|iphone|ipad|ipod|blackberry|windows phone/i.test(userAgent) || 
    ('ontouchstart' in window && isMobileWidth)
  );
}

/**
 * Returns the specific mobile platform
 */
export function getMobilePlatform(): string | null {
  if (typeof window === 'undefined') {
    return null; // SSR check
  }
  
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/iphone|ipad|ipod/i.test(userAgent)) {
    return 'ios';
  } else if (/android/i.test(userAgent)) {
    return 'android';
  } else if (/windows phone/i.test(userAgent)) {
    return 'windows';
  } else if (isMobileDevice()) {
    return 'unknown-mobile';
  }
  
  return null;
}
