-- Migration: Create validation-only student account function
-- Date: 2025-01-31
-- Purpose: Validate student data before Edge Function processing

CREATE OR REPLACE FUNCTION public.validate_student_account_data(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_student_phone TEXT DEFAULT NULL
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT,
  validation_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_guardian_id UUID := auth.uid();
  v_clean_cpf TEXT;
  v_validation_id UUID := gen_random_uuid();
  v_existing_user UUID;
BEGIN
  -- Check authentication
  IF v_guardian_id IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Usuário não autenticado', NULL;
    RETURN;
  END IF;

  -- Validate CPF format
  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  IF char_length(v_clean_cpf) <> 11 THEN
    RETURN QUERY SELECT FALSE, 'CPF inválido', NULL;
    RETURN;
  END IF;

  -- Validate email format
  IF p_student_email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
    RETURN QUERY SELECT FALSE, 'Email inválido', NULL;
    RETURN;
  END IF;

  -- Check if user already exists
  SELECT user_id INTO v_existing_user 
  FROM profiles 
  WHERE email = p_student_email 
     OR cpf = v_clean_cpf;

  IF v_existing_user IS NOT NULL THEN
    RETURN QUERY SELECT FALSE, 'Usuário já existe', NULL;
    RETURN;
  END IF;

  -- Check if guardian already has this student
  IF EXISTS (
    SELECT 1 FROM profiles p
    JOIN student_guardian_relationships sgr ON p.user_id = sgr.student_id
    WHERE sgr.guardian_id = v_guardian_id 
      AND (p.email = p_student_email OR p.cpf = v_clean_cpf)
  ) THEN
    RETURN QUERY SELECT FALSE, 'Estudante já vinculado a este responsável', NULL;
    RETURN;
  END IF;

  -- Log validation attempt
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_validation', v_guardian_id,
          jsonb_build_object(
            'validation_id', v_validation_id,
            'email', p_student_email, 
            'cpf_length', char_length(v_clean_cpf),
            'guardian_id', v_guardian_id
          ),
          NOW());

  -- Return success with validation ID for Edge Function use
  RETURN QUERY SELECT TRUE, 'Dados válidos - pronto para criação via Edge Function', v_validation_id;
END;
$$;

GRANT EXECUTE ON FUNCTION public.validate_student_account_data(TEXT, TEXT, TEXT, TEXT) TO authenticated, service_role;

-- Add comment explaining the purpose
COMMENT ON FUNCTION public.validate_student_account_data IS 'Validates student data before Edge Function processing. Does not create users - only validation.'; 