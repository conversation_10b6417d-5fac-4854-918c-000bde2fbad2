-- ==========================================
-- DEBUG FINAL - TIPOS CORRETOS
-- Data: 24/06/2025
-- Descoberta: profiles.id = INTEGER (não UUID!)
-- ==========================================

-- 1. DADOS FRANK - TABELA GUARDIANS
SELECT 
    'FRANK_GUARDIANS' as secao,
    id, email, student_id, is_active, created_at
FROM public.guardians 
WHERE email = '<EMAIL>';

-- 2. DADOS FRANK - TABELA PROFILES  
SELECT 
    'FRANK_PROFILES' as secao,
    id, email, full_name, user_type, created_at
FROM public.profiles
WHERE email = '<EMAIL>';

-- 3. SOLICITAÇÕES DE EXCLUSÃO
SELECT 
    'ACCOUNT_DELETION' as secao,
    id, student_email, student_name, status, requested_at
FROM public.account_deletion_requests
ORDER BY requested_at DESC;

-- 4. RPC FUNCTIONS EXISTEM?
SELECT 
    'RPC_FUNCTIONS' as secao,
    routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name IN ('get_guardian_deletion_requests', 'get_guardian_students');

-- 5. VINCULAÇÃO FRANK → ESTUDANTES (TIPOS CORRETOS)
SELECT 
    'FRANK_STUDENTS' as secao,
    g.id as guardian_id,
    g.student_id,
    g.email as guardian_email,
    p.id as profile_id,
    p.email as student_email,
    p.full_name as student_name
FROM public.guardians g
LEFT JOIN public.profiles p ON p.id = g.student_id
WHERE g.email = '<EMAIL>';

-- 6. CONTAGEM SIMPLES
SELECT 'CONTAGEM' as secao, 'profiles' as tabela, COUNT(*) as total FROM public.profiles
UNION ALL
SELECT 'CONTAGEM' as secao, 'guardians' as tabela, COUNT(*) as total FROM public.guardians  
UNION ALL
SELECT 'CONTAGEM' as secao, 'account_deletion_requests' as tabela, COUNT(*) as total FROM public.account_deletion_requests; 