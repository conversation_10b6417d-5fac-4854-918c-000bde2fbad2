-- =====================================================
-- ADICIONAR TABELA GUARDIANS QUE ESTÁ FALTANDO
-- Frontend está tentando acessar esta tabela (erro 404)
-- =====================================================

-- Criar tabela guardians para relacionar responsáveis com estudantes
CREATE TABLE IF NOT EXISTS public.guardians (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  guardian_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true NOT NULL,
  relationship_type TEXT DEFAULT 'parent',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  -- Evitar duplicatas
  UNIQUE(student_id, guardian_id)
);

-- Habilitar RLS
ALTER TABLE public.guardians ENABLE ROW LEVEL SECURITY;

-- Política permissiva para desenvolvimento
CREATE POLICY "Guardians policy - dev" ON public.guardians
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Trigger para updated_at
CREATE TRIGGER update_guardians_updated_at 
  BEFORE UPDATE ON public.guardians
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- POPULAR COM DADOS EXISTENTES (se houver)
-- =====================================================

-- Inserir relacionamentos existentes na nova tabela guardians
-- baseado em student_guardian_relationships
INSERT INTO public.guardians (student_id, guardian_id, email, is_active, relationship_type)
SELECT 
  sgr.student_id,
  sgr.guardian_id,
  COALESCE(gp.email, p.email, auth_users.email) as email,
  true as is_active,
  sgr.relationship_type
FROM public.student_guardian_relationships sgr
LEFT JOIN public.guardian_profiles gp ON sgr.guardian_id = gp.user_id
LEFT JOIN public.profiles p ON sgr.guardian_id = p.user_id
LEFT JOIN auth.users auth_users ON sgr.guardian_id = auth_users.id
WHERE NOT EXISTS (
  SELECT 1 FROM public.guardians g 
  WHERE g.student_id = sgr.student_id AND g.guardian_id = sgr.guardian_id
)
ON CONFLICT (student_id, guardian_id) DO NOTHING;

-- =====================================================
-- FUNÇÃO HELPER PARA SINCRONIZAR DADOS
-- =====================================================

-- Função para manter guardians sincronizado com relationships
CREATE OR REPLACE FUNCTION sync_guardians_table()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Buscar email do guardian
    INSERT INTO public.guardians (student_id, guardian_id, email, relationship_type)
    SELECT 
      NEW.student_id,
      NEW.guardian_id,
      COALESCE(gp.email, p.email, auth_users.email),
      NEW.relationship_type
    FROM (SELECT NEW.guardian_id as guardian_id) t
    LEFT JOIN public.guardian_profiles gp ON t.guardian_id = gp.user_id
    LEFT JOIN public.profiles p ON t.guardian_id = p.user_id
    LEFT JOIN auth.users auth_users ON t.guardian_id = auth_users.id
    ON CONFLICT (student_id, guardian_id) DO UPDATE SET
      email = EXCLUDED.email,
      relationship_type = EXCLUDED.relationship_type,
      updated_at = now();
      
    RETURN NEW;
  END IF;
  
  IF TG_OP = 'DELETE' THEN
    DELETE FROM public.guardians 
    WHERE student_id = OLD.student_id AND guardian_id = OLD.guardian_id;
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger para manter sincronização automática
CREATE TRIGGER sync_guardians_on_relationships_change
  AFTER INSERT OR DELETE ON public.student_guardian_relationships
  FOR EACH ROW EXECUTE FUNCTION sync_guardians_table();
