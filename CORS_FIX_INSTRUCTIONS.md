# 🚨 CORREÇÃO URGENTE - ERRO CORS Edge Function

## ❌ **PROBLEMA ATUAL**
```
Access to fetch at 'https://rsvjnndhbyyxktbczlnk.supabase.co/functions/v1/create-student-account' 
from origin 'https://sistema-monitore.com.br' has been blocked by CORS policy
```

## ✅ **SOLUÇÕES IMEDIATAS**

### **OPÇÃO 1: Via Dashboard Supabase (MAIS RÁPIDO)**

1. **Acesse:** https://supabase.com/dashboard/project/rsvjnndhbyyxktbczlnk
2. **Navegue:** SQL Editor > New Query
3. **Execute esta correção:**

```sql
-- Verificar se a função existe
SELECT proname FROM pg_proc WHERE proname = 'create_student_account';
```

### **OPÇÃO 2: Corrigir Edge Function via Dashboard**

1. **Acesse:** https://supabase.com/dashboard/project/rsvjnndhbyyxktbczlnk
2. **Vá para:** Edge Functions
3. **Selecione:** `create-student-account`
4. **Substitua linhas 4-7** por:

```typescript
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-site-url',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}
```

5. **Substitua linha 12** por:
```typescript
return new Response(null, { headers: corsHeaders, status: 204 });
```

6. **Deploy** a função no dashboard

### **OPÇÃO 3: Verificar Configurações Edge Function**

No Dashboard do Supabase:
1. **Edge Functions > Settings**
2. **Verificar se JWT verification** está configurado corretamente
3. **Verificar environment variables**:
   - `SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`

### **OPÇÃO 4: Teste via SQL (Verificação)**

Execute no SQL Editor:

```sql
-- Verificar se o usuário tem as permissões necessárias
SELECT current_user, session_user;

-- Verificar se a função RPC existe
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%student%';
```

## 🔍 **VERIFICAÇÕES PÓS-CORREÇÃO**

1. **Teste a funcionalidade** no frontend
2. **Verifique os logs** da Edge Function no dashboard
3. **Confirme o CORS** usando DevTools:
   - F12 > Network
   - Recarregar página
   - Tentar adicionar estudante
   - Verificar headers da resposta

## 📋 **HEADERS CORS CORRETOS**

A resposta deve incluir:
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Headers: authorization, x-client-info, apikey, content-type, x-site-url
Access-Control-Allow-Methods: POST, OPTIONS
```

## 🎯 **RESULTADO ESPERADO**

Após a correção, o usuário deve conseguir:
1. ✅ Clicar em "Adicionar Estudante"
2. ✅ Preencher formulário (CPF: 717.102.482-20, Nome: Fabio Leda Cunha)
3. ✅ Enviar sem erro CORS
4. ✅ Receber confirmação de sucesso
5. ✅ Ver o estudante na lista

## 🚨 **SE O PROBLEMA PERSISTIR**

1. **Verificar logs** no Dashboard Supabase > Edge Functions > Logs
2. **Verificar RLS policies** nas tabelas envolvidas
3. **Verificar se o JWT** está sendo enviado corretamente
4. **Testar em modo incógnito** para eliminar cache

---

**⏰ URGENTE:** Implemente a **OPÇÃO 1 ou 2** imediatamente para resolver o bloqueio do usuário. 