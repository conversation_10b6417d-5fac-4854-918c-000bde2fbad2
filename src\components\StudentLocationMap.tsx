
import React, { useState, useEffect, lazy, Suspense } from 'react';
import { MapPin, Loader2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
const MapView = lazy(() => import('@/components/map/MapView'));
import MapStyleSelector from '@/components/map/MapStyleSelector';
import { useMapStyle } from '@/hooks/useMapStyle';
import LocationDisplay from '@/components/location/LocationDisplay';
import EmptyLocationState from '@/components/location/EmptyLocationState';
import LocationQualityIndicator from '@/components/location/LocationQualityIndicator';
import { LocationData } from '@/types/database';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAdvancedGeolocation } from '@/hooks/useAdvancedGeolocation';

interface StudentLocationMapProps {
  selectedUserId: string | undefined;
  showControls?: boolean;
  locations?: LocationData[];
  userType?: string;
  studentDetails?: {
    name: string;
    email: string;
  } | null;
  loading?: boolean;
  error?: string | null;
  senderName?: string;
  noDataContent?: React.ReactNode;
  onShareAll?: () => Promise<void>;
  isSendingAll?: boolean;
  guardianCount?: number;
  forceUpdateKey?: number;
  focusOnLatest?: boolean;
}

const StudentLocationMap: React.FC<StudentLocationMapProps> = ({ 
  selectedUserId,
  showControls = true,
  locations = [],
  userType = 'student',
  studentDetails = null,
  loading = false,
  error = null,
  senderName = 'Estudante',
  noDataContent = null,
  onShareAll,
  isSendingAll = false,
  guardianCount = 0,
  forceUpdateKey,
  focusOnLatest
}) => {
  const { toast } = useToast();
  const [mapStyle] = useMapStyle();
  const [allLocations, setAllLocations] = useState<LocationData[]>(locations);
  const [sharingStatus, setSharingStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  // Usar o novo hook de geolocalização avançada
  const {
    isLoading: isGettingLocation,
    isRefining,
    result: geoResult,
    error: geoError,
    qualityIndicator,
    getCurrentLocation,
    improveAccuracy,
    canImprove,
    accuracy,
    address
  } = useAdvancedGeolocation({
    targetAccuracy: 10, // 10 metros de precisão alvo
    maxAttempts: 3,
    enableHybrid: true,
    autoRefine: true
  });

  // Update locations when props change
  useEffect(() => {
    setAllLocations(locations);
  }, [locations]);

  const handleGetCurrentLocation = async () => {
    if (!selectedUserId) {
      toast({
        title: "Erro",
        description: "ID do usuário não encontrado",
        variant: "destructive"
      });
      return;
    }

    const result = await getCurrentLocation();
    
    if (result) {
      try {
        // Save location to database with enhanced data
        const { data, error: dbError } = await supabase
          .from('locations')
          .insert([{
            user_id: selectedUserId,
            latitude: result.latitude,
            longitude: result.longitude,
            accuracy: result.accuracy,
            address: result.address,
            shared_with_guardians: false,
            source: result.source
          }])
          .select()
          .single();

        if (dbError) {
          throw new Error(`Erro ao salvar localização: ${dbError.message}`);
        }

        const newLocation: LocationData = {
          id: data.id,
          user_id: selectedUserId,
          latitude: result.latitude,
          longitude: result.longitude,
          timestamp: new Date().toISOString(),
          shared_with_guardians: false,
          address: result.address,
          student_name: studentDetails?.name || 'Estudante',
          student_email: studentDetails?.email || '',
          created_at: new Date().toISOString(),
          user: studentDetails ? { 
            full_name: studentDetails.name,
            user_type: 'student'
          } : undefined
        };

        setAllLocations(prev => [newLocation, ...prev]);

      } catch (error: any) {
        console.error('Error saving location:', error);
        toast({
          title: "Erro ao salvar",
          description: error.message || "Não foi possível salvar a localização no banco de dados",
          variant: "destructive"
        });
      }
    }
  };

  const handleShareAll = async () => {
    if (onShareAll) {
      setSharingStatus('loading');
      try {
        await onShareAll();
        setSharingStatus('success');
        setTimeout(() => setSharingStatus('idle'), 3000);
      } catch (error) {
        setSharingStatus('error');
        setTimeout(() => setSharingStatus('idle'), 3000);
      }
    }
  };

  const locationError = error || geoError;

  return (
    <div className="w-full h-full flex flex-col" data-cy="location-map">
      {/* Header compacto - otimizado para mobile */}
      <div className="flex-shrink-0 pb-2 md:pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
            <span className="text-sm md:text-base font-medium">Mapa de Localização</span>
          </div>
          {(loading || isGettingLocation) && (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
        </div>

        {/* Indicador de qualidade da localização - compacto em mobile */}
        {(geoResult || isGettingLocation || isRefining) && (
          <LocationQualityIndicator
            result={geoResult}
            qualityIndicator={qualityIndicator}
            isLoading={isGettingLocation}
            isRefining={isRefining}
            canImprove={canImprove}
            onImproveAccuracy={improveAccuracy}
            onRefresh={handleGetCurrentLocation}
            compact={true}
            className="mt-1 md:mt-2"
          />
        )}
      </div>

      {/* Container do mapa - ocupa todo espaço disponível */}
      <div className="flex-1 relative min-h-0">
        {locationError && (
          <div className="absolute top-2 left-2 right-2 z-10">
            <Alert variant="destructive" className="text-sm">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle className="text-sm">Erro no Mapa</AlertTitle>
              <AlertDescription className="text-xs">{locationError}</AlertDescription>
            </Alert>
          </div>
        )}

        {/* Mapa principal - otimizado para iOS e mobile */}
        <div className="relative w-full h-full map-responsive map-highlight rounded-lg md:rounded-xl overflow-hidden">
          {allLocations.length > 0 ? (
            <Suspense fallback={
              <div className="w-full h-full flex items-center justify-center bg-gray-50">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              </div>
            }>
              <MapView
                selectedUserId={selectedUserId}
                locations={allLocations}
                showControls={false}
                mapStyle={mapStyle}
                onLocationUpdate={handleGetCurrentLocation}
                focusOnLatest={focusOnLatest}
                forceUpdateKey={forceUpdateKey}
              />
            </Suspense>
          ) : (
            <EmptyLocationState
              onGetLocation={handleGetCurrentLocation}
              isGettingLocation={isGettingLocation}
            />
          )}

          {/* Controles do mapa - posicionados para não interferir no iOS */}
          <MapStyleSelector className="absolute top-2 right-2 z-10 scale-90 md:scale-100" />
        </div>

        {/* Display da localização atual - posicionado para não sobrepor o mapa */}
        {geoResult && (
          <div className="absolute bottom-2 left-2 right-2 z-10">
            <LocationDisplay
              currentLocation={{
                id: 'current',
                user_id: selectedUserId || '',
                latitude: geoResult.latitude,
                longitude: geoResult.longitude,
                timestamp: new Date(geoResult.timestamp).toISOString(),
                address: geoResult.address || null,
                shared_with_guardians: false,
                student_name: studentDetails?.name || 'Estudante',
                student_email: studentDetails?.email || '',
                created_at: new Date().toISOString()
              }}
              showQuality={true}
              quality={{
                accuracy: geoResult.accuracy,
                confidence: geoResult.confidence,
                source: geoResult.source,
                qualityScore: geoResult.quality_score
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentLocationMap;
