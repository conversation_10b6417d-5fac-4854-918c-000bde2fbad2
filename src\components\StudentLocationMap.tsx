
import React, { useState, useEffect, lazy, Suspense } from 'react';
import { MapPin, Loader2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
const MapView = lazy(() => import('@/components/map/MapView'));
import MapStyleSelector from '@/components/map/MapStyleSelector';
import { useMapStyle } from '@/hooks/useMapStyle';
import LocationDisplay from '@/components/location/LocationDisplay';
import EmptyLocationState from '@/components/location/EmptyLocationState';
import LocationQualityIndicator from '@/components/location/LocationQualityIndicator';
import { LocationData } from '@/types/database';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAdvancedGeolocation } from '@/hooks/useAdvancedGeolocation';

interface StudentLocationMapProps {
  selectedUserId: string | undefined;
  showControls?: boolean;
  locations?: LocationData[];
  userType?: string;
  studentDetails?: {
    name: string;
    email: string;
  } | null;
  loading?: boolean;
  error?: string | null;
  senderName?: string;
  noDataContent?: React.ReactNode;
  onShareAll?: () => Promise<void>;
  isSendingAll?: boolean;
  guardianCount?: number;
  forceUpdateKey?: number;
  focusOnLatest?: boolean;
}

const StudentLocationMap: React.FC<StudentLocationMapProps> = ({ 
  selectedUserId,
  showControls = true,
  locations = [],
  userType = 'student',
  studentDetails = null,
  loading = false,
  error = null,
  senderName = 'Estudante',
  noDataContent = null,
  onShareAll,
  isSendingAll = false,
  guardianCount = 0,
  forceUpdateKey,
  focusOnLatest
}) => {
  const { toast } = useToast();
  const [mapStyle] = useMapStyle();
  const [allLocations, setAllLocations] = useState<LocationData[]>(locations);
  const [sharingStatus, setSharingStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  // Usar o novo hook de geolocalização avançada
  const {
    isLoading: isGettingLocation,
    isRefining,
    result: geoResult,
    error: geoError,
    qualityIndicator,
    getCurrentLocation,
    improveAccuracy,
    canImprove,
    accuracy,
    address
  } = useAdvancedGeolocation({
    targetAccuracy: 10, // 10 metros de precisão alvo
    maxAttempts: 3,
    enableHybrid: true,
    autoRefine: true
  });

  // Update locations when props change
  useEffect(() => {
    setAllLocations(locations);
  }, [locations]);

  const handleGetCurrentLocation = async () => {
    if (!selectedUserId) {
      toast({
        title: "Erro",
        description: "ID do usuário não encontrado",
        variant: "destructive"
      });
      return;
    }

    const result = await getCurrentLocation();
    
    if (result) {
      try {
        // Save location to database with enhanced data
        const { data, error: dbError } = await supabase
          .from('locations')
          .insert([{
            user_id: selectedUserId,
            latitude: result.latitude,
            longitude: result.longitude,
            accuracy: result.accuracy,
            address: result.address,
            shared_with_guardians: false,
            source: result.source
          }])
          .select()
          .single();

        if (dbError) {
          throw new Error(`Erro ao salvar localização: ${dbError.message}`);
        }

        const newLocation: LocationData = {
          id: data.id,
          user_id: selectedUserId,
          latitude: result.latitude,
          longitude: result.longitude,
          timestamp: new Date().toISOString(),
          shared_with_guardians: false,
          address: result.address,
          student_name: studentDetails?.name || 'Estudante',
          student_email: studentDetails?.email || '',
          created_at: new Date().toISOString(),
          user: studentDetails ? { 
            full_name: studentDetails.name,
            user_type: 'student'
          } : undefined
        };

        setAllLocations(prev => [newLocation, ...prev]);

      } catch (error: any) {
        console.error('Error saving location:', error);
        toast({
          title: "Erro ao salvar",
          description: error.message || "Não foi possível salvar a localização no banco de dados",
          variant: "destructive"
        });
      }
    }
  };

  const handleShareAll = async () => {
    if (onShareAll) {
      setSharingStatus('loading');
      try {
        await onShareAll();
        setSharingStatus('success');
        setTimeout(() => setSharingStatus('idle'), 3000);
      } catch (error) {
        setSharingStatus('error');
        setTimeout(() => setSharingStatus('idle'), 3000);
      }
    }
  };

  const locationError = error || geoError;

  return (
    <div className="w-full h-full flex flex-col" data-cy="location-map">
      <div className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-blue-600" />
            <span>Mapa de Localização</span>
          </div>
          {(loading || isGettingLocation) && (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
        </div>

        {/* Indicador de qualidade da localização */}
        {(geoResult || isGettingLocation || isRefining) && (
          <LocationQualityIndicator
            result={geoResult}
            qualityIndicator={qualityIndicator}
            isLoading={isGettingLocation}
            isRefining={isRefining}
            canImprove={canImprove}
            onImproveAccuracy={improveAccuracy}
            onRefresh={handleGetCurrentLocation}
            compact={true}
            className="mt-2"
          />
        )}
      </div>

      <div className="flex-1 p-0 relative">
        {locationError && (
          <div className="absolute top-4 left-4 right-4 z-10">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erro no Mapa</AlertTitle>
              <AlertDescription>{locationError}</AlertDescription>
            </Alert>
          </div>
        )}

        <div className="relative map-responsive map-highlight">
          {allLocations.length > 0 ? (
            <Suspense fallback={<Loader2 className="h-8 w-8 animate-spin" />}>
              <MapView
                selectedUserId={selectedUserId}
                locations={allLocations}
                showControls={false}
                mapStyle={mapStyle}
                onLocationUpdate={handleGetCurrentLocation}
                focusOnLatest={focusOnLatest}
                forceUpdateKey={forceUpdateKey}
              />
            </Suspense>
          ) : (
            <EmptyLocationState
              onGetLocation={handleGetCurrentLocation}
              isGettingLocation={isGettingLocation}
            />
          )}
          <MapStyleSelector className="absolute top-2 right-2 z-10" />
        </div>

        {/* Display da localização atual com informações detalhadas */}
        {geoResult && (
          <LocationDisplay 
            currentLocation={{
              id: 'current',
              user_id: selectedUserId || '',
              latitude: geoResult.latitude,
              longitude: geoResult.longitude,
              timestamp: new Date(geoResult.timestamp).toISOString(),
              address: geoResult.address || null,
              shared_with_guardians: false,
              student_name: studentDetails?.name || 'Estudante',
              student_email: studentDetails?.email || '',
              created_at: new Date().toISOString()
            }}
            showQuality={true}
            quality={{
              accuracy: geoResult.accuracy,
              confidence: geoResult.confidence,
              source: geoResult.source,
              qualityScore: geoResult.quality_score
            }}
          />
        )}
      </div>
    </div>
  );
};

export default StudentLocationMap;
