
import React, { useState } from 'react';
import { AlertTriangle, X, Wifi } from 'lucide-react';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { Button } from '@/components/ui/button';

export function OfflineStatusBanner() {
  const { isOnline, isSlowConnection } = useNetworkStatus();
  const [isDismissed, setIsDismissed] = useState(false);

  // Não mostrar se online e conexão boa, ou se foi dispensado
  if ((isOnline && !isSlowConnection) || isDismissed) {
    return null;
  }

  const handleDismiss = () => {
    setIsDismissed(true);
    // Auto-reaparecer após 30 segundos se ainda offline
    if (!isOnline) {
      setTimeout(() => setIsDismissed(false), 30000);
    }
  };

  return (
    <div className={`
      fixed top-0 left-0 right-0 z-50 p-3 text-white text-sm
      ${isOnline 
        ? 'bg-yellow-600' 
        : 'bg-red-600'
      }
    `}>
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isOnline ? (
            <Wifi className="w-4 h-4" />
          ) : (
            <AlertTriangle className="w-4 h-4" />
          )}
          
          <span className="font-medium">
            {isOnline 
              ? 'Conexão lenta detectada'
              : 'Você está offline'
            }
          </span>
          
          <span className="opacity-90">
            {isOnline
              ? 'Alguns recursos podem estar limitados'
              : 'Funcionando com dados em cache'
            }
          </span>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="text-white hover:bg-white/20 h-auto p-1"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
