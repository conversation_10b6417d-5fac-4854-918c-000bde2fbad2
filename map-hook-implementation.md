# Documentação do Hook de Inicialização do Mapa

## Visão Geral

Este documento descreve a implementação unificada do hook `useMapInitialization` usado para inicializar mapas do Mapbox no sistema Locate-Family-Connect.

## Problema Resolvido

Anteriormente, existiam duas implementações do hook `useMapInitialization`:
- `useMapInitialization.ts`: Versão simples que aceitava apenas parâmetros de viewport (latitude, longitude, zoom)
- `useMapInitialization.tsx`: Versão avançada que suportava estilos de mapa híbridos e controles

Esta duplicação causava conflitos de tipagem e erros de build, além de exigir contornos no código usando o hook.

## Solução Implementada

Unificamos ambas as implementações em um único hook (`useMapInitialization.unified.ts`) que:
1. Combina as interfaces de ambos os hooks anteriores
2. Suporta todos os parâmetros necessários (latitude, longitude, zoom, mapStyle, showControls)
3. Retorna uma interface consistente e tipada
4. Elimina a necessidade de type assertions e verificações de runtime

## Como Usar

```tsx
import { useMapInitialization } from '@/hooks/useMapInitialization';

// Uso básico com valores padrão
const {
  mapContainer,
  mapInstance,
  mapInitialized,
  isTokenValid,
  mapError
} = useMapInitialization();

// Uso completo com todas as opções
const {
  mapContainer,
  mapInstance,
  mapInitialized,
  isTokenValid,
  mapError,
  updateViewport,
  handleUpdateLocation
} = useMapInitialization({
  latitude: -23.5489,
  longitude: -46.6388,
  zoom: 12,
  mapStyle: 'standard-satellite',
  showControls: true
});
```

## Propriedades de Entrada

| Propriedade   | Tipo    | Padrão             | Descrição                                        |
|---------------|---------|--------------------|-------------------------------------------------|
| latitude      | number  | -23.5489           | Latitude inicial do mapa (São Paulo)             |
| longitude     | number  | -46.6388           | Longitude inicial do mapa (São Paulo)            |
| zoom          | number  | 12                 | Nível de zoom inicial                            |
| mapStyle      | string  | 'standard-satellite'| Estilo do mapa (padrão, satélite, híbrido, etc) |
| showControls  | boolean | true               | Se deve mostrar controles de navegação no mapa    |

## Propriedades Retornadas

| Propriedade         | Tipo                        | Descrição                                         |
|---------------------|----------------------------|--------------------------------------------------|
| mapContainer        | RefObject<HTMLDivElement>   | Referência ao container do mapa                   |
| mapInstance         | mapboxgl.Map \| null        | Instância do mapa Mapbox                          |
| mapInitialized      | boolean                    | Indica se o mapa foi inicializado com sucesso     |
| isTokenValid        | boolean                    | Indica se o token do Mapbox é válido              |
| mapError            | string \| null             | Mensagem de erro, se houver                       |
| updateViewport      | function                   | Função para atualizar a viewport do mapa          |
| handleUpdateLocation| function                   | Função para atualizar localização usando GPS      |

## Notas de Implementação

- O hook inicializa o Mapbox com otimizações de performance
- EventListeners touch são configurados como passive para melhorar a performance
- Utiliza requestIdleCallback quando disponível para melhorar a inicialização
- Detecta automaticamente o ambiente de dashboard de responsáveis para ajustar o zoom
- Mantém compatibilidade com o código existente através de redirecionamentos nos arquivos originais

## Exemplos de Uso

### Mapa Básico
```tsx
const { mapContainer, mapInstance } = useMapInitialization();

return <div ref={mapContainer} className="h-96" />;
```

### Mapa Híbrido com Controles (Dashboard de Responsáveis)
```tsx
const { mapContainer, mapInstance } = useMapInitialization({
  mapStyle: 'standard-satellite',
  showControls: true,
  zoom: 16
});

return <div ref={mapContainer} className="h-96" />;
```

### Atualizando a Viewport
```tsx
const { mapContainer, updateViewport } = useMapInitialization();

const focusOnLocation = (location) => {
  updateViewport({
    latitude: location.latitude,
    longitude: location.longitude,
    zoom: 18
  });
};
```
