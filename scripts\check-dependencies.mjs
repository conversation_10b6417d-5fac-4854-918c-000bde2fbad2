import fs from 'fs';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);
const packages = ['vite', 'eslint', 'jest'];
let missing = false;

if (!fs.existsSync('node_modules')) {
  console.warn('⚠️ node_modules not found. Run "npm install" first.');
  missing = true;
}

for (const pkg of packages) {
  try {
    require.resolve(pkg);
  } catch {
    console.warn(`⚠️ Package "${pkg}" missing. Run "npm install".`);
    missing = true;
  }
}

if (!missing) {
  console.log('✅ Dependencies installed.');
}
