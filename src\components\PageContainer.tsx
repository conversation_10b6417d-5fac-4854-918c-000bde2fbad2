
import React from "react";
import { useResponsiveLayout } from "@/hooks/useResponsiveLayout";
import { cn } from "@/lib/utils";

interface PageContainerProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
  className?: string;
  fullWidth?: boolean;
  contentClassName?: string;
  includeBottomPadding?: boolean;
}

const PageContainer = ({ 
  children, 
  title, 
  subtitle, 
  action,
  className = "",
  fullWidth = false,
  contentClassName = "",
  includeBottomPadding = true
}: PageContainerProps) => {
  const { 
    device, 
    spacing, 
    textSizes, 
    getContainerClasses,
    getNavigationHeight 
  } = useResponsiveLayout();
  
  // Header layout based on device constraints
  const getHeaderLayout = () => {
    if (device.type === 'mobile' && device.orientation === 'landscape') {
      return 'flex-col items-start gap-1';
    }
    
    return 'flex-wrap md:flex-nowrap justify-between items-start md:items-center gap-2 md:gap-4';
  };
  
  // Content spacing with landscape optimization
  const getContentSpacing = () => {
    if (device.type === 'mobile' && device.orientation === 'landscape') {
      return 'space-y-1';
    }
    if (device.type === 'tablet' && device.orientation === 'landscape') {
      return 'space-y-2';
    }
    switch (device.size) {
      case 'xxs':
        return 'space-y-2';
      case 'xs':
        return 'space-y-2.5';
      case 'sm':
        return 'space-y-3';
      default:
        return 'space-y-4 md:space-y-6';
    }
  };
  
  // Navigation height compensation
  const getTopPadding = () => {
    const navHeight = getNavigationHeight();
    const baseClasses = device.hasNotch ? 'pt-safe-area-top' : 'pt-4';
    
    if (device.type === 'mobile') {
      return `${baseClasses} pb-20`; // Account for bottom navigation
    }
    
    return baseClasses;
  };

  return (
    <div className={cn(
      "w-full min-h-screen",
      !fullWidth && 'max-w-7xl mx-auto',
      getTopPadding(),
      spacing.padding,
      className
    )}>
      {(title || subtitle || action) && (
        <div className={cn(
          spacing.margin,
          'flex',
          getHeaderLayout()
        )}>
          <div className="w-full md:w-auto">
            {title && (
              <h1 className={cn(
                textSizes.title,
                'font-bold tracking-tight'
              )}>
                {title}
              </h1>
            )}
            {subtitle && (
              <p className={cn(
                textSizes.subtitle,
                'text-muted-foreground mt-1 md:mt-2'
              )}>
                {subtitle}
              </p>
            )}
          </div>
          {action && (
            <div className="shrink-0 mt-2 md:mt-0">
              {action}
            </div>
          )}
        </div>
      )}
      
      <div className={cn(
        getContentSpacing(),
        contentClassName
      )}>
        {children}
      </div>
      
      {/* Bottom safe area for devices with home indicator */}
      {device.hasNotch && includeBottomPadding && (
        <div className="pb-safe-area-bottom" />
      )}
    </div>
  );
};

export default PageContainer;
