DEVELOPMENT_GUIDELINES
Anti-error Verification Protocols
To minimize the risk of errors, implement multiple layers of verification. Use automated tests, code reviews, and continuous integration to catch issues early. Establish peer review and pair programming practices for critical code changes. Ensure changes are validated against requirements and regression tests.

Automated Testing: Maintain unit, integration, and end-to-end tests that run on each commit.
Code Reviews: All changes must be reviewed by at least one other developer.
Continuous Integration (CI): Set up automated builds and test runs on every pull request.
Static Analysis: Use linters and type checkers to enforce code quality and catch errors early.
Documentation Verification: Keep documentation up to date and review it as part of the change process.
Git Automation Rules
Define clear rules for using Git and automated pipelines to streamline development. Adopt a consistent branching strategy and enforce commit message conventions. Automate repetitive tasks like merging, tagging, and deployments through scripts or CI/CD workflows.

Branching Model: Use a well-defined branching strategy (e.g., Gitflow, GitHub Flow).
Commit Message Conventions: Include ticket IDs, scope, and concise descriptions in commits.
Pull Requests (PRs): Require PRs for all changes. Use templates to ensure consistent information.
Automated Workflows: Use CI/CD tools to automate testing, linting, and deployments on push events.
Pre-commit Hooks: Configure hooks (e.g., linting, format checks) to run locally before commits.
Project Architecture and Stack Overview
Provide a high-level description of the system architecture and technology stack. Clearly outline the responsibilities of each layer and the tools used. This helps all developers understand how components interact and what technologies to use.

Frontend: (e.g., React, TypeScript) for UI development; state management (Redux, MobX) and styling frameworks (CSS Modules, Tailwind).
Backend/API: (e.g., Node.js, Express, GraphQL) for business logic, data processing, and API endpoints.
Database: (e.g., PostgreSQL, MongoDB) for data storage; use an ORM/ODM (like Sequelize or TypeORM) if applicable.
Infrastructure: Cloud platform (AWS, Azure, GCP) and containerization (Docker, Kubernetes); consider serverless options for microservices.
Communication: REST or GraphQL APIs, WebSockets, or pub/sub for real-time interactions.
Security Layer: Authentication mechanisms (OAuth 2.0, JWT), authorization roles, and encryption protocols (TLS).
File Structure Overview
Maintain a consistent file and directory structure across the project. Group related files and modules logically. Include a root-level README and ensure each major directory has an overview if needed.

Root Directory: Contains main configuration files (e.g., package.json), and documentation.
src/ Folder: Primary source code directory.
components/: Reusable UI components.
pages/ or views/: Top-level pages or route handlers.
hooks/: Custom React hooks or shared logic hooks.
services/ or api/: Modules handling API calls and business logic.
utils/: Utility functions and shared helpers.
assets/: Static assets (images, fonts, etc.).
styles/: Global stylesheets or theme files.
tests/ Folder: Contains unit and integration test suites.
Configuration Files: .env folder for environment-specific settings and secrets (do not commit secrets).
Structure and Organization Best Practices
Encourage modular design and clear separation of concerns. Follow naming conventions and keep consistent code patterns. This makes the codebase easier to navigate and maintain.

Single Responsibility: Each module or component should have a single well-defined purpose.
Naming Conventions: Use consistent naming (e.g., for functions and variables).
Modularity: Group code by feature or domain to avoid large monolithic files.
Separation of Concerns: Keep business logic separate from presentation logic.
Documentation: Document public functions and APIs. Maintain an updated README and comment complex logic.
Dependency Management: Keep dependencies updated. Remove unused packages.
Code Style: Enforce consistent formatting with tools (Prettier, ESLint) and follow language-specific best practices.
Configuration and Environment Instructions
Specify how to configure the application and manage different environments (development, staging, production). Provide details on environment variables, configuration files, and secret management.

Environment Variables: Store secrets and config in commit-sensitive information to version control.
Configuration Files: Use separate config files (e.g., .env) or environment-based logic within code.
Setup Instructions: Document steps to set up the project locally (install dependencies, set environment variables, run database migrations, etc.).
Environment Parity: Keep development, staging, and production environments as similar as possible.
Example: Provide a sample .env.example with required variables.
Secret Management: Use vaults or encrypted storage for production secrets (e.g., AWS Secrets Manager, HashiCorp Vault).
Consistency and Reuse Strategies
Promote consistency across the codebase and maximize code reuse. Use shared libraries or components for common functionality. Enforce standards through tooling and guidelines.

Component Libraries: Centralize reusable UI elements and styles.
Shared Utilities: Keep common helper functions in a shared utils or lib directory.
Coding Standards: Use linting rules and code formatters consistently across the team.
Documentation: Maintain style guides or wikis for patterns and best practices.
DRY Principle: Avoid duplication by abstracting repeated logic into functions or components.
Versioning: Use semantic versioning for shared modules and document changes.
Debugging Protocols
Establish a systematic approach to identify and fix issues. Utilize debugging tools and logging to trace problems, and document common troubleshooting steps.

Logging: Implement structured and leveled logging (info, warn, error). Include context (timestamps, request IDs) in log messages.
Error Tracking: Use an error monitoring service (e.g., Sentry, Rollbar) to capture and report exceptions.
Reproduction: Always reproduce the issue in a controlled environment before fixing.
Browser DevTools/Debugger: Use breakpoints and network inspectors to analyze frontend issues.
Server Debugging: Attach debuggers or use log statements in the backend to trace execution flow.
Isolation: Isolate the faulty component or service by writing minimal reproductions or targeted tests.
Communication: When stuck, discuss the issue with peers or consult documentation.
Post-mortem: After resolving, document the cause and solution in the issue tracker for future reference.
Safe Change Methodology
Classify changes by risk level and handle each with appropriate safeguards. Define three levels of change with escalating precautions to protect production systems.

Level 1 (Low Risk): Small, isolated changes (e.g., UI text updates, minor refactors).

Require automated tests.
Single-person review and approval.
Deploy as usual after CI passes successfully.
Level 2 (Medium Risk): Moderate changes (e.g., new features, minor database migrations).

Require testing in a feature branch and multiple reviewers.
Perform integration tests and gather stakeholder sign-off.
Deploy during off-peak hours when possible.
Level 3 (High Risk): Major changes (e.g., architecture overhaul, large schema migrations).

Require design review and a detailed rollout plan.
Involve QA, security, and product teams in the review.
Use feature flags or canary deployments, and prepare rollback procedures.
Change-type Checklists
Authentication Changes
[ ] Review and update authentication flows (login, logout, password reset).
[ ] Validate security policies and access controls.
[ ] Add tests for auth logic and edge cases.
[ ] Ensure tokens/sessions are stored securely and expire correctly.
[ ] Update documentation and migration guides for auth changes.
[ ] Notify security and QA teams for a focused review.
Component Changes
[ ] Match UI against design specifications and ensure responsiveness.
[ ] Verify accessibility (ARIA labels, keyboard navigation).
[ ] Update or add unit and snapshot tests for UI changes.
[ ] Check for visual regressions using Storybook or snapshot tests.
[ ] Test cross-browser compatibility.
[ ] Document any new component props or usage patterns.
Hook Changes
[ ] Follow the Rules of Hooks (only call hooks at top level or in custom hooks).
[ ] Write or update tests for hook behavior (mock providers or context as needed).
[ ] Verify dependencies in effects (useEffect) and memoizations (useMemo, useCallback).
[ ] Ensure hooks are well-documented and reusable.
[ ] Consider performance impacts (memoize results, avoid unnecessary re-renders).
[ ] Update documentation and examples for new hooks.
Emergency Protocols
Prepare for incidents with a clear response plan. Define roles, communication channels, and steps to mitigate critical issues quickly and safely.

[ ] Incident Identification: Detect issues via alerts and define severity levels (e.g., P1, P2).
[ ] Communication: Notify stakeholders and team channels (Slack, email, paging).
[ ] Immediate Response: Triage the issue, gather logs, and attempt quick fixes or rollback.
[ ] Escalation Path: Follow an escalation matrix to involve the right personnel (on-call engineers, leads).
[ ] Rollback Plan: Maintain scripts and procedures to revert deployments if needed.
[ ] Post-Incident Review: Document the incident, root cause, and improvements.
[ ] Process Update: Refine protocols based on lessons learned from the incident.
Quality Metrics
Monitor and enforce quality standards using measurable metrics. Track these metrics to drive improvements and ensure team performance.

Test Coverage: Aim for a high percentage (e.g., ≥80%) of code covered by automated tests.
Code Review Compliance: Ensure all changes are reviewed; track PR turnaround time.
Linting & Static Analysis: Maintain zero critical linting errors and address warnings promptly.
Build Success Rate: Ensure CI builds pass consistently; minimize flaky tests.
Performance Benchmarks: Monitor app performance (page load times, API latency) against targets.
Error Rate: Track production error rates and set alert thresholds.
Security Vulnerabilities: Track identified issues; resolve all high/critical findings promptly.
Release Stability: Measure rollback frequency or hotfix count to gauge release quality.
Implementation Guides
Refactor Guide
Assess: Analyze the existing code and identify areas for improvement.
Test: Write or update automated tests to cover current functionality.
Implement: Make incremental changes, verifying behavior after each step.
Verify: Run all tests and fix any regressions.
Document: Update documentation and code comments to reflect the new structure.
Review: Perform peer review to ensure the refactor maintains functionality and performance.
New Feature Guide
Requirement Analysis: Gather and confirm requirements with stakeholders.
Design: Plan the feature architecture, data models, and user interface.
Branch: Create a feature branch with a descriptive name.
Develop: Implement the feature following coding standards.
Test: Write unit and integration tests; perform manual QA.
Documentation: Update code comments and README or user guides.
Review: Submit a pull request, address feedback, and ensure CI checks pass.
Release: Merge to main, deploy to staging, and verify before production rollout.
Bug Fix Guide
Reproduce: Isolate the problem and write a failing test if possible.
Diagnose: Trace the root cause using logs and debugging tools.
Fix: Correct the issue with minimal, focused changes.
Test: Add or update tests to cover the bug scenario and prevent regressions.
Review: Submit a pull request and get peer review, explaining the issue and fix.
Deploy: Merge and deploy the fix. Verify the issue is resolved in all environments.
Retrospective: Analyze why the bug occurred and improve processes accordingly.
Mantras and Operational Principles
Adopt guiding principles that shape the team’s mindset and workflow. Use these mantras to align development culture and decision-making.

KISS (Keep It Simple, Stupid): Prefer simple solutions over complex ones.
YAGNI (You Aren't Gonna Need It): Don't implement features until they are necessary.
DRY (Don't Repeat Yourself): Eliminate duplication through reuse.
First-time Quality: Aim to write correct code on the first attempt.
Fail Fast, Learn Fast: Detect issues early and iterate quickly.
Code as Communication: Write clear, readable code as if it were documentation.
Test Early, Test Often: Integrate testing into every stage of development.
Continuous Improvement: Regularly reflect and refine processes and code.
Security Tools
Integrate security scanning and analysis into the development process. Use specialized tools to identify vulnerabilities and enforce best practices.

Static Analysis (SAST): Use tools like SonarQube or ESLint (with security plugins) to catch insecure patterns.
Dependency Scanning: Automate checks for vulnerable packages (npm audit, Snyk, Dependabot).
Secrets Detection: Add Git hooks or CI checks to prevent committing secrets or sensitive data.
Penetration Testing: Periodically perform or commission pen tests for critical components.
Security Linters: Use specialized linters for infrastructure and config files (e.g., tfsec, kube-linter).
Encryption and TLS: Enforce HTTPS for all network communication; use encryption libraries for sensitive data.
Templates for Actions and Plans
Provide reusable templates for common workflows. Examples include pull request templates, issue templates, and release or deployment plans.

Pull Request Template:
Issue/Plan Template:
Tasks
[ ] Task 1
[ ] Task 2
[ ] Task 3