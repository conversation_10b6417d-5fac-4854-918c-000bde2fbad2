# Plano de Atualização do Frontend - Locate-Family-Connect

**Data:** 04/06/2025  
**Autor:** Time de Desenvolvimento  
**Versão:** 1.0

## Visão Geral

Este documento detalha o plano estratégico para integrar os novos campos do banco de dados (last_login_at, login_count, status) à interface do usuário do sistema Locate-Family-Connect. O plano segue o protocolo anti-erro estabelecido e visa garantir uma implementação segura e não-disruptiva.

## 1. Atualizações de Tipos

### 1.1 Atualização dos tipos no arquivo `src/types/database.ts`

```typescript
// Atualizações necessárias no UserProfile
export interface UserProfile {
  id: string | number;
  user_id?: string;
  full_name: string;
  phone?: string | null;
  email: string;
  user_type: string;
  created_at?: string | null;
  updated_at?: string | null;
  // Novos campos a serem adicionados
  status?: 'active' | 'inactive' | 'suspended' | 'pending';
  last_login_at?: string | null;
  login_count?: number;
}

// Atualizações necessárias no GuardianData
export interface GuardianData {
  id: string;
  student_id: string | null;
  guardian_id: string | null;
  email: string;
  full_name: string;
  phone?: string | null;
  is_active: boolean;
  created_at: string;
  relationship_type: string | null;
  status?: 'pending' | 'active' | 'rejected';
  // Novos campos a serem adicionados
  birth_date?: string | null;
  cpf?: string | null;
  updated_at?: string | null;
}
```

## 2. Integração no Contexto de Autenticação

### 2.1 Atualizações no `UnifiedAuthContext.tsx`

- **Modificação no `fetchUserDebounced`**:
  ```typescript
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*, last_login_at, login_count, status')
    .eq('user_id', session.user.id)
    .single();
  ```

- **Expansão do `ExtendedUser`** em `src/types/auth.ts`:
  ```typescript
  export interface ExtendedUser extends User {
    id: string;
    email: string;
    user_metadata: UserMetadata;
    // Novos campos
    status?: 'active' | 'inactive' | 'suspended' | 'pending';
    last_login_at?: string | null;
    login_count?: number;
  }
  ```

## 3. Componentes que Precisam de Atualização

### 3.1 Componentes de Perfil

1. **Criar novo componente**: `ProfileStatusBadge.tsx`
   - Exibir status do usuário de forma visual com cores apropriadas
   - Utilizar em vários pontos do sistema para consistência

   ```tsx
   // Exemplo básico de implementação
   export const ProfileStatusBadge: React.FC<{ status?: string }> = ({ status = 'active' }) => {
     const getStatusColor = () => {
       switch (status) {
         case 'active': return 'bg-green-500';
         case 'inactive': return 'bg-yellow-500';
         case 'suspended': return 'bg-red-500';
         case 'pending': return 'bg-blue-500';
         default: return 'bg-gray-500';
       }
     };
     
     return (
       <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getStatusColor()}`}>
         {status.charAt(0).toUpperCase() + status.slice(1)}
       </span>
     );
   };
   ```

2. **Criar novo componente**: `LastLoginInfo.tsx`
   - Exibir informação de último login formatada
   - Possibilidade de mostrar alerta se o último login for muito antigo

   ```tsx
   // Exemplo básico de implementação
   export const LastLoginInfo: React.FC<{ lastLoginDate?: string, showWarning?: boolean }> = ({ 
     lastLoginDate, 
     showWarning = true 
   }) => {
     if (!lastLoginDate) return <span className="text-gray-400">Nunca acessou</span>;
     
     const loginDate = new Date(lastLoginDate);
     const daysSinceLogin = Math.floor((Date.now() - loginDate.getTime()) / (1000 * 60 * 60 * 24));
     
     return (
       <div>
         <span>Último acesso: {loginDate.toLocaleDateString('pt-BR')}</span>
         {showWarning && daysSinceLogin > 30 && (
           <span className="ml-2 text-red-500 text-xs">
             Inativo há {daysSinceLogin} dias
           </span>
         )}
       </div>
     );
   };
   ```

### 3.2 Componentes de Guardião

1. **Atualizar `GuardianCard.tsx`**:
   - Adicionar exibição de CPF (com máscara de privacidade)
   - Incluir data de nascimento formatada
   - Mostrar status atual com indicador visual

2. **Atualizar `GuardianList.tsx`**:
   - Incluir status no filtro/busca
   - Adicionar indicadores visuais para status

3. **Atualizar `AddGuardianForm.tsx`**:
   - Adicionar campos de CPF e data de nascimento no formulário
   - Implementar validação de CPF no frontend usando a mesma lógica do backend

## 4. Dashboard e Alertas para Monitoramento

### 4.1 Criar um componente de Dashboard para Administradores

```tsx
// Futuro componente: AdminDashboard.tsx
// Vai exibir:
// 1. Usuários por status (gráfico)
// 2. Usuários inativos há mais de 30 dias (lista)
// 3. Estatísticas de login (frequência, média por período)
```

### 4.2 Sistema de Alertas para Contas Inativas

1. **Criar hook**: `useInactiveAccounts.tsx`
   ```typescript
   export const useInactiveAccounts = (daysThreshold = 30) => {
     const [inactiveUsers, setInactiveUsers] = useState<UserProfile[]>([]);
     const [loading, setLoading] = useState(true);
     
     useEffect(() => {
       const fetchInactiveUsers = async () => {
         setLoading(true);
         try {
           const thirtyDaysAgo = new Date();
           thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - daysThreshold);
           
           const { data, error } = await supabase
             .from('profiles')
             .select('*')
             .lt('last_login_at', thirtyDaysAgo.toISOString())
             .eq('status', 'active');
             
           if (error) throw error;
           setInactiveUsers(data || []);
         } catch (error) {
           console.error('Erro ao buscar contas inativas:', error);
         } finally {
           setLoading(false);
         }
       };
       
       fetchInactiveUsers();
     }, [daysThreshold]);
     
     return { inactiveUsers, loading };
   };
   ```

2. **Componente de Alerta**: `InactiveAccountsAlert.tsx`
   - Exibir alertas para administradores sobre contas inativas
   - Opções para enviar lembretes, reativar ou desativar contas

## 5. Consultas SQL para Análises de Atividade

Para suportar as análises de engajamento, implementaremos estas consultas:

```sql
-- Para uso futuro em análises de atividades
-- 1. Consulta de usuários por frequência de login
SELECT 
  p.full_name, 
  p.email, 
  p.user_type,
  p.status,
  p.login_count,
  p.last_login_at,
  EXTRACT(DAY FROM NOW() - p.last_login_at::timestamptz) as dias_desde_ultimo_login
FROM 
  profiles p
WHERE 
  p.last_login_at IS NOT NULL
ORDER BY 
  dias_desde_ultimo_login DESC;

-- 2. Alerta para contas inativas há mais de 30 dias
SELECT 
  p.id,
  p.full_name, 
  p.email,
  p.last_login_at,
  EXTRACT(DAY FROM NOW() - p.last_login_at::timestamptz) as dias_inativo
FROM 
  profiles p
WHERE 
  p.status = 'active' 
  AND p.last_login_at < NOW() - INTERVAL '30 days'
ORDER BY 
  p.last_login_at ASC;
```

## 6. Plano de Implementação Incremental

Para seguir o protocolo anti-erro, a implementação será feita em fases:

### Fase 1: Preparação da Base
1. Atualizar definições de tipos
2. Ajustar o contexto de autenticação 
3. Criar componentes auxiliares (badges, formatadores)

### Fase 2: Integração nos Componentes Existentes
1. Atualizar um componente por vez, começando pelos menos críticos
2. Realizar testes extensivos após cada componente

### Fase 3: Implementação de Análises e Alertas
1. Desenvolver dashboard para administradores
2. Implementar sistema de alertas para contas inativas
3. Criar ferramentas para análise de engajamento

## 7. Considerações sobre Segurança e Desempenho

1. **Segurança**: 
   - Garantir que dados sensíveis como CPF sejam exibidos apenas para usuários autorizados
   - Implementar verificações de permissão para visualização de status de outros usuários
   - Limitar acesso a relatórios de inatividade apenas para admins

2. **Desempenho**: 
   - Implementar cache para consultas de atividade que podem ser intensivas
   - Considerar índices adicionais no banco de dados se necessário
   - Paginar resultados de usuários inativos para evitar sobrecarga

3. **UX**: 
   - Garantir que alertas não sejam intrusivos para usuários regulares
   - Usar códigos de cores consistentes para indicadores de status
   - Fornecer tooltips explicativos para métricas de atividade

## 8. Testes Necessários

1. Testes unitários para novos componentes
2. Testes de integração para verificar se os novos campos são carregados corretamente
3. Testes de desempenho para consultas de análise de atividade
4. Testes de usuário para garantir que as informações são compreendidas corretamente

## 9. Próximos Passos

1. Revisão do plano pela equipe de desenvolvimento
2. Priorização das fases de implementação
3. Desenvolvimento de protótipos de interface para validação
4. Implementação e testes em ambiente de desenvolvimento
5. Documentação de mudanças para usuários finais

---

**IMPORTANTE:** Este plano se refere apenas às mudanças no frontend. As mudanças no banco de dados (adição de campos, triggers, etc.) já foram implementadas com sucesso.
