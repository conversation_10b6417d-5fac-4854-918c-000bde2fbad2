
export const useLayoutUtils = (deviceType: string, orientation: 'portrait' | 'landscape') => {
  // Adjust the padding of the main content based on device and orientation
  const getMainPadding = () => {
    if (deviceType === 'mobile') {
      return orientation === 'landscape' ? 'p-1 pb-16' : 'p-2 pb-20';
    } else if (deviceType === 'tablet') {
      return orientation === 'landscape' ? 'p-2 pb-16' : 'p-3 pb-20';
    }
    return 'p-4 lg:p-6 xl:p-8 md:pb-8';
  };

  return { getMainPadding };
};
