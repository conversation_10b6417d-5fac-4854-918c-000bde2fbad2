# Frontend Improvement Overview

Este documento resume as rotas e componentes do projeto com foco nas melhorias de usabilidade e responsividade. As principais mudanças implementadas visam destacar o mapa em todas as páginas e garantir que a interface funcione bem em diferentes tamanhos de tela e orientações.

## Rotas e Páginas

| Rota/Página                | Componentes Principais                     | Melhorias Planejadas |
|----------------------------|--------------------------------------------|----------------------|
| `/student-dashboard`       | `StudentLocationTabs`, `StudentLocationMap`| Destaque do mapa, layout responsivo |
| `/student-map/:id?`        | `StudentMapSection`, `LocationHistoryList` | Ajuste de altura por dispositivo |
| `/guardian-dashboard`      | `StudentMapTabs`, `StudentMapSection`      | Mapa com realce visual |
| Outros painéis             | `StudentMapSection`                        | Mapa reutilizado com classes de destaque |

## Componentes Revisados

- **MapView**
  - Adicionada classe `map-highlight` para borda azul e sombra.
  - Implementada classe `map-responsive` para altura adaptativa e suporte a orientação landscape.

- **StudentMapSection**
  - Substituída altura fixa (`h-[600px]`) pelo uso das novas classes responsivas.

- **StudentLocationMap**
  - Container do mapa agora utiliza `map-responsive` para evitar sobreposição e manter tamanho consistente.

- **StudentMap (página)**
  - Card do mapa recebe `map-highlight`, mantendo estilo consistente em todo o app.

## Classes CSS

```css
.map-highlight {
  border: 2px solid rgb(59 130 246); /* blue-500 */
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
  border-radius: 0.5rem;
}

.map-responsive {
  min-height: 300px;
  height: 55vh;
}

@media (orientation: landscape) and (max-width: 1024px) {
  .map-responsive {
    height: 65vh;
  }
}
```

Essas classes garantem que o mapa tenha destaque visual e tamanho adequado em telas de celular, tablet (retrato e paisagem) e desktop.

## Próximos Passos

1. Revisar outros componentes para usar `map-highlight` quando exibirem mapas.
2. Testar em dispositivos reais (Android e iOS) em diferentes orientações.
3. Ajustar os demais layouts (cards, listas) para evitar sobreposição em resoluções menores.
