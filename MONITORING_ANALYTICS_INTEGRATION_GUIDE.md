# 🔧 Guia de Integração: Monitoramento e Analytics

Este guia demonstra como integrar **Sentry**, **PostHog**, **TestCafe** e **Redis** ao projeto EduConnect.

## 📋 **Visão Geral das Ferramentas**

| Ferramenta | Propósito | Benefícios para EduConnect |
|------------|-----------|---------------------------|
| 🔍 **Sentry** | Monitoramento de erros | Detectar bugs em produção, tracking de performance, alertas |
| 📊 **PostHog** | Análise de comportamento | Entender uso do app, A/B testing, feature flags |
| 🧪 **TestCafe** | Testes cross-browser | Garantir compatibilidade, testes E2E automatizados |
| 🔄 **Redis** | Cache distribuído | Melhorar performance, cache de sessões/localizações |

---

## 🔍 **1. SENTRY - Monitoramento de Erros**

### **Instalação**
```bash
npm install @sentry/react @sentry/tracing
```

### **Configuração**
```typescript
// src/lib/monitoring/sentry-config.ts
import * as Sentry from "@sentry/react";
import { BrowserTracing } from "@sentry/tracing";

export const initSentry = () => {
  Sentry.init({
    dsn: process.env.VITE_SENTRY_DSN,
    environment: process.env.NODE_ENV,
    integrations: [
      new BrowserTracing(),
    ],
    tracesSampleRate: 1.0,
  });
};
```

### **Variáveis de Ambiente**
```env
# .env
VITE_SENTRY_DSN=https://<EMAIL>/project-id
```

### **Integração com ErrorFallback**
```typescript
// src/components/common/ErrorFallback.tsx (já existe)
import { captureError } from '@/lib/monitoring/sentry-config';

const ErrorFallback = ({ error, resetErrorBoundary }) => {
  useEffect(() => {
    if (error) {
      captureError(error, { 
        component: 'ErrorFallback',
        timestamp: new Date().toISOString()
      });
    }
  }, [error]);

  return (
    // JSX do componente...
  );
};
```

### **Uso nos Serviços**
```typescript
// src/lib/services/location/LocationService.ts
import { captureError } from '@/lib/monitoring/sentry-config';

export class LocationService {
  async shareLocation() {
    try {
      // lógica de compartilhamento
    } catch (error) {
      captureError(error, {
        service: 'LocationService',
        method: 'shareLocation',
        userId: getCurrentUserId()
      });
      throw error;
    }
  }
}
```

---

## 📊 **2. POSTHOG - Análise de Comportamento**

### **Instalação**
```bash
npm install posthog-js
```

### **Configuração**
```typescript
// src/lib/analytics/posthog-config.ts
import posthog from 'posthog-js';

export const initPostHog = () => {
  posthog.init(process.env.VITE_POSTHOG_KEY, {
    api_host: 'https://app.posthog.com',
    capture_pageview: true,
    session_recording: {
      enabled: true,
      maskAllText: true, // Privacy compliant
    }
  });
};
```

### **Variáveis de Ambiente**
```env
# .env
VITE_POSTHOG_KEY=phc_your-project-key
VITE_POSTHOG_HOST=https://app.posthog.com
```

### **Tracking de Eventos Específicos**
```typescript
// Exemplo de uso em componentes
import { trackLocationShare, trackAuthEvent } from '@/lib/analytics/posthog-config';

// No componente de localização
const handleLocationShare = () => {
  trackLocationShare('manual', studentId);
  // lógica de compartilhamento
};

// No componente de auth
const handleLogin = () => {
  trackAuthEvent('login', userType);
  // lógica de login
};
```

### **Feature Flags para A/B Testing**
```typescript
// Exemplo de feature flag
import { getFeatureFlag } from '@/lib/analytics/posthog-config';

const MapComponent = () => {
  const showNewMapUI = getFeatureFlag('new-map-interface');
  
  return showNewMapUI ? <NewMapUI /> : <OriginalMapUI />;
};
```

---

## 🧪 **3. TESTCAFE - Testes Cross-Browser**

### **Instalação**
```bash
npm install --save-dev testcafe @types/testcafe
```

### **Configuração**
```json
// .testcaferc.json
{
  "browsers": ["chrome", "firefox", "safari"],
  "src": ["tests/e2e/testcafe/**/*.test.ts"],
  "screenshots": {
    "path": "tests/screenshots/",
    "takeOnFails": true
  },
  "videoPath": "tests/videos/",
  "concurrency": 2,
  "quarantineMode": true
}
```

### **Scripts do Package.json**
```json
{
  "scripts": {
    "test:e2e": "testcafe",
    "test:e2e:chrome": "testcafe chrome tests/e2e/testcafe/",
    "test:e2e:mobile": "testcafe chrome:emulation:device=iphone X tests/e2e/testcafe/",
    "test:e2e:headless": "testcafe chrome:headless tests/e2e/testcafe/"
  }
}
```

### **Exemplo de Teste Específico**
```typescript
// tests/e2e/testcafe/location-sharing.test.ts
import { Selector, t } from 'testcafe';

fixture('EduConnect - Compartilhamento de Localização')
  .page('http://localhost:4000/student-dashboard')
  .beforeEach(async t => {
    // Login como estudante
    await t
      .typeText('[data-testid="email"]', '<EMAIL>')
      .typeText('[data-testid="password"]', 'password')
      .click('[data-testid="login-button"]');
  });

test('Deve compartilhar localização manualmente', async t => {
  await t
    .click('[data-testid="share-location-button"]')
    .expect(Selector('[data-testid="location-shared-success"]').exists).ok()
    .expect(Selector('[data-testid="last-location-time"]').textContent).contains('agora');
});

test('Deve funcionar em dispositivos móveis', async t => {
  await t.resizeWindow(375, 667);
  
  await t
    .expect(Selector('[data-testid="mobile-location-button"]').visible).ok()
    .click('[data-testid="mobile-location-button"]')
    .expect(Selector('[data-testid="location-shared-mobile"]').exists).ok();
});
```

### **Testes de Performance**
```typescript
// tests/e2e/testcafe/performance.test.ts
test('Dashboard deve carregar rapidamente', async t => {
  const startTime = Date.now();
  
  await t.expect(Selector('[data-testid="dashboard-loaded"]').exists).ok();
  
  const loadTime = Date.now() - startTime;
  await t.expect(loadTime).lt(3000, 'Dashboard deve carregar em menos de 3 segundos');
});
```

---

## 🔄 **4. REDIS - Cache Distribuído**

### **Instalação**
```bash
npm install ioredis
npm install --save-dev @types/ioredis
```

### **Docker Setup**
```yaml
# docker-compose.yml (adicionar ao existente)
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
volumes:
  redis_data:
```

### **Variáveis de Ambiente**
```env
# .env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-password
REDIS_DB=0
```

### **Uso nos Serviços**
```typescript
// src/lib/services/location/LocationService.ts
import { redisManager } from '@/lib/cache/redis-manager';

export class LocationService {
  async getStudentLocation(studentId: string) {
    // Tentar buscar do cache primeiro
    const cached = await redisManager.getLatestLocation(studentId);
    if (cached) {
      console.log('Location obtida do cache');
      return cached;
    }
    
    // Se não estiver em cache, buscar do banco
    const location = await this.fetchFromDatabase(studentId);
    
    // Salvar no cache para próximas consultas
    await redisManager.cacheLocation(studentId, location);
    
    return location;
  }
}
```

### **Cache de Sessões**
```typescript
// src/contexts/UnifiedAuthContext.tsx
import { redisManager } from '@/lib/cache/redis-manager';

export const UnifiedAuthProvider = ({ children }) => {
  const login = async (credentials) => {
    const user = await authenticateUser(credentials);
    
    // Cache da sessão do usuário
    await redisManager.cacheUserSession(user.id, {
      userId: user.id,
      email: user.email,
      role: user.role,
      lastActivity: new Date().toISOString()
    });
    
    setUser(user);
  };
};
```

---

## 🚀 **5. INTEGRAÇÃO COMPLETA**

### **Inicialização no App.tsx**
```typescript
// src/App.tsx
import { initSentry } from '@/lib/monitoring/sentry-config';
import { initPostHog } from '@/lib/analytics/posthog-config';

// Inicializar monitoramento
initSentry();
initPostHog();

function App() {
  return (
    <Sentry.ErrorBoundary fallback={ErrorFallback}>
      <QueryProvider>
        <UnifiedAuthProvider>
          {/* Resto da aplicação */}
        </UnifiedAuthProvider>
      </QueryProvider>
    </Sentry.ErrorBoundary>
  );
}
```

### **Health Check Endpoint**
```typescript
// src/lib/monitoring/health-check.ts
import { redisManager } from '@/lib/cache/redis-manager';

export const performHealthCheck = async () => {
  const checks = {
    redis: await redisManager.healthCheck(),
    sentry: !!process.env.VITE_SENTRY_DSN,
    posthog: !!process.env.VITE_POSTHOG_KEY,
    database: true, // implementar check do Supabase
  };
  
  const allHealthy = Object.values(checks).every(Boolean);
  
  return {
    status: allHealthy ? 'healthy' : 'degraded',
    checks,
    timestamp: new Date().toISOString()
  };
};
```

---

## 📊 **6. DASHBOARDS E MONITORAMENTO**

### **Métricas Importantes para EduConnect**

#### **Sentry Dashboard**
- Erros de geolocalização
- Falhas de autenticação
- Problemas de conectividade
- Performance de carregamento

#### **PostHog Analytics**
- Taxa de compartilhamento de localização
- Frequência de uso por tipo de usuário
- Funil de conversão de convites
- Tempo médio na aplicação

#### **TestCafe Reports**
- Compatibilidade entre navegadores
- Performance em dispositivos móveis
- Cobertura de testes E2E
- Tempo de execução dos testes

#### **Redis Metrics**
- Hit rate do cache
- Tempo de resposta
- Uso de memória
- Conexões ativas

---

## 🛡️ **7. BOAS PRÁTICAS E SEGURANÇA**

### **Privacy & LGPD Compliance**
```typescript
// Configuração de privacidade
const privacy = {
  sentry: {
    beforeSend: (event) => {
      // Filtrar dados sensíveis
      if (event.user) {
        delete event.user.email;
        delete event.user.ip_address;
      }
      return event;
    }
  },
  posthog: {
    property_blacklist: ['$email', '$ip', 'personal_data'],
    session_recording: {
      maskAllText: true,
      maskAllInputs: true
    }
  }
};
```

### **Alertas e Notificações**
```typescript
// Configurar alertas críticos
const criticalAlerts = {
  sentry: [
    'Erro de localização > 10/min',
    'Falha de login > 5/min',
    'Erro de database > 3/min'
  ],
  posthog: [
    'Queda na taxa de compartilhamento > 20%',
    'Aumento no tempo de carregamento > 50%'
  ],
  redis: [
    'Cache hit rate < 80%',
    'Conexões falhando > 5/min'
  ]
};
```

---

## 📝 **8. COMANDOS ÚTEIS**

```bash
# Desenvolvimento
npm run dev                    # Servidor de desenvolvimento
npm run test:e2e              # Testes cross-browser
npm run test:e2e:mobile       # Testes em dispositivos móveis

# Produção
npm run build                 # Build otimizado
npm run preview               # Preview da build
docker-compose up redis       # Iniciar Redis

# Monitoramento
npm run health-check          # Verificar saúde do sistema
npm run analytics:report      # Relatório de analytics
npm run sentry:release        # Create new Sentry release
```

---

## 🎯 **9. PRÓXIMOS PASSOS**

1. **Fase 1**: Implementar Sentry para monitoramento básico
2. **Fase 2**: Adicionar PostHog para analytics de comportamento
3. **Fase 3**: Configurar TestCafe para testes automatizados
4. **Fase 4**: Implementar Redis para cache de performance
5. **Fase 5**: Configurar dashboards e alertas
6. **Fase 6**: Otimizar baseado em métricas coletadas

---

## 🔗 **Recursos Adicionais**

- [Documentação Sentry React](https://docs.sentry.io/platforms/javascript/guides/react/)
- [PostHog JavaScript SDK](https://posthog.com/docs/libraries/js)
- [TestCafe User Guide](https://testcafe.io/documentation/402635/guides)
- [Redis Best Practices](https://redis.io/docs/manual/patterns/)

---

**📧 Suporte**: Para dúvidas sobre implementação, consulte a documentação específica de cada ferramenta ou abra uma issue no repositório. 