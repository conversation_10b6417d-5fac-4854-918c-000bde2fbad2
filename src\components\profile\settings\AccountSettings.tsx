
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { User, Lock, Shield, LogOut } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';

export const AccountSettings: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const { user } = useUser();
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handlePasswordChange = async () => {
    if (!passwordForm.currentPassword || !passwordForm.newPassword) {
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: t('errors.required'),
      });
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: t('errors.passwordMismatch'),
      });
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: t('errors.passwordTooShort'),
      });
      return;
    }

    setIsChangingPassword(true);

    try {
      const { data, error } = await supabase.functions.invoke('change-password', {
        body: {
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword,
        },
      });

      if (error) throw error;

      if (data?.success) {
        toast({
          title: t('common.success'),
          description: t('messages.passwordChanged', 'Senha alterada com sucesso.'),
        });
        setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
      } else {
        throw new Error(data?.message || t('errors.serverError'));
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: error.message || t('errors.operationFailed'),
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleTwoFactorSetup = () => {
    toast({
      title: t('common.attention'),
      description: t('messages.inDevelopment'),
    });
  };

  const handleManageSessions = () => {
    toast({
      title: t('common.attention'),
      description: t('messages.inDevelopment'),
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          {t('profile.accountSettings')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <Dialog>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start"
              >
                <Lock className="h-4 w-4 mr-2" />
                {t('profile.changePassword')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t('profile.changePassword')}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="currentPassword">{t('profile.currentPassword')}</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    value={passwordForm.currentPassword}
                    onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                    placeholder={t('profile.currentPasswordPlaceholder')}
                  />
                </div>
                <div>
                  <Label htmlFor="newPassword">{t('profile.newPassword')}</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={passwordForm.newPassword}
                    onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                    placeholder={t('profile.newPasswordPlaceholder')}
                  />
                </div>
                <div>
                  <Label htmlFor="confirmPassword">{t('profile.confirmNewPassword')}</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={passwordForm.confirmPassword}
                    onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    placeholder={t('profile.confirmNewPasswordPlaceholder')}
                  />
                </div>
                <Button
                  onClick={handlePasswordChange}
                  disabled={isChangingPassword}
                  className="w-full"
                >
                  {isChangingPassword
                    ? t('profile.changingPassword')
                    : t('profile.changePassword')}
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={handleTwoFactorSetup}
          >
            <Shield className="h-4 w-4 mr-2" />
            {t('profile.twoFactorAuth')}
          </Button>

          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={handleManageSessions}
          >
            <LogOut className="h-4 w-4 mr-2" />
            {t('profile.manageSessions')}
          </Button>
        </div>

        <Separator />

        <div className="space-y-2">
          <h4 className="text-sm font-medium">{t('profile.accountInfo')}</h4>
          <div className="text-sm text-muted-foreground space-y-1">
            <div>
              • {t('profile.lastProfileUpdate')}: {t('common.today')}
            </div>
            <div>
              • {t('profile.sessionStartedAt')}: {new Date().toLocaleDateString(i18n.language)}
            </div>
            <div>• {t('profile.connectedDevices')}: 1</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
