
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Edit2, Trash2, <PERSON>ert<PERSON>ircle, UserPlus, RefreshCw } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import EditStudentDialog from "./EditStudentDialog";
import DeleteStudentDialog from "./DeleteStudentDialog";
import { Student } from '@/types/auth';

export interface StudentsListProps {
  students: Student[];
  loading: boolean;
  error?: string | null;
  onSelectStudent?: (student: Student) => void;
  selectedStudent?: Student | null;
  onStudentUpdated?: () => void;
  onRefresh?: () => void;
}

const StudentsList = ({
  students,
  loading,
  error,
  onSelectStudent,
  selectedStudent,
  onStudentUpdated,
  onRefresh
}: StudentsListProps) => {
  const { t } = useTranslation();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [studentToEdit, setStudentToEdit] = useState<Student | null>(null);

  const handleEditClick = (student: Student) => {
    setStudentToEdit({...student});
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (student: Student) => {
    setStudentToEdit({...student});
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="space-y-4" data-cy="students-list">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl">{t('studentsList.title', 'Linked Students')}</CardTitle>
            {onRefresh && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
                className="h-8 w-8 p-0"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-[200px]" />
                    <Skeleton className="h-3 w-[150px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="flex items-center gap-2 text-red-500">
              <AlertCircle size={18} />
              <span>{error}</span>
            </div>
          ) : students.length === 0 ? (
            <div className="space-y-4">
              <div className="text-center py-8">
                <UserPlus className="mx-auto h-10 w-10 text-gray-400" />
                <p className="mt-2 text-gray-500">{t('studentsList.none', 'No students linked yet.')}</p>
                <p className="text-sm text-gray-500">{t('studentsList.helpTitle', 'How to add students')}</p>
              </div>
              
              <div className="border-t pt-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <UserPlus className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="font-medium text-blue-800 mb-2">
                        💡 {t('studentsList.helpTitle', 'How to add students')}
                      </p>
                      <div className="text-blue-700 space-y-1">
                        <p>• {t('studentsList.helpBullet1', 'Fill out the form above with the student details')}</p>
                        <p>• {t('studentsList.helpBullet2', 'Credentials are automatically emailed')}</p>
                        <p>• {t('studentsList.helpBullet3', 'Existing students will be linked to your account')}</p>
                        <p>• {t('studentsList.helpBullet4', 'You will receive confirmation when processed')}</p>
                      </div>
                      <div className="mt-3 pt-2 border-t border-blue-200">
                        <p className="text-xs text-blue-600">
                          ✅ {t('studentsList.helpFooter', 'System fully operational with multiple reliability levels')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {students.map((student) => (
                <div
                  key={student.id}
                  data-cy="student-item"
                  className={`
                    flex justify-between items-center p-3 rounded-md border
                    ${selectedStudent?.id === student.id ? 'bg-blue-50 border-blue-400 shadow-sm dark:bg-blue-900 dark:border-blue-400 dark:text-white' : 'hover:bg-gray-50 border-transparent'}
                    ${onSelectStudent ? 'cursor-pointer' : ''}
                    transition-all duration-200
                  `}
                  onClick={() => onSelectStudent && onSelectStudent(student)}
                >
                  <div>
                    <div className="font-medium" data-cy="student-name">{student.name}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-300" data-cy="student-email">{student.email}</div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditClick(student);
                      }}
                      className="dark:text-white"
                    >
                      <Edit2 size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-200 dark:hover:bg-red-900"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteClick(student);
                      }}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {studentToEdit && (
        <>
          <EditStudentDialog
            student={studentToEdit}
            open={isEditDialogOpen}
            onOpenChange={setIsEditDialogOpen}
            onSave={onStudentUpdated}
          />
          <DeleteStudentDialog
            student={studentToEdit}
            open={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
            onDelete={onStudentUpdated}
          />
        </>
      )}
    </div>
  );
};

export default StudentsList;
