
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * Base service class with common functionality
 */
export class BaseService {
  protected supabase = supabase;

  /**
   * Show error message to user
   */
  protected showError(message: string) {
    toast.error(message);
  }

  /**
   * Show success message to user
   */
  protected showSuccess(message: string) {
    toast.success(message);
  }

  /**
   * Get current authenticated user
   */
  protected async getCurrentUser() {
    const { data: { user }, error } = await this.supabase.auth.getUser();
    if (error || !user) {
      throw new Error('User not authenticated');
    }
    return user;
  }

  /**
   * Validate if a string is a valid UUID
   */
  protected isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }
}
