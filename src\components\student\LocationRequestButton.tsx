import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useLocationSharing } from "@/hooks/useLocationSharing";

interface LocationRequestButtonProps {
  onLocationShared?: () => void;
  studentName?: string;
  guardianEmail: string;
  disabled?: boolean;
}

const LocationRequestButton: React.FC<LocationRequestButtonProps> = ({
  onLocationShared,
  studentName = "Estudante",
  guardianEmail,
  disabled = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { shareLocation } = useLocationSharing();

  const handleShareLocation = async () => {
    if (!navigator.geolocation) {
      toast({
        title: "Erro",
        description: "Geolocalização não é suportada neste navegador.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          
          // Call with 4 arguments only
          const success = await shareLocation(
            latitude,
            longitude,
            guardianEmail,
            studentName
          );
          
          if (success && onLocationShared) {
            onLocationShared();
          }
        } catch (error) {
          console.error('Erro ao compartilhar localização:', error);
          toast({
            title: "Erro",
            description: "Não foi possível compartilhar a localização.",
            variant: "destructive"
          });
        } finally {
          setIsLoading(false);
        }
      },
      (error) => {
        setIsLoading(false);
        toast({
          title: "Erro de Localização",
          description: "Não foi possível obter sua localização.",
          variant: "destructive"
        });
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  };

  return (
    <Button
      onClick={handleShareLocation}
      disabled={isLoading || disabled}
    >
      {isLoading ? 'Compartilhando...' : 'Compartilhar Localização'}
    </Button>
  );
};

export default LocationRequestButton;
