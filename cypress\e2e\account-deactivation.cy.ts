/// <reference types="cypress" />

// ⚠️ Este teste NÃO remove dados reais. A solicitação de exclusão é interceptada e respondida com sucesso simulado.
// O fluxo garante que a UI permita ao estudante solicitar exclusão sem realmente apagar a conta.

describe.skip('Fluxo de Solicitação de Exclusão de Conta (Estudante)', () => {
  const studentEmail = '<EMAIL>';
  const studentPassword = '4EG8GsjBT5KjD3k';

  it('deve permitir que o estudante solicite exclusão da conta sem remover dados reais', () => {
    // Intercepta a chamada RPC de criação de solicitação
    cy.intercept('POST', '**/rest/v1/rpc/create_account_deletion_request*', (req) => {
      // Confirma que o corpo tem a estrutura esperada
      expect(req.body).to.have.property('p_reason');

      // Responde com sucesso simulado
      req.reply({
        statusCode: 200,
        body: { success: true }
      });
    }).as('createDeletion');

    // Login
    cy.visit('/login', { failOnStatusCode: false });
    cy.get('input[type="email"]').clear().type(studentEmail);
    cy.get('input[type="password"]').clear().type(studentPassword, { log: false });
    cy.contains('button', /entrar|login/i).click();

    // Confirma redirecionamento para dashboard de estudante
    cy.url({ timeout: 30000 }).should('include', '/student-dashboard');

    // Acessa página de perfil diretamente (mais estável)
    cy.visit('/profile');

    // Aguarda qualquer tab ficar visível para garantir que o componente foi montado
    cy.contains('button', /perfil/i, { timeout: 20000 }).should('be.visible');

    // Clica na aba "Privacidade" usando contains (mais tolerante a variações)
    cy.contains('button', /privacidade/i).click();

    // Aguarda o card aparecer e aciona o modal
    cy.get('[data-cy="delete-account-card"]', { timeout: 10000 }).should('be.visible').within(() => {
      cy.get('[data-cy="delete-account-button"]').click();
    });

    // Confirma ação no modal
    cy.get('[data-cy="confirm-delete-account-button"]').click();

    // Aguarda a chamada interceptada (garante que intercept realmente ocorreu)
    cy.wait('@createDeletion');

    // Verifica toast de sucesso ou erro
    cy.contains(/exclusão criada|erro ao solicitar exclusão/i, { timeout: 10000 }).should('be.visible');
  });
}); 