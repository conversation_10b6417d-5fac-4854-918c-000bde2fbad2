// @ts-nocheck
import { BaseService } from '../base/BaseService';

export interface RemovalRequest {
  id: string;
  student_name: string;
  student_email: string;
  reason?: string;
  created_at: string;
  expires_at: string;
  request_token: string;
}

export interface RemovalRequestResponse {
  success: boolean;
  message: string;
  request_id?: string;
  token?: string;
}

/**
 * Service for managing guardian removal requests
 */
export class RemovalRequestService extends BaseService {
  
  /**
   * Create a removal request (student side)
   */
  async requestGuardianRemoval(guardianId: string, reason?: string): Promise<RemovalRequestResponse> {
    try {
      console.log('🚀 [RemovalRequestService] INICIANDO requestGuardianRemoval');
      console.log('🚀 [RemovalRequestService] Parâmetros:', { guardianId, reason });
      
      console.log('🚀 [RemovalRequestService] Verificando usuário atual...');
      await this.getCurrentUser();
      console.log('🚀 [RemovalRequestService] Usuário verificado com sucesso');

      console.log('🚀 [RemovalRequestService] Chamando RPC request_guardian_removal...');
      const { data, error } = await this.supabase
        .rpc('request_guardian_removal', {
          p_guardian_id: guardianId,
          p_reason: reason
        });

      console.log('🚀 [RemovalRequestService] Resposta RPC:', { data, error });

      if (error) {
        console.error('🚀 [RemovalRequestService] ERRO NA RPC:', error);
        console.error('🚀 [RemovalRequestService] Detalhes do erro:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        this.showError('Erro ao solicitar remoção');
        return { success: false, message: error.message };
      }

      console.log('🚀 [RemovalRequestService] Dados recebidos:', data);

      if (!data || (Array.isArray(data) && data.length === 0)) {
        console.error('🚀 [RemovalRequestService] Resposta vazia ou inválida');
        return { success: false, message: 'Resposta inválida do servidor' };
      }

      const result = Array.isArray(data) ? data[0] : data;
      console.log('🚀 [RemovalRequestService] Resultado processado:', result);
      
      if (result.success) {
        console.log('🚀 [RemovalRequestService] Sucesso! Enviando notificação...');
        // Send email notification
        await this.sendRemovalNotification(result.request_id, result.token);
        this.showSuccess(result.message);
      } else {
        console.error('🚀 [RemovalRequestService] Falha na operação:', result.message);
        this.showError(result.message);
      }

      return {
        success: result.success,
        message: result.message,
        request_id: result.request_id,
        token: result.token
      };
    } catch (error: any) {
      console.error('🚀 [RemovalRequestService] EXCEÇÃO CAPTURADA:', error);
      console.error('🚀 [RemovalRequestService] Stack trace:', error.stack);
      this.showError('Erro ao processar solicitação');
      return { success: false, message: 'Erro interno do sistema' };
    }
  }

  /**
   * Send email notification for removal request
   */
  private async sendRemovalNotification(requestId: string, token: string): Promise<void> {
    try {
      // Get request details
      const { data: requestData, error: requestError } = await this.supabase
        .from('removal_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (requestError || !requestData) {
        console.error('[RemovalRequestService] Error fetching request details:', requestError);
        return;
      }

      // Call Edge Function to send email
      const { error: emailError } = await this.supabase.functions.invoke('notify-removal-request', {
        body: {
          studentName: requestData.student_name,
          guardianName: requestData.guardian_name,
          guardianEmail: requestData.guardian_email,
          reason: requestData.reason,
          requestToken: token
        }
      });

      if (emailError) {
        console.error('[RemovalRequestService] Error sending notification email:', emailError);
      } else {
        console.log('[RemovalRequestService] Notification email sent successfully');
      }
    } catch (error) {
      console.error('[RemovalRequestService] Error in sendRemovalNotification:', error);
    }
  }

  /**
   * Get pending removal requests for current guardian
   */
  async getGuardianRemovalRequests(): Promise<RemovalRequest[]> {
    try {
      await this.getCurrentUser();

      console.log('[RemovalRequestService] Fetching guardian removal requests');

      const { data, error } = await this.supabase
        .rpc('get_guardian_removal_requests');

      if (error) {
        console.error('[RemovalRequestService] Error fetching requests:', error);
        this.showError('Erro ao buscar solicitações');
        return [];
      }

      return data || [];
    } catch (error: any) {
      console.error('[RemovalRequestService] Error in getGuardianRemovalRequests:', error);
      this.showError('Erro ao acessar solicitações');
      return [];
    }
  }

  /**
   * Process removal request (approve/reject)
   */
  async processRemovalRequest(token: string, action: 'approve' | 'reject'): Promise<RemovalRequestResponse> {
    try {
      await this.getCurrentUser();

      console.log('[RemovalRequestService] Processing removal request:', { token, action });

      const { data, error } = await this.supabase
        .rpc('process_removal_request', {
          p_token: token,
          p_action: action
        });

      if (error) {
        console.error('[RemovalRequestService] Error processing request:', error);
        this.showError('Erro ao processar solicitação');
        return { success: false, message: error.message };
      }

      if (!data || (Array.isArray(data) && data.length === 0)) {
        return { success: false, message: 'Resposta inválida do servidor' };
      }

      const result = Array.isArray(data) ? data[0] : data;
      
      if (result.success) {
        this.showSuccess(result.message);
      } else {
        this.showError(result.message);
      }

      return {
        success: result.success,
        message: result.message
      };
    } catch (error: any) {
      console.error('[RemovalRequestService] Error in processRemovalRequest:', error);
      this.showError('Erro ao processar solicitação');
      return { success: false, message: 'Erro interno do sistema' };
    }
  }

  /**
   * Get removal request details by token (public method for URL processing)
   */
  async getRemovalRequestByToken(token: string): Promise<RemovalRequest | null> {
    try {
      const { data, error } = await this.supabase
        .from('removal_requests')
        .select(`
          id,
          student_name,
          guardian_name,
          guardian_email,
          reason,
          created_at,
          expires_at,
          request_token,
          status
        `)
        .eq('request_token', token)
        .eq('status', 'pending')
        .single();

      if (error || !data) {
        console.error('[RemovalRequestService] Error fetching request by token:', error);
        return null;
      }

      // Check if request is expired
      if (new Date(data.expires_at) < new Date()) {
        console.log('[RemovalRequestService] Request expired:', token);
        return null;
      }

      return {
        id: data.id,
        student_name: data.student_name,
        student_email: '', // Will be populated from auth.users if needed
        reason: data.reason,
        created_at: data.created_at,
        expires_at: data.expires_at,
        request_token: data.request_token
      };
    } catch (error: any) {
      console.error('[RemovalRequestService] Error in getRemovalRequestByToken:', error);
      return null;
    }
  }
}

export const removalRequestService = new RemovalRequestService(); 