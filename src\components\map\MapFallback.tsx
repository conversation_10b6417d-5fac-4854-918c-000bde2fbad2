
import React from 'react';
import { Card } from '../ui/card';
import { Alert, AlertTitle, AlertDescription } from '../ui/alert';
import { AlertCircle, MapPin } from 'lucide-react';
import { LocationData } from '@/types/database';

interface MapFallbackProps {
  locations: LocationData[];
}

const MapFallback: React.FC<MapFallbackProps> = ({ locations }) => {
  return (
    <Card className="w-full h-full min-h-[600px] relative" data-cy="location-map-container">
      <div className="absolute inset-0 flex flex-col items-center justify-center p-6 bg-gray-50">
        <Alert variant="destructive" className="max-w-md mb-4 space-y-2">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Mapa indisponível</AlertTitle>
          <AlertDescription className="text-sm">
            Configure o token do Mapbox para visualizar o mapa.
          </AlertDescription>
        </Alert>
        
        {locations && locations.length > 0 && (
          <div className="w-full max-w-md">
            <h3 className="font-medium mb-3 flex items-center text-center">
              <MapPin className="h-4 w-4 mr-2" />
              Dados de Localização
            </h3>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {locations.slice(0, 5).map((location, index) => (
                <div key={location.id} className="bg-white p-3 rounded border text-sm">
                  <div className="font-medium text-gray-900">
                    {index === 0 ? '🔴 Mais recente' : `${index + 1}ª localização`}
                  </div>
                  <div className="text-gray-600 font-mono text-xs">
                    {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
                  </div>
                  <div className="text-gray-500 text-xs">
                    {new Date(location.timestamp).toLocaleString('pt-BR')}
                  </div>
                  {location.address && (
                    <div className="text-gray-600 text-xs mt-1">{location.address}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default MapFallback;
