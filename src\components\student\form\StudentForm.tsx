
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';
import { StudentFormProps, StudentFormValues, studentFormSchema } from '../types/student-form.types';
import { formatCPF } from '@/lib/utils/cpf-validator';

interface StudentFormComponentProps extends StudentFormProps {
  isLoading: boolean;
  error: string | null;
  success: boolean;
  onSubmit: (data: StudentFormValues) => void;
}

export function StudentForm({
  isLoading,
  error,
  success,
  onSubmit
}: StudentFormComponentProps) {
  // Configure the form with proper types from schema
  const form = useForm<StudentFormValues>({
    resolver: zodResolver(studentFormSchema),
    defaultValues: {
      cpf: "",
      name: "",
      email: "",
      phone: "",
    },
  });

  const handleCPFChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCPF(e.target.value);
    form.setValue("cpf", formatted);
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 border-green-200">
          <AlertDescription className="text-green-800">
            Estudante adicionado com sucesso!
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="cpf">CPF do Estudante</Label>
        <Input
          id="cpf"
          placeholder="000.000.000-00"
          {...form.register("cpf")}
          onChange={handleCPFChange}
          maxLength={14}
          disabled={isLoading}
        />
        {form.formState.errors.cpf && (
          <p className="text-sm text-red-500">
            {form.formState.errors.cpf.message}
          </p>
        )}
        <p className="text-xs text-gray-500">
          O CPF será usado para identificar o estudante no sistema.
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="name">Nome Completo</Label>
        <Input
          id="name"
          placeholder="Nome Completo do Estudante"
          {...form.register("name")}
          disabled={isLoading}
        />
        {form.formState.errors.name && (
          <p className="text-sm text-red-500">
            {form.formState.errors.name.message}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">Email (opcional)</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          {...form.register("email")}
          disabled={isLoading}
        />
        {form.formState.errors.email && (
          <p className="text-sm text-red-500">
            {form.formState.errors.email.message}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">Telefone (opcional)</Label>
        <Input
          id="phone"
          placeholder="(00) 00000-0000"
          {...form.register("phone")}
          disabled={isLoading}
        />
        {form.formState.errors.phone && (
          <p className="text-sm text-red-500">
            {form.formState.errors.phone.message}
          </p>
        )}
      </div>

      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Adicionando...
          </>
        ) : (
          "Adicionar Estudante"
        )}
      </Button>
    </form>
  );
}
