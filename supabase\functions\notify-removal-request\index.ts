import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RemovalRequestPayload {
  studentName: string;
  guardianName: string;
  guardianEmail: string;
  reason?: string;
  requestToken: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Verify authorization
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Missing authorization header');
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    );

    // Get user from auth
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      throw new Error('User not authenticated');
    }

    // Parse request body
    const payload: RemovalRequestPayload = await req.json();
    const { studentName, guardianName, guardianEmail, reason, requestToken } = payload;

    // Validate required fields
    if (!studentName || !guardianName || !guardianEmail || !requestToken) {
      throw new Error('Missing required fields');
    }

    // Initialize Resend
    const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY');
    if (!RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY not configured');
    }

    // Generate email content
    const approveUrl = `${Deno.env.get('SITE_URL') || 'http://localhost:4000'}/process-removal?token=${requestToken}&action=approve`;
    const rejectUrl = `${Deno.env.get('SITE_URL') || 'http://localhost:4000'}/process-removal?token=${requestToken}&action=reject`;
    const dashboardUrl = `${Deno.env.get('SITE_URL') || 'http://localhost:4000'}/guardian-dashboard`;

    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Solicitação de Remoção - EduConnect</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
            .alert { background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0; border-radius: 4px; }
            .reason-box { background: #e9ecef; padding: 15px; border-radius: 6px; margin: 15px 0; font-style: italic; }
            .action-buttons { text-align: center; margin: 30px 0; }
            .btn { display: inline-block; padding: 12px 30px; margin: 0 10px; text-decoration: none; border-radius: 6px; font-weight: bold; }
            .btn-approve { background: #28a745; color: white; }
            .btn-reject { background: #dc3545; color: white; }
            .btn-dashboard { background: #007bff; color: white; }
            .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            .security-note { background: #d1ecf1; border-left: 4px solid #bee5eb; padding: 15px; margin: 20px 0; border-radius: 4px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🔐 Solicitação de Remoção</h1>
              <p>Sistema de Monitoramento Familiar</p>
            </div>
            
            <div class="content">
              <div class="alert">
                <strong>⚠️ Ação Necessária:</strong> Um estudante solicitou remoção do monitoramento familiar.
              </div>
              
              <h2>Detalhes da Solicitação</h2>
              <p><strong>Estudante:</strong> ${studentName}</p>
              <p><strong>Responsável:</strong> ${guardianName}</p>
              <p><strong>Data da Solicitação:</strong> ${new Date().toLocaleString('pt-BR')}</p>
              
              ${reason ? `
                <h3>Motivo Informado:</h3>
                <div class="reason-box">
                  "${reason}"
                </div>
              ` : ''}
              
              <div class="security-note">
                <h3>🛡️ Medida de Segurança</h3>
                <p>Esta solicitação foi criada como medida de proteção. Estudantes menores não podem remover responsáveis sem aprovação. Você tem 7 dias para responder.</p>
              </div>
              
              <h3>O que acontece em cada ação:</h3>
              <ul>
                <li><strong>Aprovar:</strong> O vínculo será removido permanentemente. O estudante não aparecerá mais em seu dashboard.</li>
                <li><strong>Rejeitar:</strong> O vínculo será mantido. O estudante continuará em seu monitoramento.</li>
              </ul>
              
              <div class="action-buttons">
                <a href="${approveUrl}" class="btn btn-approve">✅ Aprovar Remoção</a>
                <a href="${rejectUrl}" class="btn btn-reject">❌ Rejeitar Solicitação</a>
              </div>
              
              <div class="action-buttons">
                <a href="${dashboardUrl}" class="btn btn-dashboard">📊 Ir para Dashboard</a>
              </div>
              
              <div class="footer">
                <p>Este email foi enviado pelo Sistema EduConnect.</p>
                <p>Se você não reconhece esta solicitação, entre em contato conosco imediatamente.</p>
                <p>Esta solicitação expira em 7 dias.</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;

    // Send email via Resend
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'EduConnect <<EMAIL>>',
        to: [guardianEmail],
        subject: `🔐 Solicitação de Remoção - ${studentName}`,
        html: emailHtml,
      }),
    });

    if (!emailResponse.ok) {
      const emailError = await emailResponse.text();
      console.error('Email sending failed:', emailError);
      throw new Error(`Failed to send email: ${emailError}`);
    }

    const emailResult = await emailResponse.json();
    console.log('Email sent successfully:', emailResult);

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Notificação enviada com sucesso',
        emailId: emailResult.id,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('Error in notify-removal-request function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
}); 