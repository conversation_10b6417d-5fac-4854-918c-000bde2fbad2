# Integrations Overview - Locate-Family-Connect

## 🧩 Visão Geral das Integrações

O Locate-Family-Connect integra-se com serviços externos para fornecer funcionalidades essenciais de mapas, notificações por email e cache. Este documento detalha como essas integrações funcionam e como mantê-las.

## 🗺️ MapBox

MapBox é o serviço central usado para todas as funcionalidades de mapa e geolocalização na aplicação.

### 🔑 Configuração

```javascript
// src/lib/mapbox.ts
import mapboxgl from 'mapbox-gl';

// Configuração da API key
mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_TOKEN;

export const initializeMap = (container, options = {}) => {
  return new mapboxgl.Map({
    container,
    style: 'mapbox://styles/mapbox/streets-v12',
    center: [-47.9292, -15.7801], // Centro padrão (Brasília)
    zoom: 12,
    ...options
  });
};
```

### 🪝 Hook de Inicialização do Mapa

```typescript
// src/hooks/useMapInitialization.ts
import { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { initializeMap } from '../lib/mapbox';

export const useMapInitialization = (containerId, options = {}) => {
  const mapRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (!mapRef.current) {
      const container = document.getElementById(containerId);
      if (!container) return;

      const map = initializeMap(container, options);
      
      map.on('load', () => {
        mapRef.current = map;
        setIsLoaded(true);
      });
      
      return () => {
        map.remove();
        mapRef.current = null;
      };
    }
  }, [containerId]);

  return { map: mapRef.current, isLoaded };
};
```

### 📍 Geocodificação

```typescript
// src/lib/geocoding.ts
import { MAPBOX_API_BASE } from '../constants';

export const geocodeAddress = async (address) => {
  const url = `${MAPBOX_API_BASE}/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=${import.meta.env.VITE_MAPBOX_TOKEN}`;
  
  try {
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.features && data.features.length > 0) {
      const [lng, lat] = data.features[0].center;
      return { lat, lng, place_name: data.features[0].place_name };
    }
    
    return null;
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
};
```

### 🔄 Renderização de Mapa para Diferentes Perfis

#### Estudante (Compartilhamento de Localização)
```javascript
// src/components/student/LocationSharingMap.jsx
import React from 'react';
import { useMapInitialization } from '../../hooks/useMapInitialization';
import { useShareLocation } from '../../hooks/useShareLocation';

export const LocationSharingMap = () => {
  const { map, isLoaded } = useMapInitialization('student-map');
  const { currentLocation, isSharing, startSharing, stopSharing } = useShareLocation();
  
  // Implementação de compartilhamento
};
```

#### Responsável (Visualização de Localização)
```javascript
// src/components/guardian/StudentLocationMap.jsx
import React from 'react';
import { useMapInitialization } from '../../hooks/useMapInitialization';
import { useStudentLocations } from '../../hooks/useStudentLocations';

export const StudentLocationMap = ({ studentId }) => {
  const { map, isLoaded } = useMapInitialization('guardian-map');
  const { locations, isLoading, error } = useStudentLocations(studentId);
  
  // Implementação de visualização
};
```

### 🪣 Limites e Quotas

- **Geocodificação**: 100,000 solicitações/mês no plano gratuito
- **Renderização de Mapa**: 50,000 carregamentos/mês no plano gratuito
- **API de Navegação**: Limitada no plano gratuito

Para maior capacidade, é necessário upgrade para planos pagos.

## 📧 Resend (Email)

Resend é utilizado para envio de emails de compartilhamento de localização, notificações e alertas de geocerca.

### 🔑 Configuração

A configuração é gerenciada através das Edge Functions do Supabase:

```typescript
// supabase/functions/share-location/index.ts
import { Resend } from 'resend';

const resend = new Resend(Deno.env.get('RESEND_API_KEY'));
const fromEmail = Deno.env.get('RESEND_FROM') || '<EMAIL>';

// Função para enviar email com localização
export const sendLocationEmail = async (to, studentName, location) => {
  try {
    const response = await resend.emails.send({
      from: fromEmail,
      to,
      subject: `${studentName} compartilhou a localização`,
      html: generateLocationEmailHTML(studentName, location),
    });
    
    return { success: true, data: response };
  } catch (error) {
    console.error('Email sending error:', error);
    return { success: false, error };
  }
};
```

### 📝 Templates de Email

```typescript
// supabase/functions/share-location/templates.ts
export const generateLocationEmailHTML = (studentName, location) => {
  const mapImageUrl = `https://api.mapbox.com/styles/v1/mapbox/streets-v12/static/pin-s+f00(${location.longitude},${location.latitude})/${location.longitude},${location.latitude},15,0/600x400?access_token=${Deno.env.get('MAPBOX_TOKEN')}`;
  
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <style>/* Estilos inline */</style>
      </head>
      <body>
        <div class="email-container">
          <h1>${studentName} compartilhou a localização</h1>
          <p>Localização compartilhada em: ${new Date().toLocaleString('pt-BR')}</p>
          
          <div class="map-container">
            <img src="${mapImageUrl}" alt="Mapa" />
          </div>
          
          <p>Coordenadas: ${location.latitude}, ${location.longitude}</p>
          
          <div class="button-container">
            <a href="https://maps.google.com/?q=${location.latitude},${location.longitude}" target="_blank" class="button">Abrir no Google Maps</a>
          </div>
        </div>
      </body>
    </html>
  `;
};
```

### 🧪 Testes e Diagnóstico

Script de teste de email:

```javascript
// scripts/test-resend.mjs
import { Resend } from 'resend';
import dotenv from 'dotenv';
dotenv.config();

const resend = new Resend(process.env.RESEND_API_KEY);

async function testEmail() {
  try {
    const { data, error } = await resend.emails.send({
      from: process.env.RESEND_FROM || '<EMAIL>',
      to: '<EMAIL>',
      subject: 'Teste de Email - Locate-Family-Connect',
      html: '<h1>Teste de Email</h1><p>Este é um email de teste para verificar a integração com Resend.</p>',
    });
    
    if (error) {
      console.error('Erro:', error);
      return;
    }
    
    console.log('Email enviado com sucesso!');
    console.log('ID:', data.id);
  } catch (error) {
    console.error('Erro ao enviar email:', error);
  }
}

testEmail();
```

### ⚠️ Problemas Comuns

1. **Domínio não verificado**: Certifique-se que o domínio `sistema-monitore.com.br` esteja verificado no painel do Resend.
2. **Rate limiting**: O plano gratuito tem limites de envio por hora e dia.
3. **Emails marcados como spam**: Certifique-se de configurar registros SPF e DKIM.

## 🔄 Redis (Implementação Futura)

Redis será utilizado para cache, sistema de filas e mecanismo de circuit breaker para melhorar performance e resiliência.

### 💡 Casos de Uso Planejados

1. **Cache de dados frequentes**: Perfis de usuário, últimas localizações
2. **Sistema de filas para notificações**: Entrega confiável e assíncrona
3. **Circuit breaker para serviços externos**: Evitar falhas em cascata

### 🧠 Arquitetura Planejada

```
Cliente Web <-> API Supabase <-> Middleware Redis <-> Banco de Dados
                     |
                     v
                Edge Functions <-> Redis
```

### 🔨 Implementação Básica (Futura)

```typescript
// src/lib/redis.ts (futura implementação)
import Redis from 'ioredis';

const redisClient = new Redis(process.env.REDIS_URL);

export const cacheService = {
  async get(key) {
    try {
      const value = await redisClient.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  },
  
  async set(key, value, ttl = 3600) {
    try {
      await redisClient.set(key, JSON.stringify(value), 'EX', ttl);
      return true;
    } catch (error) {
      console.error('Redis set error:', error);
      return false;
    }
  }
};
```

### 🛡️ Circuit Breaker Pattern

```typescript
// src/lib/circuitBreaker.ts (futura implementação)
export class CircuitBreaker {
  constructor(service, options = {}) {
    this.service = service;
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.failureThreshold = options.failureThreshold || 5;
    this.resetTimeout = options.resetTimeout || 30000;
  }
  
  async call(...args) {
    if (this.state === 'OPEN') {
      throw new Error('Circuit is OPEN');
    }
    
    try {
      const result = await this.service(...args);
      this.failureCount = 0;
      return result;
    } catch (error) {
      this.failureCount++;
      
      if (this.failureCount >= this.failureThreshold) {
        this.state = 'OPEN';
        setTimeout(() => {
          this.state = 'HALF-OPEN';
        }, this.resetTimeout);
      }
      
      throw error;
    }
  }
}
```

## 🔄 Outras Integrações (Possíveis Futuras)

1. **Firebase Cloud Messaging (FCM)**: Para notificações push
2. **Twilio**: Para notificações via SMS
3. **Google OAuth/Sign-in**: Para autenticação adicional
4. **TomTom/HERE Maps**: Alternativas ao MapBox

## 🔍 Monitoramento e Manutenção

### 📊 MapBox Status

- Verifique o status: [MapBox Status](https://status.mapbox.com/)
- Monitore uso através do Dashboard MapBox

### 📬 Resend Status

- Verifique entregas e aberturas no Dashboard Resend
- Configure webhooks para rastrear eventos de email (enviado, entregue, aberto)

## 📚 Referências

- [Documentação MapBox](https://docs.mapbox.com/)
- [Documentação Resend](https://resend.com/docs)
- [Guia Redis](https://redis.io/docs/)
- [Arquivo de Configuração Resend](docs/configuracao-resend.md)
- [Documentação de Integrações](docs/INTEGRACOES.md)
