-- ==========================================
-- VERIFICAR TIPOS DA TABELA GUARDIANS
-- Data: 24/06/2025
-- Descobrir tipos exatos de guardians.student_id
-- ==========================================

-- 1. TIPOS DA TABELA GUARDIANS
SELECT 
    'TIPOS_GUARDIANS' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'guardians'
ORDER BY ordinal_position;

-- 2. DADOS FRANK SEM JOIN (SEGURO)
SELECT 
    'FRANK_GUARDIANS_RAW' as info,
    id, email, student_id, is_active, created_at
FROM public.guardians 
WHERE email = '<EMAIL>';

-- 3. DADOS FRANK PROFILES (SEGURO)  
SELECT 
    'FRANK_PROFILES_RAW' as info,
    id, email, full_name, user_type, created_at
FROM public.profiles
WHERE email = '<EMAIL>';

-- 4. CONTAGEM SIMPLES (SEGURO)
SELECT 'CONTAGEM_PROFILES' as tabela, COUNT(*) as total FROM public.profiles;
SELECT 'CONTAGEM_GUARDIANS' as tabela, COUNT(*) as total FROM public.guardians;
SELECT 'CONTAGEM_ACCOUNT_DELETION' as tabela, COUNT(*) as total FROM public.account_deletion_requests;

-- 5. SOLICITAÇÕES DE EXCLUSÃO (SEGURO)
SELECT 
    'DELETION_REQUESTS' as info,
    id, student_email, student_name, status, requested_at
FROM public.account_deletion_requests
ORDER BY requested_at DESC; 