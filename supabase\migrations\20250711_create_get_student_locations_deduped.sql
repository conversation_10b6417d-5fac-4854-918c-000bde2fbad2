-- Migration: Cria função RPC para retornar localizações deduplicadas por estudante
CREATE OR REPLACE FUNCTION public.get_student_locations_deduped(
  p_student_id uuid,
  p_start timestamptz DEFAULT NULL,
  p_end timestamptz DEFAULT NULL
)
RETURNS TABLE (
  id uuid,
  student_id uuid,
  latitude double precision,
  longitude double precision,
  accuracy double precision,
  timestamp timestamptz,
  address text
) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (
      ROUND(latitude::numeric, 5),
      ROUND(longitude::numeric, 5),
      accuracy,
      date_trunc('minute', timestamp)
    )
    id, student_id, latitude, longitude, accuracy, timestamp, address
  FROM locations
  WHERE student_id = p_student_id
    AND (p_start IS NULL OR timestamp >= p_start)
    AND (p_end IS NULL OR timestamp <= p_end)
  ORDER BY
    ROUND(latitude::numeric, 5),
    ROUND(longitude::numeric, 5),
    accuracy,
    date_trunc('minute', timestamp),
    timestamp DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 