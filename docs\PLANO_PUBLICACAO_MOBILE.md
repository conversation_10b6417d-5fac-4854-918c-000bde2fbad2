# Plano de Publicação Mobile – Locate-Family-Connect

## 1. Objetivo

Permitir que o Locate-Family-Connect seja distribuído e utilizado como aplicativo mobile, tanto via PWA (instalação pelo navegador) quanto, futuramente, como app nativo nas lojas (Play Store e App Store).

---

## 2. Situação Atual

- **Stack:** React 18 + TypeScript + Vite + Supabase + MapBox
- **Offline/PWA:** Suporte robusto a PWA (service worker, IndexedDB, smart cache, etc.)
- **Mobile Native:** Não há código React Native/Expo nem scripts de build mobile.
- **Assets:** Não há ícones, splash screens ou assets mobile dedicados.

---

## 3. Publicação como PWA (Web App Instalável)

### Vantagens
- Instalação rápida em Android/iOS via navegador
- Experiência offline e push notifications (limitado no iOS)
- Sem necessidade de aprovação de loja

### Checklist PWA
- [ ] Manifest.json completo (nome, descrição, ícone 512x512, start_url, background_color, theme_color)
- [ ] Ícones em múltiplos tamanhos (192x192, 512x512, etc.)
- [ ] Splash screen configurado
- [ ] Service worker funcional (offline, atualização automática)
- [ ] Teste de instalação em Android e iOS (Chrome, Safari)
- [ ] Instruções de instalação para o usuário (banner, tutorial)
- [ ] Teste no Lighthouse (PWA score > 90)

### Publicação
- [ ] Deploy em domínio próprio (ex: https://monitore-mvp.lovable.app)
- [ ] Divulgar instruções de instalação PWA
- [ ] Monitorar uso e feedback

---

## 4. Publicação Nativa (Play Store e App Store)

### Situação
- O projeto NÃO está pronto para publicação nativa.
- É necessário migrar/portar para React Native/Expo ou usar wrappers (Capacitor/Cordova) – com limitações.

### Passos para Publicação Nativa

#### 4.1. Migração para Mobile (Expo/React Native)
- [ ] Planejar MVP mobile: telas essenciais (login, dashboard, mapa, perfil, LGPD)
- [ ] Criar novo projeto Expo (monorepo ou repo separado)
- [ ] Reaproveitar lógica e componentes possíveis (hooks, serviços, validação)
- [ ] Adaptar navegação (React Navigation/Solito)
- [ ] Implementar assets mobile (ícones, splash, permissões)
- [ ] Testar funcionalidades offline e integração Supabase
- [ ] Gerar builds (APK/AAB para Android, IPA para iOS)

#### 4.2. Publicação nas Lojas
- [ ] Criar contas de desenvolvedor (Google Play, Apple Developer)
- [ ] Preencher ficha do app (nome, descrição, screenshots, política de privacidade)
- [ ] Submeter builds para revisão
- [ ] Corrigir eventuais pendências das lojas
- [ ] Acompanhar publicação e feedbacks

### Alternativa: Wrapper (Capacitor/Cordova)
- [ ] Avaliar limitações (push, background, performance)
- [ ] Gerar builds e testar funcionalidades críticas

---

## 5. Recomendação

- **Curto prazo:** Focar em experiência PWA, garantindo máxima compatibilidade e instruções claras de instalação.
- **Médio prazo:** Planejar MVP mobile nativo com Expo, priorizando telas e fluxos essenciais.
- **Longo prazo:** Publicar nas lojas, aproveitando feedback dos primeiros usuários.

---

## 6. Próximos Passos

1. Revisar e aprimorar manifest.json e assets PWA
2. Testar instalação e uso offline em múltiplos dispositivos
3. Criar tutorial/banner de instalação PWA para usuários
4. Iniciar planejamento do MVP mobile (levantamento de requisitos, análise de componentes reutilizáveis)
5. Documentar aprendizados e desafios durante o processo

---

**Este plano deve ser revisado periodicamente conforme o avanço do projeto e feedback dos usuários.** 