-- Migration: Remove broken create_student_account_direct function
-- Date: 2025-01-31
-- Reason: Function uses non-existent auth.create_user()

-- Drop the broken function
DROP FUNCTION IF EXISTS public.create_student_account_direct(TEXT, TEXT, TEXT, TEXT);

-- Add comment explaining why it was removed
COMMENT ON SCHEMA public IS 'create_student_account_direct RPC removed due to non-existent auth.create_user() dependency. System uses Edge Function fallback.';

-- Log the change
INSERT INTO public.auth_logs(event_type, metadata, occurred_at)
VALUES ('rpc_removed', 
        jsonb_build_object(
          'function_name', 'create_student_account_direct',
          'reason', 'uses non-existent auth.create_user()',
          'fallback', 'send-student-credentials Edge Function'
        ),
        NOW()); 