-- Fix RPC function to use student_guardian_relationships instead of guardians table
CREATE OR REPLACE FUNCTION public.get_student_locations_for_guardian(
  p_student_id UUID
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  latitude DOUBLE PRECISION,
  longitude DOUBLE PRECISION,
  timestamp TIMESTAMPTZ,
  address TEXT,
  shared_with_guardians BOOLEAN,
  student_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Verificar se o usuário atual é um responsável do estudante usando student_guardian_relationships
  IF NOT EXISTS (
    SELECT 1 FROM public.student_guardian_relationships
    WHERE student_id = p_student_id
    AND guardian_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Você não tem permissão para ver as localizações deste estudante';
  END IF;

  -- Retornar as localizações do estudante, incluindo o nome
  RETURN QUERY
  SELECT
    l.id,
    l.user_id,
    l.latitude,
    l.longitude,
    l.timestamp,
    l.address,
    l.shared_with_guardians,
    p.full_name AS student_name
  FROM
    public.locations l
    LEFT JOIN public.profiles p ON l.user_id = p.user_id
  WHERE
    l.user_id = p_student_id
    AND l.shared_with_guardians = true
  ORDER BY
    l.timestamp DESC;
END;
$$;

-- Fix RLS policy to use student_guardian_relationships instead of guardians table
DROP POLICY IF EXISTS "Responsáveis podem ver localizações compartilhadas" ON public.locations;

CREATE POLICY "Responsáveis podem ver localizações compartilhadas"
ON public.locations
FOR SELECT
USING (
  shared_with_guardians = true AND
  EXISTS (
    SELECT 1 FROM public.student_guardian_relationships
    WHERE student_id = locations.user_id
    AND guardian_id = auth.uid()
  )
);

-- Grant execute permission on the updated function
GRANT EXECUTE ON FUNCTION public.get_student_locations_for_guardian(UUID) TO authenticated; 