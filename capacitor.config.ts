import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'app.lovable.6c8163f7b02344b2bbbde884e83007bf',
  appName: 'monitore-mvp',
  webDir: 'dist',
  server: {
    url: 'https://6c8163f7-b023-44b2-bbbd-e884e83007bf.lovableproject.com?forceHideBadge=true',
    cleartext: true,
    androidScheme: 'https'
  },
  android: {
    buildOptions: {
      keystorePath: undefined,
      keystoreAlias: undefined,
      keystorePassword: undefined,
      keystoreAliasPassword: undefined,
    },
    useLegacyBridge: false
  },
  ios: {
    contentInset: 'automatic',
    allowsLinkPreview: true,
    scrollEnabled: true,
    limitsNavigationsToAppBoundDomains: true,
    // Otimizações específicas para iOS
    webContentsDebuggingEnabled: false,
    allowsInlineMediaPlayback: true,
    mediaPlaybackRequiresUserAction: false
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#ffffff',
      androidScaleType: 'CENTER_CROP',
      showSpinner: false,
      splashImmersive: true,
      splashFullScreen: true
    },
    Keyboard: {
      resize: 'body',
      style: 'dark',
      resizeOnFullScreen: true
    },
    StatusBar: {
      style: 'default',
      backgroundColor: '#ffffff',
      overlaysWebView: false,
      // iOS specific
      ios: {
        style: 'default'
      }
    },
    Haptics: {
      selectionStart: true,
      selectionChanged: true,
      selectionEnd: true
    },
    LocalNotifications: {
      smallIcon: "ic_stat_location_on",
      iconColor: "#488AFF",
      sound: "notification_sound.wav",
      actionTypeId: "LOCATION_UPDATE",
      channelId: "location-updates",
      channelName: "Location Updates",
      importance: 4, // High importance for Android
      android: {
        sound: "notification_sound",
        vibration: true,
        visibility: 1, // Public visibility
        iconColor: "#488AFF"
      },
      ios: {
        attachments: null,
        badgeNumber: 1,
        interruptionLevel: "active" // High priority for iOS
      }
    },
    Geolocation: {
      permissions: {
        ios: "The app needs your location for tracking when sharing with guardians",
        android: {
          title: "Location Permission",
          message: "The app needs your location for tracking when sharing with guardians",
          buttonPositive: "Allow"
        }
      }
    },
    App: {
      appId: 'app.lovable.6c8163f7b02344b2bbbde884e83007bf',
      appName: 'monitore-mvp',
      webDir: 'dist',
      backgroundColor: '#ffffff'
    },
    Network: {
      // Network plugin configuration
      displayNetworkAlert: true // Show alert when network status changes
    },
    Preferences: {
      // Preferences plugin configuration for storing offline data
      group: 'monitore-mvp-storage',
      secure: true
    }
  }
};

export default config;
