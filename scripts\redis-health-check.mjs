#!/usr/bin/env node
/**
 * Redis Health Check Script
 * 
 * Testa a saúde do <PERSON>is, circuit breaker, e coleta métricas de performance
 * Uso: node scripts/redis-health-check.mjs [--verbose] [--monitor] [--report]
 */

import Redis from 'ioredis';
import { performance } from 'perf_hooks';

// Configuração
const REDIS_URL = process.env.REDIS_URL || process.env.VITE_REDIS_URL || 'redis://localhost:6379';
const VERBOSE = process.argv.includes('--verbose');
const MONITOR = process.argv.includes('--monitor');
const REPORT = process.argv.includes('--report');

// Colors para output colorido
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.white) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(50));
  log(title, colors.bold + colors.cyan);
  console.log('='.repeat(50));
}

function logStatus(status, message) {
  const statusColor = status === 'OK' ? colors.green : 
                      status === 'WARNING' ? colors.yellow : colors.red;
  log(`[${status}] ${message}`, statusColor);
}

class RedisHealthChecker {
  constructor() {
    this.client = null;
    this.metrics = {
      connectionTime: 0,
      pingLatency: 0,
      getLatency: 0,
      setLatency: 0,
      pubsubLatency: 0,
      memoryUsage: '',
      connectedClients: 0,
      keyspaceHits: 0,
      keyspaceMisses: 0,
      hitRate: 0
    };
  }

  async connect() {
    try {
      const startTime = performance.now();
      
      this.client = new Redis(REDIS_URL, {
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        retryStrategy: (times) => Math.min(times * 50, 2000)
      });

      await this.client.connect();
      this.metrics.connectionTime = performance.now() - startTime;
      
      logStatus('OK', `Conectado ao Redis em ${this.metrics.connectionTime.toFixed(2)}ms`);
      return true;
    } catch (error) {
      logStatus('ERROR', `Falha na conexão: ${error.message}`);
      return false;
    }
  }

  async testBasicOperations() {
    logSection('🔧 TESTES BÁSICOS DE OPERAÇÃO');

    try {
      // Teste PING
      const pingStart = performance.now();
      const pingResult = await this.client.ping();
      this.metrics.pingLatency = performance.now() - pingStart;
      
      if (pingResult === 'PONG') {
        logStatus('OK', `PING/PONG - ${this.metrics.pingLatency.toFixed(2)}ms`);
      } else {
        logStatus('ERROR', `PING falhou - resposta: ${pingResult}`);
        return false;
      }

      // Teste SET
      const setStart = performance.now();
      await this.client.set('health:test', JSON.stringify({ 
        timestamp: new Date().toISOString(),
        test: 'redis-health-check'
      }), 'EX', 60);
      this.metrics.setLatency = performance.now() - setStart;
      logStatus('OK', `SET operação - ${this.metrics.setLatency.toFixed(2)}ms`);

      // Teste GET
      const getStart = performance.now();
      const getValue = await this.client.get('health:test');
      this.metrics.getLatency = performance.now() - getStart;
      
      if (getValue) {
        const parsed = JSON.parse(getValue);
        logStatus('OK', `GET operação - ${this.metrics.getLatency.toFixed(2)}ms`);
        if (VERBOSE) {
          log(`  Dados recuperados: ${JSON.stringify(parsed)}`, colors.white);
        }
      } else {
        logStatus('ERROR', 'GET operação falhou - valor não encontrado');
        return false;
      }

      // Limpeza
      await this.client.del('health:test');
      logStatus('OK', 'Limpeza de dados de teste');

      return true;
    } catch (error) {
      logStatus('ERROR', `Erro nas operações básicas: ${error.message}`);
      return false;
    }
  }

  async testPubSub() {
    logSection('📡 TESTE PUB/SUB');

    try {
      const subscriber = new Redis(REDIS_URL);
      const publisher = new Redis(REDIS_URL);
      
      let messageReceived = false;
      const testMessage = { test: 'pubsub', timestamp: Date.now() };

      // Configurar subscriber
      await subscriber.subscribe('health:test:channel');
      
      subscriber.on('message', (channel, message) => {
        if (channel === 'health:test:channel') {
          const parsed = JSON.parse(message);
          if (parsed.test === 'pubsub') {
            messageReceived = true;
            this.metrics.pubsubLatency = Date.now() - parsed.timestamp;
            logStatus('OK', `PUB/SUB - ${this.metrics.pubsubLatency}ms`);
          }
        }
      });

      // Aguardar subscrição estar ativa
      await new Promise(resolve => setTimeout(resolve, 100));

      // Publicar mensagem
      await publisher.publish('health:test:channel', JSON.stringify(testMessage));

      // Aguardar resposta
      await new Promise(resolve => setTimeout(resolve, 500));

      if (!messageReceived) {
        logStatus('WARNING', 'PUB/SUB - Mensagem não foi recebida');
      }

      // Cleanup
      await subscriber.quit();
      await publisher.quit();

      return messageReceived;
    } catch (error) {
      logStatus('ERROR', `Erro no teste PUB/SUB: ${error.message}`);
      return false;
    }
  }

  async collectServerMetrics() {
    logSection('📊 MÉTRICAS DO SERVIDOR');

    try {
      // Informações de memória
      const memoryInfo = await this.client.info('memory');
      const memoryMatch = memoryInfo.match(/used_memory_human:(.+)/);
      this.metrics.memoryUsage = memoryMatch ? memoryMatch[1].trim() : 'N/A';

      // Informações de clientes
      const clientsInfo = await this.client.info('clients');
      const clientsMatch = clientsInfo.match(/connected_clients:(\d+)/);
      this.metrics.connectedClients = clientsMatch ? parseInt(clientsMatch[1]) : 0;

      // Estatísticas do keyspace
      const statsInfo = await this.client.info('stats');
      const hitsMatch = statsInfo.match(/keyspace_hits:(\d+)/);
      const missesMatch = statsInfo.match(/keyspace_misses:(\d+)/);
      
      if (hitsMatch && missesMatch) {
        this.metrics.keyspaceHits = parseInt(hitsMatch[1]);
        this.metrics.keyspaceMisses = parseInt(missesMatch[1]);
        const total = this.metrics.keyspaceHits + this.metrics.keyspaceMisses;
        this.metrics.hitRate = total > 0 ? (this.metrics.keyspaceHits / total) * 100 : 0;
      }

      logStatus('OK', `Uso de memória: ${this.metrics.memoryUsage}`);
      logStatus('OK', `Clientes conectados: ${this.metrics.connectedClients}`);
      logStatus('OK', `Taxa de acertos do cache: ${this.metrics.hitRate.toFixed(2)}%`);

      if (VERBOSE) {
        log(`  Keyspace hits: ${this.metrics.keyspaceHits}`, colors.white);
        log(`  Keyspace misses: ${this.metrics.keyspaceMisses}`, colors.white);
      }

      return true;
    } catch (error) {
      logStatus('ERROR', `Erro ao coletar métricas: ${error.message}`);
      return false;
    }
  }

  async testCircuitBreakerSimulation() {
    logSection('⚡ SIMULAÇÃO DE CIRCUIT BREAKER');

    try {
      // Simular operações normais
      logStatus('OK', 'Testando operações normais...');
      for (let i = 0; i < 5; i++) {
        await this.client.set(`cb:test:${i}`, `value-${i}`, 'EX', 10);
        await this.client.get(`cb:test:${i}`);
      }
      logStatus('OK', '5 operações normais executadas com sucesso');

      // Simular falhas (tentativa de operação em chave inexistente com timeout muito baixo)
      logStatus('OK', 'Testando cenário de falhas...');
      let failures = 0;
      
      for (let i = 0; i < 3; i++) {
        try {
          // Simular timeout baixo para forçar falha
          const shortTimeoutClient = new Redis(REDIS_URL, {
            commandTimeout: 1, // 1ms - muito baixo para forçar timeout
            retryStrategy: () => null // Não retry
          });
          
          await shortTimeoutClient.get('nonexistent:key');
          await shortTimeoutClient.quit();
        } catch (error) {
          failures++;
        }
      }

      logStatus('OK', `${failures} falhas simuladas para teste do circuit breaker`);

      // Limpeza
      const keys = await this.client.keys('cb:test:*');
      if (keys.length > 0) {
        await this.client.del(...keys);
      }

      return true;
    } catch (error) {
      logStatus('ERROR', `Erro na simulação do circuit breaker: ${error.message}`);
      return false;
    }
  }

  async runPerformanceTest() {
    logSection('🚀 TESTE DE PERFORMANCE');

    const iterations = 100;
    const results = {
      set: [],
      get: [],
      del: []
    };

    try {
      // Teste SET performance
      log('Testando performance de SET...', colors.white);
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        await this.client.set(`perf:test:${i}`, `value-${i}`, 'EX', 60);
        results.set.push(performance.now() - start);
      }

      // Teste GET performance
      log('Testando performance de GET...', colors.white);
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        await this.client.get(`perf:test:${i}`);
        results.get.push(performance.now() - start);
      }

      // Teste DEL performance
      log('Testando performance de DEL...', colors.white);
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        await this.client.del(`perf:test:${i}`);
        results.del.push(performance.now() - start);
      }

      // Calcular estatísticas
      const calculateStats = (arr) => ({
        avg: arr.reduce((a, b) => a + b, 0) / arr.length,
        min: Math.min(...arr),
        max: Math.max(...arr),
        p95: arr.sort((a, b) => a - b)[Math.floor(arr.length * 0.95)]
      });

      const setStats = calculateStats(results.set);
      const getStats = calculateStats(results.get);
      const delStats = calculateStats(results.del);

      logStatus('OK', `SET - Avg: ${setStats.avg.toFixed(2)}ms, P95: ${setStats.p95.toFixed(2)}ms`);
      logStatus('OK', `GET - Avg: ${getStats.avg.toFixed(2)}ms, P95: ${getStats.p95.toFixed(2)}ms`);
      logStatus('OK', `DEL - Avg: ${delStats.avg.toFixed(2)}ms, P95: ${delStats.p95.toFixed(2)}ms`);

      // Alertas de performance
      if (setStats.avg > 10) {
        logStatus('WARNING', 'SET performance acima do esperado (>10ms)');
      }
      if (getStats.avg > 5) {
        logStatus('WARNING', 'GET performance acima do esperado (>5ms)');
      }

      return true;
    } catch (error) {
      logStatus('ERROR', `Erro no teste de performance: ${error.message}`);
      return false;
    }
  }

  generateReport() {
    logSection('📋 RELATÓRIO FINAL');

    const overallHealth = 
      this.metrics.pingLatency < 10 && 
      this.metrics.getLatency < 5 && 
      this.metrics.setLatency < 10 ? 'HEALTHY' : 'DEGRADED';

    log(`Status Geral: ${overallHealth}`, 
        overallHealth === 'HEALTHY' ? colors.green : colors.yellow);

    console.log('\n' + colors.bold + 'Métricas Coletadas:' + colors.reset);
    console.log(`├─ Tempo de conexão: ${this.metrics.connectionTime.toFixed(2)}ms`);
    console.log(`├─ Latência PING: ${this.metrics.pingLatency.toFixed(2)}ms`);
    console.log(`├─ Latência GET: ${this.metrics.getLatency.toFixed(2)}ms`);
    console.log(`├─ Latência SET: ${this.metrics.setLatency.toFixed(2)}ms`);
    console.log(`├─ Latência PUB/SUB: ${this.metrics.pubsubLatency}ms`);
    console.log(`├─ Uso de memória: ${this.metrics.memoryUsage}`);
    console.log(`├─ Clientes conectados: ${this.metrics.connectedClients}`);
    console.log(`└─ Taxa de acertos: ${this.metrics.hitRate.toFixed(2)}%`);

    // Recomendações
    console.log('\n' + colors.bold + 'Recomendações:' + colors.reset);
    if (this.metrics.hitRate < 80) {
      log('• Taxa de acertos baixa - revisar estratégia de cache', colors.yellow);
    }
    if (this.metrics.pingLatency > 10) {
      log('• Latência alta - verificar rede/servidor Redis', colors.yellow);
    }
    if (overallHealth === 'HEALTHY') {
      log('• Sistema Redis está operando dentro dos parâmetros normais', colors.green);
    }
  }

  async disconnect() {
    if (this.client) {
      await this.client.quit();
      logStatus('OK', 'Desconectado do Redis');
    }
  }
}

// Função principal
async function main() {
  log('🔍 Redis Health Check - EduConnect System', colors.bold + colors.cyan);
  log(`Testando: ${REDIS_URL}`, colors.white);

  const checker = new RedisHealthChecker();

  try {
    // Conectar
    if (!(await checker.connect())) {
      process.exit(1);
    }

    // Executar testes
    const tests = [
      { name: 'Operações Básicas', fn: () => checker.testBasicOperations() },
      { name: 'PUB/SUB', fn: () => checker.testPubSub() },
      { name: 'Métricas do Servidor', fn: () => checker.collectServerMetrics() },
      { name: 'Circuit Breaker', fn: () => checker.testCircuitBreakerSimulation() }
    ];

    if (VERBOSE) {
      tests.push({ name: 'Performance', fn: () => checker.runPerformanceTest() });
    }

    let failedTests = 0;
    for (const test of tests) {
      try {
        const success = await test.fn();
        if (!success) failedTests++;
      } catch (error) {
        logStatus('ERROR', `Falha no teste ${test.name}: ${error.message}`);
        failedTests++;
      }
    }

    // Gerar relatório
    if (REPORT || VERBOSE) {
      checker.generateReport();
    }

    // Status final
    if (failedTests === 0) {
      log('\n✅ Todos os testes passaram!', colors.bold + colors.green);
    } else {
      log(`\n❌ ${failedTests} teste(s) falharam`, colors.bold + colors.red);
      process.exit(1);
    }

  } catch (error) {
    logStatus('ERROR', `Erro crítico: ${error.message}`);
    process.exit(1);
  } finally {
    await checker.disconnect();
  }

  // Modo monitor
  if (MONITOR) {
    log('\n🔄 Modo monitor ativado - executando a cada 30 segundos...', colors.cyan);
    setInterval(async () => {
      console.log('\n' + '─'.repeat(50));
      await main();
    }, 30000);
  }
}

// Executar se for chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default main;
