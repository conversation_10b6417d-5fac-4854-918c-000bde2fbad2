import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';
import { ScrollArea } from '../ui/scroll-area';
import { LocationData } from '@/types/database';
import { Student } from '@/types/auth';

interface LocationDebugProps {
  selectedStudent: Student | null;
  locations: LocationData[];
  isLoading: boolean;
  error: string | null;
}

export const LocationDebug: React.FC<LocationDebugProps> = ({
  selectedStudent,
  locations,
  isLoading,
  error
}) => {
  if (isLoading) {
    return <div className="text-sm text-muted-foreground">Loading locations...</div>;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">Debug Info</CardTitle>
      </CardHeader>
      <CardContent className="p-2">
        <ScrollArea className="h-40">
          <pre className="text-xs whitespace-pre-wrap">
{JSON.stringify({ student: selectedStudent?.name, locations }, null, 2)}
          </pre>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
