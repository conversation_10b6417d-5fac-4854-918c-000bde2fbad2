
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PasswordFieldProps {
  value: string;
  onChange: (value: string) => void;
  showPassword: boolean;
  onToggleVisibility: () => void;
  disabled?: boolean;
  placeholder?: string;
  label?: string;
  className?: string;
}

const PasswordField: React.FC<PasswordFieldProps> = ({
  value,
  onChange,
  showPassword,
  onToggleVisibility,
  disabled = false,
  placeholder = "••••••••",
  label = '',
  className = ''
}) => {
  const { t } = useTranslation();
  const resolvedLabel = label || t('auth.login.password');
  return (
    <div className="space-y-2">
      <Label htmlFor="password" className="text-sm sm:text-base">
        {resolvedLabel}
      </Label>
      <div className="relative">
        <Input
          id="password"
          type={showPassword ? "text" : "password"}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={cn("w-full pr-10", className)}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete="current-password"
          aria-label={resolvedLabel}
          data-cy="password-input"
        />
        <button
          type="button"
          onClick={onToggleVisibility}
          className="absolute right-3 top-1/2 -translate-y-1/2"
          aria-label={showPassword ? t('auth.password.hide') : t('auth.password.show')}
          data-cy="toggle-password-visibility"
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4 text-gray-500" />
          ) : (
            <Eye className="h-4 w-4 text-gray-500" />
          )}
        </button>
      </div>
    </div>
  );
};

export default PasswordField;
