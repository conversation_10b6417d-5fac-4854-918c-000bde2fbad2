
import { useQuery } from '@tanstack/react-query';
import { studentProfileService } from '@/lib/services/student/StudentProfileService';

export function useStudents() {
  return useQuery({
    queryKey: ['students'],
    queryFn: () => studentProfileService.getStudentsForGuardian(),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false, // Evitar refetch automático
    refetchOnReconnect: true,
    retry: 1, // Apenas 1 retry
    retryDelay: 2000,
    // Timeout específico para evitar loading infinito
    meta: {
      timeout: 10000 // 10 segundos timeout
    },
    // Retornar array vazio em caso de erro para não quebrar a UI
    placeholderData: [],
  });
}
