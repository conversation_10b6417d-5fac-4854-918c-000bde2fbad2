
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Shield, Eye, Clock, MapPin } from 'lucide-react';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useTranslation } from 'react-i18next';

export const PrivacySettings: React.FC = () => {
  const { settings, updateSetting, isUpdating } = useUserSettings();
  const { t } = useTranslation();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {t('settings.privacy.title', 'Privacy Settings')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Eye className="h-4 w-4 text-blue-500" />
            <div>
              <Label>{t('settings.privacy.profileVisibility.label', 'Profile Visibility')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.privacy.profileVisibility.description', 'Allow other users to see your profile')}
              </p>
            </div>
          </div>
          <Switch
            checked={settings.profile_visibility}
            onCheckedChange={(value) => updateSetting('profile_visibility', value)}
            disabled={isUpdating}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MapPin className="h-4 w-4 text-green-500" />
            <div>
              <Label>{t('settings.privacy.autoSharing.label', 'Automatic Sharing')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.privacy.autoSharing.description', 'Share location automatically')}
              </p>
            </div>
          </div>
          <Switch
            checked={settings.auto_location_sharing}
            onCheckedChange={(value) => updateSetting('auto_location_sharing', value)}
            disabled={isUpdating}
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <Clock className="h-4 w-4 text-orange-500" />
            <Label>{t('settings.privacy.dataRetention.label', 'Data Retention')}</Label>
          </div>
          <Select
            value={settings.data_retention_days.toString()}
            onValueChange={(value) => updateSetting('data_retention_days', parseInt(value))}
            disabled={isUpdating}
          >
            <SelectTrigger className="max-w-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">{t('settings.privacy.dataRetention.options.7days', '7 days')}</SelectItem>
              <SelectItem value="30">{t('settings.privacy.dataRetention.options.30days', '30 days')}</SelectItem>
              <SelectItem value="90">{t('settings.privacy.dataRetention.options.90days', '90 days')}</SelectItem>
              <SelectItem value="365">{t('settings.privacy.dataRetention.options.1year', '1 year')}</SelectItem>
              <SelectItem value="-1">{t('settings.privacy.dataRetention.options.forever', 'Forever')}</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            {t('settings.privacy.dataRetention.description', 'Time to keep location history')}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
