
# 🐛 TEMPLATE: Correção de Bug

## Bug Fix: [Nome do Bug]

### 🚨 Reprodução do Problema
- [ ] Problema reproduzido consistentemente
- [ ] Steps to reproduce documentados
- [ ] Impacto nos usuários avaliado
- [ ] Logs de erro coletados

**Descrição do bug:**
[Descreva o comportamento atual incorreto]

**Comportamento esperado:**
[Descreva como deveria funcionar]

**Steps to reproduce:**
1. [Passo 1]
2. [Passo 2]
3. [Passo 3]
4. [Observar erro]

**Ambiente:**
- Browser: [Chrome/Firefox/Safari/Edge]
- OS: [Windows/Mac/Linux]
- Versão: [versão da aplicação]

### 🔍 Análise da Causa Raiz
- [ ] Causa raiz identificada
- [ ] Arquivos afetados mapeados
- [ ] Dependências relacionadas verificadas

**Causa raiz:**
[Explique a causa técnica do bug]

**Arquivos envolvidos:**
- `caminho/para/arquivo1.tsx` - [como está envolvido]
- `caminho/para/arquivo2.ts` - [como está envolvido]

### 🧪 Teste que Falha
- [ ] Teste criado que reproduz o bug
- [ ] Teste falha antes da correção
- [ ] Teste demonstra o comportamento incorreto

```typescript
// Exemplo de teste que falha
test('should [comportamento esperado]', () => {
  // arrange
  // act  
  // assert - deveria falhar antes da correção
});
```

### 🔧 Correção Implementada
- [ ] Correção mínima implementada
- [ ] Sem over-engineering
- [ ] Compatibilidade mantida
- [ ] Nenhuma funcionalidade não relacionada afetada

**Arquivos modificados:**
- `caminho/para/arquivo1.tsx` - [o que foi alterado]
- `caminho/para/arquivo2.ts` - [o que foi alterado]

**Tipo de correção:**
- [ ] Lógica corrigida
- [ ] Validação adicionada  
- [ ] Estado corrigido
- [ ] API call corrigida
- [ ] CSS/Layout corrigido

### ✅ Validação da Correção
- [ ] Teste criado agora passa
- [ ] Bug não reproduz mais
- [ ] Funcionalidades relacionadas testadas
- [ ] Regressão verificada (outras funcionalidades OK)
- [ ] Performance não afetada

**Cenários testados:**
- [ ] Cenário original do bug
- [ ] Edge cases relacionados
- [ ] Funcionalidades adjacentes
- [ ] Cross-browser (se UI)
- [ ] Mobile (se aplicável)

### 🧪 Testes de Regressão
- [ ] `npm run safety-check` passa
- [ ] `npm run critical-test` passa
- [ ] Testes automatizados passam
- [ ] Teste manual das funcionalidades críticas

### 📊 Impacto da Correção
**Antes da correção:**
- Usuários afetados: [número/percentual]
- Frequência do bug: [alta/média/baixa]
- Severidade: [crítica/alta/média/baixa]

**Após a correção:**
- Bug eliminado: [sim/não]
- Performance: [melhorada/mantida/degradada]
- Funcionalidades: [todas funcionando]

### 🔄 Plano de Rollback
**Se a correção causar problemas:**
```bash
# Reverter commit da correção
git revert [commit-hash-da-correcao]

# Validar que o sistema volta ao estado anterior
npm run safety-check

# Comunicar regressão do bug original
```

### 📝 Prevenção Futura
- [ ] Teste adicionado para prevenir regressão
- [ ] Documentação atualizada
- [ ] Code review rigoroso
- [ ] Monitoring adicionado (se aplicável)

**Melhorias sugeridas:**
- [Sugestão 1 para evitar bugs similares]
- [Sugestão 2 para detectar mais cedo]
- [Sugestão 3 para melhor handling]

### 📋 Checklist Final
- [ ] Bug não reproduz mais
- [ ] Teste de regressão criado
- [ ] Funcionalidades críticas validadas
- [ ] Performance mantida
- [ ] Documentação atualizada
- [ ] Rollback testado

---
**Criado em:** [Data]  
**Reportado por:** [Nome]  
**Corrigido por:** [Nome]  
**Severidade:** [Crítica|Alta|Média|Baixa]  
**Prioridade:** [P0|P1|P2|P3]
