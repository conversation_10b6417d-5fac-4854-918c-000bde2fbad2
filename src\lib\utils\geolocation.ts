
import { env } from '@/env';
import { AdvancedGeolocationService, GeolocationResult } from '@/lib/services/location/AdvancedGeolocationService';

export interface GeoLocationResult {
  lat: number;
  lon: number;
  source: 'gps' | 'ipinfo' | 'ipapi';
  accuracy?: number;
}

// Manter função original para compatibilidade
export async function fetchIpLocation(): Promise<GeoLocationResult> {
  const token = env.IPINFO_TOKEN_FALLBACK;
  const url = token
    ? `https://ipinfo.io/json?token=${token}`
    : 'https://ipinfo.io/json';

  try {
    const res = await fetch(url);
    if (res.ok) {
      const data = await res.json();
      const [lat, lon] = (data.loc as string).split(',');
      return { lat: parseFloat(lat), lon: parseFloat(lon), source: 'ipinfo' };
    }
    console.warn(`[geolocation] ipinfo failed: ${res.status}`);
  } catch (err) {
    console.warn('[geolocation] ipinfo error', err);
  }

  try {
    const res = await fetch('https://ipapi.co/json/');
    if (!res.ok) throw new Error('ipapi request failed');
    const data = await res.json();
    return {
      lat: parseFloat(data.latitude),
      lon: parseFloat(data.longitude),
      source: 'ipapi'
    };
  } catch (err) {
    console.error('[geolocation] ipapi error', err);
    throw new Error('IP location request failed');
  }
}

// Função compatível que usa o novo serviço avançado
export async function getLocationWithFallback(): Promise<GeoLocationResult> {
  const service = AdvancedGeolocationService.getInstance();
  
  try {
    const result: GeolocationResult = await service.getHighPrecisionLocation({
      targetAccuracy: env.LOCATION_TARGET_ACCURACY_NUM,
      maxAttempts: env.LOCATION_MAX_ATTEMPTS_NUM,
      enableHybrid: env.LOCATION_ENABLE_HYBRID_BOOL
    });

    // Converter para formato compatível
    return {
      lat: result.latitude,
      lon: result.longitude,
      accuracy: result.accuracy,
      source: result.source.startsWith('gps') ? 'gps' : 
              result.source.includes('ip') ? 'ipinfo' : 'gps'
    };
  } catch (error) {
    console.warn('[geolocation] Advanced service failed, using fallback:', error);
    
    // Fallback para IP location
    try {
      return await fetchIpLocation();
    } catch (fallbackError) {
      console.error('[geolocation] All methods failed:', fallbackError);
      throw new Error('All location methods failed');
    }
  }
}

// Nova função que retorna resultado completo do serviço avançado
export async function getAdvancedLocation(options?: {
  targetAccuracy?: number;
  maxAttempts?: number;
  enableHybrid?: boolean;
}): Promise<GeolocationResult> {
  const service = AdvancedGeolocationService.getInstance();
  return await service.getHighPrecisionLocation(options);
}

// Função para validar qualidade da localização
export function validateLocationQuality(
  result: GeolocationResult, 
  previousResult?: GeolocationResult
): { isValid: boolean; issues: string[]; score: number } {
  const service = AdvancedGeolocationService.getInstance();
  const validation = service.validateLocation(result, previousResult);
  
  return {
    isValid: validation.isValid,
    issues: validation.issues,
    score: result.quality_score
  };
}
