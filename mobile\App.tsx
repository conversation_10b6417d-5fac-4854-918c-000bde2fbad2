import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  SafeAreaView, 
  TouchableOpacity, 
  ActivityIndicator, 
  ScrollView, 
  Platform, 
  StatusBar,
  TextInput,
  Alert,
  KeyboardAvoidingView
} from 'react-native';
import { supabase } from './supabaseClient';
import { User } from '@supabase/supabase-js';

// Tipos de usuário
type UserType = 'student' | 'parent' | 'developer' | 'admin';

// Rotas padrão por tipo de usuário
const DEFAULT_ROUTES: Record<UserType, string> = {
  'student': 'student-dashboard',
  'parent': 'parent-dashboard',
  'developer': 'dev-dashboard',
  'admin': 'admin-dashboard'
};

// Função para obter tipo de usuário dos metadados
const getUserTypeFromMetadata = (userMetadata?: Record<string, any>): UserType => {
  if (!userMetadata) return 'student';
  
  const userType = userMetadata.user_type || userMetadata.userType;
  
  if (userType === 'parent' || userType === 'guardian') return 'parent';
  if (userType === 'developer' || userType === 'dev') return 'developer';
  if (userType === 'admin' || userType === 'administrator') return 'admin';
  
  return 'student';
};

// Função para obter rota padrão baseada no tipo de usuário
const getDefaultRouteForUserType = (userType: UserType): string => {
  return DEFAULT_ROUTES[userType] || 'student-dashboard';
};

export default function App() {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  const [userType, setUserType] = useState('student');
  const [error, setError] = useState('');
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentScreen, setCurrentScreen] = useState<'login' | 'dashboard'>('login');

  // Verificar se o usuário já está logado
  useEffect(() => {
    const checkUser = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          setUser(session.user);
          setIsAuthenticated(true);
          
          // Determinar tipo de usuário e rota
          const userTypeFromMetadata = getUserTypeFromMetadata(session.user.user_metadata);
          setUserType(userTypeFromMetadata);
          
          // Navegar para dashboard apropriado
          setCurrentScreen('dashboard');
        }
      } catch (error) {
        console.error('Erro ao verificar sessão:', error);
      }
    };

    checkUser();

    // Escutar mudanças na autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setUser(session.user);
          setIsAuthenticated(true);
          
          const userTypeFromMetadata = getUserTypeFromMetadata(session.user.user_metadata);
          setUserType(userTypeFromMetadata);
          
          setCurrentScreen('dashboard');
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setIsAuthenticated(false);
          setCurrentScreen('login');
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Por favor, preencha todos os campos.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim().toLowerCase(),
        password
      });

      if (error) {
        let errorMessage = 'Erro ao fazer login.';
        
        if (error.message?.includes('Invalid login credentials')) {
          errorMessage = 'Email ou senha incorretos.';
        } else if (error.message?.includes('Email not confirmed')) {
          errorMessage = 'Email não confirmado.';
        }
        
        setError(errorMessage);
      } else {
        console.log('Login bem-sucedido:', data.user);
        setUser(data.user);
        setIsAuthenticated(true);
        
        const userTypeFromMetadata = getUserTypeFromMetadata(data.user?.user_metadata);
        setUserType(userTypeFromMetadata);
        
        setCurrentScreen('dashboard');
      }
    } catch (error) {
      console.error('Erro no login:', error);
      setError('Erro inesperado. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      setIsAuthenticated(false);
      setCurrentScreen('login');
      setEmail('');
      setPassword('');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const renderLoginScreen = () => (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.loginContainer}>
            <Text style={styles.title}>Locate Family Connect</Text>
            <Text style={styles.subtitle}>Faça login para continuar</Text>

            {error ? (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            ) : null}

            <View style={styles.form}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={styles.input}
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Digite seu email"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Senha</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[styles.input, styles.passwordInput]}
                    value={password}
                    onChangeText={setPassword}
                    placeholder="Digite sua senha"
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Text style={styles.eyeText}>{showPassword ? '👁️' : '👁️‍🗨️'}</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={[styles.button, loading && styles.buttonDisabled]}
                onPress={handleLogin}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>Entrar</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.switchButton}
                onPress={() => setIsLogin(!isLogin)}
              >
                <Text style={styles.switchText}>
                  {isLogin ? 'Não tem conta? Cadastre-se' : 'Já tem conta? Faça login'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );

  const renderDashboard = () => {
    const isStudent = userType === 'student';
    const isParent = userType === 'parent';

    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>
            {isStudent ? 'Dashboard do Estudante' : 'Dashboard do Responsável'}
          </Text>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutText}>Sair</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.dashboardContent}>
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>
              Bem-vindo, {user?.email || 'Usuário'}!
            </Text>
            <Text style={styles.userTypeText}>
              Tipo: {userType === 'student' ? 'Estudante' : 'Responsável'}
            </Text>
          </View>

          <View style={styles.infoSection}>
            <Text style={styles.infoTitle}>Informações do App</Text>
            <Text style={styles.infoText}>
              • Login funcionando ✅{'\n'}
              • Autenticação Supabase ✅{'\n'}
              • Navegação entre telas ✅{'\n'}
              • Detecção de tipo de usuário ✅
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      {currentScreen === 'login' ? renderLoginScreen() : renderDashboard()}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  loginContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#f44336',
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
    fontSize: 14,
  },
  form: {
    width: '100%',
    maxWidth: 350,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordInput: {
    flex: 1,
    marginRight: 10,
  },
  eyeButton: {
    padding: 10,
  },
  eyeText: {
    fontSize: 20,
  },
  button: {
    backgroundColor: '#2196f3',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  switchButton: {
    marginTop: 20,
    alignItems: 'center',
  },
  switchText: {
    color: '#2196f3',
    fontSize: 14,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  logoutButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
  },
  logoutText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  dashboardContent: {
    flex: 1,
    padding: 20,
  },
  welcomeSection: {
    backgroundColor: '#e3f2fd',
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 5,
  },
  userTypeText: {
    fontSize: 14,
    color: '#666',
  },
  infoSection: {
    backgroundColor: '#f5f5f5',
    padding: 20,
    borderRadius: 10,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});