import { useEffect, useState, useCallback } from 'react';
import { Capacitor } from '@capacitor/core';
import { Network } from '@capacitor/network';
import { Preferences } from '@capacitor/preferences';
import mapboxgl from 'mapbox-gl';
import { LocationData } from '@/types/database';

interface UseOfflineMapProps {
  mapInstance: { current: mapboxgl.Map | null };
  mapInitialized: boolean;
  locations: LocationData[];
}

interface OfflineMapState {
  isOnline: boolean;
  offlineRegionStatus: 'idle' | 'downloading' | 'ready' | 'error';
  downloadProgress: number;
  lastSyncTimestamp: number | null;
}

export function useOfflineMap({ 
  mapInstance, 
  mapInitialized, 
  locations 
}: UseOfflineMapProps) {
  const [state, setState] = useState<OfflineMapState>({
    isOnline: true,
    offlineRegionStatus: 'idle',
    downloadProgress: 0,
    lastSyncTimestamp: null
  });

  // Check if we're on a native platform
  const isNative = Capacitor.isNativePlatform();

  // Function to check network status
  const checkNetworkStatus = useCallback(async () => {
    if (isNative) {
      const status = await Network.getStatus();
      setState(prev => ({ ...prev, isOnline: status.connected }));
      return status.connected;
    }
    return navigator.onLine;
  }, [isNative]);

  // Function to save locations to local storage
  const cacheLocations = useCallback(async (locationsToCache: LocationData[]) => {
    if (!locationsToCache.length) return;
    
    try {
      // Store locations in local storage
      await Preferences.set({
        key: 'cached_locations',
        value: JSON.stringify(locationsToCache)
      });
      
      // Update last sync timestamp
      const timestamp = Date.now();
      await Preferences.set({
        key: 'last_sync_timestamp',
        value: timestamp.toString()
      });
      
      setState(prev => ({ ...prev, lastSyncTimestamp: timestamp }));
      console.log('[OfflineMap] Cached', locationsToCache.length, 'locations');
    } catch (error) {
      console.error('[OfflineMap] Error caching locations:', error);
    }
  }, []);

  // Function to get cached locations
  const getCachedLocations = useCallback(async (): Promise<LocationData[]> => {
    try {
      const { value } = await Preferences.get({ key: 'cached_locations' });
      if (!value) return [];
      
      return JSON.parse(value) as LocationData[];
    } catch (error) {
      console.error('[OfflineMap] Error getting cached locations:', error);
      return [];
    }
  }, []);

  // Function to download map region for offline use
  const downloadMapRegion = useCallback(async () => {
    if (!isNative || !mapInstance.current || !mapInitialized || locations.length === 0) {
      return;
    }

    try {
      setState(prev => ({ ...prev, offlineRegionStatus: 'downloading', downloadProgress: 0 }));
      
      // Calculate bounds from locations
      const bounds = new mapboxgl.LngLatBounds();
      locations.forEach(loc => {
        bounds.extend([loc.longitude, loc.latitude]);
      });
      
      // Extend bounds slightly to include surrounding area
      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();
      const extendedBounds = new mapboxgl.LngLatBounds(
        [sw.lng - 0.01, sw.lat - 0.01],
        [ne.lng + 0.01, ne.lat + 0.01]
      );
      
      // Get current zoom level
      const zoom = mapInstance.current.getZoom();
      const minZoom = Math.max(zoom - 2, 0);
      const maxZoom = Math.min(zoom + 2, 22);
      
      // In a real implementation, we would use the Mapbox Offline plugin
      // Since we're simulating this functionality, we'll just update progress
      
      // Simulate download progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 5;
        setState(prev => ({ ...prev, downloadProgress: progress }));
        
        if (progress >= 100) {
          clearInterval(interval);
          setState(prev => ({ 
            ...prev, 
            offlineRegionStatus: 'ready', 
            downloadProgress: 100 
          }));
          
          // Save the offline region info
          Preferences.set({
            key: 'offline_region',
            value: JSON.stringify({
              bounds: extendedBounds,
              minZoom,
              maxZoom,
              timestamp: Date.now()
            })
          });
          
          console.log('[OfflineMap] Region downloaded for offline use');
        }
      }, 200);
      
      // Also cache the locations
      await cacheLocations(locations);
      
    } catch (error) {
      console.error('[OfflineMap] Error downloading region:', error);
      setState(prev => ({ ...prev, offlineRegionStatus: 'error' }));
    }
  }, [isNative, mapInstance, mapInitialized, locations, cacheLocations]);

  // Function to clear offline data
  const clearOfflineData = useCallback(async () => {
    try {
      await Preferences.remove({ key: 'offline_region' });
      await Preferences.remove({ key: 'cached_locations' });
      await Preferences.remove({ key: 'last_sync_timestamp' });
      
      setState({
        isOnline: await checkNetworkStatus(),
        offlineRegionStatus: 'idle',
        downloadProgress: 0,
        lastSyncTimestamp: null
      });
      
      console.log('[OfflineMap] Cleared offline data');
    } catch (error) {
      console.error('[OfflineMap] Error clearing offline data:', error);
    }
  }, [checkNetworkStatus]);

  // Initialize network status listener
  useEffect(() => {
    if (!isNative) return;
    
    // Check initial network status
    checkNetworkStatus();
    
    // Get last sync timestamp
    Preferences.get({ key: 'last_sync_timestamp' }).then(({ value }) => {
      if (value) {
        setState(prev => ({ ...prev, lastSyncTimestamp: parseInt(value, 10) }));
      }
    });
    
    // Check offline region status
    Preferences.get({ key: 'offline_region' }).then(({ value }) => {
      if (value) {
        setState(prev => ({ ...prev, offlineRegionStatus: 'ready' }));
      }
    });
    
    // Listen for network status changes
    let networkListener: any = null;
    
    Network.addListener('networkStatusChange', status => {
      setState(prev => ({ ...prev, isOnline: status.connected }));
      console.log('[OfflineMap] Network status changed:', status.connected ? 'online' : 'offline');
    }).then(listener => {
      networkListener = listener;
    });
    
    return () => {
      if (networkListener) {
        networkListener.remove();
      }
    };
  }, [isNative, checkNetworkStatus]);

  return {
    ...state,
    downloadMapRegion,
    clearOfflineData,
    cacheLocations,
    getCachedLocations
  };
}