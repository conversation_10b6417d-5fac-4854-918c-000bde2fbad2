-- Correção da função accept_student_request
-- O problema: A função estava verificando guardian_email vs usuário atual (estudante)
-- A solução: Verificar apenas se o student_id corresponde ao usuário atual

CREATE OR REPLACE FUNCTION public.accept_student_request(
    p_invitation_token TEXT
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_student_id UUID;
    v_invitation_record RECORD;
    v_guardian_id UUID;
    v_existing_relationship UUID;
BEGIN
    -- Get current user (student)
    v_student_id := auth.uid();
    
    IF v_student_id IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Usuário não autenticado'::TEXT;
        RETURN;
    END IF;
    
    -- Get invitation details
    SELECT * INTO v_invitation_record
    FROM public.family_invitations
    WHERE invitation_token = p_invitation_token
    AND status = 'pending'
    AND expires_at > now();
    
    IF v_invitation_record IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Solicitação inválida ou expirada'::TEXT;
        RETURN;
    END IF;
    
    -- CORREÇÃO: Verificar apenas se a solicitação é PARA este estudante
    -- Removido check incorreto do guardian_email
    IF v_invitation_record.student_id != v_student_id THEN
        RETURN QUERY SELECT FALSE, 'Esta solicitação não é para você'::TEXT;
        RETURN;
    END IF;
    
    -- Get guardian ID by email
    SELECT u.id INTO v_guardian_id
    FROM auth.users u
    WHERE u.email = v_invitation_record.guardian_email;
    
    IF v_guardian_id IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Responsável não encontrado no sistema'::TEXT;
        RETURN;
    END IF;
    
    -- Check if relationship already exists
    SELECT id INTO v_existing_relationship
    FROM public.student_guardian_relationships
    WHERE student_id = v_student_id
    AND guardian_id = v_guardian_id;
    
    IF v_existing_relationship IS NOT NULL THEN
        -- Update invitation status even if relationship exists
        UPDATE public.family_invitations
        SET status = 'accepted',
            accepted_at = now(),
            accepted_by_guardian_id = v_guardian_id
        WHERE id = v_invitation_record.id;
        
        RETURN QUERY SELECT TRUE, 'Relacionamento já existe e foi confirmado'::TEXT;
        RETURN;
    END IF;
    
    -- Create new relationship
    INSERT INTO public.student_guardian_relationships (
        student_id,
        guardian_id,
        relationship_type,
        is_primary
    ) VALUES (
        v_student_id,
        v_guardian_id,
        'parent',
        FALSE
    );
    
    -- Update invitation status
    UPDATE public.family_invitations
    SET status = 'accepted',
        accepted_at = now(),
        accepted_by_guardian_id = v_guardian_id
    WHERE id = v_invitation_record.id;
    
    RETURN QUERY SELECT TRUE, 'Solicitação aceita! Você agora está vinculado ao responsável'::TEXT;
END;
$$; 