# Questionário EduConnect - Respostas Resumidas

Este documento resume as respostas fornecidas ao questionário sobre o projeto EduConnect. As informações refletem o estado atual do repositório e a documentação disponível.

## 1. Arquitetura e Infraestrutura
- **Supabase** está configurado no projeto (`src/lib/supabase.ts`). É necessário testar no painel para confirmar funcionamento.
- **Resend API** é utilizada para envio de emails. Verifique sua chave no painel Resend.
- **MapBox** exige o token `VITE_MAPBOX_TOKEN` em `.env`.
- O domínio de produção ainda não está definido no repositório.

## 2. Usuários e Autenticação
- Três tipos de usuário: Estudante, Responsável (Guardian) e Developer.
- Estudantes se registram e responsáveis podem enviar convites para vínculo.
- Há uma rota única de login com identificação do perfil via Supabase.
- A recuperação de senha usa o fluxo padrão do Supabase Auth.

## 3. Convites e Vínculos
- Responsáveis podem buscar estudantes e enviar convites.
- Convites podem expirar (padrão de 7 dias sugerido).
- É recomendado enviar email quando o convite é criado e aceito.
- O esquema permite múltiplos responsáveis por estudante.

## 4. Compartilhamento de Localização
- O projeto suporta compartilhamento manual e atualização em tempo real.
- Histórico de localizações é salvo em `locations`; período de retenção não está definido.
- Responsáveis podem visualizar localizações atuais e passadas.
- Geofencing está documentado como futura funcionalidade.

## 5. Interface
- Parent Dashboard lista estudantes vinculados e exibe mapas.
- Student Dashboard possui botão para compartilhar localização e listar responsáveis.
- A aplicação é responsiva, priorizando uso em dispositivos móveis.

## 6. Configurações e Privacidade
- Estudantes podem remover responsáveis e pausar compartilhamento.
- É possível configurar notificações, embora algumas opções estejam em desenvolvimento.
- A conformidade com a LGPD é citada na documentação.

## 7. Problema Atual
- O erro 500 em `invite-student` está relacionado a políticas RLS. Testes locais devem confirmar a correção.

## 8. Próximos Passos Sugeridos
1. Implementar notificações por email.
2. Corrigir bugs e testar funcionalidades.
3. Melhorar a interface dos dashboards.
4. Expandir recursos de mapa e histórico.

---

Para detalhes completos consulte os arquivos de documentação na pasta `docs/` e o `README.md` principal do projeto.
