# Relatório Técnico: Falha de Internacionalização (i18n) no Parent Dashboard

## Contexto

O Parent Dashboard do sistema apresenta diversos textos e labels que **não estão sendo traduzidos** corretamente, mesmo após tentativas de implementação de i18n (internacionalização) com i18next/react-i18next.

---

## Sintomas Observados

- Chaves de tradução aparecem cruas na interface, por exemplo:
  - `dashboard.parent.title`
  - `dashboard.parent.addStudent`
- Labels e títulos permanecem em português mesmo com idioma configurado para inglês:
  - "Estudantes Vinculados"
  - "Histórico de Localizações"
  - "Clique em qualquer localização para visualizá-la no mapa"
  - "Usuário logado", "Estudante selecionado", "Estado do carregamento", etc.
- Alguns textos aparecem corretamente em inglês (ex: "Profile", "Logout", "Map", "History"), mas outros não.

---

## Exemplos Reais da Interface

```
Profile
Logout
Mauro Frank Lima de Lima
dashboard.parent.title
dashboard.parent.addStudent
Estudantes Vinculados
...
Histórico de Localizações
189 localizações
Clique em qualquer localização para visualizá-la no mapa
...
Usuário logado: <EMAIL>
Estudante selecionado: Maurício Williams Ferreira
Estado do carregamento:Finalizado
Localizações encontradas: 189
Última localização:
Data: 08/07/2025, 14:35:22
Coordenadas: 52.4746752, -0.966656
Compartilhada: Sim
```

---

## Causas Prováveis

1. **Chaves de tradução ausentes** nos arquivos `en-GB.json` e/ou `pt-BR.json`.
2. **Chaves duplicadas** ou sobrescritas nos arquivos JSON, fazendo com que apenas parte das traduções seja carregada.
3. **Uso incorreto do hook `t()`** nos componentes, por exemplo:
   - `t('dashboard.parent.title')` sem fallback, quando a chave não existe.
   - Chave escrita errada ou namespace não carregado.
4. **Configuração incorreta do i18next**:
   - Aliases de idioma não mapeados corretamente (`en` ≠ `en-GB`).
   - Namespace não incluído no carregamento.
5. **Cache/localStorage corrompido**: idioma antigo persistente, impedindo atualização das traduções.

---

## Recomendações para o Programador

- **Revisar todos os arquivos de tradução** e garantir que:
  - Todas as chaves usadas na interface existem em ambos os arquivos (`en-GB.json` e `pt-BR.json`).
  - Não há blocos duplicados (ex: dois `"dashboard"` ou dois `"profile"`).
- **Sempre use fallback no `t()`**:
  ```tsx
  t('dashboard.parent.title', 'Linked Students')
  ```
- **Verifique a configuração do i18next**:
  - Certifique-se de que o idioma ativo é `en-GB` ou `pt-BR`.
  - Mapeie aliases (`en` → `en-GB`, `pt` → `pt-BR`).
- **Limpe o cache/localStorage** após alterações de idioma ou tradução.
- **Evite hardcode**: todo texto visível deve ser chave de tradução.
- **Documente as chaves faltantes** e reporte para o responsável pelo i18n.

---

## Observação Final

Este relatório não propõe correção, apenas documenta o problema para rastreabilidade e referência futura. A falha de i18n no Parent Dashboard compromete a experiência bilíngue e deve ser tratada como prioridade para garantir consistência e acessibilidade no sistema. 