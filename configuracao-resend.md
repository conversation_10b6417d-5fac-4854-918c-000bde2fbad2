# Configuração do Resend API para Envio de E-mails - Locate Family Connect

## Introdução

Este documento descreve o processo de configuração e utilização do serviço [Resend](https://resend.com) para o envio de e-mails no projeto **Locate Family Connect** (EduConnect). O Resend é uma API moderna de e-mail projetada para desenvolvedores, oferecendo alta entregabilidade e integração simplificada com React/Vite e Supabase.

## Pré-requisitos

- Conta ativa na plataforma Resend
- API Key do Resend configurada
- Domínio verificado: `sistema-monitore.com.br`
- Projeto React 18+ com Vite
- Integração com Supabase configurada

## Stack Tecnológica Atual

- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Supabase (PostgreSQL + Edge Functions)
- **Roteamento**: React Router Dom
- **Email**: Resend API
- **Desenvolvimento**: Port 4000
- **Build**: Vite + TypeScript

## Configuração Atual do Projeto

### Variáveis de Ambiente

O projeto utiliza as seguintes variáveis de ambiente:

```env
# Resend API Configuration
VITE_RESEND_API_KEY=re_P8whBAgb_QDkLcB9DHzGfBy4JiBTw5f29
VITE_RESEND_WEBHOOK_SECRET=whsec_3PJt8OntzAgvdOGUmC5U0gJWTC3H4cCQ

# App Configuration
VITE_APP_URL=https://www.sistema-monitore.com.br
VITE_SITE_URL=https://www.sistema-monitore.com.br

# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Domínio Verificado

O projeto utiliza o domínio verificado `sistema-monitore.com.br` na plataforma Resend:

- **Status**: ✅ Verificado
- **Região**: us-east-1 (North Virginia)
- **Registros DNS**: Configurados corretamente (MX, TXT, DKIM, SPF, DMARC)
- **E-mail de origem**: `<EMAIL>`

## Implementação no Código

### Dependências Instaladas

O projeto já inclui o Resend no `package.json`:

```json
{
  "dependencies": {
    "resend": "^4.5.0"
  }
}
```

### Serviço de E-mail (`src/lib/services/EmailService.ts`)

```typescript
interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

class EmailService {
  private apiKey: string;
  private baseUrl = 'https://api.resend.com/emails';

  constructor() {
    this.apiKey = import.meta.env.VITE_RESEND_API_KEY;
    
    if (!this.apiKey) {
      throw new Error('VITE_RESEND_API_KEY não configurada no ambiente');
    }
  }

  async sendEmail(options: EmailOptions): Promise<{ success: boolean; id?: string; error?: string }> {
    const payload = {
      from: options.from || 'Locate Family Connect <<EMAIL>>',
      to: [options.to],
      subject: options.subject,
      html: options.html,
      tags: []
    };

    try {
      console.log('[EmailService] Enviando email para:', options.to);
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[EmailService] Erro na API:', errorText);
        throw new Error(`Falha no envio. Status: ${response.status}, Resposta: ${errorText}`);
      }

      const data = await response.json();
      console.log('[EmailService] Email enviado com sucesso:', data.id);
      
      return {
        success: true,
        id: data.id
      };

    } catch (error) {
      console.error('[EmailService] Erro no envio:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  async sendPasswordResetEmail(userEmail: string, userName: string, resetToken: string): Promise<boolean> {
    const resetUrl = `${import.meta.env.VITE_APP_URL}/redefinir-senha?token=${resetToken}`;
    
    const html = this.getPasswordResetEmailHTML(userName, resetUrl);
    
    const result = await this.sendEmail({
      to: userEmail,
      subject: 'Recuperação de Senha - Locate Family Connect',
      html
    });

    return result.success;
  }

  private getPasswordResetEmailHTML(userName: string, resetUrl: string): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
          <h2 style="color: #2563eb; margin-bottom: 20px;">Recuperação de Senha</h2>
          <p>Olá <strong>${userName}</strong>,</p>
          <p>Você solicitou a recuperação de senha para sua conta no Locate Family Connect.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 6px; display: inline-block; 
                      font-weight: bold;">
              Redefinir Senha
            </a>
          </div>
          
          <p style="font-size: 14px; color: #666;">
            Se você não conseguir clicar no botão, copie e cole este link no seu navegador:
          </p>
          <p style="font-size: 12px; color: #888; word-break: break-all;">
            ${resetUrl}
          </p>
          
          <hr style="margin: 30px 0; border-color: #eee;" />
          
          <p style="font-size: 14px; color: #666;">
            <strong>Importante:</strong>
          </p>
          <ul style="font-size: 14px; color: #666;">
            <li>Este link expirará em 1 hora</li>
            <li>Se você não solicitou esta recuperação, ignore este email</li>
            <li>Por segurança, não compartilhe este link com terceiros</li>
          </ul>
          
          <footer style="margin-top: 40px; font-size: 12px; color: #888; text-align: center;">
            <p>© 2025 Locate Family Connect - Sistema de Monitoramento Familiar</p>
            <p>Este é um email automático, não responda a esta mensagem.</p>
          </footer>
        </div>
      </div>
    `;
  }
}

export const emailService = new EmailService();
```

### Integração com Supabase Edge Functions

#### Edge Function: `supabase/functions/send-password-reset/index.ts`

```typescript
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')!;
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

serve(async (req) => {
  if (req.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    const { cpf } = await req.json();

    // Validar CPF
    if (!cpf || cpf.length !== 11) {
      return new Response(
        JSON.stringify({ error: 'CPF inválido' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Conectar ao Supabase
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Buscar usuário
    const { data: user, error } = await supabase
      .from('user_profiles')
      .select('id, full_name, email')
      .eq('cpf', cpf)
      .single();

    if (error || !user) {
      return new Response(
        JSON.stringify({ error: 'Usuário não encontrado' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Gerar token de recuperação
    const resetToken = crypto.randomUUID().replace(/-/g, '');
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hora

    // Salvar token no banco
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({
        reset_token: resetToken,
        reset_token_expires: expiresAt.toISOString()
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Erro ao salvar token:', updateError);
      throw new Error('Erro ao processar solicitação');
    }

    // Enviar email via Resend
    const resetUrl = `https://www.sistema-monitore.com.br/redefinir-senha?token=${resetToken}`;
    
    const emailPayload = {
      from: 'Locate Family Connect <<EMAIL>>',
      to: [user.email],
      subject: 'Recuperação de Senha - Locate Family Connect',
      html: getPasswordResetEmailHTML(user.full_name, resetUrl)
    };

    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailPayload)
    });

    if (!emailResponse.ok) {
      const errorText = await emailResponse.text();
      console.error('Erro no Resend:', errorText);
      throw new Error('Falha no envio do email');
    }

    const emailData = await emailResponse.json();
    console.log('Email enviado:', emailData.id);

    // Mascarar email para resposta
    const maskedEmail = user.email.replace(/(.{3}).*@/, '$1***@');

    return new Response(
      JSON.stringify({
        message: 'Instruções de recuperação enviadas com sucesso',
        email: maskedEmail
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Erro na edge function:', error);
    return new Response(
      JSON.stringify({ error: 'Erro interno do servidor' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
});

function getPasswordResetEmailHTML(userName: string, resetUrl: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
        <h2 style="color: #2563eb; margin-bottom: 20px;">Recuperação de Senha</h2>
        <p>Olá <strong>${userName}</strong>,</p>
        <p>Você solicitou a recuperação de senha para sua conta no Locate Family Connect.</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
            Redefinir Senha
          </a>
        </div>
        <p style="font-size: 14px; color: #666;">
          Se você não conseguir clicar no botão, copie e cole este link: ${resetUrl}
        </p>
        <p style="font-size: 14px; color: #666;">
          Este link expirará em 1 hora.
        </p>
        <footer style="margin-top: 40px; font-size: 12px; color: #888; text-align: center;">
          <p>© 2025 Locate Family Connect. Todos os direitos reservados.</p>
        </footer>
      </div>
    </div>
  `;
}
```

### Hook React para Recuperação de Senha (`src/hooks/usePasswordRecovery.ts`)

```typescript
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface PasswordRecoveryResult {
  success: boolean;
  message: string;
  email?: string;
}

export const usePasswordRecovery = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendPasswordRecovery = async (cpf: string): Promise<PasswordRecoveryResult> => {
    setLoading(true);
    setError(null);

    try {
      // Chamar a Edge Function do Supabase
      const { data, error } = await supabase.functions.invoke('send-password-reset', {
        body: { cpf }
      });

      if (error) {
        throw new Error(error.message || 'Erro ao enviar recuperação');
      }

      setLoading(false);
      return {
        success: true,
        message: data.message,
        email: data.email
      };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      setLoading(false);
      
      return {
        success: false,
        message: errorMessage
      };
    }
  };

  const resetPassword = async (token: string, newPassword: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.functions.invoke('reset-password', {
        body: { token, newPassword }
      });

      if (error) {
        throw new Error(error.message || 'Erro ao redefinir senha');
      }

      setLoading(false);
      return true;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      setLoading(false);
      return false;
    }
  };

  return {
    sendPasswordRecovery,
    resetPassword,
    loading,
    error
  };
};
```

### Componente de Recuperação (`src/pages/RecuperarSenha.tsx`)

```typescript
import React, { useState } from 'react';
import { usePasswordRecovery } from '@/hooks/usePasswordRecovery';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Link } from 'react-router-dom';

export const RecuperarSenha: React.FC = () => {
  const [cpf, setCpf] = useState('');
  const [success, setSuccess] = useState(false);
  const [maskedEmail, setMaskedEmail] = useState('');
  
  const { sendPasswordRecovery, loading, error } = usePasswordRecovery();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!cpf || cpf.length !== 11) {
      return;
    }

    const result = await sendPasswordRecovery(cpf);
    
    if (result.success) {
      setSuccess(true);
      setMaskedEmail(result.email || '');
    }
  };

  const formatCPF = (value: string) => {
    // Remove tudo que não é dígito
    const digits = value.replace(/\D/g, '');
    // Limita a 11 dígitos
    return digits.slice(0, 11);
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-green-600">Email Enviado!</CardTitle>
            <CardDescription>
              Instruções de recuperação foram enviadas para {maskedEmail}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertDescription>
                Verifique sua caixa de entrada e pasta de spam. 
                O link expirará em 1 hora.
              </AlertDescription>
            </Alert>
            
            <div className="text-center">
              <Link to="/login" className="text-blue-600 hover:underline">
                Voltar para o login
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle>Recuperar Senha</CardTitle>
          <CardDescription>
            Digite seu CPF para receber as instruções
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <div className="space-y-2">
              <label htmlFor="cpf" className="text-sm font-medium">
                CPF do Responsável
              </label>
              <Input
                id="cpf"
                type="text"
                placeholder="Digite seu CPF (apenas números)"
                value={cpf}
                onChange={(e) => setCpf(formatCPF(e.target.value))}
                maxLength={11}
                required
              />
            </div>
            
            <Button 
              type="submit" 
              className="w-full" 
              disabled={loading || cpf.length !== 11}
            >
              {loading ? 'Enviando...' : 'Enviar instruções'}
            </Button>
            
            <div className="text-center">
              <Link to="/login" className="text-sm text-blue-600 hover:underline">
                Voltar para o login
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
```

## Scripts de Desenvolvimento

### Comandos Disponíveis

```bash
# Desenvolvimento (porta 4000)
npm run dev

# Build para produção
npm run build

# Visualizar build
npm run preview

# Deploy das Edge Functions
npm run supabase:build
npm run supabase:deploy

# Testes
npm test
npm run test:coverage
```

### Teste de Email (`scripts/test-email.mjs`)

```javascript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testEmailSending() {
  try {
    console.log('Testando envio de email...');
    
    const { data, error } = await supabase.functions.invoke('send-password-reset', {
      body: { 
        cpf: '43803016215' // CPF de teste
      }
    });

    if (error) {
      console.error('Erro:', error);
      return;
    }

    console.log('✅ Email enviado com sucesso:', data);
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

testEmailSending();
```

## Configuração de Webhook

### Edge Function de Webhook (`supabase/functions/resend-webhook/index.ts`)

```typescript
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

const WEBHOOK_SECRET = Deno.env.get('RESEND_WEBHOOK_SECRET')!;

serve(async (req) => {
  if (req.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    const signature = req.headers.get('Resend-Signature');
    const payload = await req.text();

    // Verificar assinatura do webhook
    if (!verifyWebhookSignature(payload, signature, WEBHOOK_SECRET)) {
      return new Response('Unauthorized', { status: 401 });
    }

    const event = JSON.parse(payload);
    
    // Processar eventos do Resend
    switch (event.type) {
      case 'email.sent':
        console.log('📧 Email enviado:', event.data.id);
        break;
      case 'email.delivered':
        console.log('✅ Email entregue:', event.data.id);
        break;
      case 'email.bounced':
        console.log('❌ Email rejeitado:', event.data.id);
        break;
      case 'email.complained':
        console.log('⚠️  Reclamação de spam:', event.data.id);
        break;
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Erro no webhook:', error);
    return new Response('Webhook processing failed', { status: 500 });
  }
});

function verifyWebhookSignature(payload: string, signature: string | null, secret: string): boolean {
  if (!signature) return false;
  
  // Implementar verificação da assinatura
  // (lógica específica do Resend para verificar HMAC)
  return true; // Simplificado para exemplo
}
```

## Monitoramento e Logs

### Dashboard Resend

Acesse [Dashboard do Resend](https://resend.com/emails) para monitorar:

- **E-mails enviados**: Status e IDs únicos
- **Taxa de entrega**: Métricas de deliverability  
- **Bounces e reclamações**: Emails rejeitados
- **Logs detalhados**: Timeline de cada envio

### Logs do Supabase

```bash
# Verificar logs das Edge Functions
npx supabase logs --type edge-function

# Logs específicos da função
npx supabase logs --type edge-function --function send-password-reset
```

## Solução de Problemas

### Problemas Comuns

1. **Variáveis de Ambiente não Carregam**
   ```bash
   # Verificar se o arquivo .env existe na raiz
   # Variáveis devem ter prefixo VITE_ para serem acessíveis no frontend
   ```

2. **Erro 401 - API Key Inválida**
   ```bash
   # Verificar se a API key está correta
   echo $VITE_RESEND_API_KEY
   ```

3. **CORS na Edge Function**
   ```typescript
   // Adicionar headers CORS na resposta
   const corsHeaders = {
     'Access-Control-Allow-Origin': '*',
     'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
   };
   ```

4. **Build não Inclui Variáveis**
   ```typescript
   // Verificar se as variáveis estão sendo definidas corretamente
   console.log('API Key presente:', !!import.meta.env.VITE_RESEND_API_KEY);
   ```

### Scripts de Diagnóstico

```bash
# Verificar variáveis de ambiente
npm run dev | grep VITE_

# Testar Edge Function
curl -X POST http://localhost:54321/functions/v1/send-password-reset \
  -H "Content-Type: application/json" \
  -d '{"cpf":"43803016215"}'

# Verificar build
npm run build && npm run preview
```

## Melhores Práticas

### 1. **Segurança**
- Nunca expor API keys no código frontend
- Usar Edge Functions para operações sensíveis
- Validar todas as entradas do usuário
- Implementar rate limiting

### 2. **Performance**
- Cache de configurações do Resend
- Implementar retry logic para falhas temporárias
- Usar templates otimizados
- Monitorar métricas de entrega

### 3. **Manutenibilidade**
- Centralizar lógica de email em serviços
- Usar TypeScript para type safety
- Implementar logs estruturados
- Testes automatizados para email

### 4. **Monitoramento**
- Configurar alertas para falhas
- Dashboards de métricas
- Logs centralizados
- Backup de configurações

## Recursos Adicionais

- [Documentação do Resend](https://resend.com/docs)
- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [React Router Dom](https://reactrouter.com/)
- [Dashboard do Resend](https://resend.com/domains/sistema-monitore.com.br)

---

**Última atualização**: Janeiro 2025  
**Stack**: React 18 + Vite + TypeScript + Supabase  
**Status**: ✅ Configuração ativa e funcional