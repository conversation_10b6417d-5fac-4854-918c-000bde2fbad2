
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';

export interface Student {
  id: string;
  name: string;
  email: string;
  created_at: string;
  status: string;
  avatar_url?: string | null;
  phone?: string | null;
}

export const useGuardianStudents = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();

  const fetchStudents = async () => {
    if (!user) {
      setStudents([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      console.log('[GUARDIAN_STUDENTS] Fetching students for guardian:', user.email);

      // First try to get relationships
      const { data: relationships, error: relationError } = await supabase
        .from('student_guardian_relationships')
        .select('student_id')
        .eq('guardian_id', user.id);

      if (relationError) {
        console.error('[GUARDIAN_STUDENTS] Relationship query error:', relationError);
        throw relationError;
      }

      if (!relationships || relationships.length === 0) {
        console.log('[GUARDIAN_STUDENTS] No student relationships found');
        setStudents([]);
        return;
      }

      const studentIds = relationships.map(r => r.student_id);
      console.log('[GUARDIAN_STUDENTS] Found student IDs:', studentIds);

      // Get student profiles
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        // user_id é UUID, id é serial INT; precisamos filtrar e retornar pelo user_id
        .select('user_id, full_name, email, phone, created_at')
        .in('user_id', studentIds)
        .eq('user_type', 'student');

      if (profileError) {
        console.error('[GUARDIAN_STUDENTS] Profile query error:', profileError);
        throw profileError;
      }

      const studentList: Student[] = profiles?.map(profile => ({
        id: String(profile.user_id) || '',
        name: profile.full_name || profile.email || 'Estudante',
        email: profile.email || '',
        created_at: profile.created_at || '',
        status: 'active',
        avatar_url: null,
        phone: profile.phone || null,
      })) || [];

      console.log('[GUARDIAN_STUDENTS] Returning students:', studentList.length);
      setStudents(studentList);

    } catch (error: any) {
      console.error('[GUARDIAN_STUDENTS] Error fetching students:', error);
      setError(error.message || 'Erro ao buscar estudantes');
      setStudents([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStudents();
  }, [user]);

  return {
    students,
    isLoading,
    error,
    refetch: fetchStudents
  };
};
