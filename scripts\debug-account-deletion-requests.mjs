#!/usr/bin/env node

/**
 * Script de Debug - Account Deletion Requests
 * 
 * Objetivo: Identificar por que as solicitações de exclusão não aparecem na interface
 * Data: 24/06/2025
 */

import { createClient } from '@supabase/supabase-js';
import 'dotenv/config';

const supabaseUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔍 SCRIPT DE DEBUG - ACCOUNT DELETION REQUESTS');
console.log('='.repeat(60));

async function debugAccountDeletionRequests() {
  try {
    console.log('\n1️⃣ VERIFICAR DADOS BÁSICOS DA TABELA');
    console.log('-'.repeat(40));
    
    // Verificar se a tabela existe e tem dados
    const { data: allRequests, error: allError } = await supabase
      .from('account_deletion_requests')
      .select('*')
      .limit(10);
    
    if (allError) {
      console.log('❌ Erro ao buscar dados:', allError);
    } else {
      console.log(`✅ Tabela existe. Total de registros encontrados: ${allRequests?.length || 0}`);
      if (allRequests && allRequests.length > 0) {
        console.log('📋 Primeira solicitação:');
        console.log(JSON.stringify(allRequests[0], null, 2));
      }
    }

    console.log('\n2️⃣ VERIFICAR SOLICITAÇÃO ESPECÍFICA DO MAURÍCIO');
    console.log('-'.repeat(40));
    
    const { data: mauricioRequest, error: mauricioError } = await supabase
      .from('account_deletion_requests')
      .select('*')
      .eq('student_email', '<EMAIL>')
      .eq('status', 'pending');
    
    if (mauricioError) {
      console.log('❌ Erro ao buscar solicitação do Maurício:', mauricioError);
    } else {
      console.log(`✅ Solicitações do Maurício encontradas: ${mauricioRequest?.length || 0}`);
      if (mauricioRequest && mauricioRequest.length > 0) {
        console.log('📋 Dados da solicitação:');
        console.log(JSON.stringify(mauricioRequest[0], null, 2));
      }
    }

    console.log('\n3️⃣ VERIFICAR TABELA GUARDIANS');
    console.log('-'.repeat(40));
    
    // Verificar estrutura da tabela guardians
    const { data: guardians, error: guardiansError } = await supabase
      .from('guardians')
      .select('*')
      .eq('email', '<EMAIL>')
      .limit(5);
    
    if (guardiansError) {
      console.log('❌ Erro ao buscar guardians:', guardiansError);
    } else {
      console.log(`✅ Registros de guardian encontrados: ${guardians?.length || 0}`);
      if (guardians && guardians.length > 0) {
        console.log('📋 Estrutura dos guardians:');
        guardians.forEach((g, i) => {
          console.log(`Guardian ${i + 1}:`, JSON.stringify(g, null, 2));
        });
      }
    }

    console.log('\n4️⃣ VERIFICAR VINCULAÇÃO MAURÍCIO -> FRANK');
    console.log('-'.repeat(40));
    
    // Buscar student_id do Maurício
    const { data: mauricioProfile, error: mauricioProfileError } = await supabase
      .from('profiles')
      .select('id, email, full_name')
      .eq('email', '<EMAIL>')
      .single();
    
    if (mauricioProfileError) {
      console.log('❌ Erro ao buscar perfil do Maurício:', mauricioProfileError);
    } else {
      console.log('✅ Perfil do Maurício:');
      console.log(JSON.stringify(mauricioProfile, null, 2));
      
      if (mauricioProfile) {
        // Verificar se há vinculação entre Maurício e Frank
        const { data: relationship, error: relationshipError } = await supabase
          .from('guardians')
          .select('*')
          .eq('student_id', mauricioProfile.id)
          .eq('email', '<EMAIL>');
        
        if (relationshipError) {
          console.log('❌ Erro ao verificar relacionamento:', relationshipError);
        } else {
          console.log(`✅ Relacionamentos Maurício-Frank: ${relationship?.length || 0}`);
          if (relationship && relationship.length > 0) {
            console.log('📋 Relacionamento encontrado:');
            console.log(JSON.stringify(relationship[0], null, 2));
          } else {
            console.log('⚠️ PROBLEMA IDENTIFICADO: Nenhum relacionamento encontrado entre Maurício e Frank!');
          }
        }
      }
    }

    console.log('\n5️⃣ TESTAR RPC FUNCTION');
    console.log('-'.repeat(40));
    
    try {
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_guardian_deletion_requests');
      
      if (rpcError) {
        console.log('❌ Erro na RPC function:', rpcError);
        console.log('💡 Detalhes do erro:', {
          message: rpcError.message,
          code: rpcError.code,
          details: rpcError.details,
          hint: rpcError.hint
        });
      } else {
        console.log(`✅ RPC executada com sucesso. Resultados: ${rpcData?.length || 0}`);
        if (rpcData && rpcData.length > 0) {
          console.log('📋 Dados da RPC:');
          rpcData.forEach((item, i) => {
            console.log(`Item ${i + 1}:`, JSON.stringify(item, null, 2));
          });
        }
      }
    } catch (rpcError) {
      console.log('❌ Exceção na RPC:', rpcError);
    }

    console.log('\n6️⃣ SIMULAR QUERY DO HOOK (FALLBACK)');
    console.log('-'.repeat(40));
    
    // Simular exatamente o que o hook faz no fallback
    try {
      // Primeiro: buscar student_ids dos guardians
      const { data: studentIds, error: guardianError } = await supabase
        .from('guardians')
        .select('student_id')
        .eq('guardian_id', 'algum_user_id'); // Vai falhar propositalmente
      
      console.log('❌ Primeira tentativa (guardian_id):', guardianError?.message || 'Sucesso inesperado');
      
      // Segunda tentativa: usar email (como deveria ser)
      const { data: studentIdsEmail, error: guardianEmailError } = await supabase
        .from('guardians')
        .select('student_id')
        .eq('email', '<EMAIL>');
      
      if (guardianEmailError) {
        console.log('❌ Segunda tentativa (email):', guardianEmailError);
      } else {
        console.log(`✅ Student IDs via email: ${studentIdsEmail?.length || 0}`);
        if (studentIdsEmail && studentIdsEmail.length > 0) {
          const studentIdsList = studentIdsEmail.map(s => s.student_id);
          console.log('📋 IDs dos estudantes:', studentIdsList);
          
          // Buscar solicitações para esses estudantes
          const { data: deletionRequests, error: requestsError } = await supabase
            .from('account_deletion_requests')
            .select('*')
            .in('student_id', studentIdsList)
            .order('requested_at', { ascending: false });
          
          if (requestsError) {
            console.log('❌ Erro ao buscar solicitações:', requestsError);
          } else {
            console.log(`✅ Solicitações encontradas: ${deletionRequests?.length || 0}`);
            if (deletionRequests && deletionRequests.length > 0) {
              console.log('📋 Solicitações:');
              deletionRequests.forEach((req, i) => {
                console.log(`Solicitação ${i + 1}:`, {
                  id: req.id,
                  student_name: req.student_name,
                  student_email: req.student_email,
                  status: req.status,
                  requested_at: req.requested_at
                });
              });
            }
          }
        }
      }
    } catch (fallbackError) {
      console.log('❌ Erro no fallback:', fallbackError);
    }

    console.log('\n7️⃣ DIAGNÓSTICO FINAL');
    console.log('-'.repeat(40));
    
    console.log('📊 RESUMO DOS TESTES:');
    console.log('- Tabela account_deletion_requests: Verificada');
    console.log('- Solicitação do Maurício: Verificada');
    console.log('- Tabela guardians: Verificada');
    console.log('- Relacionamento Maurício-Frank: Verificado');
    console.log('- RPC function: Testada');
    console.log('- Query fallback: Simulada');
    
    console.log('\n🎯 POSSÍVEIS PROBLEMAS IDENTIFICADOS:');
    console.log('1. Hook usa guardian_id em vez de email');
    console.log('2. RPC function pode estar falhando');
    console.log('3. Relacionamento guardian-student pode estar ausente');
    console.log('4. Políticas RLS podem estar bloqueando acesso');

  } catch (error) {
    console.error('❌ Erro geral no script:', error);
  }
}

// Executar o debug
debugAccountDeletionRequests().then(() => {
  console.log('\n✅ Debug concluído!');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
}); 