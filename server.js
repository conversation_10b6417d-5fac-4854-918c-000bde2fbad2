import express from 'express';
import { LocationClient } from '@aws-sdk/client-location';
import dotenv from 'dotenv';
dotenv.config();
import fetch from 'node-fetch'; // npm install node-fetch@2

const app = express();

// Add error handling for JSON parsing
// Use standard JSON parsing with better error handling
app.use(express.json({
  limit: '10mb'
}));

// Global error handler for JSON parsing errors
app.use((error, _req, res, next) => {
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    console.error('Bad JSON received:', error.message);
    return res.status(400).json({
      error: 'Invalid JSON format',
      message: 'Please check your request body format'
    });
  }
  next();
});

// Add CORS headers
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// AWS client for future use (currently unused)
const _client = new LocationClient({
  region: 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

app.post('/api/resolve-location', async (req, res) => {
  let { latitude, longitude } = req.body || {};
  let source = 'aws';
  const accuracy = null;

  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    // Fallback: resolve por IP usando ipinfo.io
    try {
      const ipinfo = await fetch('https://ipinfo.io/json');
      const data = await ipinfo.json();
      if (data.loc) {
        const [lat, lng] = data.loc.split(',').map(Number);
        latitude = lat;
        longitude = lng;
        source = 'ipinfo';
      } else {
        latitude = -23.5489;
        longitude = -46.6388;
        source = 'default';
      }
    } catch (_e) {
      latitude = -23.5489;
      longitude = -46.6388;
      source = 'default';
    }
    return res.json({ latitude, longitude, accuracy, source });
  }

  // Se latitude/longitude vieram do frontend, pode usar AWS Place Index se quiser (opcional)
  // Aqui apenas retorna os dados recebidos
  return res.json({ latitude, longitude, accuracy, source });
});

app.listen(4001, () => {
  console.log('API server running on http://localhost:4001');
}); 