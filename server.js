import express from 'express';
import { LocationClient } from '@aws-sdk/client-location';
import dotenv from 'dotenv';
dotenv.config();
import fetch from 'node-fetch'; // npm install node-fetch@2
import process from 'node:process';

const app = express();

// Add error handling for JSON parsing
// Use standard JSON parsing with better error handling
app.use(express.json({
  limit: '10mb'
}));

// Global error handler for JSON parsing errors
app.use((error, _req, res, next) => {
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    console.error('Bad JSON received:', error.message);
    return res.status(400).json({
      error: 'Invalid JSON format',
      message: 'Please check your request body format'
    });
  }
  next();
});

// Add CORS headers
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// AWS client configuration (only initialize if credentials are available)
let _awsClient = null;
if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
  try {
    _awsClient = new LocationClient({
      region: 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
    console.log('AWS Location client initialized successfully');
  } catch (error) {
    console.warn('Failed to initialize AWS client:', error.message);
  }
} else {
  console.log('AWS credentials not found, using IP fallback only');
}

app.post('/api/resolve-location', async (req, res) => {
  try {
    console.log('[API] Received location request:', req.body);

    let { latitude, longitude } = req.body || {};
    let source = 'provided';
    const accuracy = null;

    // Validate input
    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      console.log('[API] Invalid coordinates provided, using IP fallback');

      // Fallback: resolve por IP usando ipinfo.io
      try {
        console.log('[API] Fetching IP location from ipinfo.io');
        const ipinfo = await fetch('https://ipinfo.io/json', {
          timeout: 5000,
          headers: {
            'User-Agent': 'locate-family-connect/1.0'
          }
        });

        if (!ipinfo.ok) {
          throw new Error(`IPinfo API returned ${ipinfo.status}`);
        }

        const data = await ipinfo.json();
        console.log('[API] IPinfo response:', data);

        if (data.loc) {
          const [lat, lng] = data.loc.split(',').map(Number);
          if (!isNaN(lat) && !isNaN(lng)) {
            latitude = lat;
            longitude = lng;
            source = 'ipinfo';
            console.log('[API] Successfully resolved location via IP:', { latitude, longitude });
          } else {
            throw new Error('Invalid coordinates from IPinfo');
          }
        } else {
          throw new Error('No location data from IPinfo');
        }
      } catch (ipError) {
        console.warn('[API] IP location failed:', ipError.message);

        // Final fallback to São Paulo coordinates
        latitude = -23.5489;
        longitude = -46.6388;
        source = 'default';
        console.log('[API] Using default São Paulo coordinates');
      }
    } else {
      console.log('[API] Using provided coordinates:', { latitude, longitude });
    }

    const response = {
      latitude,
      longitude,
      accuracy,
      source,
      timestamp: new Date().toISOString()
    };

    console.log('[API] Sending response:', response);
    return res.json(response);

  } catch (error) {
    console.error('[API] Unexpected error in resolve-location:', error);

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to resolve location',
      latitude: -23.5489,
      longitude: -46.6388,
      accuracy: null,
      source: 'error_fallback'
    });
  }
});

app.listen(4001, () => {
  console.log('API server running on http://localhost:4001');
}); 