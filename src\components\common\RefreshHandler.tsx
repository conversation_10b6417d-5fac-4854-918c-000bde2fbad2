import React, { useEffect } from 'react';

/**
 * Componente para lidar com refresh da página (F5) e garantir
 * que a aplicação seja recarregada corretamente como SPA
 */
const RefreshHandler: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    // Detectar se a página foi recarregada (F5, Ctrl+R, etc.)
    const handlePageLoad = () => {
      console.log('[RefreshHandler] Page loaded, checking for refresh');
      
      // Verificar se é um refresh real vs navegação normal
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const wasRefreshed = navigationEntry?.type === 'reload';
      
      if (wasRefreshed) {
        console.log('[RefreshHandler] Page was refreshed, ensuring proper SPA routing');
        
        // Garantir que o roteamento funcione após refresh
        const currentPath = window.location.pathname;
        
        // Se não é uma rota conhecida, redirecionar para login
        const knownRoutes = [
          '/login', '/register', '/dashboard', '/parent-dashboard', 
          '/student-dashboard', '/profile', '/guardians', '/student-map',
          '/accept-invitation', '/activate-account', '/system-test'
        ];
        
        const isKnownRoute = knownRoutes.some(route => 
          currentPath === route || currentPath.startsWith(route)
        );
        
        if (!isKnownRoute && currentPath !== '/') {
          console.log('[RefreshHandler] Unknown route after refresh, redirecting to root');
          window.history.replaceState({}, '', '/');
        }
      }
    };

    // Detectar refresh via beforeunload
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Salvar timestamp do refresh
      sessionStorage.setItem('lastRefresh', Date.now().toString());
    };

    // Verificar se acabou de fazer refresh
    const checkRecentRefresh = () => {
      const lastRefresh = sessionStorage.getItem('lastRefresh');
      if (lastRefresh) {
        const timeSinceRefresh = Date.now() - parseInt(lastRefresh);
        if (timeSinceRefresh < 5000) { // 5 segundos
          console.log('[RefreshHandler] Recent refresh detected');
          // Limpar o marcador
          sessionStorage.removeItem('lastRefresh');
          return true;
        }
      }
      return false;
    };

    // Executar verificações
    handlePageLoad();
    const wasRecentRefresh = checkRecentRefresh();
    
    if (wasRecentRefresh) {
      console.log('[RefreshHandler] Handling post-refresh state recovery');
    }

    // Adicionar listeners
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return <>{children}</>;
};

export default RefreshHandler; 