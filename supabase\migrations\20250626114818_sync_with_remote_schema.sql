-- =====================================================
-- SINCRONIZAÇÃO COMPLETA COM SCHEMA REMOTO
-- Baseado no banco de produção (24 tabelas)
-- =====================================================

-- Extensõ<PERSON> necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Remover tabela de teste anterior (se existir)
DROP TABLE IF EXISTS public.test_table;

-- =====================================================
-- PROFILES (Perfis de usuários) - ATUALIZADA
-- =====================================================
DROP TABLE IF EXISTS public.profiles CASCADE;
CREATE TABLE public.profiles (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT NOT NULL,
  phone VARCHAR,
  user_type TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  email TEXT NOT NULL DEFAULT '',
  cpf VARCHAR NOT NULL UNIQUE,
  birth_date DATE,
  parent_email TEXT,
  parent_cpf TEXT,
  registration_status TEXT DEFAULT 'active',
  requires_parent_confirmation BOOLEAN DEFAULT false,
  status TEXT DEFAULT 'active',
  last_login_at TIMESTAMPTZ,
  login_count INTEGER DEFAULT 0
);

-- =====================================================
-- LOCATIONS (Localizações) - ATUALIZADA
-- =====================================================
DROP TABLE IF EXISTS public.locations CASCADE;
CREATE TABLE public.locations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL DEFAULT auth.uid() REFERENCES auth.users(id) ON DELETE CASCADE,
  latitude DOUBLE PRECISION NOT NULL,
  longitude DOUBLE PRECISION NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT now() NOT NULL,
  address TEXT,
  shared_with_guardians BOOLEAN DEFAULT false,
  accuracy NUMERIC,
  speed NUMERIC,
  bearing NUMERIC,
  battery_level NUMERIC,
  is_mocked BOOLEAN DEFAULT false,
  source TEXT DEFAULT 'manual',
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- REMOVAL_REQUESTS (Sistema de remoção segura) - ATUALIZADA
-- =====================================================
DROP TABLE IF EXISTS public.removal_requests CASCADE;
CREATE TABLE public.removal_requests (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  guardian_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  student_name TEXT NOT NULL,
  guardian_name TEXT NOT NULL,
  guardian_email TEXT NOT NULL,
  reason TEXT,
  status TEXT DEFAULT 'pending',
  request_token UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  expires_at TIMESTAMPTZ DEFAULT (now() + interval '7 days'),
  processed_at TIMESTAMPTZ,
  processed_by UUID REFERENCES auth.users(id)
);

-- =====================================================
-- STUDENT_GUARDIAN_RELATIONSHIPS (Relacionamentos)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.student_guardian_relationships (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  guardian_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  relationship_type TEXT NOT NULL,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- FAMILY_INVITATIONS (Convites familiares)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.family_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  guardian_email TEXT NOT NULL,
  student_name TEXT NOT NULL,
  student_email TEXT NOT NULL,
  invitation_token TEXT NOT NULL UNIQUE,
  status TEXT DEFAULT 'pending',
  expires_at TIMESTAMPTZ DEFAULT (now() + interval '7 days') NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  accepted_at TIMESTAMPTZ,
  accepted_by_guardian_id UUID REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- STUDENTS (Estudantes)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.students (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  school_id TEXT NOT NULL,
  school_name TEXT NOT NULL,
  grade TEXT NOT NULL,
  class TEXT NOT NULL,
  status TEXT DEFAULT 'active',
  guardian_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  location_sharing BOOLEAN DEFAULT false NOT NULL,
  last_location JSONB,
  created_at TIMESTAMPTZ DEFAULT timezone('utc', now()),
  updated_at TIMESTAMPTZ DEFAULT timezone('utc', now())
);

-- =====================================================
-- GUARDIAN_PROFILES (Perfis de responsáveis)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.guardian_profiles (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT,
  cpf TEXT,
  birth_date DATE,
  status TEXT DEFAULT 'active',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- TABELAS AUXILIARES DO SISTEMA
-- =====================================================

-- AUTH_LOGS (Logs de autenticação)
CREATE TABLE IF NOT EXISTS public.auth_logs (
  id SERIAL PRIMARY KEY,
  event_type TEXT,
  user_id UUID,
  metadata JSONB,
  occurred_at TIMESTAMPTZ DEFAULT now()
);

-- LOCATION_NOTIFICATIONS (Notificações de localização)
CREATE TABLE IF NOT EXISTS public.location_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  location_id UUID REFERENCES public.locations(id) ON DELETE CASCADE,
  guardian_id UUID,
  guardian_email TEXT NOT NULL,
  student_id UUID NOT NULL,
  status TEXT DEFAULT 'unread' NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  viewed_at TIMESTAMPTZ
);

-- ACCOUNT_DELETION_REQUESTS (Solicitações de exclusão)
CREATE TABLE IF NOT EXISTS public.account_deletion_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reason TEXT,
  status TEXT DEFAULT 'pending',
  created_at TIMESTAMPTZ DEFAULT now(),
  processed_at TIMESTAMPTZ,
  processed_by UUID REFERENCES auth.users(id)
);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.removal_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.student_guardian_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guardian_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.auth_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.location_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.account_deletion_requests ENABLE ROW LEVEL SECURITY;

-- Políticas permissivas para desenvolvimento local
CREATE POLICY "Local dev - profiles" ON public.profiles
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - locations" ON public.locations
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - removal_requests" ON public.removal_requests
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - relationships" ON public.student_guardian_relationships
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - family_invitations" ON public.family_invitations
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - students" ON public.students
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - guardian_profiles" ON public.guardian_profiles
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - auth_logs" ON public.auth_logs
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - location_notifications" ON public.location_notifications
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Local dev - account_deletion_requests" ON public.account_deletion_requests
  FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- =====================================================
-- FUNÇÕES RPC ESSENCIAIS
-- =====================================================

-- Função principal para buscar localizações
CREATE OR REPLACE FUNCTION get_student_locations_with_names(
  p_student_id UUID DEFAULT NULL,
  p_time_filter TEXT DEFAULT 'all'
)
RETURNS TABLE (
  id UUID,
  latitude DOUBLE PRECISION,
  longitude DOUBLE PRECISION,
  location_timestamp TIMESTAMPTZ,
  address TEXT,
  shared_with_guardians BOOLEAN,
  accuracy NUMERIC,
  speed NUMERIC,
  bearing NUMERIC,
  battery_level NUMERIC,
  is_mocked BOOLEAN,
  source TEXT,
  created_at TIMESTAMPTZ,
  student_name TEXT
) 
LANGUAGE plpgsql 
SECURITY DEFINER 
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    l.id,
    l.latitude,
    l.longitude,
    l.timestamp as location_timestamp,
    l.address,
    l.shared_with_guardians,
    l.accuracy,
    l.speed,
    l.bearing,
    l.battery_level,
    l.is_mocked,
    l.source,
    l.created_at,
    COALESCE(p.full_name, 'Usuário') as student_name
  FROM public.locations l
  LEFT JOIN public.profiles p ON l.user_id = p.user_id
  WHERE (p_student_id IS NULL OR l.user_id = p_student_id)
  ORDER BY l.timestamp DESC
  LIMIT 100;
END;
$$;

-- Função para criar solicitação de remoção
CREATE OR REPLACE FUNCTION create_removal_request(
  p_student_id UUID,
  p_guardian_id UUID,
  p_reason TEXT DEFAULT NULL
)
RETURNS JSON 
LANGUAGE plpgsql 
SECURITY DEFINER 
AS $$
DECLARE
  student_profile RECORD;
  guardian_profile RECORD;
  new_request_id UUID;
BEGIN
  -- Buscar perfis
  SELECT * INTO student_profile FROM public.profiles WHERE user_id = p_student_id;
  SELECT * INTO guardian_profile FROM public.profiles WHERE user_id = p_guardian_id;
  
  -- Verificações básicas
  IF student_profile.user_id IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'Estudante não encontrado');
  END IF;
  
  IF guardian_profile.user_id IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'Responsável não encontrado');
  END IF;
  
  -- Criar solicitação
  INSERT INTO public.removal_requests (
    student_id, guardian_id, student_name, guardian_name, 
    guardian_email, reason, status
  ) VALUES (
    p_student_id, p_guardian_id, student_profile.full_name, 
    guardian_profile.full_name, guardian_profile.email, p_reason, 'pending'
  ) RETURNING id INTO new_request_id;
  
  RETURN json_build_object(
    'success', true,
    'request_id', new_request_id,
    'message', 'Solicitação criada com sucesso'
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object('success', false, 'message', 'Erro: ' || SQLERRM);
END;
$$;

-- Função para buscar solicitações do responsável
CREATE OR REPLACE FUNCTION get_guardian_removal_requests(p_guardian_id UUID)
RETURNS TABLE (
  id UUID,
  student_name TEXT,
  reason TEXT,
  status TEXT,
  request_token UUID,
  created_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ
) 
LANGUAGE plpgsql 
SECURITY DEFINER 
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    rr.id, rr.student_name, rr.reason, rr.status,
    rr.request_token, rr.created_at, rr.expires_at
  FROM public.removal_requests rr
  WHERE rr.guardian_id = p_guardian_id
    AND rr.status = 'pending'
    AND rr.expires_at > now()
  ORDER BY rr.created_at DESC;
END;
$$;

-- =====================================================
-- TRIGGERS PARA ATUALIZAÇÃO AUTOMÁTICA
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar triggers onde necessário
CREATE TRIGGER update_profiles_updated_at 
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at 
  BEFORE UPDATE ON public.students
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_guardian_profiles_updated_at 
  BEFORE UPDATE ON public.guardian_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- DADOS DE TESTE LOCAL
-- =====================================================

-- Usuário de teste local (compatível com ambiente)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    INSERT INTO auth.users (
      id, email, raw_app_meta_data, raw_user_meta_data, 
      is_super_admin, role, aud, created_at, updated_at, email_confirmed_at
    ) VALUES (
      '00000000-0000-0000-0000-000000000001', '<EMAIL>',
      '{}', '{}', false, 'authenticated', 'authenticated',
      now(), now(), now()
    );
    
    INSERT INTO public.profiles (
      user_id, full_name, email, cpf, user_type
    ) VALUES (
      '00000000-0000-0000-0000-000000000001',
      'Usuário Local', '<EMAIL>', '12345678901', 'student'
    );
  END IF;
END $$;
