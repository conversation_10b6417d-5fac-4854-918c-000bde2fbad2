FROM node:20-slim as builder

WORKDIR /app

# Primeiro, copiar arquivos de configuração TypeScript
COPY tsconfig*.json ./

# Depois, copiar arquivos de pacote e instalar dependências
COPY package*.json ./
RUN npm config set fetch-retry-maxtimeout 100000 \
    && npm config set fetch-retries 8 \
    && npm ci

# Agora copiar o resto dos arquivos
COPY . .

# Verificar arquivos de configuração existentes
RUN echo "Verificando arquivos do projeto:" && ls -la \
    && echo "\nLocalizando arquivos de configuração TypeScript:" \
    && find . -name "tsconfig*.json" -o -name "*.config.js" | grep . || echo "No config files found"

# Garantir que o tsconfig.json existe e é válido
RUN if [ ! -f "tsconfig.json" ]; then \
      echo "tsconfig.json não encontrado. Criando arquivo..." \
      && echo '{ "compilerOptions": { "target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "jsx": "react-jsx", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "paths": { "@/*": ["./src/*"] } }, "include": ["src"], "exclude": ["node_modules"] }' > tsconfig.json; \
    fi \
    && cat tsconfig.json

# Criar configuração específica do Vite para build no Docker
RUN echo "Criando configuração otimizada para build..." \
    && echo 'import { defineConfig } from "vite";\
import react from "@vitejs/plugin-react-swc";\
import path from "path";\
\
export default defineConfig({\
  plugins: [react()],\
  resolve: {\
    alias: {\
      "@": path.resolve(__dirname, "./src"),\
    },\
  },\
  build: {\
    target: "es2020",\
    outDir: "dist",\
    assetsDir: "assets",\
    sourcemap: false,\
    emptyOutDir: true,\
    chunkSizeWarningLimit: 2000\
  }\
});' > vite.config.build.ts \
    && cat vite.config.build.ts

# Compilar com a configuração especial para build
RUN echo "\nCompilando com Vite usando configuração otimizada:" \
    && npx vite build --config vite.config.build.ts

# Development stage
FROM node:20-slim
WORKDIR /app

# Copy package files and install production dependencies + serve (for static file serving)
COPY package*.json ./
RUN npm config set fetch-retry-maxtimeout 600000 \
    && npm config set fetch-retry-mintimeout 100000 \
    && npm config set fetch-retries 5 \
    && npm ci --omit=dev --fetch-timeout=600000 \
    && npm install -g serve

# Copy built files from builder
COPY --from=builder /app/dist ./dist

# Expose the port
EXPOSE 8080

# Use serve to serve static files in all environments
CMD serve -s dist -l 8080
