
// Extending the UserSession interface
export interface UserSession {
  id: string;
  email: string;
  user_metadata?: {
    user_type?: string;
    full_name?: string;
    phone?: string;
    [key: string]: any;
  };
}

export interface Student {
  id: string;
  name: string;
  email: string;
  created_at: string;
}

export interface StudentWithProfiles extends Student {
  user_profiles?: {
    name: string;
    email: string;
  };
}

// Guardian interface padronizada
export interface Guardian {
  id: string;
  full_name: string;
  email: string;
  phone?: string | null;
  cpf?: string | null;
  birth_date?: string | null;
  status?: string | null;
  created_at: string;
  is_active?: boolean;
  student_id?: string;
}

// Alias para compatibilidade
export type GuardianData = Guardian;

export interface InviteStudentResult {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

// Tipos de autenticação
export interface AuthCredentials {
  email: string;
  password: string;
  userType?: string;
}

export interface AuthResult {
  success: boolean;
  user?: any;
  error?: string;
}

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
}

export interface ExtendedUser {
  id: string;
  email: string;
  user_metadata: {
    user_type: string;
    full_name?: string;
    phone?: string;
  };
  // Campos retornados do perfil para acesso facilitado
  full_name?: string;
  phone?: string;
  status?: string;
  last_login_at?: string | null;
  login_count?: number;
}
