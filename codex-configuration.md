# Configuração do Codex para Locate-Family-Connect

## Acesso à Internet para o Agente

Para o projeto Locate-Family-Connect, a configuração recomendada de acesso à internet para o agente Codex é:

### Nível de Acesso: Lista de Permissões Personalizadas

Baseado na lista "Common dependencies" com adições específicas para o projeto:

```
# Domínios base (já inclusos na lista "Common dependencies")
github.com
githubusercontent.com
npmjs.com
npmjs.org

# Domínios específicos para o projeto
supabase.com        # Para documentação e referências Supabase
supabase.io         # Endpoints de API Supabase
mapbox.com          # Documentação e API MapBox
resend.com          # Documentação Resend API
resend.dev          # API Resend
```

### Métodos HTTP Permitidos

Para máxima segurança, recomendamos restringir os métodos HTTP apenas para:
- `GET`
- `HEAD`
- `OPTIONS`

Esta configuração permite que o Codex acesse documentação e referências necessárias sem comprometer a segurança do projeto.

## Mitigação de Riscos

Para proteger o projeto contra risks potenciais:

1. **Prompt Injection**: Nunca pedir para o Codex buscar e executar conteúdo de fontes não confiáveis
2. **Exfiltração de código**: Revisar todas as alterações propostas pelo Codex antes de mesclá-las
3. **Vulnerabilidades**: Verificar o código gerado com ferramentas de análise de segurança

## Protocolo de Verificação para Trabalho do Codex

Seguindo o princípio "Break-Safe Philosophy" do projeto:

1. Verificar funcionamento atual (`npm run build` e `npm run dev`)
2. Criar backup de arquivos críticos antes de alterações
3. Validar todas as alterações propostas pelo Codex
4. Testar funcionalidades afetadas
5. Fazer commit apenas após verificação completa

Este protocolo garante que todas as contribuições do Codex sigam os padrões de segurança e qualidade do projeto.
