-- Migration: Remove obsolete guardians table
-- Date: 2025-06-26
-- Reason: Table is empty and redundant with guardian_profiles + student_guardian_relationships
-- Safety: Verified no data loss (0 records) and no dependent tables

BEGIN;

-- Log the removal for auditing
DO $$
BEGIN
    RAISE NOTICE 'Removing obsolete guardians table - verified empty (0 records)';
    RAISE NOTICE 'Active guardian system uses guardian_profiles + student_guardian_relationships';
END $$;

-- Verify table is indeed empty before dropping (safety check)
DO $$
DECLARE
    guardian_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO guardian_count FROM public.guardians;
    IF guardian_count > 0 THEN
        RAISE EXCEPTION 'SAFETY CHECK FAILED: guardians table contains % records - aborting drop', guardian_count;
    END IF;
    RAISE NOTICE 'Safety check passed: guardians table is empty';
END $$;

-- Drop the obsolete table
DROP TABLE IF EXISTS public.guardians CASCADE;

-- Add comment for documentation
COMMENT ON SCHEMA public IS 'Schema updated on 2025-06-26: Removed obsolete guardians table. Active guardian system uses guardian_profiles + student_guardian_relationships.';

COMMIT; 