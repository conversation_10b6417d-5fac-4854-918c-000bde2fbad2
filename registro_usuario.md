# 📋 Especificação: Tela de Cadastro de Usuários

## 🎯 Objetivo

Evitar erros no tipo de conta criada e reforçar segurança e usabilidade na criação de contas no sistema Locate-Family-Connect.

---

## ✅ Requisitos Funcionais

1. **Data de nascimento obrigatória**

   * A data de nascimento deve ser informada durante o cadastro.
   * Usada para verificar se o usuário é menor de idade e impedir que um menor crie uma conta de guardião.
   * Também evita que um guardião crie uma conta de estudante por engano.

2. **Confirmação do tipo de conta**

   * Antes de permitir o preenchimento do cadastro, o sistema deve perguntar:

     > "Você deseja criar uma conta de **guardião** ou de **estudante**?"
   * A escolha deve ser clara e obrigatória.

3. **Fluxo válido para estudantes**

   * Estudantes podem criar contas normalmente.
   * Posteriormente, o aluno pode:

     * Cadastrar um responsável.
     * Enviar um **link mágico** para convidar o guardião.
   * Isso associa automaticamente o aluno ao pai/mãe/responsável.

4. **Evitar erros de cadastro**

   * A verificação de tipo de conta impede cadastros acidentais.
   * O sistema deve:

     * Permitir que o usuário exclua a conta via perfil.
     * Opcionalmente, excluir contas inativas por 10–30 dias com aviso prévio.

---

## 🔐 Validação de Senha

* O sistema **deve mostrar regras de senha claras**:

  * Ao digitar uma senha inválida, deve aparecer um alerta ou pop-up com:

    > "Sua senha deve conter ao menos 8 caracteres, uma letra maiúscula, um número e um caractere especial."

---

## 📩 Confirmação por E-mail

* Após aceitar os termos e concluir o cadastro:

  * O usuário deve receber um **link de verificação por e-mail**.
  * Isso garante a ativação da conta apenas com e-mail válido.

---

## 💬 Frases-chave de requisitos (para referência técnica)

* "Temos que perguntar se ela confirma realmente que ela quer criar uma conta de guardião."
* "Não pode criar direto."
* "Tem que primeiro perguntar se ela quer realmente criar uma conta de pai ou de estudante."
* "Tem que ter no perfil da pessoa a possibilidade de ela excluir a conta."
* "Se a conta ficar mais de 10, 20, 30 dias sem uso, podemos excluir com aviso."
* "Ela tem que saber o tipo de senha que pode criar."
* "Se errar, mostrar alerta explicando os critérios."
* "Tem que ser informada que será enviado e-mail com link."

---

## 🚩 Status de Implementação

- [ ] Confirmar tipo de conta antes do cadastro
- [ ] Validação adequada da data de nascimento
- [ ] Regras de senha claras e consistentes 
- [ ] Fluxo de verificação por e-mail
- [ ] Opção para exclusão da conta no perfil
- [ ] Sistema de exclusão de contas inativas

*Última atualização: 04/06/2025*
