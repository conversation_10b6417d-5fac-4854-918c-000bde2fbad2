import React, { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';
import { Bell, X } from 'lucide-react';

interface NativeNotificationProps {
  title: string;
  body: string;
  icon?: React.ReactNode;
  duration?: number;
  onClose?: () => void;
  actions?: {
    text: string;
    onClick: () => void;
    destructive?: boolean;
  }[];
}

export default function NativeNotification({
  title,
  body,
  icon = <Bell size={18} />,
  duration = 5000,
  onClose,
  actions = []
}: NativeNotificationProps) {
  const [visible, setVisible] = useState(true);
  const [exiting, setExiting] = useState(false);
  
  const isIOS = Capacitor.getPlatform() === 'ios';
  const isAndroid = Capacitor.getPlatform() === 'android';
  const isNative = Capacitor.isNativePlatform();
  
  const platformClass = isIOS ? 'ios-notification' : isAndroid ? 'android-notification' : '';
  
  const handleClose = () => {
    setExiting(true);
    setTimeout(() => {
      setVisible(false);
      if (onClose) onClose();
    }, 300); // Match animation duration
  };
  
  // Auto-close after duration
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [duration]);
  
  if (!visible) return null;
  
  return (
    <div 
      className={`native-notification-toast ${platformClass} ${exiting ? 'notification-exit' : ''}`}
      role="alert"
    >
      <div className="notification-icon">
        {icon}
      </div>
      
      <div className="notification-content">
        <div className="notification-title">{title}</div>
        <div className="notification-body">{body}</div>
        <div className="notification-time">{new Date().toLocaleTimeString()}</div>
        
        {actions.length > 0 && (
          <div className="notification-actions">
            {actions.map((action, index) => (
              <button
                key={index}
                className={`notification-action-button ${action.destructive ? 'destructive' : ''}`}
                onClick={() => {
                  action.onClick();
                  handleClose();
                }}
              >
                {action.text}
              </button>
            ))}
          </div>
        )}
      </div>
      
      <button 
        onClick={handleClose}
        className="notification-close"
        aria-label="Close notification"
        style={{ background: 'transparent', border: 'none', cursor: 'pointer', padding: '4px' }}
      >
        <X size={16} />
      </button>
    </div>
  );
}