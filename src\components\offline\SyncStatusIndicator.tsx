
/**
 * Sync Status Indicator - Shows synchronization status and controls
 * Integrates with existing network and cache indicators
 */

import React from 'react';
import { RefreshCw, Clock, AlertCircle, CheckCircle, Pause, Play, RotateCcw } from 'lucide-react';
import { useSyncQueue } from '@/hooks/useSyncQueue';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface SyncStatusIndicatorProps {
  className?: string;
  variant?: 'minimal' | 'detailed' | 'card';
  showControls?: boolean;
}

export function SyncStatusIndicator({ 
  className = '', 
  variant = 'minimal',
  showControls = false 
}: SyncStatusIndicatorProps) {
  const { 
    queueStats, 
    syncStats, 
    isInitialized, 
    isSyncing,
    forceSync,
    clearCompleted,
    error 
  } = useSyncQueue();
  
  const { isOnline } = useNetworkStatus();

  if (!isInitialized) {
    return (
      <div className={cn("flex items-center gap-1 text-xs text-gray-500", className)}>
        <RefreshCw className="w-3 h-3 animate-pulse" />
        <span>Inicializando sync...</span>
      </div>
    );
  }

  const totalActions = queueStats ? 
    queueStats.totalPending + queueStats.totalFailed + queueStats.totalCompleted : 0;
  
  const progressPercentage = queueStats && totalActions > 0 ? 
    ((queueStats.totalCompleted) / totalActions) * 100 : 100;

  if (variant === 'minimal') {
    return (
      <div className={cn(
        "flex items-center gap-1 text-xs",
        error ? "text-red-600" : 
        isSyncing ? "text-blue-600" : 
        (queueStats?.totalPending || 0) > 0 ? "text-yellow-600" : 
        "text-green-600",
        className
      )}>
        <RefreshCw className={cn(
          "w-3 h-3",
          isSyncing && "animate-spin"
        )} />
        
        {error ? (
          <span>Erro sync</span>
        ) : isSyncing ? (
          <span className="hidden sm:inline">Sincronizando...</span>
        ) : (queueStats?.totalPending || 0) > 0 ? (
          <Badge variant="secondary" className="text-xs">
            {queueStats?.totalPending} pendente{(queueStats?.totalPending || 0) > 1 ? 's' : ''}
          </Badge>
        ) : (
          <span className="hidden sm:inline">Sync OK</span>
        )}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={cn(
        "p-3 rounded-lg border bg-card text-card-foreground",
        className
      )}>
        <div className="flex items-center gap-2 mb-3">
          <RefreshCw className={cn(
            "w-4 h-4",
            isSyncing && "animate-spin",
            error ? "text-red-600" : 
            isSyncing ? "text-blue-600" : 
            "text-green-600"
          )} />
          <span className="font-medium text-sm">Sistema de Sincronização</span>
          
          {!isOnline && (
            <Badge variant="outline" className="text-xs">
              Offline
            </Badge>
          )}
        </div>

        {queueStats && (
          <div className="space-y-2 text-xs">
            {/* Progress bar for sync status */}
            {totalActions > 0 && (
              <div className="space-y-1">
                <div className="flex justify-between text-gray-600">
                  <span>Progresso:</span>
                  <span>{Math.round(progressPercentage)}%</span>
                </div>
                <Progress value={progressPercentage} className="h-1" />
              </div>
            )}
            
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="flex flex-col">
                <span className="font-medium text-green-600">{queueStats.totalCompleted}</span>
                <span className="text-gray-500">Completas</span>
              </div>
              <div className="flex flex-col">
                <span className="font-medium text-yellow-600">{queueStats.totalPending}</span>
                <span className="text-gray-500">Pendentes</span>
              </div>
              <div className="flex flex-col">
                <span className="font-medium text-red-600">{queueStats.totalFailed}</span>
                <span className="text-gray-500">Falharam</span>
              </div>
            </div>
            
            {syncStats?.lastSyncTime && (
              <div className="flex items-center gap-1 text-gray-600 mt-2">
                <Clock className="w-3 h-3" />
                <span>Último sync: {syncStats.lastSyncTime.toLocaleTimeString()}</span>
              </div>
            )}

            {syncStats && (
              <div className="flex justify-between text-gray-600">
                <span>Taxa de sucesso:</span>
                <span>{Math.round(syncStats.successRate)}%</span>
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="flex items-center gap-1 text-red-600 text-xs mt-2">
            <AlertCircle className="w-3 h-3" />
            <span>{error}</span>
          </div>
        )}

        {showControls && (
          <div className="flex gap-2 mt-3">
            <Button
              size="sm"
              variant="outline"
              onClick={forceSync}
              disabled={!isOnline || isSyncing}
              className="text-xs h-7"
            >
              <RefreshCw className={cn(
                "w-3 h-3 mr-1",
                isSyncing && "animate-spin"
              )} />
              {isSyncing ? 'Sincronizando' : 'Forçar Sync'}
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={clearCompleted}
              className="text-xs h-7"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Limpar
            </Button>
          </div>
        )}
      </div>
    );
  }

  // Variant 'card'
  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          <RefreshCw className={cn(
            "w-4 h-4",
            isSyncing && "animate-spin",
            error ? "text-red-600" : 
            isSyncing ? "text-blue-600" : 
            "text-green-600"
          )} />
          Sistema de Sincronização
          
          {error && <Badge variant="destructive">Erro</Badge>}
          {isSyncing && <Badge variant="secondary">Ativo</Badge>}
          {!isOnline && <Badge variant="outline">Offline</Badge>}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {queueStats && syncStats && (
          <>
            {/* Status summary */}
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-3 h-3 text-green-600" />
                <span>{queueStats.totalCompleted} sincronizadas</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="w-3 h-3 text-yellow-600" />
                <span>{queueStats.totalPending} pendentes</span>
              </div>
              
              {queueStats.totalFailed > 0 && (
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-3 h-3 text-red-600" />
                  <span>{queueStats.totalFailed} falharam</span>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  Taxa: {Math.round(syncStats.successRate)}%
                </span>
              </div>
            </div>

            {/* Progress bar */}
            {totalActions > 0 && (
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-gray-600">
                  <span>Progresso da sincronização</span>
                  <span>{Math.round(progressPercentage)}%</span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
              </div>
            )}

            {/* Last sync time */}
            {syncStats.lastSyncTime && (
              <div className="text-xs text-gray-600">
                Última sincronização: {syncStats.lastSyncTime.toLocaleString()}
              </div>
            )}
          </>
        )}

        {error && (
          <div className="text-red-600 text-sm flex items-center gap-1">
            <AlertCircle className="w-4 h-4" />
            {error}
          </div>
        )}

        {showControls && (
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={forceSync}
              disabled={!isOnline || isSyncing}
              className="flex-1"
            >
              <RefreshCw className={cn(
                "w-3 h-3 mr-1",
                isSyncing && "animate-spin"
              )} />
              {isSyncing ? 'Sincronizando' : 'Sincronizar'}
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={clearCompleted}
              className="flex-1"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Limpar Completas
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
