# 📈 Plano de Melhoria do Frontend – Student Dashboard

## 1. Objetivo
Melhorar a experiência visual, usabilidade, acessibilidade e internacionalização da Student Dashboard, tornando-a mais clara, moderna e fácil de usar em web e mobile.

---

## 2. Problemas Atuais Identificados
- Hierarquia visual fraca: títulos e seções pouco destacados
- Layout "chapado" (tudo junto, sem separação visual)
- Botões e ações pouco evidentes
- Informações importantes (nome, email, ações) misturadas
- Falta de agrupamento lógico (ex: perfil, ações rápidas, responsáveis)
- Responsividade limitada
- Textos ainda em português ou como raw keys
- Acessibilidade básica

---

## 3. Propostas de Melhoria

### 3.1 Hierarquia Visual e Agrupamento
- **Usar cards com sombra, borda e padding** para separar seções:
  - Perfil do estudante
  - Ações rápidas (Quick Actions)
  - Lista de responsáveis (My Guardians)
  - Histórico/Mapa
  - Solicitações de conexão
- **Títulos destacados** com tamanho, peso e cor diferenciados
- **Separadores** (dividers) entre blocos principais

### 3.2 Layout e Responsividade
- **Grid ou flexbox** para organizar cards em colunas (desktop) e empilhados (mobile)
- **Espaçamento consistente** entre cards e elementos
- **Breakpoints** para mobile-first, garantindo boa visualização em telas pequenas

### 3.3 Botões e Ações
- **Botões padronizados** (usando UI kit, ex: shadcn ou Tamagui)
- **Ações rápidas** (ex: Update Location, Send to Guardians) em destaque, com ícones e feedback visual
- **Botões de remover responsável** claros, com confirmação

### 3.4 Perfil e Informações Pessoais
- **Card de perfil** com avatar, nome, email, telefone
- **Ações de perfil** (editar, logout) agrupadas e visíveis
- **Tradução de todos os labels** (ex: Name, Email, Phone)

### 3.5 Lista de Responsáveis (Guardians)
- **Card para cada responsável** com nome, email, status, telefone, data de adição
- **Status visual** (ex: badge "Active", "Connected")
- **Botão de remover** com confirmação
- **Ação de adicionar responsável** em destaque

### 3.6 Histórico e Mapa
- **Card separado** para histórico de localização e mapa
- **Títulos claros** ("Location History", "Map")
- **Ajustar textos para internacionalização**

### 3.7 Solicitações de Conexão
- **Card próprio** para "Guardian Requests"
- **Mensagem amigável** quando não houver solicitações

### 3.8 Internacionalização
- **Todos os textos via t()** e presentes nos arquivos de tradução
- **Garantir fallback e chaves paralelas em pt-BR/en-GB**

### 3.9 Acessibilidade
- **Uso de aria-labels** e roles apropriados
- **Tabindex e navegação por teclado**
- **Contraste adequado** para textos e botões

---

## 4. Passos para Refatoração
1. **Auditar JSX atual** e mapear todos os textos e seções
2. **Criar estrutura de cards** com Tailwind (ou UI kit)
3. **Reorganizar layout** usando grid/flex responsivo
4. **Padronizar botões e ações**
5. **Ajustar todos os textos para t()**
6. **Revisar responsividade e acessibilidade**
7. **Testar em web e mobile**
8. **Revisar com usuários reais (estudantes)**

---

## 5. Referências e Inspirações
- Figma: Dashboards modernos (Material, shadcn, Tamagui)
- [shadcn/ui Dashboard](https://ui.shadcn.com/examples/dashboard)
- [Tamagui Examples](https://tamagui.dev/showcase)
- [Tailwind UI](https://tailwindui.com/components/application-ui/dashboards)

---

## 6. Checklist de Implementação
- [ ] Cards para cada seção
- [ ] Títulos destacados
- [ ] Layout responsivo (grid/flex)
- [ ] Botões padronizados
- [ ] Textos internacionalizados
- [ ] Acessibilidade validada
- [ ] Testes em web/mobile
- [ ] Feedback visual para ações

---

**Este plano deve ser seguido para garantir uma Student Dashboard moderna, acessível, internacionalizada e fácil de usar.** 