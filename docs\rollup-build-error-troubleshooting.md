# Rollup Build Error Troubleshooting Guide

## Resumo do Problema

Você está enfrentando o erro:

```
Error: Cannot find module '@rollup/rollup-linux-x64-gnu'
```

Esse erro ocorre durante o build do projeto (Vite/Rollup) em ambientes Linux (CI/CD, Lovable, etc).

---

## Causa Raiz

- O Rollup (bundler usado pelo Vite) depende de binários nativos específicos para cada plataforma.
- O npm, por vezes, **não instala corretamente dependências opcionais** (optional dependencies) em ambientes Linux, especialmente em builds automatizados.
- O binário `@rollup/rollup-linux-x64-gnu` está ausente do `node_modules`, impedindo o build.
- Isso é um **bug conhecido do npm** (veja [npm issue #4828](https://github.com/npm/cli/issues/4828)).

---

## Como Identificar

- O erro aparece logo no início do build, antes de qualquer código do projeto ser processado.
- Mesmo com configurações mínimas do Vite, o erro persiste.
- Limpar cache, reinstalar dependências localmente geralmente resolve, mas em ambientes CI/CD pode persistir.

---

## Soluções e Procedimentos

### 1. Limpar dependências e reinstalar

No terminal do ambiente de build (se possível):

```sh
rm -rf node_modules package-lock.json
yarn cache clean # ou npm cache clean --force
npm install
```

- Isso força o npm a baixar todas as dependências novamente, incluindo as opcionais.

---

### 2. Verificar Versão do NPM

- Use sempre uma versão estável e atualizada do npm (>= 8.x).
- Em ambientes CI/CD, especifique a versão do Node/NPM no setup.

---

### 3. Ambiente de Build (Lovable, Vercel, etc)

- Se o erro persistir, **é um problema de infraestrutura** da plataforma.
- Entre em contato com o suporte da Lovable (ou da sua plataforma) e envie o log do erro.
- Solicite um rebuild limpo do ambiente.

---

### 4. Documentação e Referências

- [npm issue #4828 - Optional dependencies not installed](https://github.com/npm/cli/issues/4828)
- [Rollup GitHub Issues](https://github.com/rollup/rollup/issues)
- [Vite Build Troubleshooting](https://vitejs.dev/guide/troubleshooting.html)

---

## Conclusão

- **Não é um erro do seu código ou configuração do Vite.**
- **É um problema de dependências nativas do Rollup no ambiente de build.**
- **A solução definitiva depende de uma reinstalação limpa das dependências ou intervenção do suporte da plataforma.**