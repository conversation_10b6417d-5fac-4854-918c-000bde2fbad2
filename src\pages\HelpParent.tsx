import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PublicPageContainer from '@/components/PublicPageContainer';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { useTranslation } from 'react-i18next';

const HelpParent: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleGoBack = () => navigate(-1);
  const handleProceed = () => navigate('/login');

  return (
    <PublicPageContainer
      className="space-y-8 text-base leading-relaxed text-gray-900 dark:text-gray-100"
      withPattern={false}
    >
      <h1 className="text-3xl font-bold">{t('help.parent.title')}</h1>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.parent.dashboard.title')}</h2>
        <p className="font-semibold">{t('help.parent.dashboard.description')}</p>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.parent.managing.title')}</h2>
        <ul className="list-disc pl-6 space-y-2 font-semibold">
          <li>{t('help.parent.managing.invite')}</li>
          <li>{t('help.parent.managing.pending')}</li>
          <li>{t('help.parent.managing.remove')}</li>
        </ul>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.parent.monitoring.title')}</h2>
        <p className="font-semibold">{t('help.parent.monitoring.description')}</p>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">
          {t('help.parent.removeDuplicates.title', 'Remover Localizações Duplicadas')}
        </h2>
        <ol className="list-decimal pl-6 space-y-3 font-semibold">
          <li>{t('help.parent.removeDuplicates.step1', 'Selecione um estudante no painel do responsável.')}</li>
          <li>{t('help.parent.removeDuplicates.step2', 'Clique no botão vermelho "Remover duplicatas do banco" no topo do histórico de localizações.')}</li>
          <li>{t('help.parent.removeDuplicates.step3', 'Configure os critérios de remoção (raio, tempo, precisão) conforme desejado.')}</li>
          <li>{t('help.parent.removeDuplicates.step4', 'Digite CONFIRMAR (ou CONFIRM) para habilitar a exclusão e clique em "Remover duplicatas".')}</li>
          <li>{t('help.parent.removeDuplicates.step5', 'Aguarde a confirmação. As localizações duplicadas serão removidas do banco de dados de forma permanente.')}</li>
        </ol>
        <p className="mt-3 text-base font-semibold text-zinc-700 dark:text-zinc-300">
          {t('help.parent.removeDuplicates.warning', 'Atenção: Esta ação é irreversível. Certifique-se de revisar os critérios antes de confirmar.')}
        </p>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.parent.profile.title')}</h2>
        <p className="font-semibold">{t('help.parent.profile.description')}</p>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.parent.faq.title')}</h2>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="q1">
            <AccordionTrigger className="font-semibold text-xl">{t('help.parent.faq.q1')}</AccordionTrigger>
            <AccordionContent className="font-semibold">{t('help.parent.faq.a1')}</AccordionContent>
          </AccordionItem>
          <AccordionItem value="q2">
            <AccordionTrigger className="font-semibold text-xl">{t('help.parent.faq.q2')}</AccordionTrigger>
            <AccordionContent className="font-semibold">{t('help.parent.faq.a2')}</AccordionContent>
          </AccordionItem>
          <AccordionItem value="q3">
            <AccordionTrigger className="font-semibold text-xl">{t('help.parent.faq.q3')}</AccordionTrigger>
            <AccordionContent className="font-semibold">{t('help.parent.faq.a3')}</AccordionContent>
          </AccordionItem>
        </Accordion>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-3">{t('help.parent.support.title')}</h2>
        <p className="font-semibold">
          {t('help.parent.support.description')}{' '}
          <a className="text-blue-600 underline" href="mailto:<EMAIL>">
            <EMAIL>
          </a>.
        </p>
      </section>

      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={handleGoBack} className="flex items-center gap-2 text-lg font-semibold">
          <ArrowLeft className="h-5 w-5" />
          {t('help.buttons.back')}
        </Button>
        <Button onClick={handleProceed} className="flex items-center gap-2 text-lg font-semibold">
          {t('help.buttons.proceed')}
          <ArrowRight className="h-5 w-5" />
        </Button>
      </div>
    </PublicPageContainer>
  );
};

export default HelpParent;
