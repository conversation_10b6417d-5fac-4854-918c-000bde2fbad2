import React, { useEffect, useCallback, useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, UserPlus, Loader2 } from 'lucide-react';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useNavigate } from 'react-router-dom';
import AccountDeletionRequestsList from '@/components/lgpd/AccountDeletionRequestsList';
import { studentProfileService } from '@/lib/services/student/StudentProfileService';
import { guardianService } from '@/lib/services/guardian/GuardianService';
import { GuardianData } from '@/types/database';
import GuardianManager from '@/components/GuardianManager';
import { StudentPendingRequests } from '@/components/family/StudentPendingRequests';
import { useGuardianData } from '@/hooks/useGuardianData';
import ErrorFallback from '@/components/common/ErrorFallback';
import { useTranslation } from 'react-i18next';

interface Student {
  id: string;
  name: string;
  email: string;
  created_at: string;
  status: string;
  avatar_url: string | null;
  phone: string | null;
}

const RelationshipsSection: React.FC = React.memo(() => {
  const { t } = useTranslation();
  const { user } = useUser();
  const [students, setStudents] = useState<Student[]>([]);
  const {
    loading,
    error,
    guardians,
    fetchGuardians,
    addGuardian,
    removeGuardian
  } = useGuardianData();
  const navigate = useNavigate();

  const userType = useMemo(() => user?.user_metadata?.user_type, [user?.user_metadata?.user_type]);
  const userId = useMemo(() => user?.id, [user?.id]);
  const userEmail = useMemo(() => user?.email, [user?.email]);

  useEffect(() => {
    if (userType === 'student' && userId) {
      fetchGuardians(userId);
    }
  }, [userType, userId, fetchGuardians]);

  // Handler para adicionar responsável
  const handleAddGuardian = useCallback(async (guardian: Partial<GuardianData>) => {
    if (userId && guardian.email) {
      await addGuardian(userId, guardian.email);
    }
  }, [addGuardian, userId]);

  // Handler para remover responsável
  const handleRemoveGuardian = useCallback(async (id: string) => {
    const guardian = guardians.find(g => g.id === id);
    if (guardian) {
      await removeGuardian(guardian);
    }
  }, [guardians, removeGuardian]);

  // Handler para compartilhar localização (placeholder)
  const handleShareLocation = useCallback(async (_guardian: GuardianData) => {
    // Implementar se necessário
    return;
  }, []);

  // Componente para responsáveis
  const ParentView = useMemo(() => (
    <div className="space-y-6">
      <Card className="bg-transparent border-0 shadow-none">
        <CardHeader className="bg-transparent border-0 shadow-none">
          <CardTitle className="flex items-center gap-2 bg-transparent">
            <Users className="h-5 w-5" />
            {t('relationships.supervisedTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent className="bg-transparent border-0 shadow-none">
          <p className="text-gray-600 mb-4">
            {t('relationships.supervisedDescription')}
          </p>
          
          {loading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 mx-auto mb-2 animate-spin" />
              <p className="text-gray-500">Carregando estudantes...</p>
            </div>
          ) : error ? (
            <ErrorFallback
              title={t('common.error')}
              message={error}
              onRetry={() => fetchGuardians(userId || '')}
            />
          ) : students.length > 0 ? (
            <div className="space-y-3">
              {students.map((student) => (
                <div key={student.id} className="p-3 border-0 rounded-lg bg-transparent">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{student.name}</h4>
                      <p className="text-sm text-gray-600">{student.email}</p>
                      {student.phone && (
                        <p className="text-sm text-gray-500">{student.phone}</p>
                      )}
                    </div>
                    <div className="text-sm text-green-600">
                      {student.status === 'active' ? t('relationships.guardianActive') : t('relationships.guardianInactive')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <UserPlus className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>{t('relationships.noStudents')}</p>
              <p className="text-sm mt-1">{t('relationships.inviteStudents')}</p>
            <p className="text-xs mt-2 text-blue-600">
              💡 {t('relationships.inviteStudents')}
            </p>
            <Button className="mt-4" onClick={() => navigate('/add-student')}>
              {t('relationships.addStudentButton')}
            </Button>
          </div>
        )}
        </CardContent>
      </Card>

      <AccountDeletionRequestsList />
    </div>
  ), [loading, error, students, navigate, userId, fetchGuardians, t]);

  // Componente para estudantes
  const StudentView = (
    <div className="space-y-6">
      <GuardianManager
        guardians={guardians}
        isLoading={loading}
        error={error}
        onAddGuardian={handleAddGuardian}
        onDeleteGuardian={handleRemoveGuardian}
        onShareLocation={handleShareLocation}
        sharingStatus={{}}
      />
      <StudentPendingRequests />
    </div>
  );

  // Renderização condicional otimizada
  if (userType === 'student') {
    return StudentView;
  }

  if (userType === 'parent') {
    return ParentView;
  }

  return (
    <Card className="bg-transparent border-0 shadow-none">
      <CardContent className="p-6 text-center text-gray-500 bg-transparent border-0 shadow-none">
        <p>Seção de relacionamentos não disponível para este tipo de usuário.</p>
      </CardContent>
    </Card>
  );
});

RelationshipsSection.displayName = 'RelationshipsSection';

export default RelationshipsSection;
