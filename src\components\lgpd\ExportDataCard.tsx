
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ExportDataCardProps {
  onExportData: () => void;
  isExporting: boolean;
}

const ExportDataCard: React.FC<ExportDataCardProps> = ({ onExportData, isExporting }) => {
  const { t } = useTranslation();
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          {t('lgpd.export.title')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-foreground dark:text-white mb-4">
          {t('lgpd.export.description')}
        </p>
        <Button 
          onClick={onExportData} 
          disabled={isExporting}
          className="w-full"
        >
          {isExporting ? t('lgpd.export.exporting') : t('lgpd.export.button')}
        </Button>
      </CardContent>
    </Card>
  );
};

export default ExportDataCard;
