
import React, { useState } from 'react';
import { InviteStudentForm } from '../components/student/InviteStudentForm';
import StudentsListContainer from '../components/student/StudentsListContainer';
import { Button } from '../components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Student } from '@/types/auth';
import { useTranslation } from 'react-i18next';

export function AddStudent() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleStudentAdded = () => {
    // Incrementar para forçar atualização da lista
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            className="mr-4"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back')}
          </Button>
          <h1 className="text-2xl font-bold">{t('student.manageStudentsTitle')}</h1>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">{t('student.addStudent')}</h2>
            <div className="bg-white shadow-md rounded-lg p-6">
              <InviteStudentForm onStudentAdded={handleStudentAdded} />
            </div>
          </div>

          <div>
            <StudentsListContainer
              key={refreshTrigger}
              onSelectStudent={setSelectedStudent}
              selectedStudent={selectedStudent}
              onStudentUpdated={handleStudentAdded}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default AddStudent;

