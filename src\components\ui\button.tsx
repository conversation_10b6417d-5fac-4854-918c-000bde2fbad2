
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { useDevice } from "@/hooks/useDevice"
import { cn } from "@/lib/utils"

export const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 active:scale-95",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        login: [
          "bg-blue-600 text-white shadow-sm",
          "hover:bg-blue-700",
          "active:scale-[0.98]",
        ],
        register: [
          "bg-emerald-600 text-white shadow-sm",
          "hover:bg-emerald-700",
          "active:scale-[0.98]",
        ],
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
        xs: "h-7 text-xs rounded-md px-2 py-1",
        mobile: "h-11 w-full px-4 py-2.5 text-base font-medium",
        touch: "h-12 px-6 py-3 text-base font-medium min-w-[44px]", // iOS touch target guidelines
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, children, ...props }, ref) => {
    const device = useDevice();
    const Comp = asChild ? Slot : "button"
    
    // Auto-adjust button size based on device unless explicitly set
    const getResponsiveSize = () => {
      if (size) return size; // Use provided size if specified
      
      // Touch-friendly sizing for mobile devices
      if (device.isTouch) {
        if (device.orientation === 'portrait') {
          switch (device.size) {
            case 'xxs':
              return 'sm';
            case 'xs':
              return 'default';
            case 'sm':
              return 'touch';
            default:
              return 'touch';
          }
        } else {
          // Landscape - more compact
          switch (device.size) {
            case 'xxs':
            case 'xs':
              return 'xs';
            case 'sm':
              return 'sm';
            default:
              return 'default';
          }
        }
      }
      
      return 'default';
    };
    
    // Adjust icon size based on button size and device
    const getIconStyles = () => {
      const currentSize = getResponsiveSize();
      
      switch (currentSize) {
        case 'xs':
          return '[&_svg]:h-3 [&_svg]:w-3';
        case 'sm':
          return '[&_svg]:h-3.5 [&_svg]:w-3.5';
        case 'touch':
          return '[&_svg]:h-5 [&_svg]:w-5';
        case 'mobile':
          return '[&_svg]:h-5 [&_svg]:w-5';
        case 'lg':
          return '[&_svg]:h-5 [&_svg]:w-5';
        default:
          return '[&_svg]:h-4 [&_svg]:w-4';
      }
    };
    
    // Add safe area padding for iOS devices with notch
    const getSafeAreaStyles = () => {
      const currentSize = getResponsiveSize();
      if (device.hasNotch && (currentSize === 'mobile' || size === 'mobile')) {
        return 'safe-area-inset-bottom';
      }
      return '';
    };
    
    // Add haptic feedback for touch devices
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      // Haptic feedback for iOS
      if (device.isIOS && 'vibrate' in navigator) {
        navigator.vibrate(1);
      }
      
      // Call original onClick if provided
      if (props.onClick) {
        props.onClick(event);
      }
    };
    
    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size: getResponsiveSize() }),
          getIconStyles(),
          getSafeAreaStyles(),
          // Better touch targets for mobile
          device.isTouch && 'min-h-[44px]',
          className
        )}
        ref={ref}
        onClick={handleClick}
        {...props}
      >
        {children}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button }
