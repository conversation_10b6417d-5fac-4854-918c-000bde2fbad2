/**
 * HMR Resilience Module
 * Melhora a estabilidade da conexão do navegador durante mudanças de rede
 * Solução para o problema "ERR_NETWORK_CHANGED"
 */

/**
 * Configura medidas de resiliência para lidar com problemas de rede e
 * evitar erros ERR_NETWORK_CHANGED durante o desenvolvimento
 */
export function setupHMRResilience(): void {
  // Só executar em ambiente de desenvolvimento
  if (!import.meta.env.DEV) return;

  // Contadores para tentativas de reconexão
  let reconnectCount = 0;
  const maxReconnects = 5;
  
  // 1. Monitoramento básico de estado da rede
  window.addEventListener('offline', () => {
    console.log('[Conexão] Modo offline detectado');
    document.body.classList.add('network-offline');
  });
  
  window.addEventListener('online', () => {
    console.log('[Conexão] Voltou online, recarregando recursos...');
    document.body.classList.remove('network-offline');
    
    // Quando voltar online, tenta recarregar recursos com erro
    if (reconnectCount < maxReconnects) {
      reconnectCount++;
      setTimeout(() => {
        console.log(`[Conexão] Tentativa de reconexão ${reconnectCount}/${maxReconnects}`);
        reloadFailedResources();
      }, 2000);
    } else if (reconnectCount === maxReconnects) {
      // Se atingir número máximo, recarregar página
      console.log('[Conexão] Tentativas de reconexão excedidas. Recarregando página...');
      setTimeout(() => window.location.reload(), 1000);
    }
  });
  
  // 2. Função para tentar recarregar recursos com falha
  function reloadFailedResources() {
    // Se a aplicação estiver usando Vite HMR, tentar reconectar
    if (import.meta.hot) {
      try {
        // "vite:reconnect" é um evento especial do Vite para forçar reconexão HMR
        import.meta.hot.send('vite:reconnect', {});
      } catch (err) {
        console.error('[HMR] Falha ao reconectar:', err);
      }
    }
    
    // Recarregar scripts com erro
    document.querySelectorAll('script').forEach(script => {
      if (script.dataset.tried) return;
      
      const src = script.getAttribute('src');
      if (src) {
        // Marcar o script como já tentado
        script.dataset.tried = 'true';
        
        // Criar uma nova tag de script para forçar o recarregamento
        const newScript = document.createElement('script');
        newScript.src = src + (src.includes('?') ? '&' : '?') + 'reload=' + Date.now();
        script.parentNode?.replaceChild(newScript, script);
      }
    });
  }
  
  // 3. Configurar retry para recursos falhados quando possível
  if (window.fetch) {
    console.log('[Conexão] Configurando resiliência para recursos da rede');
    
    // Criar um cache das URLs que já foram tratadas
    const processedUrls = new Set<string>();
    
    // Observar erros de recursos
    window.addEventListener('error', function(e) {
      const target = e.target as HTMLElement;
      if (!target || !('src' in target)) return;
      
      const url = (target as HTMLImageElement | HTMLScriptElement).src;
      if (!url || processedUrls.has(url)) return;
      
      processedUrls.add(url);
      console.log(`[Recuperação] Detectado recurso com falha: ${url.split('/').pop()}`);
      
      // Tentar recarregar o recurso após um atraso
      setTimeout(() => {
        if (target.tagName === 'SCRIPT') {
          const script = document.createElement('script');
          script.src = url + (url.includes('?') ? '&' : '?') + '_retry=' + Date.now();
          document.head.appendChild(script);
        } else if (target.tagName === 'IMG') {
          (target as HTMLImageElement).src = url + (url.includes('?') ? '&' : '?') + '_retry=' + Date.now();
        }
      }, 3000);
    }, true);
  }
}
