-- ==========================================
-- DEBUG: ESTRUTURA E DADOS DA TABELA GUARDIANS
-- Data: 24/06/2025
-- Objetivo: Entender por que RPC falha
-- ==========================================

-- 1. VERIFICAR ESTRUTURA DA TABELA GUARDIANS
SELECT 
    'STRUCTURE_GUARDIANS' as info,
    column_name, 
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'guardians'
ORDER BY ordinal_position;

-- 2. CONTAR REGISTROS NA TABELA GUARDIANS
SELECT 
    'COUNT_GUARDIANS' as info,
    COUNT(*) as total_records
FROM public.guardians;

-- 3. VERIFICAR SE TABELA EXISTE
SELECT 
    'TABLE_EXISTS' as info,
    COUNT(*) as exists_count
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name = 'guardians';

-- 4. VERIFICAR SAMPLE DE DADOS (SE EXISTIR)
SELECT 
    'SAMPLE_GUARDIANS' as info,
    id, student_id, email, full_name, created_at
FROM public.guardians
LIMIT 5;

-- 5. VERIFICAR RPC FUNCTIONS RELACIONADAS
SELECT 
    'RPC_FUNCTIONS' as info,
    routine_name,
    routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name LIKE '%guardian%'
ORDER BY routine_name; 