import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import translationPT from '../i18n/pt-BR.json';
import translationEN from '../i18n/en-GB.json';
import { supportedLocales, defaultLocale } from '../i18n/config';

const resources = {
  'pt-BR': { translation: translationPT },
  'pt': { translation: translationPT },
  'en-GB': { translation: translationEN },
  'en': { translation: translationEN }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: [defaultLocale, 'pt-BR', 'en-GB'],
    debug: import.meta.env.DEV,
    detection: {
      order: ['localStorage', 'cookie', 'navigator', 'htmlTag'],
      caches: ['localStorage', 'cookie'],
      lookupLocalStorage: 'lang',
      lookupCookie: 'NEXT_LOCALE',
      cookieMinutes: 525600
    },
    interpolation: { escapeValue: false },
    react: { useSuspense: false }
  });

// Expose i18n globally for legacy scripts expecting window.i18n
if (typeof window !== 'undefined') {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  window.i18n = i18n;
}

export default i18n;
