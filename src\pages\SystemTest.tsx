
import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import SystemTestSuite from '@/components/debug/SystemTestSuite';

const SystemTest: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="content-overlay rounded-xl bg-transparent p-4 md:p-8 w-full max-w-2xl mx-auto">
        <div className="mb-6">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => navigate(-1)} 
            className="flex items-center gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
        </div>

        <div className="mb-8">
          <h1 className="text-3xl font-bold">Teste do Sistema Pós-Migração</h1>
          <p className="text-muted-foreground mt-2">
            Verificação completa das funcionalidades após a consolidação das tabelas e correção dos conflitos de CPF.
          </p>
        </div>

        <SystemTestSuite />
      </div>
    </div>
  );
};

export default SystemTest;
