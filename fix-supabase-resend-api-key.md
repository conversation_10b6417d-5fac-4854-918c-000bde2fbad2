# 🔧 CORREÇÃO URGENTE: Adicionar RESEND_API_KEY no Supabase

## 🚨 Problema Identificado

A Edge Function `send-password-reset` está falhando com erro 500:
```json
{
  "success": false,
  "message": "Erro: RESEND_API_KEY não configurada no servidor",
  "details": "Error: RESEND_API_KEY não configurada no servidor"
}
```

## ✅ Solução

### **Passo 1: Adicionar Variável no Dashboard**

1. Acesse: https://supabase.com/dashboard/project/rsvjnndhbyyxktbczlnk/functions/secrets
2. Clique em "Add new secrets"
3. Adicione:
   - **Key:** `RESEND_API_KEY`
   - **Value:** `re_GaNw4cs9_KFzUiLKkiA6enex1APBhbRHu`
4. Clique em "Save"

### **Passo 2: Verificar Configuração**

A variável deve aparecer na lista com:
- Name: `RESEND_API_KEY`
- Digest: SHA256 hash da chave
- Status: Disponível para Edge Functions

### **Passo 3: Redeployar Edge Function (se necessário)**

```bash
# Navegue para o diretório do projeto
cd supabase/functions/send-password-reset

# Redeploy da função (opcional)
supabase functions deploy send-password-reset
```

## 🧪 Teste Após Correção

Após adicionar a variável, teste novamente:

1. Acesse: http://localhost:4000/test-franklin-password-reset.html
2. Clique em "Testar Edge Function"
3. Deve retornar sucesso em vez de erro 500

## 📋 Variáveis Necessárias (Checklist)

Confirme que estas variáveis existem no Supabase:

- ✅ `SUPABASE_URL`
- ✅ `SUPABASE_SERVICE_ROLE_KEY`
- ❌ `RESEND_API_KEY` ← **FALTA ESTA**
- ✅ `VITE_RESEND_API_KEY` (para frontend)

## 🎯 Resultado Esperado

Após a correção, o teste deve mostrar:
```json
{
  "success": true,
  "message": "Email de recuperação enviado com sucesso",
  "supabase": true
}
```

## 📞 Para Franklin

Após essa correção, o processo de recuperação de senha funcionará completamente:

1. ✅ Rota `/reset-password` (já corrigida)
2. ✅ Edge Function funcionando (após adicionar variável)
3. ✅ CORS resolvido (já corrigido)
4. ✅ Email sendo enviado corretamente

---

**🚨 AÇÃO REQUERIDA: Adicionar `RESEND_API_KEY` no dashboard do Supabase imediatamente!** 