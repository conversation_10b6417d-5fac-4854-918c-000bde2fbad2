-- Verificação corrigida do status do sistema
-- Usando CAST para garantir tipos compatíveis

SELECT 
  'mauricio_luciana_relationship' as check_type,
  COUNT(*)::text as result
FROM student_guardian_relationships sgr
JOIN auth.users u1 ON sgr.student_id = u1.id
JOIN auth.users u2 ON sgr.guardian_id = u2.id
WHERE u1.email = '<EMAIL>'
  AND u2.email = '<EMAIL>'

UNION ALL

SELECT 
  'total_relationships' as check_type,
  COUNT(*)::text as result
FROM student_guardian_relationships

UNION ALL

SELECT 
  'family_invitations_mauricio' as check_type,
  COUNT(*)::text as result
FROM family_invitations
WHERE student_email = '<EMAIL>'

UNION ALL

SELECT 
  'function_exists' as check_type,
  CASE WHEN COUNT(*) > 0 THEN 'YES' ELSE 'NO' END as result
FROM pg_proc 
WHERE proname = 'get_student_guardians_from_relationships'

UNION ALL

SELECT 
  'invitation_status' as check_type,
  COALESCE(status, 'NO_INVITATION') as result
FROM family_invitations
WHERE student_email = '<EMAIL>'
ORDER BY check_type; 