# Passos Após Reinicialização - Supabase e Docker

**Data:** 2025-06-02 - **Atualização mais recente:** 2025-06-02 16:25

> **IMPORTANTE:** Problemas resolvidos com sucesso em 02/06/2025. Seguir este documento para configuração reproduzível.

## Objetivo Principal

Criar um ambiente Docker estável e reproduzível que integre de forma confiável:

1. **Frontend React + TypeScript + Vite:** Interface de usuário do sistema Locate-Family-Connect
2. **Supabase:** Backend completo oferecendo autenticação PKCE, banco de dados PostgreSQL e Edge Functions
3. **Drizzle ORM:** Camada de abstração para interações estruturadas e type-safe com o banco de dados

## Benefícios Esperados

- **Ambiente isolado:** Garantia de consistência independente da máquina de desenvolvimento
- **Persistência de dados:** Volumes Docker preservam dados entre reinicializações
- **Comunicação confiável:** Resolução dos problemas de rede entre containers
- **Build otimizado:** Configuração TypeScript adequada para compilação no Docker

## 0. Reconstruir Docker Compose (TAREFA PRIORITÁRIA)

```bash
# Reconstruir todos os containers com as correções de TypeScript aplicadas
docker compose -f docker-compose.yml up -d --build
```

## 1. Verificação Inicial do Docker

```bash
# Verificar se o Docker está funcionando corretamente
docker info

# Confirmar que as novas configurações de rede foram aplicadas
docker network ls

# Verificar se não há containers problemáticos
docker ps -a
```

## 2. Limpeza Completa do Ambiente Supabase

```bash
# Parar todos os serviços Supabase
npx supabase stop

# Remover possíveis containers residuais
docker rm -f $(docker ps -a -q --filter "name=supabase_*") 2>/dev/null || true

# Remover networks residuais do Supabase
docker network rm supabase-network 2>/dev/null || true
docker network prune -f
```

## 3. Recriar Ambiente de Rede Dedicado

```bash
# Criar uma rede dedicada para o Supabase
docker network create --driver bridge supabase-network
```

## 4. Iniciar o Supabase

```bash
# Iniciar o Supabase com a nova configuração
npx supabase start
```

## 5. Verificação de Funcionamento

```bash
# Verificar se todos os containers estão em execução
docker ps

# Verificar logs do container Vector (que tinha problemas)
docker logs $(docker ps -q --filter "name=supabase_vector_*")

# Verificar status do Supabase
npx supabase status
```

## 6. Iniciar o Frontend

```bash
# Garantir que a porta esteja livre
kill-port 8080

# Instalar dependências caso seja necessário
npm install

# Iniciar o servidor de desenvolvimento
npm run dev
```

## 7. Verificação da Aplicação

1. Abrir navegador em `http://localhost:8080`
2. Verificar se a autenticação Supabase está funcionando
3. Testar funcionalidades que dependem do Supabase (autenticação, banco de dados)
4. Verificar se as Edge Functions estão disponíveis

## Solução de Problemas Persistentes

### Se o Vector ainda apresentar erros:

```bash
# Entrar no WSL e executar o script de correção
wsl -d <sua_distro>
sudo ./locate-family-connect/.wsl-fix.sh
exit
```

### Se outros containers apresentarem erros:

1. Verificar logs específicos do container com problema:  
   `docker logs <container_id>`

2. Recriar o container específico:  
   `docker rm -f <container_id> && npx supabase start`

3. Se necessário, verificar portas em uso:  
   `netstat -ano | findstr 8080`  
   `netstat -ano | findstr 5432`

### Para verificar a conexão do frontend com o Supabase:

Verificar no console do navegador se não há erros de conexão com a API Supabase.
