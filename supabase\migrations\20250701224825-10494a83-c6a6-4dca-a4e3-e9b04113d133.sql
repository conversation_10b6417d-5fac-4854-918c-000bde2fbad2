-- Migration: Create direct student account creation RPC function
-- Timestamp: **************
-- Based on documented solution in project

CREATE OR REPLACE FUNCTION public.create_student_account_direct(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_student_phone TEXT DEFAULT NULL,
  p_guardian_id UUID
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT,
  student_id UUID,
  temp_password TEXT,
  activation_token TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_clean_cpf TEXT;
  v_temp_password TEXT;
  v_activation_token TEXT;
  v_student_user_id UUID;
  v_existing_student_id UUID;
  v_guardian_email TEXT;
  v_guardian_name TEXT;
BEGIN
  -- Validar guardian
  SELECT u.email, COALESCE(p.full_name, u.email)
  INTO v_guardian_email, v_guardian_name
  FROM auth.users u
  LEFT JOIN public.profiles p ON p.user_id = u.id
  WHERE u.id = p_guardian_id;

  IF v_guardian_email IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Guardian não encontrado', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Validar dados básicos
  IF p_student_name IS NULL OR TRIM(p_student_name) = '' THEN
    RETURN QUERY SELECT FALSE, 'Nome do estudante é obrigatório', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  IF p_student_email IS NULL OR p_student_email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
    RETURN QUERY SELECT FALSE, 'Email inválido', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Limpar CPF
  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  IF char_length(v_clean_cpf) <> 11 THEN
    RETURN QUERY SELECT FALSE, 'CPF deve ter 11 dígitos válidos', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Verificar se estudante já existe por CPF
  SELECT user_id INTO v_existing_student_id
  FROM public.profiles
  WHERE regexp_replace(cpf, '[^0-9]', '', 'g') = v_clean_cpf
  AND user_type = 'student';

  IF v_existing_student_id IS NOT NULL THEN
    -- Verificar se já está vinculado
    IF EXISTS (
      SELECT 1 FROM public.student_guardian_relationships 
      WHERE student_id = v_existing_student_id AND guardian_id = p_guardian_id
    ) THEN
      RETURN QUERY SELECT FALSE, 'Estudante já está vinculado à sua conta', NULL::UUID, NULL::TEXT, NULL::TEXT;
      RETURN;
    ELSE
      -- Criar relacionamento com estudante existente
      INSERT INTO public.student_guardian_relationships (
        student_id, guardian_id, relationship_type, is_primary
      ) VALUES (
        v_existing_student_id, p_guardian_id, 'parent', TRUE
      );
      
      RETURN QUERY SELECT TRUE, 'Estudante vinculado com sucesso', v_existing_student_id, NULL::TEXT, NULL::TEXT;
      RETURN;
    END IF;
  END IF;

  -- Verificar se email já existe
  IF EXISTS (SELECT 1 FROM auth.users WHERE LOWER(email) = LOWER(p_student_email)) THEN
    RETURN QUERY SELECT FALSE, 'Este email já está registrado no sistema', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Gerar credenciais temporárias
  v_temp_password := substring(upper(p_student_name) from 1 for 3) || 
                     substr(v_clean_cpf, 9, 2) || 
                     to_char(EXTRACT(YEAR FROM NOW()), '99') || '!';
  v_activation_token := 'act_' || encode(gen_random_bytes(32), 'hex') || '_' || extract(epoch from now())::bigint;

  -- Gerar UUID para o novo estudante
  v_student_user_id := gen_random_uuid();

  -- Criar entrada no family_invitations para rastreamento
  INSERT INTO public.family_invitations(
    id, guardian_id, guardian_email, student_name, student_email, student_cpf, student_phone,
    invitation_type, status, temp_password, activation_token, expires_at
  ) VALUES (
    gen_random_uuid(), p_guardian_id, v_guardian_email, p_student_name, p_student_email,
    v_clean_cpf, p_student_phone, 'direct_rpc_creation', 'auth_pending',
    v_temp_password, v_activation_token, NOW() + interval '7 days'
  );

  -- Log da operação
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_account_direct_creation_initiated', p_guardian_id,
          jsonb_build_object(
            'method', 'direct_rpc',
            'student_email', p_student_email,
            'temp_user_id', v_student_user_id
          ),
          NOW());

  -- Retornar dados para que Edge Function possa criar usuário auth
  RETURN QUERY SELECT TRUE, 'Dados preparados para criação de conta', v_student_user_id, v_temp_password, v_activation_token;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.create_student_account_direct(TEXT, TEXT, TEXT, TEXT, UUID) TO authenticated;

COMMENT ON FUNCTION public.create_student_account_direct IS
'Direct RPC approach for student account creation. Validates data, checks for existing students,
and prepares credentials for auth user creation. Called by frontend when Edge Function fails.';

-- Also ensure the activate function migration is applied
CREATE OR REPLACE FUNCTION public.activate_student_account(
  p_activation_token TEXT,
  p_new_password TEXT
)
RETURNS TABLE(success BOOLEAN, message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_profile_record RECORD;
BEGIN
  -- Buscar perfil pelo token de ativação
  SELECT * INTO v_profile_record
  FROM public.profiles
  WHERE activation_token = p_activation_token
  AND user_type = 'student'
  AND activation_expires_at > NOW()
  AND activated_at IS NULL;

  IF v_profile_record IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Token de ativação inválido, expirado ou a conta já foi ativada.'::TEXT;
    RETURN;
  END IF;

  -- Validação de senha
  IF p_new_password IS NULL OR char_length(p_new_password) < 8 THEN
    RETURN QUERY SELECT FALSE, 'Nova senha inválida. Deve ter pelo menos 8 caracteres.'::TEXT;
    RETURN;
  END IF;

  -- Atualizar senha no auth.users
  UPDATE auth.users
  SET
    password = p_new_password,
    email_confirmed_at = COALESCE(email_confirmed_at, NOW())
  WHERE id = v_profile_record.user_id;

  -- Marcar conta como ativada no profiles
  UPDATE public.profiles
  SET
    activated_at = NOW(),
    requires_password_change = FALSE,
    activation_token = NULL,
    status = 'active',
    registration_status = 'completed',
    updated_at = NOW()
  WHERE user_id = v_profile_record.user_id;

  -- Log successful activation
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_account_activated', v_profile_record.user_id,
          jsonb_build_object('email', v_profile_record.email, 'activation_method', 'token_link'),
          NOW());

  RETURN QUERY SELECT TRUE, 'Conta ativada e senha atualizada com sucesso!'::TEXT;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING '[ACTIVATE_STUDENT_ACCOUNT_RPC] Error for token % (User ID %): %',
                  p_activation_token, v_profile_record.user_id, SQLERRM;
    RETURN QUERY SELECT FALSE, 'Erro interno ao processar a ativação da conta. Por favor, contate o suporte.'::TEXT;
END;
$$;

GRANT EXECUTE ON FUNCTION public.activate_student_account(TEXT, TEXT) TO authenticated;