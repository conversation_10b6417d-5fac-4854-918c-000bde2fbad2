
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useUser } from '@/contexts/UnifiedAuthContext';
import AuthContainer from '@/components/AuthContainer';
import { AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getUserTypeFromMetadata, getDefaultRouteForUserType } from '@/lib/types/user-types';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { user, isLoading } = useUser();
  const { t } = useTranslation();
  const [error, setError] = useState('');
  const redirectHandledRef = useRef(false);
  
  const queryParams = new URLSearchParams(location.search);
  const redirectMessage = queryParams.get('message');

  // Handle redirect message if present - only once
  useEffect(() => {
    if (redirectMessage && !redirectHandledRef.current) {
      console.log(`[LOGIN] Redirect message present: ${redirectMessage}`);
      toast({
        title: t('common.attention'),
        description: redirectMessage,
        variant: 'default'
      });
      redirectHandledRef.current = true;
    }
  }, [redirectMessage, toast]);

  // Handle authenticated users - redirect to appropriate dashboard
  useEffect(() => {
    if (isLoading || redirectHandledRef.current) return;
    
    if (user) {
      console.log('[LOGIN] User already authenticated, redirecting:', user);
      redirectHandledRef.current = true;
      
      // Get user type using the utility function
      const userType = getUserTypeFromMetadata(user.user_metadata);
      const redirectPath = getDefaultRouteForUserType(userType);
      
      // Redirect based on user type with small delay to ensure context is fully updated
      setTimeout(() => {
        navigate(redirectPath, { replace: true });
      }, 100);
    }
  }, [user, isLoading, navigate]);

  // Display loading state
  if (isLoading) {
    return (
      <div className="min-h-screen subtle-map-background flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-blue-600 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  // Don't render login form if user is authenticated and redirect is in progress
  if (user && redirectHandledRef.current) {
    return (
      <div className="min-h-screen subtle-map-background flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-blue-600 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4" data-cy="login-page">
      {error && (
        <Alert variant="destructive" className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md" data-cy="login-error">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <div className="content-overlay rounded-xl bg-transparent p-4 md:p-8 w-full max-w-md mx-auto">
        <AuthContainer initialScreen="login" data-cy="login-container" />
      </div>
    </div>
  );
};

export default Login;
