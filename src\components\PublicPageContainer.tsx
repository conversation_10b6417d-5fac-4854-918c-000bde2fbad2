import React from 'react';
import Logo from '@/components/Logo';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { cn } from '@/lib/utils';

interface PublicPageContainerProps {
  children: React.ReactNode;
  className?: string;
  /**
   * Show the subtle pattern background. Disable to let the Mapbox background
   * be fully visible through the page.
   */
  withPattern?: boolean;
}

const PublicPageContainer: React.FC<PublicPageContainerProps> = ({
  children,
  className,
  withPattern = true,
}) => {
  return (
    <div
      className={cn(
        'min-h-screen flex items-center justify-center p-4',
        withPattern && 'subtle-map-background'
      )}
    >
      <LanguageSwitcher className="absolute top-4 right-4 z-20" variant="outline" size="sm" />
      <div className={cn('content-overlay rounded-xl bg-transparent p-4 md:p-8 w-full max-w-md mx-auto', className)}>
        <Logo className="w-48 mx-auto mb-6" />
        {children}
      </div>
    </div>
  );
};

export default PublicPageContainer;
