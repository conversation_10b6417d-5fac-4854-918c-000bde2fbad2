
import React from 'react';
import { Button } from '../ui/button';
import { ZoomIn, Navigation, MapPin } from 'lucide-react';
import { LocationData } from '@/types/database';
import { useToast } from '@/components/ui/use-toast';
import { useNotifications } from '@/components/providers/NotificationProvider';
import { isParentDashboard } from '@/utils/mapHelpers';
import { useMapControlsLayout } from '@/hooks/useMapControlsLayout';
import { Capacitor } from '@capacitor/core';
import mapboxgl from 'mapbox-gl';

interface MapControlButtonsProps {
  showControls: boolean;
  selectedUserId?: string;
  locations: LocationData[];
  mapInstance: React.MutableRefObject<mapboxgl.Map | null>;
  onLocationUpdate?: () => void;
  isNative?: boolean;
  triggerHaptic?: (intensity: 'light' | 'medium' | 'heavy') => Promise<void>;
  triggerSuccessHaptic?: () => Promise<void>;
  triggerErrorHaptic?: () => Promise<void>;
}

const MapControlButtons: React.FC<MapControlButtonsProps> = ({
  showControls,
  selectedUserId,
  locations,
  mapInstance,
  onLocationUpdate,
  isNative,
  triggerHaptic,
  triggerSuccessHaptic,
  triggerErrorHaptic
}) => {
  const { toast } = useToast();
  const { showNotification } = useNotifications();
  const { getControlsPosition } = useMapControlsLayout();
  const { containerClass, buttonSize, maxWidth } = getControlsPosition();
  
  // Detect platform for styling
  const isIOS = isNative && Capacitor.getPlatform() === 'ios';
  const isAndroid = isNative && Capacitor.getPlatform() === 'android';

  const handleZoomToLatest = () => {
    // Provide haptic feedback if available
    if (isNative && triggerHaptic) {
      triggerHaptic('medium');
    }
    
    if (mapInstance.current && locations.length > 0) {
      const mostRecentLocation = locations[0];
      mapInstance.current.flyTo({
        center: [mostRecentLocation.longitude, mostRecentLocation.latitude],
        zoom: 19,
        essential: true,
        speed: 0.5
      });
      
      // Success haptic feedback when animation completes
      if (isNative && triggerSuccessHaptic) {
        setTimeout(() => {
          triggerSuccessHaptic();
        }, 500);
      }
      
      showNotification(
        "Visualização ampliada",
        `Zoom máximo na localização mais recente de ${new Date(mostRecentLocation.timestamp).toLocaleString()}`,
        {
          type: 'location',
          duration: 3000
        }
      );
    }
  };

  const handleShowLocalHistory = () => {
    // Provide haptic feedback if available
    if (isNative && triggerHaptic) {
      triggerHaptic('medium');
    }
    
    if (mapInstance.current && locations.length > 1) {
      const mostRecentLocation = locations[0];
      const nearbyLocations = locations.filter(loc => {
        const latDiff = Math.abs(loc.latitude - mostRecentLocation.latitude);
        const lngDiff = Math.abs(loc.longitude - mostRecentLocation.longitude);
        return latDiff < 5 && lngDiff < 5;
      });
      
      if (nearbyLocations.length > 1) {
        const nearbyBounds = new mapboxgl.LngLatBounds();
        nearbyLocations.forEach(loc => {
          nearbyBounds.extend([loc.longitude, loc.latitude]);
        });
        
        mapInstance.current.fitBounds(nearbyBounds, {
          padding: 70,
          maxZoom: 16
        });
        
        // Success haptic feedback when animation completes
        if (isNative && triggerSuccessHaptic) {
          setTimeout(() => {
            triggerSuccessHaptic();
          }, 500);
        }
        
        showNotification(
          "Histórico local",
          `Mostrando ${nearbyLocations.length} localizações na região atual`,
          {
            type: 'location',
            duration: 3000
          }
        );
      } else {
        mapInstance.current.flyTo({
          center: [mostRecentLocation.longitude, mostRecentLocation.latitude],
          zoom: 16,
          essential: true
        });
      }
    }
  };

  const handleUpdateLocation = () => {
    // Provide haptic feedback if available
    if (isNative && triggerHaptic) {
      triggerHaptic('medium');
    }
    
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          
          if (mapInstance.current) {
            mapInstance.current.flyTo({
              center: [longitude, latitude],
              zoom: 17,
              speed: 0.8,
              essential: true
            });
          }
          
          // Success haptic feedback when location is updated
          if (isNative && triggerSuccessHaptic) {
            triggerSuccessHaptic();
          }
          
          showNotification(
            "Localização atualizada",
            "Sua localização atual foi atualizada no mapa.",
            {
              type: 'location',
              duration: 3000
            }
          );
          
          if (onLocationUpdate) {
            onLocationUpdate();
          }
        },
        (error) => {
          console.error('Error getting location:', error);
          
          // Error haptic feedback
          if (isNative && triggerErrorHaptic) {
            triggerErrorHaptic();
          }
          
          showNotification(
            "Erro",
            "Não foi possível obter sua localização",
            {
              type: 'alert',
              duration: 5000,
              actions: [
                {
                  text: "Tentar novamente",
                  onClick: () => handleUpdateLocation()
                }
              ]
            }
          );
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0
        }
      );
    } else {
      // Error haptic feedback
      if (isNative && triggerErrorHaptic) {
        triggerErrorHaptic();
      }
      
      showNotification(
        "Erro",
        "Seu navegador não suporta geolocalização",
        {
          type: 'alert',
          duration: 5000
        }
      );
    }
  };

  if (!showControls || !selectedUserId) return null;
  
  // Platform-specific button styling
  const getButtonClass = (isOutline = false) => {
    let baseClass = "text-xs whitespace-nowrap";
    
    if (isIOS) {
      baseClass += " ios-button rounded-full shadow-sm";
      if (isOutline) {
        baseClass += " ios-button-outline";
      }
    } else if (isAndroid) {
      baseClass += " android-button rounded-md shadow-md";
      if (isOutline) {
        baseClass += " android-button-outline";
      }
    }
    
    return baseClass;
  };

  return (
    <div className={`${containerClass} ${maxWidth} ${isNative ? 'native-controls' : ''}`}>
      {isParentDashboard() && locations.length > 0 && (
        <div className={`flex flex-col gap-2 ${isNative ? 'native-button-group' : ''}`}>
          <Button
            variant="outline"
            size={buttonSize}
            onClick={handleZoomToLatest}
            className={getButtonClass(true)}
          >
            <ZoomIn className={`${isNative ? 'h-4 w-4' : 'h-3 w-3'}`} />
            <span className="ml-1">Ampliar</span>
          </Button>
          
          {locations.length > 1 && (
            <Button
              variant="outline"
              size={buttonSize}
              onClick={handleShowLocalHistory}
              className={getButtonClass(true)}
            >
              <Navigation className={`${isNative ? 'h-4 w-4' : 'h-3 w-3'}`} />
              <span className="ml-1">Histórico</span>
            </Button>
          )}
        </div>
      )}
      
      <Button
        variant="default"
        size={buttonSize}
        onClick={handleUpdateLocation}
        className={getButtonClass(false)}
      >
        <MapPin className={`${isNative ? 'h-4 w-4' : 'h-3 w-3'}`} />
        <span className="ml-1">Atualizar</span>
      </Button>
    </div>
  );
};

export default MapControlButtons;
