
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { User, Shield, Users, Settings } from 'lucide-react';
import ProfileEditForm from './ProfileEditForm';
import StudentBirthDateSection from './StudentBirthDateSection';
import PrivacySection from './PrivacySection';
import RelationshipsSection from './RelationshipsSection';
import SettingsSection from './SettingsSection';
import { useUser } from '@/contexts/UnifiedAuthContext';

const ProfileTabs: React.FC = () => {
  const { user } = useUser();
  const userType = user?.user_metadata?.user_type;

  return (
    <Tabs defaultValue="profile" className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="profile" className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span className="hidden sm:inline">Perfil</span>
        </TabsTrigger>
        <TabsTrigger value="privacy" className="flex items-center gap-2">
          <Shield className="h-4 w-4" />
          <span className="hidden sm:inline">Privacidade</span>
        </TabsTrigger>
        <TabsTrigger value="relationships" className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          <span className="hidden sm:inline">Vínculos</span>
        </TabsTrigger>
        <TabsTrigger value="settings" className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          <span className="hidden sm:inline">Config</span>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="profile" className="space-y-6">
        <ProfileEditForm />
        {userType === 'student' && <StudentBirthDateSection />}
      </TabsContent>

      <TabsContent value="privacy">
        <PrivacySection />
      </TabsContent>

      <TabsContent value="relationships">
        <RelationshipsSection />
      </TabsContent>

      <TabsContent value="settings">
        <SettingsSection />
      </TabsContent>
    </Tabs>
  );
};

export default ProfileTabs;
