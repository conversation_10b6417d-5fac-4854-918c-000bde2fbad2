import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Plus } from "lucide-react";
import { GuardianData } from "@/types/auth";
import { GuardianCard } from "./guardian/GuardianCard";
import { cn } from "@/lib/utils";
import { formatCPF, validateCPF } from '@/lib/utils/cpf-validator';

interface GuardianManagerProps {
  guardians: GuardianData[];
  isLoading: boolean;
  error: string | null;
  onAddGuardian: (guardian: Partial<GuardianData>) => Promise<void>;
  onDeleteGuardian: (id: string) => Promise<void>;
  onShareLocation: (guardian: GuardianData) => Promise<void>;
  sharingStatus: Record<string, string>;
  className?: string;
}

const GuardianManager: React.FC<GuardianManagerProps> = ({
  guardians,
  isLoading,
  error,
  onAddGuardian,
  onDeleteGuardian,
  onShareLocation,
  sharingStatus,
  className = "",
}) => {
  const [newGuardian, setNewGuardian] = useState({
    full_name: "",
    email: "",
    phone: "",
    cpf: "",
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { t } = useTranslation();

  const handleAddGuardian = async () => {
    // Validar CPF se fornecido
    if (newGuardian.cpf && !validateCPF(newGuardian.cpf).isValid) {
      return; // O erro será mostrado no campo
    }
    await onAddGuardian(newGuardian);
    setNewGuardian({ full_name: "", email: "", phone: "", cpf: "" });
    setIsDialogOpen(false);
  };

  // Função para recarregar a lista após uma solicitação de remoção
  const handleGuardianUpdated = () => {
    // Força recarregamento da página para mostrar mudanças
    window.location.reload();
  };

  return (
    <>
      <Card className={cn("mt-8", className)}>
        <CardHeader className="flex items-center justify-between mb-4">
          <CardTitle>{t("guardianManager.title")}</CardTitle>
          <Button
            onClick={() => setIsDialogOpen(true)}
            aria-label={t("guardianManager.addGuardianAria")}
          >
            <Plus className="mr-2 h-4 w-4" />
            {t("guardianManager.addGuardian")}
          </Button>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="animate-spin h-6 w-6" />
            </div>
          ) : error ? (
            <div className="text-red-500 p-4 bg-red-50 rounded-lg border border-red-200">
              {error}
            </div>
          ) : guardians.length === 0 ? (
            <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
              <p className="text-lg mb-2">{t("guardianManager.noGuardians")}</p>
              <p className="text-sm">{t("guardianManager.addFirstGuardian")}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {guardians.map((guardian) => (
                <GuardianCard
                  key={guardian.id}
                  id={guardian.id}
                  name={guardian.full_name || "Responsável"}
                  email={guardian.email}
                  phone={guardian.phone || ""}
                  cpf={guardian.cpf || ""}
                  birthDate={guardian.birth_date || ""}
                  status={guardian.status || "active"}
                  isActive={guardian.is_active}
                  createdAt={guardian.created_at}
                  onRemove={onDeleteGuardian}
                  onSendInvite={(email, name) => onShareLocation(guardian)}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal para adicionar responsável */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("guardianManager.addGuardian")}</DialogTitle>
            <DialogDescription>
              {t("guardianManager.addFirstGuardian")}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="email">{t("guardianManager.email")}</Label>
              <Input
                id="email"
                type="email"
                placeholder={t(
                  "guardianManager.emailPlaceholder",
                  "<EMAIL>",
                )}
                value={newGuardian.email}
                onChange={(e) =>
                  setNewGuardian((g) => ({ ...g, email: e.target.value }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">{t("guardianManager.name")}</Label>
              <Input
                id="name"
                placeholder={t(
                  "guardianManager.fullNamePlaceholder",
                  "Full name",
                )}
                value={newGuardian.full_name}
                onChange={(e) =>
                  setNewGuardian((g) => ({ ...g, full_name: e.target.value }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">{t("guardianManager.phone")}</Label>
              <Input
                id="phone"
                placeholder={t(
                  "guardianManager.phonePlaceholder",
                  "+XX (XX) XXXXX-XXXX",
                )}
                value={newGuardian.phone}
                onChange={(e) =>
                  setNewGuardian((g) => ({ ...g, phone: e.target.value }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="cpf">CPF (opcional)</Label>
              <Input
                id="cpf"
                placeholder="000.000.000-00"
                value={newGuardian.cpf}
                onChange={(e) => {
                  const formattedCPF = formatCPF(e.target.value);
                  setNewGuardian((g) => ({ ...g, cpf: formattedCPF }));
                }}
                maxLength={14}
                className={newGuardian.cpf && !validateCPF(newGuardian.cpf).isValid ? "border-red-500" : ""}
              />
              {newGuardian.cpf && !validateCPF(newGuardian.cpf).isValid && (
                <p className="text-sm text-red-500">CPF inválido</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              {t("guardianManager.cancel")}
            </Button>
            <Button
              onClick={handleAddGuardian}
              disabled={!newGuardian.email.trim()}
            >
              {t("guardianManager.add")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default GuardianManager;
