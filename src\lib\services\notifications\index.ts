
/**
 * Serviço de notificações em tempo real usando Redis
 * 
 * Implementa o sistema de notificações para:
 * 1. Atualizações de localização
 * 2. Notificações de segurança
 * 
 * Se<PERSON><PERSON> as diretrizes do Anti-Break Protocol:
 * - Todos os métodos incluem tratamento de erros
 * - Estado da conexão é monitorado
 * - Sem dados sensíveis armazenados no código
 */

import { redisService } from '../redis';

// Canais de notificação
export const NOTIFICATION_CHANNELS = {
  GUARDIAN: 'guardian:notifications',
  STUDENT: 'student:notifications',
  LOCATION_UPDATES: 'location:updates',
  SECURITY_ALERTS: 'security:alerts'
};

// Tipos de notificação
export enum NotificationType {
  LOCATION_UPDATE = 'location_update',
  GEOFENCE_ENTER = 'geofence_enter',
  GEOFENCE_EXIT = 'geofence_exit',
  STUDENT_ARRIVED = 'student_arrived',
  STUDENT_LEFT = 'student_left',
  SECURITY_ALERT = 'security_alert',
  CONNECTION_LOST = 'connection_lost',
  CONNECTION_RESTORED = 'connection_restored',
  SYSTEM = 'system'
}

// Interface para as notificações
export interface Notification {
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  studentId?: string;
  guardianId?: string;
  data?: Record<string, any>;
  read?: boolean;
  priority?: 'low' | 'medium' | 'high' | 'critical';
}

class NotificationService {
  /**
   * Publica uma notificação para responsáveis sobre a localização de um estudante
   */
  async notifyLocationUpdate(studentId: string, latitude: number, longitude: number, address?: string): Promise<void> {
    try {
      const notification: Notification = {
        type: NotificationType.LOCATION_UPDATE,
        title: 'Atualização de Localização',
        message: address 
          ? `Localização atualizada: ${address}` 
          : 'Nova localização compartilhada',
        timestamp: new Date().toISOString(),
        studentId,
        data: {
          latitude,
          longitude,
          address
        },
        priority: 'medium'
      };

      // Log notification since we don't have Redis publish method yet
      console.log(`[NotificationService] Notificação de localização para estudante ${studentId}:`, notification);
    } catch (error) {
      console.error('[NotificationService] Erro ao enviar notificação:', error);
    }
  }

  /**
   * Publica uma notificação de segurança (cerca virtual, chegada/saída)
   */
  async notifySecurityEvent(
    type: NotificationType,
    studentId: string,
    guardianId: string,
    message: string,
    data?: Record<string, any>
  ): Promise<void> {
    try {
      const notification: Notification = {
        type,
        title: this.getSecurityEventTitle(type),
        message,
        timestamp: new Date().toISOString(),
        studentId,
        guardianId,
        data,
        priority: this.getSecurityEventPriority(type)
      };

      console.log(`[NotificationService] Notificação de segurança (${type}):`, notification);
    } catch (error) {
      console.error('[NotificationService] Erro ao enviar notificação de segurança:', error);
    }
  }

  /**
   * Obtém título para o tipo de evento de segurança
   */
  private getSecurityEventTitle(type: NotificationType): string {
    switch (type) {
      case NotificationType.GEOFENCE_ENTER:
        return 'Entrada em Área Segura';
      case NotificationType.GEOFENCE_EXIT:
        return 'Saída de Área Segura';
      case NotificationType.STUDENT_ARRIVED:
        return 'Chegada na Escola';
      case NotificationType.STUDENT_LEFT:
        return 'Saída da Escola';
      case NotificationType.SECURITY_ALERT:
        return 'Alerta de Segurança';
      default:
        return 'Notificação de Segurança';
    }
  }

  /**
   * Determina a prioridade da notificação com base no tipo
   */
  private getSecurityEventPriority(type: NotificationType): 'low' | 'medium' | 'high' | 'critical' {
    switch (type) {
      case NotificationType.GEOFENCE_EXIT:
      case NotificationType.SECURITY_ALERT:
        return 'high';
      case NotificationType.STUDENT_LEFT:
        return 'medium';
      case NotificationType.GEOFENCE_ENTER:
      case NotificationType.STUDENT_ARRIVED:
        return 'low';
      default:
        return 'medium';
    }
  }

  /**
   * Envia uma notificação de sistema
   */
  async sendSystemNotification(message: string, data?: Record<string, any>): Promise<void> {
    try {
      const notification: Notification = {
        type: NotificationType.SYSTEM,
        title: 'Notificação do Sistema',
        message,
        timestamp: new Date().toISOString(),
        data,
        priority: 'medium'
      };

      console.log(`[NotificationService] Notificação de sistema: ${message}`, notification);
    } catch (error) {
      console.error('[NotificationService] Erro ao enviar notificação de sistema:', error);
    }
  }
}

// Instância singleton
const notificationService = new NotificationService();
export default notificationService;
