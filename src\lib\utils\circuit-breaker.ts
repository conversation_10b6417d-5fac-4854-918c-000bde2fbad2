/**
 * Implementação do padrão Circuit Breaker
 * 
 * Este padrão previne sobrecarga de um serviço falho ao detectar falhas
 * e falhar rapidamente por um período antes de tentar novamente.
 * 
 * Estados do Circuit Breaker:
 * - CLOSED: Operação normal, todas as requisições são tentadas
 * - OPEN: Muitas falhas, requisições são rejeitadas imediatamente
 * - HALF-OPEN: Período de teste, uma requisição é permitida para verificar recuperação
 */
export type CircuitBreakerState = 'CLOSED' | 'OPEN' | 'HALF-OPEN';

export type CircuitBreakerOptions = {
  /**
   * Número de falhas necessárias para abrir o circuit
   */
  failureThreshold: number;
  
  /**
   * Tempo em ms para tentar novamente após abrir o circuit
   */
  resetTimeout: number;
  
  /**
   * Nome para identificação em logs
   */
  name?: string;
  
  /**
   * Função para registrar eventos do circuit breaker
   */
  onStateChange?: (
    newState: CircuitBreakerState, 
    oldState: CircuitBreakerState,
    metrics: CircuitBreakerMetrics
  ) => void;
};

export type CircuitBreakerMetrics = {
  failures: number;
  successes: number;
  rejected: number;
  lastFailureTime: number | null;
  lastSuccessTime: number | null;
  state: CircuitBreakerState;
};

/**
 * Implementação de Circuit Breaker para proteção de serviços externos
 */
export class CircuitBreaker {
  private state: CircuitBreakerState = 'CLOSED';
  private failures = 0;
  private successes = 0;
  private rejected = 0;
  private lastFailureTime: number | null = null;
  private lastSuccessTime: number | null = null;
  private name: string;
  
  constructor(
    private options: CircuitBreakerOptions
  ) {
    this.name = options.name || 'circuit-breaker';
    
    console.log(`[CircuitBreaker] ${this.name} inicializado no estado CLOSED`);
  }
  
  /**
   * Executa uma operação com proteção de circuit breaker
   * 
   * @param operation A operação a ser executada
   * @param fallback Função opcional de fallback em caso de falha
   * @returns O resultado da operação ou do fallback
   */
  async execute<T>(
    operation: () => Promise<T>, 
    fallback?: () => Promise<T>
  ): Promise<T> {
    // Se o circuit está aberto, rejeitar imediatamente
    if (this.state === 'OPEN') {
      // Verificar se é hora de tentar novamente
      if (this.lastFailureTime && 
          Date.now() - this.lastFailureTime > this.options.resetTimeout) {
        this.transitionTo('HALF-OPEN');
      } else {
        this.rejected++;
        console.log(`[CircuitBreaker] ${this.name} está OPEN - rejeitando requisição`);
        
        if (fallback) {
          return fallback();
        }
        
        throw new Error(`CircuitBreaker ${this.name} is OPEN`);
      }
    }
    
    try {
      // Executar a operação
      const result = await operation();
      
      // Sucesso
      this.onSuccess();
      
      return result;
    } catch (error) {
      // Falha
      this.onFailure();
      
      // Se temos fallback, usar
      if (fallback) {
        return fallback();
      }
      
      // Caso contrário, propagar o erro
      throw error;
    }
  }
  
  /**
   * Registra um sucesso e atualiza o estado
   */
  private onSuccess(): void {
    this.lastSuccessTime = Date.now();
    this.successes++;
    
    // Se estamos em HALF-OPEN, voltar para CLOSED
    if (this.state === 'HALF-OPEN') {
      this.transitionTo('CLOSED');
    }
  }
  
  /**
   * Registra uma falha e potencialmente abre o circuit
   */
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    // Verificar se atingimos o limite de falhas em CLOSED
    if (this.state === 'CLOSED' && this.failures >= this.options.failureThreshold) {
      this.transitionTo('OPEN');
    } 
    // Se estamos em HALF-OPEN, voltar para OPEN imediatamente
    else if (this.state === 'HALF-OPEN') {
      this.transitionTo('OPEN');
    }
  }
  
  /**
   * Muda o estado do circuit breaker e notifica listeners
   */
  private transitionTo(newState: CircuitBreakerState): void {
    const oldState = this.state;
    this.state = newState;
    
    // Reset de contadores em certas transições
    if (newState === 'CLOSED' && oldState === 'HALF-OPEN') {
      this.failures = 0;
      this.successes = 0;
    } else if (newState === 'HALF-OPEN') {
      this.successes = 0;
    }
    
    console.log(`[CircuitBreaker] ${this.name} transição de ${oldState} para ${newState}`);
    
    // Notificar listeners
    if (this.options.onStateChange) {
      this.options.onStateChange(newState, oldState, this.getMetrics());
    }
  }
  
  /**
   * Reinicia o circuit breaker para o estado CLOSED
   */
  reset(): void {
    this.failures = 0;
    this.successes = 0;
    this.rejected = 0;
    this.transitionTo('CLOSED');
    console.log(`[CircuitBreaker] ${this.name} foi reiniciado manualmente`);
  }
  
  /**
   * Retorna o estado atual do circuit breaker
   */
  getState(): CircuitBreakerState {
    return this.state;
  }
  
  /**
   * Retorna métricas do circuit breaker
   */
  getMetrics(): CircuitBreakerMetrics {
    return {
      failures: this.failures,
      successes: this.successes,
      rejected: this.rejected,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      state: this.state
    };
  }
}
