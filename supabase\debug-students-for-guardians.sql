-- ==========================================
-- DEBUG: RESPONSÁVEIS DE MAURÍCIO WILLIAMS
-- Data: 24/06/2025  
-- Objetivo: Encontrar responsáveis via tabela students
-- ==========================================

-- 1. BUSCAR DADOS DO MAURÍCIO
SELECT 
    'MAURICIO_DATA' as info,
    id, full_name, email, guardian_id, created_at
FROM profiles
WHERE email = '<EMAIL>';

-- 2. BUSCAR OUTROS ESTUDANTES COM MESMO GUARDIAN_ID
SELECT 
    'SAME_GUARDIAN_STUDENTS' as info,
    p.id, p.full_name, p.email, p.guardian_id,
    s.id as student_id, s.guardian_id as student_guardian_id
FROM profiles p
LEFT JOIN students s ON p.id::text = s.guardian_id
WHERE p.guardian_id = (
    SELECT guardian_id FROM profiles WHERE email = '<EMAIL>'
);

-- 3. BUSCAR PERFIL DO RESPONSÁVEL (Frank)
SELECT 
    'GUARDIAN_PROFILE' as info,
    id, full_name, email, phone, user_type, created_at
FROM profiles
WHERE id = (
    SELECT guardian_id FROM profiles WHERE email = '<EMAIL>'
);

-- 4. VERIFICAR ESTRUTURA DA TABELA STUDENTS
SELECT 
    'STUDENTS_STRUCTURE' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'students'
ORDER BY ordinal_position;

-- 5. DADOS DA TABELA STUDENTS RELACIONADOS AO MAURÍCIO
SELECT 
    'STUDENTS_DATA' as info,
    s.id, s.full_name, s.email, s.guardian_id, s.created_at
FROM students s
WHERE s.email = '<EMAIL>'
   OR s.guardian_id = (
       SELECT guardian_id FROM profiles WHERE email = '<EMAIL>'
   ); 