
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface ErrorFallbackProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetry?: boolean;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  title = "Erro ao carregar",
  message = "Ocorreu um erro inesperado. Tente novamente.",
  onRetry,
  showRetry = true
}) => {
  return (
    <Card className="border-red-200 bg-red-50">
      <CardContent className="p-6 text-center">
        <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-3" />
        <h3 className="font-semibold text-red-800 mb-2">{title}</h3>
        <p className="text-red-700 text-sm mb-4">{message}</p>
        {showRetry && onRetry && (
          <Button 
            variant="outline" 
            onClick={onRetry}
            className="border-red-300 text-red-700 hover:bg-red-100"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Tentar Novamente
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default ErrorFallback;
