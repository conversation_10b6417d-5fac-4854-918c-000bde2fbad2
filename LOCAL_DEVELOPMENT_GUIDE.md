# LOCAL DEVELOPMENT GUIDE

## AMBIENTE LOCAL CONFIGURADO

### Frontend Local: http://localhost:4000
### Supabase Studio: http://127.0.0.1:54323  
### API Local: http://127.0.0.1:54321

## COMANDOS BÁSICOS

### Iniciar ambiente:
npx supabase start
npm run dev

### Parar ambiente:
npx supabase stop

### Status:
npx supabase status

## CREDENCIAIS LOCAIS
- Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres
- Usuario: postgres
- Senha: postgres

## VANTAGENS
- Funciona offline
- Sem custos
- Independente do Supabase Cloud
- Backup completo do projeto

Agora você tem total independência!
