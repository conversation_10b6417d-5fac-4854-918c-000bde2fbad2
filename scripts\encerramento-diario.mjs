#!/usr/bin/env node
/**
 * Script de Encerramento Diário para Locate-Family-Connect
 * 
 * Automatiza o protocolo de encerramento seguro de sessões de desenvolvimento,
 * seguindo rigorosamente o Anti-Break Protocol.
 * 
 * Uso:
 *   node scripts/encerramento-diario.mjs
 *   node scripts/encerramento-diario.mjs --full    # Inclui limpeza Docker completa
 *   node scripts/encerramento-diario.mjs --report  # Gera relatório detalhado
 * 
 * @version 1.0.0
 * @created 2025-06-03
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

// Configuração
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const PROJECT_ROOT = path.resolve(__dirname, '..');
const BACKUP_DIR = path.join(PROJECT_ROOT, '.backup');
const LOGS_DIR = path.join(PROJECT_ROOT, 'logs');
const PLAN_FILE = path.join(PROJECT_ROOT, 'docs', 'plano-execucao.md');

// Conversão de funções callback para Promises
const execAsync = promisify(exec);
const mkdirAsync = promisify(fs.mkdir);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const copyFileAsync = promisify(fs.copyFile);
const existsAsync = promisify(fs.exists);

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  gray: '\x1b[90m',
  bold: '\x1b[1m'
};

// Argumentos da linha de comando
const args = process.argv.slice(2);
const isFullCleanup = args.includes('--full');
const generateReport = args.includes('--report');

// Interface para input do usuário
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Prompt com Promise
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => resolve(answer));
  });
}

/**
 * Função principal
 */
async function main() {
  try {
    console.log(`
${colors.bold}${colors.cyan}=== PROTOCOLO DE ENCERRAMENTO DIÁRIO ===
${colors.reset}${colors.gray}Data: ${new Date().toLocaleString()}
${colors.reset}${colors.gray}Projeto: Locate-Family-Connect
${colors.reset}`);

    // 1. Verificar estado do git antes de iniciar
    await checkGitStatus();

    // 2. Encerrar serviços Docker
    await shutdownDockerServices();
    
    // 3. Backup de arquivos sensíveis
    await backupSensitiveFiles();
    
    // 4. Verificação de segurança
    await securityCheck();
    
    // 5. Registro de estado
    await recordState();
    
    // 6. Diagnóstico e verificação
    await runDiagnostics();
    
    // 7. Gerar relatório (opcional)
    if (generateReport) {
      await generateSessionReport();
    }

    console.log(`\n${colors.green}${colors.bold}✅ ENCERRAMENTO DIÁRIO CONCLUÍDO COM SUCESSO${colors.reset}`);
    console.log(`${colors.cyan}Para retomar o trabalho amanhã, execute: ${colors.bold}node scripts/iniciar-ambiente.mjs${colors.reset}`);
    
  } catch (error) {
    console.error(`\n${colors.red}${colors.bold}❌ ERRO NO ENCERRAMENTO: ${error.message}${colors.reset}`);
    process.exit(1);
  } finally {
    rl.close();
  }
}

/**
 * Verifica o estado do Git e notifica sobre alterações não salvas
 */
async function checkGitStatus() {
  console.log(`\n${colors.bold}🔍 Verificando estado do Git...${colors.reset}`);
  
  try {
    const { stdout } = await execAsync('git status --porcelain');
    
    if (stdout.trim()) {
      console.log(`${colors.yellow}⚠️ Existem alterações não commitadas:${colors.reset}`);
      console.log(`${colors.gray}${stdout}${colors.reset}`);
      
      const answer = await prompt(`${colors.yellow}Deseja continuar mesmo com alterações pendentes? (s/N): ${colors.reset}`);
      
      if (answer.toLowerCase() !== 's') {
        throw new Error('Encerramento cancelado. Faça commit das alterações pendentes primeiro.');
      }
    } else {
      console.log(`${colors.green}✓ Repositório limpo, sem alterações pendentes${colors.reset}`);
    }
  } catch (error) {
    if (error.message.includes('Encerramento cancelado')) {
      throw error;
    }
    console.error(`${colors.red}✗ Erro ao verificar Git: ${error.message}${colors.reset}`);
  }
}

/**
 * Encerra todos os serviços Docker relacionados ao projeto
 */
async function shutdownDockerServices() {
  console.log(`\n${colors.bold}🐳 Encerrando serviços Docker...${colors.reset}`);
  
  try {
    // Verifica se existe docker-compose-redis.yml
    const redisComposeExists = await existsAsync(path.join(PROJECT_ROOT, 'docker-compose-redis.yml'));
    
    // Parar contêineres do projeto principal
    console.log(`${colors.gray}Parando contêineres principais...${colors.reset}`);
    await execAsync('docker compose -f docker-compose.yml down', { cwd: PROJECT_ROOT });
    
    // Parar contêineres Redis se existir o arquivo
    if (redisComposeExists) {
      console.log(`${colors.gray}Parando contêineres Redis...${colors.reset}`);
      await execAsync('docker compose -f docker-compose-redis.yml down', { cwd: PROJECT_ROOT });
    }
    
    // Verificar se todos os contêineres foram encerrados
    console.log(`${colors.gray}Verificando contêineres ativos...${colors.reset}`);
    const { stdout: runningContainers } = await execAsync('docker ps --filter "name=locate-family" -q');
    
    if (runningContainers.trim()) {
      console.log(`${colors.yellow}⚠️ Ainda existem contêineres em execução:${colors.reset}`);
      const { stdout: containerDetails } = await execAsync('docker ps --filter "name=locate-family"');
      console.log(`${colors.gray}${containerDetails}${colors.reset}`);
      
      const answer = await prompt(`${colors.yellow}Deseja forçar a parada desses contêineres? (s/N): ${colors.reset}`);
      
      if (answer.toLowerCase() === 's') {
        await execAsync(`docker stop ${runningContainers.split('\n').join(' ')}`);
        console.log(`${colors.green}✓ Contêineres forçadamente parados${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ Contêineres permanecem em execução${colors.reset}`);
      }
    } else {
      console.log(`${colors.green}✓ Todos os contêineres foram encerrados${colors.reset}`);
    }
    
    // Limpeza completa se solicitado
    if (isFullCleanup) {
      console.log(`${colors.gray}Executando limpeza completa do Docker...${colors.reset}`);
      
      const answer = await prompt(`${colors.yellow}Isso removerá volumes e imagens não utilizadas. Continuar? (s/N): ${colors.reset}`);
      
      if (answer.toLowerCase() === 's') {
        await execAsync('docker system prune -f --volumes');
        console.log(`${colors.green}✓ Limpeza completa realizada${colors.reset}`);
      } else {
        console.log(`${colors.gray}Limpeza completa cancelada${colors.reset}`);
      }
    }
  } catch (error) {
    console.error(`${colors.red}✗ Erro ao encerrar Docker: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️ Continuando com o protocolo...${colors.reset}`);
  }
}

/**
 * Realiza backup de arquivos sensíveis com timestamp
 */
async function backupSensitiveFiles() {
  console.log(`\n${colors.bold}💾 Realizando backup de arquivos sensíveis...${colors.reset}`);
  
  try {
    // Garantir que o diretório de backup existe
    await mkdirAsync(BACKUP_DIR, { recursive: true });
    
    // Obter timestamp para os arquivos
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Lista de arquivos para backup
    const filesToBackup = [
      { src: '.env', dest: `.backup/.env.backup-${timestamp}` },
      { src: '.gitignore', dest: `.backup/.gitignore.backup-${timestamp}` },
      { src: 'docker-compose.yml', dest: `.backup/docker-compose.backup-${timestamp}` }
    ];
    
    // Verificar se docker-compose-redis.yml existe e adicioná-lo à lista
    if (await existsAsync(path.join(PROJECT_ROOT, 'docker-compose-redis.yml'))) {
      filesToBackup.push({
        src: 'docker-compose-redis.yml', 
        dest: `.backup/docker-compose-redis.backup-${timestamp}`
      });
    }
    
    // Realizar backup de cada arquivo
    for (const file of filesToBackup) {
      const srcPath = path.join(PROJECT_ROOT, file.src);
      const destPath = path.join(PROJECT_ROOT, file.dest);
      
      if (await existsAsync(srcPath)) {
        await copyFileAsync(srcPath, destPath);
        console.log(`${colors.green}✓ Backup de ${file.src} realizado${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ Arquivo ${file.src} não encontrado, backup ignorado${colors.reset}`);
      }
    }
    
    console.log(`${colors.green}✓ Todos os backups foram realizados em .backup/${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}✗ Erro ao fazer backup: ${error.message}${colors.reset}`);
    throw new Error('Falha crítica no backup de arquivos sensíveis');
  }
}

/**
 * Realiza verificações de segurança
 */
async function securityCheck() {
  console.log(`\n${colors.bold}🔒 Realizando verificação de segurança...${colors.reset}`);
  
  try {
    // 1. Verificar conteúdo do .gitignore
    console.log(`${colors.gray}Verificando .gitignore...${colors.reset}`);
    
    const gitignorePath = path.join(PROJECT_ROOT, '.gitignore');
    const gitignoreContent = await readFileAsync(gitignorePath, 'utf8');
    
    const requiredPatterns = [
      '.env', 
      'mcp_config.json', 
      '*.local',
      '.backup',
      'supabase/.temp/'
    ];
    
    const missingPatterns = requiredPatterns.filter(pattern => 
      !gitignoreContent.includes(pattern)
    );
    
    if (missingPatterns.length > 0) {
      console.log(`${colors.yellow}⚠️ Padrões ausentes no .gitignore:${colors.reset}`);
      missingPatterns.forEach(pattern => 
        console.log(`${colors.yellow}  - ${pattern}${colors.reset}`)
      );
      
      const answer = await prompt(`${colors.yellow}Deseja adicionar esses padrões ao .gitignore? (s/N): ${colors.reset}`);
      
      if (answer.toLowerCase() === 's') {
        const newContent = gitignoreContent + '\n\n# Adicionado automaticamente pelo protocolo de encerramento\n' + 
          missingPatterns.join('\n');
        await writeFileAsync(gitignorePath, newContent);
        console.log(`${colors.green}✓ .gitignore atualizado${colors.reset}`);
      }
    } else {
      console.log(`${colors.green}✓ .gitignore contém todos os padrões necessários${colors.reset}`);
    }
    
    // 2. Verificar arquivos com potencial exposição de tokens
    console.log(`${colors.gray}Verificando exposição de tokens...${colors.reset}`);
    
    // Padrões para verificar
    const tokenPatterns = [
      'VITE_SUPABASE_ANON_KEY',
      'VITE_SUPABASE_SERVICE_KEY',
      'VITE_RESEND_API_KEY',
      'VITE_MAPBOX_TOKEN',
      'REDIS_PASSWORD',
      'JWT_SECRET'
    ];
    
    const excludeDirs = [
      'node_modules',
      '.git',
      '.backup',
      'dist',
      'logs'
    ];
    
    const excludeParam = excludeDirs.map(dir => `--exclude-dir=${dir}`).join(' ');
    const patterns = tokenPatterns.join('\\|');
    
    try {
      const { stdout: grepResult } = await execAsync(
        `grep -r "${patterns}" ${excludeParam} --include="*.{js,jsx,ts,tsx,md,yml,yaml}" .`,
        { cwd: PROJECT_ROOT, timeout: 10000 }
      );
      
      if (grepResult.trim()) {
        console.log(`${colors.yellow}⚠️ Possível exposição de tokens encontrada:${colors.reset}`);
        console.log(`${colors.gray}${grepResult}${colors.reset}`);
        
        console.log(`${colors.yellow}⚠️ Verifique os arquivos acima e remova quaisquer tokens expostos${colors.reset}`);
      } else {
        console.log(`${colors.green}✓ Nenhuma exposição de token encontrada${colors.reset}`);
      }
    } catch (error) {
      // Pode falhar se grep não encontrar nada ou outro erro
      if (!error.message.includes('exit code 1')) {
        console.error(`${colors.yellow}⚠️ Erro na verificação de tokens: ${error.message}${colors.reset}`);
      } else {
        console.log(`${colors.green}✓ Nenhuma exposição de token encontrada${colors.reset}`);
      }
    }
  } catch (error) {
    console.error(`${colors.red}✗ Erro na verificação de segurança: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️ Continuando com o protocolo...${colors.reset}`);
  }
}

/**
 * Registra o estado atual e prepara plano para a próxima sessão
 */
async function recordState() {
  console.log(`\n${colors.bold}📝 Registrando estado atual...${colors.reset}`);
  
  try {
    // Verificar se o diretório docs existe
    const docsDir = path.join(PROJECT_ROOT, 'docs');
    await mkdirAsync(docsDir, { recursive: true });
    
    // Verificar se o arquivo de plano existe
    let planContent = '';
    if (await existsAsync(PLAN_FILE)) {
      planContent = await readFileAsync(PLAN_FILE, 'utf8');
    }
    
    // Adicionar seção para a próxima sessão
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowDate = tomorrow.toISOString().split('T')[0];
    
    const newSection = `\n\n## Plano para ${tomorrowDate}\n\n`;
    
    // Obter tarefas pendentes no Git
    const { stdout: todoList } = await execAsync('git grep -i "TODO\\|FIXME" --name-only', { cwd: PROJECT_ROOT });
    
    let todoItems = '';
    if (todoList.trim()) {
      console.log(`${colors.yellow}⚠️ TODOs encontrados no código:${colors.reset}`);
      
      const todoFiles = todoList.split('\n').filter(Boolean);
      for (const file of todoFiles) {
        const { stdout: todos } = await execAsync(`git grep -i "TODO\\|FIXME" "${file}"`, { cwd: PROJECT_ROOT });
        todoItems += todos.split('\n').filter(Boolean)
          .map(todo => `- [ ] ${file}: ${todo.trim()}\n`)
          .join('');
      }
    }
    
    // Construir conteúdo do plano
    const newPlanContent = planContent.includes(`## Plano para ${tomorrowDate}`) 
      ? planContent 
      : planContent + newSection + todoItems;
    
    // Salvar o plano
    await writeFileAsync(PLAN_FILE, newPlanContent);
    
    console.log(`${colors.green}✓ Estado registrado em ${PLAN_FILE}${colors.reset}`);
    console.log(`${colors.yellow}ℹ️ Edite manualmente para adicionar mais detalhes para a próxima sessão${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}✗ Erro ao registrar estado: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️ Continuando com o protocolo...${colors.reset}`);
  }
}

/**
 * Executa diagnósticos do sistema
 */
async function runDiagnostics() {
  console.log(`\n${colors.bold}🔬 Executando diagnósticos...${colors.reset}`);
  
  try {
    // 1. Verificar integridade do Docker
    console.log(`${colors.gray}Verificando integridade do Docker...${colors.reset}`);
    
    const { stdout: dockerDf } = await execAsync('docker system df', { cwd: PROJECT_ROOT });
    console.log(`${colors.cyan}Uso de recursos Docker:${colors.reset}`);
    console.log(`${colors.gray}${dockerDf}${colors.reset}`);
    
    // 2. Verificar estado das branches Git
    console.log(`\n${colors.gray}Verificando branches Git...${colors.reset}`);
    
    const { stdout: gitBranches } = await execAsync('git branch', { cwd: PROJECT_ROOT });
    console.log(`${colors.cyan}Branches disponíveis:${colors.reset}`);
    console.log(`${colors.gray}${gitBranches}${colors.reset}`);
    
    // 3. Verificar estado do npm
    console.log(`\n${colors.gray}Verificando dependências npm...${colors.reset}`);
    
    try {
      await execAsync('npm outdated --depth=0', { cwd: PROJECT_ROOT, timeout: 10000 });
      console.log(`${colors.green}✓ Todas as dependências estão atualizadas${colors.reset}`);
    } catch (error) {
      // npm outdated retorna código 1 quando há dependências desatualizadas
      if (error.stdout) {
        console.log(`${colors.yellow}⚠️ Dependências desatualizadas:${colors.reset}`);
        console.log(`${colors.gray}${error.stdout}${colors.reset}`);
      } else {
        console.error(`${colors.red}✗ Erro ao verificar dependências: ${error.message}${colors.reset}`);
      }
    }
  } catch (error) {
    console.error(`${colors.red}✗ Erro nos diagnósticos: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️ Continuando com o protocolo...${colors.reset}`);
  }
}

/**
 * Gera relatório detalhado da sessão
 */
async function generateSessionReport() {
  console.log(`\n${colors.bold}📊 Gerando relatório da sessão...${colors.reset}`);
  
  try {
    // Garantir que o diretório de logs existe
    await mkdirAsync(LOGS_DIR, { recursive: true });
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = path.join(LOGS_DIR, `session-report-${timestamp}.md`);
    
    // Coletar informações para o relatório
    const gitLog = await execAsync('git log --since="1 day" --pretty=format:"%h - %an, %ar : %s"', { cwd: PROJECT_ROOT })
      .then(res => res.stdout)
      .catch(() => 'Nenhum commit nas últimas 24 horas');
    
    const dockerStatus = await execAsync('docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"', { cwd: PROJECT_ROOT })
      .then(res => res.stdout)
      .catch(() => 'Erro ao obter status do Docker');
    
    const gitStatus = await execAsync('git status', { cwd: PROJECT_ROOT })
      .then(res => res.stdout)
      .catch(() => 'Erro ao obter status do Git');
    
    // Construir relatório
    const report = `# Relatório de Sessão: ${new Date().toLocaleDateString()}

## Commits Recentes

\`\`\`
${gitLog}
\`\`\`

## Estado do Docker

\`\`\`
${dockerStatus}
\`\`\`

## Estado do Git

\`\`\`
${gitStatus}
\`\`\`

## Backups Realizados

- .env.backup-${timestamp}
- .gitignore.backup-${timestamp}
- docker-compose.backup-${timestamp}

## Notas Adicionais

Este relatório foi gerado automaticamente pelo protocolo de encerramento diário.
`;

    // Salvar relatório
    await writeFileAsync(reportFile, report);
    
    console.log(`${colors.green}✓ Relatório gerado em ${reportFile}${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}✗ Erro ao gerar relatório: ${error.message}${colors.reset}`);
  }
}

// Executar função principal
main().catch(error => {
  console.error(`${colors.red}${colors.bold}❌ FALHA CRÍTICA: ${error.message}${colors.reset}`);
  process.exit(1);
});
