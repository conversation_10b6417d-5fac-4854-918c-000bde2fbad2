# Relatório de Diagnóstico — <PERSON><PERSON><PERSON> ↔ <PERSON><PERSON> (DB *Reno*)

> Data: 26-Jun-2025 • Autor: *Cascade* • Projeto: **EduConnect / Reno**  
> Objetivo: identificar e resolver o erro de tipo de dados no RPC `get_student_guardians_from_relationships` que afeta a relação entre o aluno **<PERSON><PERSON><PERSON><PERSON>** (`<EMAIL>`) e a responsável **<PERSON><PERSON>** (`<EMAIL>`).

---

## 1. Estrutura de Tabelas

| Tabela | Coluna | Tipo | Nulo | Default |
|--------|--------|------|------|---------|
| **student_guardian_relationships** | id | uuid | NO | gen_random_uuid() |
| | student_id | uuid | NO | – |
| | guardian_id | uuid | NO | – |
| | relationship_type | text | NO | 'parent' |
| | created_at | timestamptz | YES | now() |
| **profiles** | id | int | NO | seq |
| | user_id | uuid | YES | – |
| | full_name | text | NO | – |
| | phone | varchar(20) | YES | – |
| | user_type | text | NO | – |
| … | … | … | … | … |
| **family_invitations** | id | uuid | NO | gen_random_uuid() |
| | student_id | uuid | NO | – |
| | guardian_email | text | NO | – |
| | status | text | NO | 'pending' |
| … | … | … | … | … |
| **guardians** | id | uuid | NO | gen_random_uuid() |
| | student_id | uuid | YES | – |
| | guardian_profile_id | uuid | YES | – |
| | email | text | YES | – |

*(Linhas completas disponíveis no anexo de consulta SQL.)*

---

## 2. Restrições & Índices

**Restrições (excerpt)**
```
student_guardian_relationships_pkey (PRIMARY KEY)
student_guardian_relationships_student_id_guardian_id_key (UNIQUE)
student_guardian_relationships_student_id_fkey (FK → auth.users.id)
student_guardian_relationships_guardian_id_fkey (FK → auth.users.id)
…
```

**Índices Relevantes**
```
idx_profiles_user_type_status (profiles)
idx_family_invitations_student_id (family_invitations)
student_guardian_relationships_student_id_guardian_id_key (unique)
…
```

---

## 3. Contagem de Registros & Integridade Referencial

| Entidade | Total |
|----------|-------|
| student_guardian_relationships | 4 |
| profiles | 7 |
| family_invitations | 1 |
| guardians (legado) | 0 |
| auth.users | 6 |

Validações de integridade (0 = ok):

```
Relationships without valid student ...... 0
Relationships without valid guardian ..... 0
Users without profiles .................... 0
```

---

## 4. Relação Maurício ↔ Luciana

```
relationship_id .......... 97530052-f731-46e3-8a27-907236bd6b71
student_id ............... 864a6c0b-4b17-4df7-8709-0c3f7cf0be91
guardian_id .............. dafa3a37-cc79-486c-8b2c-735a54805419
relationship_created ..... 2025-06-25 13:54 UTC
student_email ............ <EMAIL>
guardian_email ........... <EMAIL>
```

### Histórico de Convites

```
family_invitations: status = accepted (token: 0Ij4Rd3…)
criado em 2025-06-25 12:51 • aceito em 2025-06-25 13:54
```

---

## 5. Funções RPC Relacionadas

| Função | Observação |
|--------|------------|
| get_student_guardians_from_relationships | ok (corrigido em 26-Jun-2025) |
| get_guardian_students | ok |
| is_guardian_of_student | ok |
| … | … |

### Fonte da Função com Erro
```plpgsql
CREATE OR REPLACE FUNCTION public.get_student_guardians_from_relationships(p_student_id uuid)
RETURNS TABLE(
    id text,
    student_id uuid,
    email text,
    full_name text,
    phone text,
    is_active boolean,
    created_at timestamptz)
AS $$
BEGIN
    IF auth.uid() <> p_student_id THEN
        RAISE EXCEPTION 'Unauthorized access to student guardians';
    END IF;

    RETURN QUERY
    SELECT
        sgr.id::text,
        sgr.student_id,
        u.email,              -- ← varchar(255) causa conflito
        COALESCE(p.full_name, u.email) AS full_name,
        p.phone,
        TRUE,
        sgr.created_at
    FROM public.student_guardian_relationships sgr
    JOIN auth.users u ON u.id = sgr.guardian_id
    LEFT JOIN public.profiles p ON p.user_id = sgr.guardian_id
    WHERE sgr.student_id = p_student_id
    ORDER BY sgr.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## 6. Políticas RLS (Resumo)

- `student_guardian_relationships`: SELECT permitido para *student_id = auth.uid()* ou *guardian_id = auth.uid()*.
- `family_invitations`: SELECT permitido quando `guardian_email = auth.email()` OR `student_id = auth.uid()`.
- `profiles`: select/update restritos ao próprio `user_id`.

*(Ver anexo para lista completa.)*

---

## 7. Funções de Performance & Tamanho

| Tabela | Tamanho Total |
|--------|--------------|
| spatial_ref_sys | 7.0 MB |
| audit_log_entries | 880 kB |
| profiles | 192 kB |
| ... | ... |

---

## 8. Validação da Correção

```sql
SELECT * FROM public.get_student_guardians_from_relationships('864a6c0b-4b17-4df7-8709-0c3f7cf0be91');
-- ✔ 1 registro retornado — função executada com sucesso após inclusão de cast explícito
```

---

## 9. Diagnóstico

1. O campo `email` de `auth.users` é `character varying(255)`, mas a *signature* da função exige `text`.
2. Durante o `RETURN QUERY`, o PostgreSQL não faz cast implícito de `varchar` para `text` quando a função é `SECURITY DEFINER` em Schema `public`.
3. Isto dispara o erro **42804** imediatamente ao ser chamada.

---

## 10. Correção Proposta

```plpgsql
-- Alterar apenas a linha do SELECT dentro da função
u.email::text AS email
```

Ou, alternativamente, mudar a assinatura da função:

```sql
ALTER FUNCTION public.get_student_guardians_from_relationships(uuid)
  RETURNS TABLE(
    id text,
    student_id uuid,
    email varchar,
    full_name text,
    phone text,
    is_active boolean,
    created_at timestamptz);
```

**Recomendação:** aplicar a primeira opção para manter consistência com outras funções (`email text`).

---

## 11. Passos Seguintes

1. **Aplicar migração** adicionando cast explícito na função.
2. Rodar testes unitários e E2E que chamam esta RPC.
3. Monitorar logs Supabase por 24 h para garantir ausência de novos erros.
4. Atualizar documentação e checklist de migrações.

---

### Anexos
- Logs completos de RLS, constraints, índices e estatísticas.
- Saída integral das queries executadas (*raw JSON*), armazenada em `docs/appendix-mauricio-luciana-queries-20250625.json` para consulta.
