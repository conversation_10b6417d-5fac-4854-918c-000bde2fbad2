
import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';

export function useLGPDActions() {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useUser();

  const exportUserData = async () => {
    setLoading(true);
    try {
      if (!user?.id) {
        throw new Error('Usuário não autenticado');
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (profileError) throw profileError;

      // Get user locations
      const { data: locations, error: locationsError } = await supabase
        .from('locations')
        .select('*')
        .eq('user_id', user.id);

      if (locationsError) throw locationsError;

      // Get guardian relationships using RPC
      const { data: guardianRelationships, error: guardianError } = await supabase.rpc(
        'get_student_guardians_from_relationships',
        { p_student_id: user.id }
      );

      if (guardianError) {
        console.warn('Could not fetch guardian relationships:', guardianError);
      }

      const userData = {
        profile,
        locations: locations || [],
        guardianRelationships: guardianRelationships || [],
        exportedAt: new Date().toISOString()
      };

      // Create and download file
      const blob = new Blob([JSON.stringify(userData, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `user-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: 'Dados exportados com sucesso',
        description: 'Seus dados foram baixados em formato JSON'
      });
    } catch (error: any) {
      console.error('Export error:', error);
      toast({
        title: 'Erro ao exportar dados',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const deleteUserData = async () => {
    setLoading(true);
    try {
      if (!user?.id) {
        throw new Error('Usuário não autenticado');
      }

      // Delete user locations
      const { error: locationsError } = await supabase
        .from('locations')
        .delete()
        .eq('user_id', user.id);

      if (locationsError) throw locationsError;

      // Delete guardian relationships
      const { error: relationshipsError } = await supabase
        .from('student_guardian_relationships')
        .delete()
        .eq('student_id', user.id);

      if (relationshipsError) {
        console.warn('Could not delete guardian relationships:', relationshipsError);
      }

      // Delete user profile
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('user_id', user.id);

      if (profileError) throw profileError;

      toast({
        title: 'Dados deletados com sucesso',
        description: 'Todos os seus dados foram removidos do sistema'
      });

      // Sign out user
      await supabase.auth.signOut();
    } catch (error: any) {
      console.error('Delete error:', error);
      toast({
        title: 'Erro ao deletar dados',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const requestAccountDeletion = async (reason?: string) => {
    setLoading(true);
    try {
      if (!user?.id) {
        throw new Error('Usuário não autenticado');
      }

      // Use RPC function to create account deletion request
      const { error } = await supabase.rpc('create_account_deletion_request', {
        p_reason: reason || null
      });

      if (error) throw error;

      toast({
        title: 'Solicitação de exclusão criada',
        description: 'Seus responsáveis foram notificados para aprovar a exclusão da conta'
      });
    } catch (error: any) {
      console.error('Account deletion request error:', error);
      toast({
        title: 'Erro ao solicitar exclusão',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    exportUserData,
    deleteUserData,
    requestAccountDeletion
  };
}

