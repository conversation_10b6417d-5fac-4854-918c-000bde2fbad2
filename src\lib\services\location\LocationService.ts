
import { BaseService } from '../base/BaseService';
import { LocationData } from '@/types/database';
import { supabase } from '@/integrations/supabase/client';

/**
 * Service for handling location-related operations
 * Restored to working version using correct RPC functions
 */
export class LocationService extends BaseService {
  /**
   * Validate if a string is a valid UUID
   */
  isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Get student locations using the corrected RPC function or deduplicated RPC
   */
  async getStudentLocations(
    studentId: string,
    options?: { deduplicate?: boolean; start?: string; end?: string }
  ): Promise<LocationData[]> {
    try {
      await this.getCurrentUser();
      if (!this.isValidUUID(studentId)) {
        throw new Error('Invalid student ID format');
      }
      const { deduplicate = false, start = undefined, end = undefined } = options || {};
      if (deduplicate) {
        // Chama a nova função deduplicada
        const { data, error } = await this.supabase.rpc<any, any[]>('get_student_locations_deduped', {
          p_student_id: studentId,
          p_start: start || null,
          p_end: end || null
        });
        if (error) {
          console.error('[LocationService] RPC deduped error:', error);
          throw error;
        }
        if (!Array.isArray(data) || data.length === 0) return [];
        return data.map((location: any) => ({
          id: location.id,
          user_id: location.student_id,
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: location.timestamp || location["timestamp"],
          address: location.address,
          shared_with_guardians: location.shared_with_guardians,
          student_name: undefined,
          student_email: '',
          created_at: location.timestamp || location["timestamp"],
          user: undefined
        }));
      } else {
        // Use the corrected RPC function
        const { data, error } = await this.supabase.rpc('get_guardian_locations_bypass_v2', {
          p_student_id: studentId
        });
      
      if (error) {
        console.error('[LocationService] RPC error:', error);
        throw error;
      }

      console.log('[LocationService] RPC returned:', data?.length || 0, 'locations');
      
      if (!data || data.length === 0) {
        return [];
      }

      // Transform the data to match LocationData interface
      return data.map((location: any) => ({
        id: location.id,
        user_id: location.user_id,
        latitude: location.latitude,
        longitude: location.longitude,
        timestamp: location.location_timestamp,
        address: location.address,
        shared_with_guardians: location.shared_with_guardians,
        student_name: location.student_name,
        student_email: location.student_email || '',
        created_at: location.location_timestamp,
        user: {
          full_name: location.student_name,
          user_type: 'student'
        }
      }));
      }
    } catch (error: unknown) {
      console.error('[LocationService] Error fetching student locations:', error);
      this.showError('Não foi possível buscar as localizações');
      return [];
    }
  }
  
  /**
   * Save a new location, avoiding duplicates within 60 seconds and ~10m radius
   */
  async saveLocation(locationData: Partial<LocationData>): Promise<LocationData | null> {
    try {
      const user = await this.getCurrentUser();
      const userId = locationData.user_id || user.id;
      const lat = locationData.latitude;
      const lng = locationData.longitude;
      if (typeof lat !== 'number' || typeof lng !== 'number') {
        throw new Error('Latitude e longitude obrigatórias');
      }
      // 1. Verificar se já existe localização próxima no tempo e espaço
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
      const { data: existing, error: selectError } = await this.supabase
        .from('locations')
        .select('*')
        .eq('user_id', userId)
        .gte('timestamp', oneMinuteAgo.toISOString())
        .filter('latitude', 'gte', (Math.round(lat * 10000) - 1) / 10000)
        .filter('latitude', 'lte', (Math.round(lat * 10000) + 1) / 10000)
        .filter('longitude', 'gte', (Math.round(lng * 10000) - 1) / 10000)
        .filter('longitude', 'lte', (Math.round(lng * 10000) + 1) / 10000)
        .limit(1)
        .maybeSingle();
      if (selectError) {
        throw selectError;
      }
      if (existing) {
        // Já existe localização semelhante, retorna ela (completa os campos)
        // Get profile data para completar LocationData
        const { data: profileData } = await this.supabase
          .from('profiles')
          .select('full_name, email')
          .eq('user_id', existing.user_id)
          .single();
        return {
          ...existing,
          student_name: profileData?.full_name || 'Unknown',
          student_email: profileData?.email || '',
          created_at: existing.created_at || existing.timestamp,
          user: profileData ? {
            full_name: profileData.full_name,
            user_type: 'student'
          } : undefined
        } as LocationData;
      }
      // 2. Se não existe, insere normalmente
      const { data, error } = await this.supabase
        .from('locations')
        .insert([{
          user_id: userId,
          latitude: lat,
          longitude: lng,
          address: locationData.address,
          shared_with_guardians: locationData.shared_with_guardians || false
        }])
        .select()
        .single();
      if (error) {
        throw error;
      }
      // Get profile data for complete LocationData object
      const { data: profileData } = await this.supabase
        .from('profiles')
        .select('full_name, email')
        .eq('user_id', data.user_id)
        .single();
      return {
        id: data.id,
        user_id: data.user_id,
        latitude: data.latitude,
        longitude: data.longitude,
        timestamp: data.timestamp,
        address: data.address,
        shared_with_guardians: data.shared_with_guardians,
        student_name: profileData?.full_name || 'Unknown',
        student_email: profileData?.email || '',
        created_at: data.created_at,
        user: profileData ? {
          full_name: profileData.full_name,
          user_type: 'student'
        } : undefined
      } as LocationData;
    } catch (error: unknown) {
      console.error('[LocationService] Error saving location:', error);
      this.showError('Não foi possível salvar a localização');
      return null;
    }
  }

  /**
   * Reverse geocode coordinates to get address using MapBox API
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<string> {
    try {
      const MAPBOX_TOKEN = 'pk.eyJ1IjoidGVjaC1lZHUtbGFiIiwiYSI6ImNtN3cxaTFzNzAwdWwyanMxeHJkb3RrZjAifQ.h0g6a56viW7evC7P0c5mwQ';
      
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${MAPBOX_TOKEN}&language=pt`
      );
      
      if (!response.ok) {
        throw new Error('MapBox geocoding service unavailable');
      }
      
      const data = await response.json();
      
      if (data.features && data.features.length > 0) {
        return data.features[0].place_name;
      }
      
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
        
    } catch (error) {
      console.warn('[LocationService] Geocoding failed:', error);
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
  }
}

/**
 * Remove duplicated locations for a student in the database using the backend RPC.
 * @param studentId UUID do estudante
 * @param radius Raio em metros (default 50)
 * @param timeWindow Janela de tempo em segundos (default 10)
 * @param accuracy Nível de precisão (opcional)
 * @returns Número de registros removidos
 */
export async function deleteStudentLocationDuplicates(
  userId: string,
  radius: number = 50,
  timeWindow: number = 10, // valor em segundos vindo do componente
  accuracy?: string
): Promise<number> {
  // Converter segundos para minutos, mínimo 1
  const timeWindowMin = Math.max(1, Math.round(timeWindow / 60));
  const { data, error } = await (supabase as any).rpc('delete_student_locations_duplicates', {
    p_user_id: userId,
    p_radius_meters: radius,
    p_time_window_min: timeWindowMin, // parâmetro correto
    p_accuracy: accuracy ?? null,
  });
  if (error) {
    throw new Error(error.message || 'Erro ao remover duplicatas');
  }
  return typeof data === 'number' ? data : 0;
}

export const locationService = new LocationService();
