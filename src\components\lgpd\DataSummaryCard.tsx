
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Eye, User, Mail, FileText, MapPin } from 'lucide-react';
import { DataSummary } from '@/types/lgpd';
import { useTranslation } from 'react-i18next';

interface DataSummaryCardProps {
  dataSummary: DataSummary;
  userType: string;
}

const DataSummaryCard: React.FC<DataSummaryCardProps> = ({ dataSummary, userType }) => {
  const { t } = useTranslation();

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          {t('lgpd.dataSummary.title')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center gap-3 p-3 bg-transparent rounded">
            <User className="h-5 w-5 text-gray-600 dark:text-gray-200" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-200">{t('lgpd.dataSummary.name')}</p>
              <p className="font-medium dark:text-white">{dataSummary.personal.name}</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-transparent rounded">
            <Mail className="h-5 w-5 text-gray-600 dark:text-gray-200" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-200">{t('lgpd.dataSummary.email')}</p>
              <p className="font-medium dark:text-white">{dataSummary.personal.email}</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-transparent rounded">
            <FileText className="h-5 w-5 text-gray-600 dark:text-gray-200" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-200">{t('lgpd.dataSummary.userType')}</p>
              <p className="font-medium dark:text-white">{dataSummary.personal.type}</p>
            </div>
          </div>
          {userType === 'student' && (
            <div className="flex items-center gap-3 p-3 bg-transparent rounded">
              <MapPin className="h-5 w-5 text-gray-600 dark:text-gray-200" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-200">
                  {t('lgpd.dataSummary.locationTitle', 'Location data')}
                </p>
                <p className="font-medium dark:text-white">
                  {t('lgpd.dataSummary.locationStatusActive', 'Sharing active')}
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DataSummaryCard;
