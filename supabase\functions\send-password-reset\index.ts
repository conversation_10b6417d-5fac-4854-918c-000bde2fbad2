import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
// @ts-ignore - Remote URL cache warning (works in production)
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[PASSWORD_RESET] Iniciando processamento...')
    
    // Parse request body
    const { email } = await req.json()
    
    if (!email) {
      console.error('[PASSWORD_RESET] Email não fornecido')
      return new Response(
        JSON.stringify({ error: 'Email é obrigatório' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log(`[PASSWORD_RESET] Processando reset para: ${email}`)

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('[PASSWORD_RESET] Variáveis Supabase não configuradas')
      throw new Error('Configuração do Supabase incompleta')
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Send password reset email through Supabase Auth
    console.log('[PASSWORD_RESET] Enviando email via Supabase Auth...')
    
    // Determine the correct site URL based on environment
    const siteUrl = Deno.env.get('SITE_URL') || 
                   (req.headers.get('origin') || 'https://monitore-mvp.lovable.app')
    
    console.log(`[PASSWORD_RESET] Usando site URL: ${siteUrl}`)
    
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${siteUrl}/reset-password`,
    })

    if (error) {
      console.error('[PASSWORD_RESET] Erro Supabase Auth:', error)
      
      // Try fallback with Resend
      console.log('[PASSWORD_RESET] Tentando fallback com Resend...')
      
      const resendApiKey = Deno.env.get('RESEND_API_KEY') || Deno.env.get('VITE_RESEND_API_KEY')
      if (!resendApiKey) {
        console.error('[PASSWORD_RESET] RESEND_API_KEY ou VITE_RESEND_API_KEY não configurada no servidor')
        throw new Error('RESEND_API_KEY não configurada no servidor')
      }

      const resendResponse = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${resendApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: 'Sistema Monitore <<EMAIL>>',
          to: [email],
          subject: 'Recuperação de Senha - Sistema Monitore',
          html: `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <title>Redefinir Senha - Sistema Monitore</title>
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa;">
              <div style="max-width: 600px; margin: 0 auto; background-color: white;">
                <!-- Header -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
                  <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 300;">
                    🔐 Sistema Monitore
                  </h1>
                  <p style="color: rgba(255,255,255,0.9); margin: 5px 0 0 0; font-size: 16px;">
                    Localização Familiar Segura
                  </p>
                </div>
                
                <!-- Content -->
                <div style="padding: 40px 30px;">
                  <h2 style="color: #2c3e50; margin: 0 0 20px 0; font-size: 24px; font-weight: 400;">
                    Redefinição de Senha Solicitada
                  </h2>
                  
                  <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                    Olá! 👋
                  </p>
                  
                  <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
                    Recebemos uma solicitação para redefinir a senha da sua conta <strong>${email}</strong> no Sistema Monitore.
                  </p>
                  
                  <!-- Security Notice -->
                  <div style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 20px; margin: 25px 0; border-radius: 8px;">
                    <h3 style="margin: 0 0 10px 0; color: #856404; font-size: 16px;">
                      🛡️ Segurança em Primeiro Lugar
                    </h3>
                    <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
                      Para garantir sua segurança, entre em contato conosco para concluir o processo de redefinição de senha.
                    </p>
                  </div>
                  
                  <!-- Contact Info -->
                  <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 25px; border-radius: 12px; text-align: center; margin: 30px 0;">
                    <h3 style="color: white; margin: 0 0 15px 0; font-size: 18px;">
                      📞 Entre em Contato
                    </h3>
                    <p style="color: rgba(255,255,255,0.9); margin: 0 0 15px 0; font-size: 16px;">
                      <strong>Email:</strong> <EMAIL>
                    </p>
                    <p style="color: rgba(255,255,255,0.8); margin: 0; font-size: 14px;">
                      Nossa equipe está pronta para ajudar você!
                    </p>
                  </div>
                  
                  <p style="color: #6c757d; font-size: 14px; line-height: 1.5; margin: 20px 0 0 0;">
                    <strong>Não foi você?</strong> Pode ignorar este email com segurança. Sua senha permanecerá inalterada.
                  </p>
                </div>
                
                <!-- Footer -->
                <div style="background-color: #f8f9fa; padding: 25px 30px; border-top: 1px solid #e9ecef;">
                  <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 14px; font-weight: 600;">
                    📱 Sistema Monitore - Localização Familiar
                  </p>
                  <p style="margin: 0; color: #868e96; font-size: 12px; line-height: 1.4;">
                    Este é um email automático do sistema de recuperação de senha. Não responda a esta mensagem.<br>
                    Para suporte, visite: <a href="https://sistema-monitore.com.br" style="color: #667eea;">sistema-monitore.com.br</a>
                  </p>
                </div>
              </div>
            </body>
            </html>
          `,
        }),
      })

      const resendData = await resendResponse.json()
      
      if (!resendResponse.ok) {
        console.error('[PASSWORD_RESET] Erro Resend:', resendData)
        throw new Error(`Resend API error: ${resendData.message || 'Unknown error'}`)
      }

      console.log('[PASSWORD_RESET] Email enviado via Resend:', resendData.id)
      
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Email de recuperação enviado com sucesso via Resend',
          id: resendData.id,
          fallback: true
        }),
        { 
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('[PASSWORD_RESET] Email enviado via Supabase Auth com sucesso')
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Email de recuperação enviado com sucesso',
        supabase: true
      }),
      { 
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('[PASSWORD_RESET] Erro geral:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    
    return new Response(
      JSON.stringify({ 
        error: 'Erro interno do servidor',
        details: errorMessage,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
