
import { z } from 'zod';
import { validateCPF } from '../../../lib/utils/cpf-validator';

// Define the schema for form validation
export const studentFormSchema = z.object({
  cpf: z.string()
    .min(1, { message: "CPF é obrigatório." })
    .refine((cpf) => {
      const validation = validateCPF(cpf);
      return validation.isValid;
    }, {
      message: "CPF inválido. Verifique os dígitos informados.",
    }),
  name: z.string().min(1, {
    message: "O nome é obrigatório.",
  }),
  email: z.string().email({
    message: "Por favor insira um email válido.",
  }).optional(),
  phone: z.string().optional(),
});

// Use z.infer to derive the type from the schema
export type StudentFormValues = z.infer<typeof studentFormSchema>;

// Define the common props interface
export interface StudentFormProps {
  onStudentAdded?: () => void;
}
