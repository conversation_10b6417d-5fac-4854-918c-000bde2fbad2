/* Native Map Styles for Mobile Platforms */

/* Common native styles */
.native-map-container {
  touch-action: manipulation;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;
}

.native-controls {
  z-index: 10;
  padding: 8px;
}

.native-button-group {
  gap: 12px;
}

/* iOS specific styles */
.ios-native-map {
  -webkit-tap-highlight-color: transparent;
}

.ios-button {
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ios-button:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.ios-button-outline {
  border: 1px solid rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Android specific styles */
.android-native-map {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.android-button {
  padding: 8px 16px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.android-button:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.android-button-outline {
  border: 1px solid rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 0.9);
}

/* Native-like animations */
@keyframes native-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.native-pulse {
  animation: native-pulse 0.3s ease-in-out;
}

/* Offline mode styles */
.offline-control {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.ios-native-map .offline-control {
  width: 44px;
  height: 44px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.ios-native-map .offline-control:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.android-native-map .offline-control {
  width: 48px;
  height: 48px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.android-native-map .offline-control:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.offline-progress-container {
  background-color: white;
  padding: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  width: 100px;
}

.offline-progress-bar {
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.offline-progress-fill {
  height: 100%;
  background-color: #3b82f6;
  transition: width 0.3s ease;
}

.offline-indicator {
  position: absolute;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.offline-indicator.online {
  background-color: #10b981;
  color: white;
}

.offline-indicator.offline {
  background-color: #f59e0b;
  color: white;
}