
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Mail, MessageSquare, Phone, MapPin, Loader2 } from 'lucide-react';
import { useCommunicationPreferences } from '@/hooks/useCommunicationPreferences';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface MultiChannelLocationSharingProps {
  recipientEmail: string;
  recipientName?: string;
  latitude: number;
  longitude: number;
  senderName?: string;
  className?: string;
}

export const MultiChannelLocationSharing: React.FC<MultiChannelLocationSharingProps> = ({
  recipientEmail,
  recipientName,
  latitude,
  longitude,
  senderName,
  className = ''
}) => {
  const { preferences, isLoading: prefsLoading } = useCommunicationPreferences();
  const { toast } = useToast();
  const [isSharing, setIsSharing] = useState(false);

  const getActiveChannels = () => {
    if (!preferences) return ['email'];
    
    const channels = [];
    if (preferences.email_enabled) channels.push('email');
    if (preferences.sms_enabled) channels.push('sms');
    if (preferences.whatsapp_enabled) channels.push('whatsapp');
    
    return channels;
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'email': return <Mail className="h-3 w-3" />;
      case 'sms': return <Phone className="h-3 w-3" />;
      case 'whatsapp': return <MessageSquare className="h-3 w-3" />;
      default: return null;
    }
  };

  const getChannelColor = (channel: string) => {
    switch (channel) {
      case 'email': return 'bg-blue-100 text-blue-800';
      case 'sms': return 'bg-green-100 text-green-800';
      case 'whatsapp': return 'bg-emerald-100 text-emerald-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleShare = async () => {
    if (!preferences) return;
    
    setIsSharing(true);
    
    try {
      // Chamar edge function para compartilhamento multi-canal
      const { data, error } = await supabase.functions.invoke('send-multi-channel', {
        body: {
          recipient_email: recipientEmail,
          recipient_name: recipientName,
          latitude,
          longitude,
          sender_name: senderName,
          preferences: {
            email_enabled: preferences.email_enabled,
            sms_enabled: preferences.sms_enabled,
            whatsapp_enabled: preferences.whatsapp_enabled,
            phone_number: preferences.phone_number,
            preferred_method: preferences.preferred_method
          }
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      const result = data as { success: boolean; channels_used: string[]; errors?: string[] };
      
      if (result.success) {
        toast({
          title: "Localização compartilhada",
          description: `Enviada via ${result.channels_used.join(', ')} para ${recipientName || recipientEmail}`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "Erro parcial no envio",
          description: result.errors ? result.errors.join(', ') : 'Alguns canais falharam',
        });
      }
    } catch (error: any) {
      console.error('Erro ao compartilhar localização:', error);
      toast({
        variant: "destructive",
        title: "Erro no compartilhamento",
        description: error.message || 'Não foi possível compartilhar a localização',
      });
    } finally {
      setIsSharing(false);
    }
  };

  const activeChannels = getActiveChannels();
  const isDisabled = prefsLoading || isSharing || activeChannels.length === 0;

  return (
    <div className={`space-y-2 ${className}`}>
      <Button
        onClick={handleShare}
        disabled={isDisabled}
        className="w-full flex items-center gap-2"
      >
        {isSharing ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <MapPin className="h-4 w-4" />
        )}
        {isSharing ? 'Enviando...' : 'Compartilhar Localização'}
      </Button>
      
      {activeChannels.length > 0 && (
        <div className="flex items-center gap-1 justify-center">
          <span className="text-xs text-muted-foreground">Via:</span>
          {activeChannels.map((channel) => (
            <Badge
              key={channel}
              variant="outline"
              className={`text-xs px-2 py-0.5 ${getChannelColor(channel)}`}
            >
              {getChannelIcon(channel)}
              <span className="ml-1 capitalize">{channel}</span>
            </Badge>
          ))}
        </div>
      )}
      
      {preferences?.preferred_method && (
        <p className="text-xs text-center text-muted-foreground">
          Preferido: {preferences.preferred_method}
        </p>
      )}
    </div>
  );
};
