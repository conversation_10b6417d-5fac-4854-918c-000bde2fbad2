import { supabase } from '@/integrations/supabase/client';

export interface EmailConfirmationData {
  email: string;
  fullName: string;
  userType: 'student' | 'parent';
  confirmationToken?: string;
}

export class EmailConfirmationService {
  private static instance: EmailConfirmationService;
  private resendApiKey: string;
  private baseUrl: string;

  private constructor() {
    // Get environment variables
    this.resendApiKey = import.meta.env.VITE_RESEND_API_KEY || '';
    this.baseUrl = import.meta.env.DEV 
      ? 'http://localhost:4000' 
      : 'https://sistema-monitore.com.br';
  }

  public static getInstance(): EmailConfirmationService {
    if (!EmailConfirmationService.instance) {
      EmailConfirmationService.instance = new EmailConfirmationService();
    }
    return EmailConfirmationService.instance;
  }

  /**
   * Send custom confirmation email via Resend
   */
  async sendConfirmationEmail(data: EmailConfirmationData): Promise<{
    success: boolean;
    message: string;
    emailId?: string;
  }> {
    try {
      if (!this.resendApiKey) {
        throw new Error('RESEND_API_KEY not configured');
      }

      const confirmationUrl = `${this.baseUrl}/auth/confirm?token=${data.confirmationToken}&type=signup`;
      
      const emailHtml = this.generateConfirmationEmailHtml(
        confirmationUrl,
        data.fullName,
        data.userType,
        data.email
      );
      
      const emailText = this.generateConfirmationEmailText(
        confirmationUrl,
        data.fullName,
        data.userType
      );

      const brandName = data.userType === 'student' ? 'Aplicativo Monitor' : 'Monitoring';
      const resendResponse = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.resendApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: `${brandName} <<EMAIL>>`,
          to: [data.email],
          subject: `✅ Confirme sua conta - ${brandName}`,
          html: emailHtml,
          text: emailText,
          reply_to: '<EMAIL>',
          tags: [
            {
              name: 'type',
              value: 'email_confirmation'
            },
            {
              name: 'user_type',
              value: data.userType
            }
          ]
        }),
      });

      if (!resendResponse.ok) {
        const errorText = await resendResponse.text();
        console.error('Resend API error:', errorText);
        throw new Error(`Failed to send email via Resend: ${errorText}`);
      }

      const resendData = await resendResponse.json();
      console.log('✅ Email sent successfully via Resend:', resendData);

             // Log to console for now (email_logs table will be created via migration)
       console.log('📧 Email confirmation sent:', {
         email: data.email,
         userType: data.userType,
         emailId: resendData.id,
         confirmationUrl
       });

      return {
        success: true,
        message: 'Confirmation email sent successfully',
        emailId: resendData.id
      };

    } catch (error) {
      console.error('❌ Error sending confirmation email:', error);
      return {
        success: false,
        message: `Erro ao enviar email: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      };
    }
  }

  // Note: Email logging will be implemented after email_logs table migration

  /**
   * Generate HTML email template
   */
  private generateConfirmationEmailHtml(
    confirmationUrl: string,
    fullName: string,
    userType: string,
    email: string
  ): string {
    const userTypeDisplay = userType === 'student' ? 'Student' : 'Guardian';
    const greeting = fullName ? `Hello, ${fullName}!` : 'Hello!';
    const brandName = userType === 'student' ? 'Aplicativo Monitor' : 'Monitoring';
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm your account - Monitoring System</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px;
            background-color: #f9fafb;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header { 
            text-align: center; 
            padding: 20px 0; 
            border-bottom: 2px solid #e5e7eb; 
            margin-bottom: 30px;
        }
        .logo { 
            font-size: 32px; 
            font-weight: bold; 
            color: #1f2937;
            margin-bottom: 10px;
        }
        .subtitle { 
            color: #6b7280; 
            font-size: 16px;
        }
        .content { 
            padding: 20px 0; 
        }
        .highlight-box { 
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); 
            color: white;
            padding: 30px; 
            margin: 30px 0; 
            border-radius: 12px;
            text-align: center;
        }
        .btn { 
            display: inline-block; 
            background: white; 
            color: #3b82f6; 
            padding: 16px 32px; 
            text-decoration: none; 
            border-radius: 8px; 
            font-weight: bold; 
            text-align: center; 
            margin: 20px 0;
            border: 2px solid white;
            transition: all 0.3s ease;
        }
        .btn:hover { 
            background: #f8fafc; 
            transform: translateY(-1px);
        }
        .user-info {
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .features {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .features ul {
            list-style: none;
            padding: 0;
        }
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .features li:last-child {
            border-bottom: none;
        }
        .footer { 
            text-align: center; 
            padding: 30px 0; 
            border-top: 1px solid #e5e7eb; 
            margin-top: 40px; 
            color: #6b7280; 
            font-size: 14px;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            body { padding: 10px; }
            .container { padding: 20px; }
            .btn { display: block; margin: 20px 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📍 ${brandName}</div>
            <div class="subtitle">Secure Family Location</div>
        </div>

        <div class="content">
            <h2 style="color: #1f2937; margin-bottom: 20px;">✅ Confirm your account</h2>
            
            <p style="font-size: 16px; margin-bottom: 20px;">${greeting}</p>
            
            <p>Your account was successfully created! To start using ${brandName}, you need to confirm your email.</p>
            
            <div class="user-info">
                <p style="margin: 0;"><strong>📧 Email:</strong> ${email}</p>
                <p style="margin: 10px 0 0 0;"><strong>👤 Tipo de conta:</strong> ${userTypeDisplay}</p>
            </div>

            <div class="highlight-box">
                <h3 style="margin-top: 0; color: white;">🎯 Next step</h3>
                <p style="margin-bottom: 20px;">Click the button below to confirm your account and start using all features of the system:</p>
                
                <a href="${confirmationUrl}" class="btn" style="color: #3b82f6; text-decoration: none;">
                    ✅ Confirm my account
                </a>
            </div>

            <div class="warning">
                <p style="margin: 0;"><strong>⏰ Important:</strong> This confirmation link expires in 24 hours. If you don't confirm by then, you'll need to request a new confirmation email.</p>
            </div>

            <div class="features">
                <h4 style="margin-top: 0; color: #1f2937;">What you can do after confirming:</h4>
                ${userType === 'student' ? `
                <ul>
                    <li>📱 Share your location with guardians</li>
                    <li>🔒 Control when and with whom to share</li>
                    <li>🔗 Accept family link invitations</li>
                    <li>📊 View your location history</li>
                </ul>
                ` : `
                <ul>
                    <li>👥 View linked students' locations</li>
                    <li>🔗 Request links with students</li>
                    <li>📍 See students on the map in real time</li>
                    <li>⚡ Send notifications and alerts</li>
                </ul>
                `}
            </div>

            <p style="margin-top: 30px;"><strong>If you didn't create this account</strong>, you can safely ignore this email.</p>

            <p><strong>Need help?</strong><br>
            Contact us: <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a></p>
        </div>

        <div class="footer">
            <p><strong>© 2025 ${brandName}</strong></p>
            <p>Secure Family Location</p>
            <p style="margin-top: 15px;">This email was sent to ${email}</p>
            <p>If you no longer wish to receive these emails, <a href="mailto:<EMAIL>" style="color: #6b7280;">click here</a></p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate plain text email template
   */
  private generateConfirmationEmailText(
    confirmationUrl: string,
    fullName: string,
    userType: string
  ): string {
    const userTypeDisplay = userType === 'student' ? 'Student' : 'Guardian';
    const greeting = fullName ? `Hello, ${fullName}!` : 'Hello!';
    
    const brandName = userType === 'student' ? 'Aplicativo Monitor' : 'Monitoring';
    return `
${greeting}

 ✅ CONFIRM YOUR ACCOUNT - ${brandName.toUpperCase()}

 Your account was created as ${userTypeDisplay}!

To start using ${brandName}, confirm your account by clicking the link below:

${confirmationUrl}

⏰ IMPORTANT: This link expires in 24 hours.

${userType === 'student' ? `
After confirming, you can:
• Share your location with guardians
• Control when and with whom to share
• Accept family link invitations
• View your location history
` : `
After confirming, you can:
• View linked students' locations
• Request links with students
• See students on the map in real time
• Send notifications and alerts
`}

If you didn't create this account, ignore this email.

Need help? Contact us: <EMAIL>

---
© 2025 ${brandName} - Secure Family Location
`;
  }

  // Note: Resend functionality will be implemented later with proper admin API
}

export const emailConfirmationService = EmailConfirmationService.getInstance(); 
