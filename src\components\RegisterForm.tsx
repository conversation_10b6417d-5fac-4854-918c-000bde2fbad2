
import React, { useState, useEffect, FormEvent } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, EyeOff, User, Lock, Phone, School, Book, Mail, UserPlus, Plus, Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { validateEmail } from '@/lib/utils/email-validator';
import { UserType } from '@/lib/auth-redirects';
import LGPDConsent from '@/components/LGPDConsent';
import { formatCPF, validateCPF, cleanCPF } from '@/lib/utils/cpf-validator';
import { emailConfirmationService } from '@/lib/services/EmailConfirmationService';
import { useTranslation } from 'react-i18next';

export interface RegisterFormProps {
  userType: UserType;
  onLoginClick: () => void;
  variant?: 'login' | 'register';
}

const RegisterForm: React.FC<RegisterFormProps> = ({
  userType,
  onLoginClick,
  variant,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [studentEmails, setStudentEmails] = useState<string[]>([]);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [lgpdConsent, setLgpdConsent] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const defaultCountry: 'BR' | 'GB' = i18n.language.startsWith('pt') ? 'BR' : 'GB';

  const [formState, setFormState] = useState<{
    email: string;
    name: string;
    phone?: string;
    cpf?: string;
    nationalId?: string;
    countryCode: 'BR' | 'GB';
  }>({
    email: '',
    name: '',
    phone: '',
    cpf: '',
    nationalId: '',
    countryCode: defaultCountry
  });
  const [phoneCountry, setPhoneCountry] = useState<'BR' | 'GB' | 'US' | 'PT'>(defaultCountry);
  const [countryCode, setCountryCode] = useState<'BR' | 'GB'>(defaultCountry);

  const schema = z.object({
    name: z.string().min(1, t('errors.required')),
    email: z.string().email(t('errors.invalidEmail')).min(1, t('errors.required')),
    countryCode: z.enum(['BR', 'GB']),
    cpf: z.string().optional(),
    nationalId: z.string().optional(),
    password: z.string()
      .min(8, t('errors.passwordTooShort'))
      .regex(/[A-Z]/, 'Password must have at least one uppercase letter')
      .regex(/[a-z]/, 'Password must have at least one lowercase letter')
      .regex(/[0-9]/, 'Password must have at least one number')
      .regex(/[^A-Za-z0-9]/, 'Password must have at least one special character')
      .refine((password) => {
        const commonWords = ['password', '123456', 'qwerty', 'abc123'];
        return !commonWords.some(word => password.toLowerCase().includes(word));
      }, t('forms.passwordGuidance.title')),
    confirmPassword: z.string().min(1, t('errors.required')),
    phone: z.string().optional(),
  }).superRefine((data, ctx) => {
    if (data.password !== data.confirmPassword) {
      ctx.addIssue({ code: 'custom', message: t('errors.passwordMismatch'), path: ['confirmPassword'] });
    }
    // CPF is required IF country is BR AND phone is not UK-like (or phone is empty, which !isPhoneUK covers)
    const isPhoneUK = data.phone && data.phone.startsWith('+44');
    if (data.countryCode === 'BR' && !isPhoneUK) {
      if (!data.cpf) {
        ctx.addIssue({ code: 'custom', message: t('forms.cpfRequired'), path: ['cpf'] });
      } else {
        const validation = validateCPF(data.cpf);
        if (!validation.isValid) {
          ctx.addIssue({ code: 'custom', message: t('forms.cpfInvalid'), path: ['cpf'] });
        }
      }
    }
  });

  const { register, handleSubmit, formState: { errors }, setValue, reset, watch } = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      countryCode: defaultCountry,
      cpf: '', // Ensure cpf is initialized to handle optional nature
      nationalId: '',
      phone: '',
    }
  });

  const watchedCountryCode = watch('countryCode');
  const watchedPhone = watch('phone');

  useEffect(() => {
    // Initialize form or react to country/phone changes if needed for CPF visibility
  }, [userType, reset, watchedCountryCode, watchedPhone]);

  const handleSignupError = (error: {
    code?: string;
    message: string;
    status?: number;
  }) => {
    console.error('Supabase signup error:', error);
    console.error('Error code:', error.code);
    console.error('Error status:', error.status);
    console.error('Error message:', error.message);
    
    let errorMessage = t('forms.registrationError');
    
    if (error.code === 'user_already_exists' || error.message.includes('User already registered')) {
      errorMessage = t('forms.userAlreadyExists');
      
      toast({
        title: t('forms.userAlreadyRegistered'),
        description: t('forms.redirectingToLogin'),
        variant: "default",
      });
      
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } else if (error.message.includes('Database error') || error.code === 'unexpected_failure') {
      errorMessage = `${t('forms.databaseError')} ${error.code || 'DB-ERROR'}`;
      
      toast({
        title: t('forms.serverError'),
        description: t('forms.serverErrorDescription'),
        variant: "destructive",
      });
      
      try {
        const tempData = {
          email: formState.email,
          name: formState.name,
          userType,
          timestamp: new Date().toISOString()
        };
        localStorage.setItem('pendingRegistration', JSON.stringify(tempData));
        console.log('Saved registration data for later retry:', tempData);
      } catch (err) {
        console.error('Failed to save temp data:', err);
      }
      
    } else if (error.message.includes('Password')) {
      errorMessage = t('forms.passwordRequirements');
    } else if (error.message.includes('CPF')) {
      errorMessage = t('forms.invalidCpf');
    }
    
    setError(errorMessage);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'name' || name === 'email' || name === 'password' || name === 'confirmPassword' || name === 'phone') {
      setValue(name, value as any); // Use `as any` or ensure specific type for setValue
    }
  };

  const handleCPFChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedCPF = formatCPF(e.target.value);
    setValue('cpf', formattedCPF);
    setFormState(prev => ({ ...prev, cpf: formattedCPF }));
  };

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue('confirmPassword', e.target.value);
  };

  const handleStudentEmailChange = (index: number, value: string) => {
    const newStudentEmails = [...studentEmails];
    newStudentEmails[index] = value;
    setStudentEmails(newStudentEmails);
  };

  const addStudentEmail = () => {
    setStudentEmails(prev => [...prev, '']);
  };

  const handleCountryChange = (newCountry: 'BR' | 'GB') => {
    setCountryCode(newCountry); // Internal state for conditional rendering
    setValue('countryCode', newCountry); // RHF state

    // Optionally, adjust phone prefix or clear phone if logic requires
    // For example, if changing to UK, and phone was a BR number, you might want to clear or reformat.
    // Current phone formatting logic might handle this already.
    const currentPhone = watch('phone') || '';
    setValue('phone', formatPhoneNumber(currentPhone, newCountry)); // Re-format phone for new country
  };
  
  const handlePhoneCountryChange = (newCountry: 'BR' | 'GB' | 'US' | 'PT') => {
    setPhoneCountry(newCountry); // This seems to be for formatting phone input, might be distinct from registration country
    
    const currentPhone = watch('phone') || '';
    const reformattedPhone = formatPhoneNumber(currentPhone, newCountry);
    setValue('phone', reformattedPhone);
  };

  const formatPhoneNumber = (phone: string, country: 'BR' | 'GB' | 'US' | 'PT') => {
    // Remove todos os caracteres não-numéricos, exceto '+'
    const digits = phone.replace(/[^\d+]/g, '');
    
    let formattedPhone = digits;
    if (formattedPhone.includes('+')) {
      formattedPhone = '+' + formattedPhone.replace(/\+/g, '');
    }
    
    switch (country) {
      case 'BR': {
        if (!formattedPhone.startsWith('+55') && !formattedPhone.startsWith('+')) {
          formattedPhone = '+55' + formattedPhone;
        } else if (formattedPhone.startsWith('+') && !formattedPhone.startsWith('+55')) {
          // If it has a different country code, keep it, or clear it based on desired UX
        }
        
        const brDigits = formattedPhone.replace('+55', '').replace(/\D/g, '');
        if (brDigits.length >= 2) {
          const ddd = brDigits.substring(0, 2);
          const firstPart = brDigits.substring(2, 7);
          const secondPart = brDigits.substring(7, 11);
          
          formattedPhone = `+55 (${ddd})`;
          if (firstPart) formattedPhone += ` ${firstPart}`;
          if (secondPart) formattedPhone += `-${secondPart}`;
        } else {
          formattedPhone = `+55 ${brDigits}`; // Handle incomplete numbers
        }
        break;
      }
        
      case 'GB': {
         if (!formattedPhone.startsWith('+44') && !formattedPhone.startsWith('+')) {
          formattedPhone = '+44' + formattedPhone;
        } else if (formattedPhone.startsWith('+') && !formattedPhone.startsWith('+44')) {
          // If it has a different country code, keep it
        }
        
        const ukDigits = formattedPhone.replace('+44', '').replace(/\D/g, '');
        if (ukDigits.length > 0) {
          // Basic formatting, can be improved based on UK number structure
          formattedPhone = `+44 ${ukDigits.substring(0,4)} ${ukDigits.substring(4)}`;
        } else {
          formattedPhone = `+44 ${ukDigits}`;
        }
        break;
      }
        
      // US and PT cases remain the same
      case 'US': {
        if (!formattedPhone.startsWith('+1') && !formattedPhone.startsWith('+')) {
          formattedPhone = '+1' + formattedPhone;
        }
        const usDigits = formattedPhone.replace('+1', '').replace(/\D/g, '');
        if (usDigits.length >= 3) {
          const areaCode = usDigits.substring(0, 3);
          const firstPart = usDigits.substring(3, 6);
          const secondPart = usDigits.substring(6,10);
          formattedPhone = `+1 (${areaCode})`;
          if (firstPart) formattedPhone += ` ${firstPart}`;
          if (secondPart) formattedPhone += `-${secondPart}`;
        } else {
           formattedPhone = `+1 ${usDigits}`;
        }
        break;
      }
        
      case 'PT': {
        if (!formattedPhone.startsWith('+351') && !formattedPhone.startsWith('+')) {
          formattedPhone = '+351' + formattedPhone;
        }
        const ptDigits = formattedPhone.replace('+351', '').replace(/\D/g, '');
         if (ptDigits.length > 0) {
            formattedPhone = `+351 ${ptDigits.substring(0,3)} ${ptDigits.substring(3,6)} ${ptDigits.substring(6,9)}`;
        } else {
           formattedPhone = `+351 ${ptDigits}`;
        }
        break;
      }
    }
    
    return formattedPhone;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    // Use watchedCountryCode here for formatting based on the selected registration country,
    // or phoneCountry if that's specifically for the phone input's current formatting context.
    // Assuming phoneCountry is for direct input formatting aid:
    const formattedValue = formatPhoneNumber(rawValue, phoneCountry);
    setValue('phone', formattedValue);
  };

  const getCleanPhoneForDatabase = (phone: string) => {
    return phone.replace(/[^\d+]/g, '');
  };

  const onSubmit = async (data: {
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
    cpf?: string; // CPF is optional at the data level
    phone?: string;
    countryCode: 'BR' | 'GB';
  }) => {
    if (!lgpdConsent) {
      toast({
        title: t('forms.consentRequired'),
        description: t('forms.consentDescription'),
        variant: "destructive",
      });
      return;
    }

    // Password confirmation check already in Zod, but good for immediate feedback
    if (data.password !== data.confirmPassword) {
      toast({
        title: t('forms.passwordMismatchError'),
        description: t('forms.passwordsDoNotMatch'),
        variant: "destructive",
      });
      return;
    }

    const emailValidation = validateEmail(data.email);
    if (!emailValidation.isValid) {
      toast({
        title: t('forms.invalidEmail'),
        description: emailValidation.error,
        variant: "destructive",
      });
      return;
    }

    // Zod schema handles CPF validation based on countryCode and phone,
    // so direct validation here might be redundant if form submits only on Zod success.
    // However, if there was an error from Zod, it should be displayed by RHF.

    setIsLoading(true);
    setError('');
    
    // Update internal state if still used, otherwise RHF data is primary
    setFormState({
      email: data.email,
      name: data.name,
      phone: data.phone,
      cpf: data.cpf,
      countryCode: data.countryCode
    });

    try {
      const cleanPhone = data.phone ? getCleanPhoneForDatabase(data.phone) : undefined;

      const isPhoneUK = data.phone && data.phone.startsWith('+44');
      const needsCpf = data.countryCode === 'BR' && !isPhoneUK;
      const cpfToSend = needsCpf && data.cpf ? cleanCPF(data.cpf) : null;

      console.log('Submitting signup. Country:', data.countryCode, 'Phone:', cleanPhone, 'CPF to send:', cpfToSend);
      
      const { data: authData, error } = await supabase.auth.signUp({
        email: data.email.trim().toLowerCase(),
        password: data.password,
        options: {
          data: {
            full_name: data.name.trim(),
            user_type: userType,
            country_code: data.countryCode,
            phone: cleanPhone,
            cpf: cpfToSend, // Use the conditionally cleaned or nulled CPF
            // national_id field removed - using email as identifier for UK users
            lgpd_consent: data.countryCode === 'BR', // Consider if UK also needs a consent flag
            gdpr_consent: data.countryCode === 'GB',
            // Assuming consent_date is generic or have specific ones
            consent_date: new Date().toISOString()
          },
          emailRedirectTo: `${window.location.origin}/auth/confirm`
        }
      });

      if (error) {
        handleSignupError(error);
        setIsLoading(false);
        return;
      }

      if (authData.user) {
        try {
          // Log consent based on actual policy applied
          const policyType = data.countryCode === 'BR' ? 'lgpd_consent_granted' : 'gdpr_consent_granted';
          await supabase.from('auth_logs').insert({
            user_id: authData.user.id,
            event_type: policyType,
            metadata: {
              consent_granted_at: new Date().toISOString(),
              user_type: userType,
              country_code: data.countryCode,
              data_types_consented: ['personal_identification', 'contact_info', 'location_data'],
              cpf_provided: !!cpfToSend
            }
          });
        } catch (logError) {
          console.warn('Failed to log consent:', logError);
        }

        const isEmailConfirmed = authData.user.email_confirmed_at !== null;
        
        if (!isEmailConfirmed) {
          toast({
            title: t('forms.accountCreated'),
            description: t('forms.verifyEmail'),
            variant: "default",
          });
          
          try {
            console.log('🔄 Sending custom confirmation email...');
            await emailConfirmationService.sendConfirmationEmail({
              email: data.email.trim().toLowerCase(),
              fullName: data.name.trim(),
              userType: userType,
              // Ensure confirmationToken is what Resend/your backend expects
              confirmationToken: authData.session?.access_token || authData.user.id
            });
            console.log('✅ Custom confirmation email sent successfully!');
          } catch (emailError) {
            console.warn('⚠️ Error sending custom confirmation email:', emailError);
          }
          
          navigate('/register/confirm', { replace: true });
          
        } else {
          toast({
            title: t('forms.registrationComplete'),
            description: t('forms.accountCreatedConfirmed', { 
              userType: userType === 'student' ? t('forms.studentAccount') : t('forms.parentAccount')
            }),
            variant: "default",
          });
          
          const redirectPath = userType === 'student' ? '/student-dashboard' : '/parent-dashboard';
          navigate(redirectPath, { replace: true });
        }
      }

      if (userType === 'parent' && studentEmails.length > 0) {
        // This logic seems for inviting students post-registration, ensure it's still relevant
        for (const studentEmail of studentEmails) {
          const studentEmailValidation = validateEmail(studentEmail);
          if (!studentEmailValidation.isValid) {
            toast({
              title: "Email de estudante inválido",
              description: `${studentEmail}: ${studentEmailValidation.error}`,
              variant: "destructive",
            });
            // Do not stop submission if parent registration was successful
            // This should be a secondary action
            console.warn("Invalid student email in list:", studentEmail);
            // setIsLoading(false); // Removed to not block parent registration
            // return;
          }
        }
        localStorage.setItem('pendingStudentEmails', JSON.stringify(studentEmails));
      }
      
    } catch (error: any) {
      console.error('Registration error:', error);
      handleSignupError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderPasswordGuidance = () => {
    return (
      <div className="text-sm text-gray-600 mt-2 space-y-1">
        <span className="font-medium">{t('forms.passwordGuidance.title')}</span>
        <ul className="list-disc pl-5">
          <li>{t('forms.passwordGuidance.tip1')}</li>
          <li>{t('forms.passwordGuidance.tip2')}</li>
          <li>{t('forms.passwordGuidance.tip3')}</li>
          <li>{t('forms.passwordGuidance.example')}</li>
        </ul>
      </div>
    );
  };

  const showCpfField = watchedCountryCode === 'BR' && !(watchedPhone && watchedPhone.startsWith('+44'));
  const showNationalIdField = watchedCountryCode === 'GB';

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" data-cy="register-form">
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm" data-cy="error-message">
          {error}
        </div>
      )}

      <div className="space-y-2">
        <Label>{t('auth.register.fullName')}</Label>
        <Input
          {...register('name')}
          placeholder={t('auth.register.fullName')}
          className="w-full"
          data-cy="fullname-input"
        />
        {errors.name && (
          <p className="text-sm text-red-500">{errors.name.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label>{t('auth.register.country')}</Label>
        <select
          {...register('countryCode')}
          // value={countryCode} // RHF handles value via register
          onChange={(e) => handleCountryChange(e.target.value as 'BR' | 'GB')}
          className="w-full border rounded-md p-2"
          data-cy="country-select"
        >
          <option value="BR">{t('auth.register.countryBR')}</option>
          <option value="GB">{t('auth.register.countryUK')}</option>
        </select>
      </div>

      {showCpfField && (
        <div className="space-y-2">
          <Label>{t('auth.register.cpf')}</Label>
          <Input
            {...register('cpf')}
            placeholder="000.000.000-00"
            className="w-full"
            data-cy="cpf-input"
            onChange={handleCPFChange} // Keeps existing formatting behavior
            maxLength={14}
          />
          {errors.cpf && (
            <p className="text-sm text-red-500">{errors.cpf.message}</p>
          )}
          <p className="text-xs text-gray-500">
            {t('auth.register.cpfHint', 'O CPF será usado para evitar contas duplicadas e facilitar a conexão entre responsáveis e estudantes.')}
          </p>
        </div>
      )}

      {showNationalIdField && (
        <div className="space-y-2">
          <Label>{t('auth.register.nationalId')}</Label>
          <Input
            {...register('nationalId')}
            placeholder="AB123456C"
            className="w-full"
            data-cy="national-id-input"
          />
          <p className="text-xs text-gray-500">
            {t('auth.register.nationalIdHint', 'Your national ID helps prevent duplicate accounts.')}
          </p>
        </div>
      )}

      <div className="space-y-2">
        <Label>{t('auth.register.email')}</Label>
        <Input
          {...register('email')}
          type="email"
          placeholder={countryCode === 'BR' ? '<EMAIL>' : '<EMAIL>'}
          className="w-full"
          data-cy="email-input"
        />
        {errors.email && (
          <p className="text-sm text-red-500">{errors.email.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label>{t('auth.register.password')}</Label>
        <div className="relative">
          <Input
            {...register('password')}
            type={showPassword ? "text" : "password"}
            placeholder="••••••••"
            className="w-full"
            data-cy="password-input"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
        {errors.password && (
          <p className="text-sm text-red-500">{errors.password.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label>{t('auth.register.confirmPassword')}</Label>
        <div className="relative">
          <Input
            {...register('confirmPassword')}
            type={showConfirmPassword ? "text" : "password"}
            placeholder="••••••••"
            className="w-full"
            data-cy="password-confirm-input"
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
        {errors.confirmPassword && (
          <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label>{t('auth.register.phone')}</Label>
        <Input
          {...register('phone')}
          type="tel"
          placeholder="(XX) XXXXX-XXXX"
          className="w-full"
        />
        {errors.phone && (
          <p className="text-sm text-red-500">{errors.phone.message}</p>
        )}
      </div>

      <LGPDConsent
        onConsentChange={setLgpdConsent}
        consented={lgpdConsent}
        userType={userType}
      />

      <Button
        type="submit"
        disabled={isLoading || !lgpdConsent}
        variant="register"
        className="w-full"
        data-cy="submit-button"
      >
        {isLoading ? (
          <>
            <span className="mr-2">{t('common.processing')}</span>
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
          </>
        ) : (
          t('auth.register.createAccount')
        )}
      </Button>

      <div className="text-center mt-4">
        <button
          type="button"
          onClick={onLoginClick}
          className="text-blue-600 hover:text-blue-800 text-sm"
          data-cy="login-link"
        >
          {t('auth.register.haveAccount')} {t('auth.register.signIn')}
        </button>
      </div>
    </form>
  );
};

export default RegisterForm;

