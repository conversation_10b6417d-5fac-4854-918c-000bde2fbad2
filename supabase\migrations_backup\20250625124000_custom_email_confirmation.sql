-- Migration: Custom Email Confirmation System
-- Description: Configure custom email confirmation using Resend

-- Create function to send custom confirmation email
CREATE OR REPLACE FUNCTION public.send_custom_confirmation_email()
RETURNS TRIGGER AS $$
DECLARE
    confirmation_url TEXT;
    user_metadata JSONB;
    full_name TEXT;
    user_type TEXT;
    base_url TEXT;
BEGIN
    -- Only proceed for email confirmations on new user creation
    IF NEW.email_confirmed_at IS NULL AND OLD.email_confirmed_at IS NULL THEN
        
        -- Get user metadata
        user_metadata := NEW.raw_user_meta_data;
        full_name := COALESCE(user_metadata->>'full_name', 'Usuário');
        user_type := COALESCE(user_metadata->>'user_type', 'student');
        
        -- Set the correct base URL based on environment
        -- In development, use localhost:4000
        -- In production, use your actual domain
        base_url := COALESCE(
            current_setting('app.base_url', true),
            'http://localhost:4000'
        );
        
        -- Create confirmation URL with the correct domain
        confirmation_url := base_url || '/auth/confirm?token=' || NEW.confirmation_token;
        
        -- Call Edge Function to send email via Resend
        -- Note: This uses supabase_functions.http_request which requires the function to be deployed
        PERFORM
            net.http_post(
                url := current_setting('supabase.edge_functions_url', true) || '/send-confirmation-email',
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || current_setting('supabase.service_role_key', true)
                ),
                body := jsonb_build_object(
                    'email', NEW.email,
                    'confirmationUrl', confirmation_url,
                    'fullName', full_name,
                    'userType', user_type
                )
            );
        
        -- Log the attempt
        INSERT INTO public.email_logs (
            user_id,
            email_type,
            recipient_email,
            status,
            metadata,
            created_at
        ) VALUES (
            NEW.id,
            'email_confirmation',
            NEW.email,
            'sent',
            jsonb_build_object(
                'confirmation_url', confirmation_url,
                'user_type', user_type,
                'full_name', full_name
            ),
            NOW()
        );
        
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create email logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.email_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email_type TEXT NOT NULL,
    recipient_email TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_email_logs_user_id ON public.email_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_type_status ON public.email_logs(email_type, status);

-- Enable RLS on email_logs
ALTER TABLE public.email_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for email_logs (only service role can access)
CREATE POLICY "Service role can manage email logs" ON public.email_logs
    FOR ALL USING (auth.role() = 'service_role');

-- Create trigger to send custom confirmation emails
DROP TRIGGER IF EXISTS trigger_send_custom_confirmation_email ON auth.users;
CREATE TRIGGER trigger_send_custom_confirmation_email
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.send_custom_confirmation_email();

-- Update settings for custom email handling
-- Note: These settings would typically be set via environment variables
-- INSERT INTO pg_settings (name, setting) VALUES ('app.base_url', 'http://localhost:4000') ON CONFLICT (name) DO UPDATE SET setting = EXCLUDED.setting;

-- Create function to handle email confirmation with correct redirect
CREATE OR REPLACE FUNCTION public.handle_email_confirmation(confirmation_token TEXT)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    redirect_url TEXT
) AS $$
DECLARE
    user_record auth.users%ROWTYPE;
    base_url TEXT;
BEGIN
    -- Get base URL
    base_url := COALESCE(
        current_setting('app.base_url', true),
        'http://localhost:4000'
    );
    
    -- Find user by confirmation token
    SELECT * INTO user_record
    FROM auth.users
    WHERE confirmation_token = $1
    AND email_confirmed_at IS NULL
    AND confirmation_sent_at IS NOT NULL
    AND confirmation_sent_at > NOW() - INTERVAL '24 hours'; -- Token expires in 24 hours
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT FALSE, 'Token inválido ou expirado', base_url || '/register';
    ELSE
        -- Confirm the email
        UPDATE auth.users 
        SET 
            email_confirmed_at = NOW(),
            confirmation_token = NULL
        WHERE id = user_record.id;
        
        -- Log successful confirmation
        UPDATE public.email_logs
        SET 
            status = 'confirmed',
            updated_at = NOW(),
            metadata = metadata || jsonb_build_object('confirmed_at', NOW())
        WHERE user_id = user_record.id AND email_type = 'email_confirmation';
        
        RETURN QUERY SELECT TRUE, 'Email confirmado com sucesso!', base_url || '/login?confirmed=true';
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create email template storage for future customization
CREATE TABLE IF NOT EXISTS public.email_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_name TEXT NOT NULL UNIQUE,
    subject TEXT NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT,
    variables JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert default confirmation email template
INSERT INTO public.email_templates (
    template_name,
    subject,
    html_content,
    text_content,
    variables
) VALUES (
    'email_confirmation',
    '✅ Confirme sua conta - Sistema Monitore',
    '<!-- HTML template would go here -->',
    'Texto da confirmação...',
    '["email", "confirmationUrl", "fullName", "userType"]'::jsonb
) ON CONFLICT (template_name) DO NOTHING;

-- Enable RLS on email_templates
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for email_templates (service role only)
CREATE POLICY "Service role can manage email templates" ON public.email_templates
    FOR ALL USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public.email_templates TO anon, authenticated;
GRANT INSERT, SELECT, UPDATE ON public.email_logs TO service_role; 