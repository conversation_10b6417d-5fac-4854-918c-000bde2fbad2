{"name": "mobile", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "postinstall": "patch-package", "build": "eas build --platform all", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:preview": "eas build --profile preview", "build:development": "eas build --profile development", "build:production": "eas build --profile production"}, "dependencies": {"@expo/metro-config": "^0.20.17", "@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native/assets-registry": "^0.80.1", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@supabase/supabase-js": "^2.39.7", "expo": "53.0.20", "metro": "^0.82.0", "metro-config": "^0.80.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-url-polyfill": "2.0.0", "react-native-web": "^0.20.0", "shared": "file:../shared", "tamagui": "^1.90.0"}, "devDependencies": {"babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0"}}