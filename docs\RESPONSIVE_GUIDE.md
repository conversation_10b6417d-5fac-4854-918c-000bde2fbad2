# Guia de Responsividade: Tablets, Android e iOS

Este documento orienta como garantir que o frontend funcione perfeitamente em tablets, celulares Android e iOS, com foco em projetos React + TailwindCSS.

---

## 1. Checklist de Responsividade

- [ ] **Testar em diferentes larguras:** Use DevTools (Chrome, Firefox) e dispositivos reais.
- [ ] **Verificar rolagem horizontal:** O layout nunca deve forçar scroll lateral.
- [ ] **Cards e colunas:** Devem usar `w-full`, `min-w-0`, `max-w-md` e `flex-col` para ocupar bem o espaço.
- [ ] **Botões:** Devem ser grandes o suficiente para toque (`h-12`, `py-3`, `w-full` em mobile).
- [ ] **Fontes:** Usar `text-base`, `text-sm` e evitar textos muito pequenos.
- [ ] **Mapa:** Deve ocupar 100% da largura do container em mobile/tablet.
- [ ] **Espaçamentos:** Usar `gap-2`, `gap-4`, `px-4`, `py-4` para evitar elementos colados.
- [ ] **Acessibilidade:** Contraste, navegação por teclado e áreas clicáveis amplas.

---

## 2. Ajustes de Tailwind e Layout

- Use breakpoints: `sm:`, `md:`, `lg:` para alternar entre colunas e linhas.
- Cards: `min-w-0 w-full max-w-md` para responsividade.
- Containers principais: `max-w-5xl mx-auto px-4`.
- Botões: `w-full` em mobile, `w-auto` em desktop.
- Mapas: `w-full h-[300px] md:h-[400px]`.

---

## 3. Testes em Dispositivos Reais e Emuladores

- **Android:**
  - Use dispositivos reais ou o emulador do Android Studio.
  - Teste com Chrome e Firefox Mobile.
- **iOS:**
  - Use iPhone/iPad reais ou o simulador do Xcode.
  - Teste com Safari e Chrome Mobile.
- **Tablets:**
  - Teste em iPad, tablets Samsung, e emuladores.
- **Dica:** Use o modo “Device Toolbar” do Chrome DevTools para simular vários dispositivos.

---

## 4. Dicas Específicas

- **Mapas:**
  - Sempre use `w-full` e altura fixa em mobile.
  - Evite tooltips ou popups muito pequenos.
- **Botões:**
  - Use `h-12`, `py-3`, `rounded-md`.
  - Garanta espaçamento entre botões (`gap-2`).
- **Fontes:**
  - `text-base` para textos principais, `text-sm` para legendas.
- **Inputs:**
  - `w-full` e padding confortável.

---

## 5. Ferramentas Recomendadas para QA

- **BrowserStack** ou **Sauce Labs**: Testes cross-browser e cross-device.
- **Lighthouse** (Chrome): Audita responsividade e acessibilidade.
- **Responsively App**: Visualização simultânea em múltiplos dispositivos.
- **Chrome DevTools**: Simulação rápida de dispositivos.

---

## 6. Boas Práticas Gerais

- Sempre use `min-w-0` em flex/grid para evitar overflow.
- Prefira `flex-col` em mobile e `flex-row` em desktop.
- Teste navegação por toque e teclado.
- Garanta que todos os botões e links sejam facilmente clicáveis.
- Use `overflow-x-hidden` no body para evitar scroll lateral acidental.
- Mantenha o layout limpo, sem excesso de informações em telas pequenas.

---

## 7. Exemplo de Estrutura Responsiva

```tsx
<div className="max-w-5xl mx-auto flex flex-col gap-8 px-4 py-8">
  <div className="flex flex-col md:flex-row gap-8 w-full">
    <div className="flex-1 min-w-[220px] max-w-md">
      {/* Informações pessoais */}
    </div>
    <div className="flex-1 min-w-[220px] max-w-md">
      {/* Ações rápidas */}
    </div>
  </div>
  <div className="w-full">
    {/* Mapa/histórico */}
  </div>
</div>
```

---

**Siga este guia para garantir uma experiência mobile e tablet de alta qualidade!** 