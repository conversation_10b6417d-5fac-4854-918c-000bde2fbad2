
import React from 'react';
import { Wifi, WifiOff, Signal, AlertTriangle } from 'lucide-react';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { cn } from '@/lib/utils';

interface NetworkStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
  variant?: 'minimal' | 'detailed' | 'badge';
}

export function NetworkStatusIndicator({ 
  className = '', 
  showDetails = false,
  variant = 'minimal'
}: NetworkStatusIndicatorProps) {
  const { 
    isOnline, 
    isSlowConnection, 
    effectiveType, 
    downlink, 
    rtt 
  } = useNetworkStatus();

  if (variant === 'minimal') {
    return (
      <div className={cn(
        "flex items-center gap-1 text-xs",
        isOnline ? "text-green-600" : "text-red-600",
        className
      )}>
        {isOnline ? (
          <>
            {isSlowConnection ? (
              <Signal className="w-3 h-3 text-yellow-500" />
            ) : (
              <Wifi className="w-3 h-3" />
            )}
          </>
        ) : (
          <WifiOff className="w-3 h-3" />
        )}
        
        {showDetails && (
          <span className="hidden sm:inline">
            {isOnline ? (isSlowConnection ? 'Lenta' : 'Online') : 'Offline'}
          </span>
        )}
      </div>
    );
  }

  if (variant === 'badge') {
    return (
      <div className={cn(
        "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
        isOnline 
          ? (isSlowConnection 
              ? "bg-yellow-100 text-yellow-800 border border-yellow-200" 
              : "bg-green-100 text-green-800 border border-green-200")
          : "bg-red-100 text-red-800 border border-red-200",
        className
      )}>
        {isOnline ? (
          <>
            {isSlowConnection ? (
              <AlertTriangle className="w-3 h-3" />
            ) : (
              <Wifi className="w-3 h-3" />
            )}
            {isSlowConnection ? 'Conexão Lenta' : 'Online'}
          </>
        ) : (
          <>
            <WifiOff className="w-3 h-3" />
            Offline
          </>
        )}
      </div>
    );
  }

  // Variant 'detailed'
  return (
    <div className={cn(
      "p-3 rounded-lg border",
      isOnline 
        ? (isSlowConnection 
            ? "bg-yellow-50 border-yellow-200" 
            : "bg-green-50 border-green-200")
        : "bg-red-50 border-red-200",
      className
    )}>
      <div className="flex items-center gap-2 mb-2">
        {isOnline ? (
          <>
            {isSlowConnection ? (
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
            ) : (
              <Wifi className="w-4 h-4 text-green-600" />
            )}
            <span className="font-medium text-sm">
              {isSlowConnection ? 'Conexão Lenta' : 'Conectado'}
            </span>
          </>
        ) : (
          <>
            <WifiOff className="w-4 h-4 text-red-600" />
            <span className="font-medium text-sm text-red-800">
              Sem Conexão
            </span>
          </>
        )}
      </div>

      {isOnline && showDetails && (
        <div className="text-xs space-y-1 text-gray-600">
          {effectiveType && (
            <div>Tipo: {effectiveType.toUpperCase()}</div>
          )}
          {downlink !== null && (
            <div>Download: {downlink.toFixed(1)} Mbps</div>
          )}
          {rtt !== null && (
            <div>Latência: {rtt}ms</div>
          )}
        </div>
      )}

      {!isOnline && (
        <p className="text-xs text-red-700 mt-1">
          Funcionando em modo offline com dados em cache
        </p>
      )}
    </div>
  );
}
