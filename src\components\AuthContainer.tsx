import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { LanguageSwitcher } from './LanguageSwitcher';
import { useNavigate } from 'react-router-dom';
import Logo from './Logo';
import AuthTabs from './AuthTabs';
import LoginForm from './LoginForm';
import RegisterForm from './RegisterForm';
import ForgotPasswordForm from './ForgotPasswordForm';
import UserTypeSelector from './auth/UserTypeSelector';
import { useToast } from "@/hooks/use-toast";
import { useDevice } from '@/hooks/use-mobile';
import { authContainerVariants, authHeaderVariants, authButtonVariants } from '@/lib/auth-styles';
import { cn } from '@/lib/utils';
import { UserType } from '@/lib/auth-redirects';
import { getCommitHash } from '@/lib/utils/version';

type AuthScreen = 'login' | 'register' | 'forgotPassword';

interface AuthContainerProps {
  initialScreen?: AuthScreen;
  onBack?: () => void;
}

const AuthContainer: React.FC<AuthContainerProps> = ({ initialScreen = 'login', onBack }) => {
  const [currentScreen, setCurrentScreen] = useState<AuthScreen>(initialScreen);
  const [userType, setUserType] = useState<UserType>('student');
  const [selectedUserType, setSelectedUserType] = useState<UserType | null>(null);
  const [userTypeConfirmed, setUserTypeConfirmed] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const commitHash = getCommitHash();
  const showCommitHash =
    commitHash &&
    commitHash !== 'dev-build' &&
    commitHash.toLowerCase() !== 'unknown';
  const { 
    type: deviceType, 
    orientation, 
    isXs, 
    isXxs, 
    aspectRatio 
  } = useDevice();

  const getContainerPadding = () => {
    if (orientation === 'portrait') {
      if (isXxs) return 'p-2 py-4';
      if (isXs) return 'p-3 py-5';
      if (deviceType === 'mobile') return 'p-4 py-6';
      return 'p-4 md:p-6';
    } else {
      if (isXxs) return 'p-1';
      if (isXs) return 'p-1.5'; 
      if (deviceType === 'mobile') return 'p-2';
      return 'p-4 md:p-6';
    }
  };
  
  const getTitleSize = () => {
    if (orientation === 'portrait') {
      if (isXxs) return 'text-lg';
      if (isXs) return 'text-xl';
      if (deviceType === 'mobile') return 'text-2xl';
      return 'text-2xl md:text-3xl';
    } else {
      if (isXxs) return 'text-base';
      if (isXs) return 'text-base';
      if (deviceType === 'mobile') return 'text-lg';
      return 'text-xl md:text-2xl';
    }
  };
  
  const getLandscapeStyles = () => {
    if (orientation === 'landscape' && (isXs || isXxs || (deviceType === 'mobile' && aspectRatio > 1.8))) {
      return 'my-1 py-1 max-h-[90vh] overflow-y-auto';
    }
    
    if (orientation === 'portrait') {
      return 'my-4 md:my-8';
    }
    
    return '';
  };
  
  const containerClasses = cn(
    authContainerVariants({
      type: currentScreen === 'register' ? 'register' : 'login',
      userType
    }),
    getContainerPadding(),
    getLandscapeStyles(),
    'transition-all duration-300 ease-in-out',
    isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4',
    'bg-transparent border-0 shadow-none text-foreground' // Transparência total
  );

  const headerClasses = cn(
    authHeaderVariants({
      type: currentScreen === 'register' ? 'register' : 'login',
      userType
    }),
    getTitleSize(),
    'transition-all duration-300 ease-in-out'
  );
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 500);
    
    return () => clearTimeout(timer);
  }, []);
  
  useEffect(() => {
    setCurrentScreen(initialScreen);
  }, [initialScreen]);
  
  const handleTabChange = (tab: UserType) => {
    setUserType(tab);
  };

  const handleUserTypeSelect = (type: UserType) => {
    setSelectedUserType(type);
  };

  const handleUserTypeConfirm = () => {
    if (selectedUserType) {
      setUserType(selectedUserType);
      setUserTypeConfirmed(true);
    }
  };

  // Reset user type confirmation when screen changes
  useEffect(() => {
    if (currentScreen !== 'register') {
      setUserTypeConfirmed(currentScreen === 'login'); // Login doesn't need type confirmation
      setSelectedUserType(null);
    }
  }, [currentScreen]);
  
  const renderScreenTitle = () => {
    switch (currentScreen) {
      case 'login':
        return t('auth.login.title');
      case 'register': {
        if (!userTypeConfirmed) {
          return t('auth.selectProfile');
        }
        const typeLabel = t(`auth.userTypes.${userType}`);
        return `${t('auth.register.title')} - ${typeLabel}`;
      }
      case 'forgotPassword':
        return t('auth.forgotPassword.title');
      default:
        return '';
    }
  };

  const handleLoginClick = () => {
    navigate('/login');
  };
  
  const renderScreenContent = () => {
    if (!isLoaded) {
      return (
        <div className="flex justify-center items-center p-4 sm:p-6">
          <div className={cn(
            "relative w-10 h-10 sm:w-12 sm:h-12",
            "before:content-[''] before:absolute before:w-full before:h-full before:border-2",
            "before:border-transparent before:border-t-current before:rounded-full",
            "before:animate-[spin_0.6s_linear_infinite]",
            "after:content-[''] after:absolute after:w-full after:h-full after:border-2",
            "after:border-transparent after:border-l-current after:rounded-full",
            "after:animate-[spin_0.6s_linear_infinite_reverse]",
            currentScreen === 'register' 
              ? 'text-emerald-500/70' 
              : 'text-blue-500/70'
          )}></div>
        </div>
      );
    }
    
    const type = currentScreen === 'register' ? 'register' : 'login';
    
    switch (currentScreen) {
      case 'login':
        return (
          <LoginForm
            userType={userType}
            onRegisterClick={() => navigate('/register')}
            onForgotPasswordClick={() => setCurrentScreen('forgotPassword')}
            variant={type}
          />
        );
      case 'register':
        // Show user type selector first for registration
        if (!userTypeConfirmed) {
          return (
            <UserTypeSelector
              selectedType={selectedUserType}
              onSelect={handleUserTypeSelect}
              onConfirm={handleUserTypeConfirm}
            />
          );
        }
        
        return (
          <RegisterForm
            userType={userType}
            onLoginClick={handleLoginClick}
            variant={type}
          />
        );
      case 'forgotPassword':
        return (
          <ForgotPasswordForm
            userType={userType}
            onBackToLogin={() => setCurrentScreen('login')}
            variant={type}
          />
        );
      default:
        return null;
    }
  };
  
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('Global error caught:', event.error);
      toast({
        title: "Erro na aplicação",
        description: "Ocorreu um erro inesperado. Por favor, tente novamente.",
        variant: "destructive",
      });
    };
    
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, [toast]);

  return (
    <div
      className={containerClasses}
      data-cy="login-container"
    >
      <LanguageSwitcher className="absolute top-2 right-2 sm:top-3 sm:right-3" />
      <div className={cn(
        "w-full max-w-sm mx-auto transition-all duration-300",
        currentScreen === 'register' ? 'space-y-6' : 'space-y-4'
      )}>
        <div className={cn(
          "flex flex-col items-center justify-center space-y-2",
          "transition-all duration-300 ease-in-out"
        )} data-cy="auth-header">
          <Logo className="w-auto h-12 sm:h-16" />
          <h1 className={headerClasses} data-cy="login-title">
            {renderScreenTitle()}
          </h1>
        </div>
        
        {currentScreen === 'register' && userTypeConfirmed && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <button
                onClick={() => setUserTypeConfirmed(false)}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                ← {t('auth.changeAccountType', 'Change account type')}
              </button>
              <AuthTabs
                activeTab={userType}
                onTabChange={handleTabChange}
                data-cy="auth-tabs"
              />
            </div>
          </div>
        )}
        
        <div data-cy="auth-form-container">
          {renderScreenContent()}
        </div>
        
        {/* Build info - discreto */}
        {showCommitHash && (
          <div className="flex justify-center mt-4">
            <span className="text-xs text-muted-foreground/50 select-none">
              v{commitHash}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthContainer;
