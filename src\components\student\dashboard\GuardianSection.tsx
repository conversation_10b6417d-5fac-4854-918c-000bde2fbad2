import React from 'react';
import GuardianManager from '@/components/GuardianManager';
import { Guardian } from '@/types/auth';
import { StudentPendingRequests } from '@/components/family/StudentPendingRequests';
import { cn } from '@/lib/utils';

interface GuardianSectionProps {
  guardians: Guardian[];
  isLoading: boolean;
  error: string | null;
  onAddGuardian: (guardianData: Partial<Guardian>) => Promise<void>;
  onRemoveGuardian: (id: string) => Promise<void>;
  onShareLocation: (guardian: Guardian) => Promise<void>;
  sharingStatus: Record<string, string>;
  className?: string;
}

const GuardianSection: React.FC<GuardianSectionProps> = ({
  guardians,
  isLoading,
  error,
  onAddGuardian,
  onRemoveGuardian,
  onShareLocation,
  sharingStatus,
  className
}) => {
  const cardClasses =
    'bg-white/60 backdrop-blur shadow-lg border border-white/30 text-foreground p-4 rounded-2xl min-w-[280px] w-full';

  return (
    <div className={cn('space-y-4', className)}>
      <GuardianManager
        className={cardClasses}
        guardians={guardians}
        isLoading={isLoading}
        error={error}
        onAddGuardian={onAddGuardian}
        onDeleteGuardian={onRemoveGuardian}
        onShareLocation={onShareLocation}
        sharingStatus={sharingStatus}
      />
      <StudentPendingRequests className={cardClasses} />
    </div>
  );
};

export default GuardianSection;
