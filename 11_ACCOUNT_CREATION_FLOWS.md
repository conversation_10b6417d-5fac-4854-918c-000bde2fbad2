# Account Creation and Family Relationship Flows

This document describes the different user types in Locate-Family-Connect and the possible account creation and linking scenarios. It also details the proposed `account_creation_method` field in the `profiles` table.

## User Types

Locate-Family-Connect works with three main profiles:

1. **Student** – shares their location with guardians.
2. **Guardian** – monitors students' locations.
3. **Developer** – administrative role used for diagnostics.

The `user_type` column in `profiles` indicates which role a user has.

## Account Creation Scenarios

The system supports multiple flows so that guardians and students can create their own accounts or invite each other.

### 1. Guardian creates their own account
- The guardian signs up and receives an entry in `auth.users` and `profiles` with `user_type: guardian`.
- After registering, the guardian can create student accounts, which receive temporary credentials by email.
- The relationship is stored in `student_guardian_relationships`.

### 2. Guardian registers a student
- The guardian creates the student's profile and an account in `auth.users` with `user_type: student`.
- The student receives a provisional password and, upon first login, already sees the guardian linked in the dashboard.

### 3. Student registers and invites guardians
- A student can sign up independently and invite a guardian via `family_invitations`.
- The guardian accepts the invitation and is linked through `student_guardian_relationships`.

### 4. Student links to an existing guardian
- When a student invites a guardian whose profile already exists (for example via email or CPF), the system only creates the new relationship without duplicating accounts.

### 5. Multiple guardians per student
- A student can be associated with more than one guardian. Each relationship is recorded in `student_guardian_relationships`.

## Tracking the Creation Method

To make account origins explicit, add a column `account_creation_method` to `profiles`. Suggested values:

- `self_registered` – the user created their own account.
- `created_by_guardian` – the account was created by a guardian (usually for a student).
- `invited_by_student` – the guardian joined through a student invitation.

This field helps auditing and ensures transparency when reviewing how each user joined the system.

## Direct guardian student creation (2025-07-01)

To simplify onboarding, the edge function `create-student-account` was replaced
by the database function `create_student_account_direct`. The function generates
a temporary password and activation token directly in the database, sends the
credentials via **Resend** and records the invitation status. Guardians can track
requests on their dashboard and students activate their accounts using the token
received by email.

