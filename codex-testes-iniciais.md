# Testes Iniciais para o Codex CLI

Este documento contém uma série de tarefas de teste simples e seguras para avaliar o desempenho do Codex CLI antes de avançar para refatorações críticas no projeto Locate-Family-Connect.

## Princípios dos Testes

Para todos os testes:
1. Use o modo `suggest` (padrão)
2. <PERSON><PERSON><PERSON><PERSON> todas as alterações antes de aplicar
3. Mantenha um git branch separado para testes
4. Execute verificações antes e depois das alterações

## Tarefas de Teste Seguras

### 1. Documentação e Análise

```bash
# Criar documentação para funções existentes - Nível 3 (Superficial)
npm run codex -- "gerar documentação JSDoc para as funções em src/lib/utils/"
```

**Objetivo:** Avaliar a capacidade do Codex de entender funções existentes sem modificar funcionalidade.

**Verificação:** Compare a documentação gerada com a implementação real para confirmar precisão.

---

### 2. Detecção de Problemas

```bash
# Identificar possíveis melhorias - Nível 3 (Superficial)
npm run codex -- "analisar e sugerir melhorias de código em src/components/"
```

**Objetivo:** Testar a capacidade analítica sem fazer alterações automáticas.

**Verificação:** Avaliar a qualidade das sugestões e sua aderência aos princípios do projeto.

---

### 3. Criação de Testes Simples

```bash
# Gerar testes básicos - Nível 2 (Moderado)
npm run codex -- "criar testes unitários para src/lib/utils/formatters.ts (sem instalar novas dependências)"
```

**Objetivo:** Avaliar a capacidade de gerar testes sem modificar o código principal.

**Verificação:** Executar os testes gerados e validar cobertura.

---

### 4. Scripts Auxiliares

```bash
# Criar scripts de utilidade - Nível 3 (Superficial)
npm run codex -- "criar um script para verificação de integridade do banco de dados em scripts/"
```

**Objetivo:** Testar criação de novas ferramentas auxiliares sem impactar o sistema principal.

**Verificação:** Executar e testar o script em ambiente controlado.

---

### 5. Pequenos Ajustes de UI

```bash
# Melhorias de acessibilidade - Nível 3 (Superficial)
npm run codex -- "adicionar atributos ARIA faltantes em src/components/ui/"
```

**Objetivo:** Avaliar modificações de UI sem mudar comportamento.

**Verificação:** Executar auditoria de acessibilidade antes e depois.

---

### 6. Análise de Dependências

```bash
# Análise de dependências - Nível 3 (Superficial)
npm run codex -- "analisar package.json para identificar dependências desatualizadas ou não utilizadas"
```

**Objetivo:** Avaliar capacidade analítica sem fazer alterações.

**Verificação:** Confirmar manualmente as recomendações.

---

## Protocolo de Avaliação

Após cada teste:

1. **Qualidade do Código**:
   - O código segue os padrões do projeto?
   - Ele é consistente com o estilo existente?

2. **Precisão**:
   - As alterações/análises estão corretas?
   - Há falsos positivos ou falsos negativos?

3. **Compreensão do Projeto**:
   - O Codex demonstra entendimento da arquitetura?
   - Ele respeita os princípios estabelecidos no AGENTS.md?

4. **Segurança**:
   - As alterações introduzem vulnerabilidades?
   - O Codex respeita os limites de acesso e permissões?

## Próximos Passos

Após completar com sucesso estes testes iniciais, documentar os resultados e então planejar testes mais avançados seguindo os princípios de "Break-Safe Philosophy", progredindo gradualmente para níveis mais críticos do sistema.

Se algum dos testes apresentar problemas significativos, refinar o arquivo AGENTS.md com instruções mais específicas antes de prosseguir.
