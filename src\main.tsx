
import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import "./index.css";
import "./lib/i18n";
import { ThemeProvider } from "next-themes";

// Import service worker registration
import './lib/offline/service-worker-registration';
import { checkCacheClearRequest, clearAppCache } from "./lib/utils/cache-manager";

// Import and setup HMR resilience to handle network changes
import { setupHMRResilience } from "./lib/hmr-resilience";
setupHMRResilience();
checkCacheClearRequest();

// Enhanced Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('[ERROR_BOUNDARY] Error caught:', error);
    if (error.message && error.message.includes('className is not defined')) {
      console.warn('[ERROR_BOUNDARY] Clearing cache due to className error');
      try {
        clearAppCache(false);
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistrations().then((regs) => {
            regs.forEach((reg) =>
              reg.unregister().catch((err) =>
                console.error('[ERROR_BOUNDARY] Failed to unregister SW:', err)
              )
            );
          });
          if (window.caches) {
            caches.keys().then((keys) =>
              Promise.all(keys.map((key) => caches.delete(key)))
            );
          }
        }
      } catch (e) {
        console.error('[ERROR_BOUNDARY] Failed to clear cache:', e);
      }
    }
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[ERROR_BOUNDARY] Error details:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md mx-auto text-center p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Oops! Algo deu errado
            </h1>
            <p className="text-gray-600 mb-6">
              Ocorreu um erro inesperado. Por favor, recarregue a página.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              Recarregar Página
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Optimize window message handling to avoid spam
const optimizeWindowMessages = () => {
  let messageCount = 0;
  const startTime = Date.now();
  
  const originalAddEventListener = window.addEventListener;
  window.addEventListener = function(type: string, listener: any, options?: any) {
    if (type === 'message') {
      // Throttle message event processing
      const throttledListener = throttle((event: MessageEvent) => {
        messageCount++;
        
        // Only log every 10th message after the first 5 to reduce spam
        if (messageCount <= 5 || messageCount % 10 === 0) {
          console.log('[DEBUG][window.message] event recebido');
          console.log('[DEBUG][window.message] event.data tipo:', typeof event.data);
          
          if (event.data && typeof event.data === 'object') {
            console.log('[DEBUG][window.message] object keys:', Object.keys(event.data));
          }
        }
        
        // Reset counter every minute
        if (Date.now() - startTime > 60000) {
          messageCount = 0;
        }
        
        // Call original listener if it's a function
        if (typeof listener === 'function') {
          listener(event);
        }
      }, 100);
      
      return originalAddEventListener.call(this, type, throttledListener, options);
    }
    
    return originalAddEventListener.call(this, type, listener, options);
  };
};

// Throttle utility function
function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), wait);
    }
  };
}

// Initialize optimizations
optimizeWindowMessages();

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    </ThemeProvider>
  </React.StrictMode>
);
