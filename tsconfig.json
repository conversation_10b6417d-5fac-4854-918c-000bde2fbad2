{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@": ["./src"], "shared": ["./shared"], "shared/*": ["./shared/*"]}}, "include": ["src/**/*", "shared/**/*", "web/src/**/*", "mobile/src/**/*"], "exclude": ["node_modules", "dist", "build"]}