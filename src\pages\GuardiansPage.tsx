import React, { useState, useEffect } from "react";
import { ArrowLeft } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useNavigate } from "react-router-dom";
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useGuardianData } from "@/hooks/useGuardianData";
import GuardianList from "@/components/guardian/GuardianList";
import AddGuardianForm from "@/components/guardian/AddGuardianForm";

const GuardiansPage = () => {
  const navigate = useNavigate();
  const { user } = useUser();
  const [showAddGuardian, setShowAddGuardian] = useState(false);

  // Use the same hook as StudentDashboard (that works)
  const {
    loading,
    error,
    guardians,
    fetchGuardians,
    addG<PERSON><PERSON>,
    remove<PERSON><PERSON>ian
  } = useGuardianData();

  // Fetch guardians when user is available
  useEffect(() => {
    if (user?.id) {
      fetchGuardians(user.id);
    }
  }, [user?.id, fetchGuardians]);

  // Navigate back to the dashboard
  const goBackToDashboard = () => {
    const userType = user?.user_metadata?.user_type || 'student';
    if (userType === 'parent') {
      navigate('/parent-dashboard');
    } else {
      navigate('/student-dashboard');
    }
  };

  // Check if the user is authenticated
  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertTitle>Acesso restrito</AlertTitle>
          <AlertDescription>
            Você precisa estar autenticado para acessar esta página.
          </AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => navigate("/login")}>Ir para Login</Button>
        </div>
      </div>
    );
  }

  // Handle add guardian with proper typing
  const handleAddGuardian = async (values: { name?: string; email?: string; phone?: string; }) => {
    if (values.email && user?.id) {
      await addGuardian(user.id, values.email);
    }
  };

  // Handle remove guardian
  const handleRemoveGuardian = async (guardianId: string) => {
    const guardian = guardians.find(g => g.id === guardianId);
    if (guardian) {
      await removeGuardian(guardian);
    }
  };

  // Handle send invite - placeholder for now
  const handleSendInvite = async (email: string, name: string | null) => {
    console.log('Send invite to:', email, name);
    // This could be implemented if needed
  };

  // Convert GuardianData to Guardian interface for GuardianList
  const formattedGuardians = guardians.map(g => ({
    id: g.id,
    full_name: g.full_name,
    email: g.email,
    phone: g.phone,
    cpf: g.cpf,
    birth_date: g.birth_date,
    status: g.status,
    is_active: g.is_active,
    created_at: g.created_at
  }));

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="content-overlay rounded-xl bg-transparent p-4 md:p-8 w-full max-w-2xl mx-auto">
        {/* Conteúdo original do GuardiansPage */}
        <div className="mb-4">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={goBackToDashboard} 
            className="flex items-center gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
        </div>

        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Meus Responsáveis</h1>
            <p className="text-muted-foreground">
              Gerencie as pessoas responsáveis que podem acompanhar sua localização
            </p>
          </div>
          <Button onClick={() => setShowAddGuardian(true)}>
            Adicionar Responsável
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTitle>Erro</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <GuardianList
          guardians={formattedGuardians}
          loading={loading}
          onAddClick={() => setShowAddGuardian(true)}
          onRemoveGuardian={handleRemoveGuardian}
          onSendInvite={handleSendInvite}
        />

        {/* Modal for adding guardian */}
        <Dialog open={showAddGuardian} onOpenChange={setShowAddGuardian}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Adicionar Responsável</DialogTitle>
              <DialogDescription>
                Adicione uma pessoa responsável para acompanhar sua localização.
              </DialogDescription>
            </DialogHeader>
            
            <AddGuardianForm onSubmit={handleAddGuardian} />
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAddGuardian(false)}>
                Cancelar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default GuardiansPage;
