{"name": "educonnect-monorepo", "private": true, "type": "module", "workspaces": ["shared", "mobile", "web"], "scripts": {"dev": "npm run dev:web", "dev:web": "npm run dev --workspace=web", "dev:mobile": "npm run start --workspace=mobile", "dev:api": "node server.js", "dev:api:watch": "nodemon server.js", "dev:all": "concurrently --kill-others-on-fail \"npm run dev:api\" \"npm run dev:web\"", "dev:all:watch": "concurrently --kill-others-on-fail \"npm run dev:api:watch\" \"npm run dev:web\"", "build": "npm run build:web", "build:web": "npm run build --workspace=web", "build:mobile": "npm run build --workspace=mobile", "build:dev": "npm run build --workspace=web", "build:shared": "npm run build --workspace=shared", "preview": "npm run preview --workspace=web", "postinstall": "patch-package", "health-check": "node -e \"console.log('Node.js:', process.version); console.log('Platform:', process.platform);\"", "test:api": "curl -X POST http://localhost:4001/api/resolve-location -H \"Content-Type: application/json\" -d \"{\\\"latitude\\\": -23.5505, \\\"longitude\\\": -46.6333}\" || echo \"API test failed\"", "clean": "rm -rf node_modules/.cache && rm -rf dist && rm -rf web/dist"}, "dependencies": {"@aws-sdk/client-location": "^3.848.0", "@expo/metro-runtime": "~5.0.4", "@radix-ui/react-tooltip": "^1.2.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.11.0", "autoprefixer": "^10.4.21", "dotenv": "^17.2.0", "express": "^5.1.0", "i18next": "^25.3.2", "lovable-tagger": "^1.1.8", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-i18next": "^15.6.0", "shared": "file:./shared", "tailwindcss": "^4.1.11", "vite": "^7.0.5"}, "devDependencies": {"concurrently": "^9.2.0", "patch-package": "^8.0.0"}}