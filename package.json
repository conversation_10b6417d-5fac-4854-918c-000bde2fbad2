{"name": "locate-family-connect", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:api": "node server.js", "dev:api:watch": "nodemon server.js", "dev:all": "concurrently --kill-others-on-fail \"npm run dev:api\" \"npm run dev\"", "dev:all:watch": "concurrently --kill-others-on-fail \"npm run dev:api:watch\" \"npm run dev\"", "build": "vite build", "preview": "vite preview", "postinstall": "patch-package", "health-check": "node -e \"console.log('Node.js:', process.version); console.log('Platform:', process.platform);\"", "test:api": "curl -X POST http://localhost:4001/api/resolve-location -H \"Content-Type: application/json\" -d \"{\\\"latitude\\\": -23.5505, \\\"longitude\\\": -46.6333}\" || echo \"API test failed\"", "clean": "rm -rf node_modules/.cache && rm -rf dist"}, "dependencies": {"@aws-sdk/client-location": "^3.848.0", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.59.16", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "express": "^5.1.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.0.0", "lucide-react": "^0.468.0", "mapbox-gl": "^3.8.0", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.0", "react-i18next": "^15.6.0", "react-router-dom": "^7.1.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "vite": "^7.0.5", "zod": "^3.24.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/express": "^5.0.0", "@types/node": "^22.10.2", "@types/node-fetch": "^2.6.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.11.0", "concurrently": "^9.2.0", "nodemon": "^3.1.9", "patch-package": "^8.0.0", "terser": "^5.43.1", "typescript": "^5.7.2"}}