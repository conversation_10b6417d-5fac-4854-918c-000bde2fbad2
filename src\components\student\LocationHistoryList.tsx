
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { LocationData } from '@/types/database';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import LocationRequestButton from './LocationRequestButton';
import { formatDistanceToNow } from 'date-fns';
import { getDateLocale } from '@/utils/dateLocale';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';
import { Badge } from '../ui/badge';
import { 
  MapPin, 
  Clock, 
  AlertCircle, 
  User, 
  MapIcon, 
  Navigation,
  Share2,
  Calendar,
  MousePointer
} from 'lucide-react';
import { locationService } from '@/lib/services/location/LocationService';
import { useTranslation } from 'react-i18next';
import LocationFiltersComponent from './LocationFilters';
import { useLocationFilters } from '@/hooks/useLocationFilters';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { deleteStudentLocationDuplicates } from '@/lib/services/location/LocationService';


interface LocationHistoryListProps {
  locationData: LocationData[];
  loading: boolean;
  error: string | null;
  userType?: string;
  studentDetails?: { id: string; name: string; email: string } | null;
  senderName?: string;
  onLocationSelect?: (location: LocationData) => void;
  selectedLocationId?: string;
  refetch?: () => void;
}

const LocationHistoryList: React.FC<LocationHistoryListProps> = ({ 
  locationData, 
  loading, 
  error, 
  userType, 
  studentDetails,
  senderName,
  onLocationSelect,
  selectedLocationId,
  refetch
}) => {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [enrichedLocations, setEnrichedLocations] = useState<(LocationData & { enrichedAddress?: string })[]>([]);
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  
  // Estado para modal de deleção de duplicatas
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteRadius, setDeleteRadius] = useState(50);
  const [deleteTimeWindow, setDeleteTimeWindow] = useState(10);
  const [deleteAccuracy, setDeleteAccuracy] = useState('all');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteResult, setDeleteResult] = useState<string | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');

  // Hook de filtros
  const {
    filters,
    setFilters,
    filteredLocations,
    totalCount,
    filteredCount
  } = useLocationFilters(enrichedLocations);

  // Enriquecer localizações com endereços quando os dados mudarem
  useEffect(() => {
    const enrichLocationsWithAddresses = async () => {
      if (!locationData || locationData.length === 0) {
        setEnrichedLocations([]);
        return;
      }

      setLoadingAddresses(true);
      console.log('[LocationHistoryList] Enriquecendo localizações com endereços...');

      try {
        const enrichedData = await Promise.all(
          locationData.map(async (location) => {
            // Se já tem endereço, usar ele, senão fazer geocodificação reversa
            if (location.address && location.address.trim()) {
              return {
                ...location,
                enrichedAddress: location.address
              };
            }

            try {
              const enrichedAddress = await locationService.reverseGeocode(
                location.latitude, 
                location.longitude
              );
              return {
                ...location,
                enrichedAddress
              };
            } catch (error) {
              console.warn('[LocationHistoryList] Erro ao enriquecer endereço:', error);
              return {
                ...location,
                enrichedAddress: `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`
              };
            }
          })
        );

        setEnrichedLocations(enrichedData);
        console.log(`[LocationHistoryList] ${enrichedData.length} localizações enriquecidas com endereços`);
      } catch (error) {
        console.error('[LocationHistoryList] Erro ao enriquecer localizações:', error);
        setEnrichedLocations(locationData.map(loc => ({ ...loc, enrichedAddress: 'Endereço não disponível' })));
      } finally {
        setLoadingAddresses(false);
      }
    };

    enrichLocationsWithAddresses();
  }, [locationData]);

  const shareLocationViaEmail = async (locationId: string) => {
    toast({
      title: "Enviando email...",
      description: "Estamos enviando sua localização por email para seus responsáveis",
    });

    try {
      const { error } = await supabase.functions.invoke('share-location', {
        body: { locationId }
      });

      if (error) {
        console.error('Error sharing location:', error);
        toast({
          title: "Erro",
          description: "Não foi possível enviar o email com sua localização",
          variant: "destructive"
        });
        return;
      }

      toast({
        title: "Sucesso",
        description: "Email com sua localização enviado para seus responsáveis",
      });
    } catch (err) {
      console.error('Error invoking function:', err);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao tentar enviar o email",
        variant: "destructive"
      });
    }
  };

  const getTimeAgo = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), {
        addSuffix: true,
        locale: getDateLocale(i18n.language)
      });
    } catch (error) {
      return 'Data inválida';
    }
  };

  const getLocationAccuracy = (location: LocationData) => {
    // Simular precisão baseada em dados disponíveis
    if (location.address && location.address.trim()) {
      return 'Alta';
    }
    return 'Média';
  };

  const handleLocationClick = (location: LocationData) => {
    if (onLocationSelect) {
      onLocationSelect(location);
    }
  };

  // Mostrar loading apenas quando realmente carregando
  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="flex flex-col items-center gap-3">
          <div className="animate-spin h-8 w-8 border-4 border-blue-600 rounded-full border-t-transparent"></div>
          <p className="text-sm text-gray-600">
            {loadingAddresses ? t('locationHistory.loadingAddresses', 'Loading addresses...') : t('locationHistory.loadingHistory', 'Loading history...')}
          </p>
        </div>
      </div>
    );
  }

  // Mostrar erro apenas se houver erro e não estiver carregando
  if (error && !loading) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-4 flex items-center gap-3">
          <AlertCircle className="h-5 w-5 text-red-500" />
          <div>
            <p className="font-medium text-red-700">{t('locationHistory.errorTitle', 'Error loading history')}</p>
            <p className="text-sm text-red-600">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Mostrar estado vazio quando não há dados originais
  if (!loading && enrichedLocations.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="mb-4">
          <MapIcon className="h-12 w-12 mx-auto text-gray-400 mb-3" />
          <p className="text-lg font-medium text-gray-600 mb-2">
            {t('locationHistory.noneFound', 'No location history found')}
          </p>
          <p className="text-sm text-gray-500">
            {studentDetails?.name ? 
              t('locationHistory.noLocationsForStudent', { name: studentDetails.name, defaultValue: '{{name}} has not shared any locations yet' }) : 
              t('locationHistory.noLocations', 'No locations registered yet')
            }
          </p>
        </div>
        
        {userType === 'parent' && studentDetails && (
          <div className="mt-6">
            <p className="text-sm text-gray-600 mb-3">
              {t('locationHistory.requestShare', { name: studentDetails.name, defaultValue: 'Request that {{name}} shares their current location.' })}
            </p>
            {studentDetails.email && (
              <LocationRequestButton 
                guardianEmail={studentDetails.email}
                studentName={studentDetails.name} 
              />
            )}
          </div>
        )}
      </div>
    );
  }

  // Renderizar lista de localizações apenas quando não estiver carregando e houver dados
  return (
    <div className="space-y-4">
      {/* Componente de filtros */}
      {enrichedLocations.length > 0 && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
          <LocationFiltersComponent
            filters={filters}
            onFiltersChange={setFilters}
            totalLocations={totalCount}
            filteredCount={filteredCount}
          />
          {/* Botão de deleção de duplicatas - só para responsáveis */}
          {userType === 'parent' && studentDetails && (
            <Button
              variant="destructive"
              size="sm"
              className="whitespace-nowrap bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
              onClick={() => {
                console.log('[Remover duplicatas] Clique no botão', { studentDetails });
                setDeleteDialogOpen(true);
              }}
              aria-label={t('locationHistory.deleteDuplicates', 'Remove duplicate locations from database')}
            >
              {t('locationHistory.deleteDuplicates', 'Remover duplicatas do banco')}
            </Button>
          )}
        </div>
      )}

      {/* Modal de confirmação/configuração de deleção de duplicatas - Modernizado */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 border border-gray-200/60 dark:border-gray-700/60 shadow-2xl backdrop-blur-md">
          <DialogHeader className="pb-4 border-b border-gray-200/50 dark:border-gray-700/50">
            <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
              {t('locationHistory.deleteDuplicatesTitle', 'Remover duplicatas do banco')}
            </DialogTitle>
            <DialogDescription className="text-muted-foreground mt-2 leading-relaxed">
              {t('locationHistory.deleteDuplicatesDesc', 'Esta ação irá remover permanentemente localizações duplicadas deste estudante, mantendo apenas a mais recente de cada grupo. Esta ação é irreversível.')}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground" htmlFor="confirmText">
                {t('locationHistory.confirmInstruction', 'Para confirmar a exclusão, digite CONFIRMAR no campo abaixo.')}
              </label>
              <Input
                id="confirmText"
                type="text"
                value={deleteConfirmText}
                onChange={e => setDeleteConfirmText(e.target.value)}
                className="refined-input"
                placeholder="Digite CONFIRMAR"
                aria-label={t('locationHistory.confirmInstruction', 'Para confirmar a exclusão, digite CONFIRMAR no campo abaixo.')}
                autoComplete="off"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="radius">
                {t('locationHistory.radius', 'Raio de agrupamento (metros)')}
              </label>
              <Input
                id="radius"
                type="number"
                min={1}
                max={500}
                value={deleteRadius}
                onChange={e => setDeleteRadius(Number(e.target.value))}
                aria-label={t('locationHistory.radius', 'Raio de agrupamento (metros)')}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="timeWindow">
                {t('locationHistory.timeWindow', 'Janela de tempo (segundos)')}
              </label>
              <Input
                id="timeWindow"
                type="number"
                min={1}
                max={3600}
                value={deleteTimeWindow}
                onChange={e => setDeleteTimeWindow(Number(e.target.value))}
                aria-label={t('locationHistory.timeWindow', 'Janela de tempo (segundos)')}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="accuracy">
                {t('locationHistory.accuracyFilter', 'Precisão')}
              </label>
              <Select value={deleteAccuracy} onValueChange={setDeleteAccuracy}>
                <SelectTrigger id="accuracy">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('locationHistory.all', 'Todas')}</SelectItem>
                  <SelectItem value="high">{t('locationHistory.high', 'Alta')}</SelectItem>
                  <SelectItem value="medium">{t('locationHistory.medium', 'Média')}</SelectItem>
                  <SelectItem value="low">{t('locationHistory.low', 'Baixa')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {deleteResult && <div className="text-green-700 text-sm">{deleteResult}</div>}
            {deleteError && <div className="text-red-700 text-sm">{deleteError}</div>}
          </div>
          <DialogFooter className="pt-4 border-t border-gray-200/50 dark:border-gray-700/50 gap-3">
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleteLoading}
              className="refined-input border-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              {t('common.cancel', 'Cancelar')}
            </Button>
            <Button
              variant="destructive"
              disabled={
                deleteLoading ||
                (i18n.language === 'pt-BR'
                  ? deleteConfirmText.trim().toUpperCase() !== 'CONFIRMAR'
                  : deleteConfirmText.trim().toUpperCase() !== 'CONFIRM')
              }
              className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
              onClick={async () => {
                console.log('[Remover duplicatas] Confirmação final', { studentDetails, deleteRadius, deleteTimeWindow, deleteAccuracy });
                setDeleteLoading(true);
                setDeleteResult(null);
                setDeleteError(null);
                try {
                  if (!studentDetails?.id) throw new Error('Estudante não selecionado');
                  const removed = await deleteStudentLocationDuplicates(
                    studentDetails.id,
                    deleteRadius,
                    deleteTimeWindow,
                    deleteAccuracy || undefined
                  );
                  setDeleteResult(
                    t(
                      'locationHistory.deleteSuccess',
                      `Duplicatas removidas com sucesso. ${removed} registro(s) excluído(s).`,
                      { count: removed }
                    )
                  );
                  setDeleteError(null);
                  // Sempre recarrega a lista após deleção
                  if (typeof refetch === 'function') refetch();
                  setTimeout(() => {
                    setDeleteDialogOpen(false);
                    setDeleteConfirmText('');
                  }, 1200);
                } catch (err: any) {
                  setDeleteError(
                    t('locationHistory.deleteError', 'Erro ao remover duplicatas: ') + (err?.message || err)
                  );
                  setDeleteResult(null);
                } finally {
                  setDeleteLoading(false);
                }
              }}
              aria-label={t('locationHistory.confirmDelete', 'Confirmar remoção de duplicatas')}
            >
              {deleteLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              )}
              {t('locationHistory.deleteButton', 'Remover duplicatas')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Navigation className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold text-lg">{t('locationHistory.title', 'Location History')}</h3>
        </div>
        <Badge variant="outline" className="text-sm" data-testid="location-count">
          {filteredCount} de {totalCount} {totalCount === 1 ? t('locationHistory.oneLocation', 'location') : t('locationHistory.manyLocations', 'locations')}
        </Badge>
      </div>

      {onLocationSelect && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 text-blue-700">
            <MousePointer className="h-4 w-4" />
            <span className="text-sm font-medium">
              {t('locationHistory.clickToView', 'Click any location to view it on the map')}
            </span>
          </div>
        </div>
      )}

      {/* Mostrar mensagem quando não há localizações após filtragem */}
      {filteredCount === 0 && totalCount > 0 && (
        <div className="text-center py-8">
          <div className="mb-4">
            <MapIcon className="h-8 w-8 mx-auto text-gray-400 mb-3" />
            <p className="text-base font-medium text-gray-600 mb-2">
              Nenhuma localização encontrada com os filtros aplicados
            </p>
            <p className="text-sm text-gray-500">
              Tente ajustar os filtros para ver mais resultados
            </p>
          </div>
        </div>
      )}

      <div className="space-y-3">
        {filteredLocations.map((location, index) => {
          const isRecent = index === 0;
          const isSelected = selectedLocationId === location.id;
          const timeAgo = getTimeAgo(location.timestamp);
          const accuracy = getLocationAccuracy(location);
          const studentName = location.student_name || location.user?.full_name || t('locationHistory.student', 'Student');

          return (
            <Card 
              key={location.id} 
              className={`transition-all duration-200 ${
                onLocationSelect ? 'cursor-pointer hover:shadow-md hover:border-blue-300' : ''
              } ${
                isRecent ? 'ring-2 ring-blue-200 bg-blue-50' : ''
              } ${
                isSelected ? 'ring-2 ring-green-400 bg-green-50 shadow-lg' : ''
              }`}
              onClick={() => handleLocationClick(location)}
            >
              <CardContent className="p-4">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div className="flex-1 space-y-3">
                    {/* Header com nome e badges */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-blue-600" />
                        <span className="font-semibold text-gray-900">{studentName}</span>
                        {isRecent && (
                          <Badge className="bg-green-100 text-green-700 border-green-300">
                            {t('locationHistory.mostRecent', 'Most recent')}
                          </Badge>
                        )}
                        {isSelected && (
                          <Badge className="bg-green-500 text-white">
                            {t('locationHistory.selected', 'Selected')}
                          </Badge>
                        )}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {t('locationHistory.accuracy', 'Accuracy')}: {accuracy}
                      </Badge>
                    </div>

                    {/* Endereço */}
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-700">
                          {loadingAddresses ? t('locationHistory.loadingAddress', 'Loading address...') : (location.enrichedAddress || t('locationHistory.addressUnavailable', 'Address not available'))}
                        </p>
                        <p className="text-xs text-gray-500 font-mono">
                          {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
                        </p>
                      </div>
                    </div>

                    {/* Data e hora */}
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{timeAgo}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(location.timestamp).toLocaleString('pt-BR')}</span>
                      </div>
                    </div>

                    {/* Status de compartilhamento */}
                    {location.shared_with_guardians && (
                      <div className="flex items-center gap-1 text-xs text-blue-600">
                        <Share2 className="h-3 w-3" />
                        <span>{t('locationHistory.sharedWithGuardians', 'Shared with guardians')}</span>
                      </div>
                    )}

                    {/* Indicador visual para cards clicáveis */}
                    {onLocationSelect && (
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <MousePointer className="h-3 w-3" />
                        <span>{t('locationHistory.clickToSeeOnMap', 'Click to view on map')}</span>
                      </div>
                    )}
                  </div>

                  {/* Ações */}
                  {userType === 'student' && (
                    <div className="flex-shrink-0">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent card click when clicking button
                          shareLocationViaEmail(location.id);
                        }}
                        className="flex items-center gap-2"
                      >
                        <Share2 className="h-4 w-4" />
                        {t('locationHistory.sendByEmail', 'Send by Email')}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Informações adicionais para responsáveis */}
      {userType === 'parent' && studentDetails && enrichedLocations.length > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">{t('locationHistory.importantInfo', 'Important Information')}</span>
            </div>
            <p className="text-sm text-blue-700">
              {t('locationHistory.automaticSharing', { name: studentDetails.name, defaultValue: 'Locations are shared automatically when {{name}} allows. Address information is obtained automatically based on GPS coordinates.' })}
            </p>
            {onLocationSelect && (
              <p className="text-sm text-blue-700 mt-2">
                {t('locationHistory.clickToHighlight', 'Click any location in the history to highlight it on the map.')}
              </p>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LocationHistoryList;

