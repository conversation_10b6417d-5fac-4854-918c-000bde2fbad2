
import { supabase } from '@/integrations/supabase/client';

export const apiService = {
  async getStudents() {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_type', 'student');

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching students:', error);
      throw error;
    }
  },
  
  async getGuardians() {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_type', 'parent');

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching guardians:', error);
      throw error;
    }
  },
  
  async getProfile(userId: string) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      
      // Ensure all required fields are present
      return {
        id: data.id,
        user_id: data.user_id,
        full_name: data.full_name,
        email: data.email,
        phone: data.phone,
        user_type: data.user_type,
        created_at: data.created_at,
        updated_at: data.updated_at,
        cpf: data.cpf // Required field
      };
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  },

  async updateProfile(userId: string, updates: any) {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString(),
        cpf: updates.cpf || '000.000.000-00' // Ensure cpf is always present
      };

      const { data, error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      
      return {
        id: data.id,
        user_id: data.user_id,
        full_name: data.full_name,
        email: data.email,
        phone: data.phone,
        user_type: data.user_type,
        created_at: data.created_at,
        updated_at: data.updated_at,
        cpf: data.cpf
      };
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  },

  async getLocationHistory(userId: string) {
    try {
      const { data, error } = await supabase
        .from('location_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching location history:', error);
      throw error;
    }
  },

  async createLocation(locationData: any) {
    try {
      const { data, error } = await supabase
        .from('locations')
        .insert([locationData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating location:', error);
      throw error;
    }
  },

  async updateLocation(locationId: string, updates: any) {
    try {
      const { data, error } = await supabase
        .from('locations')
        .update(updates)
        .eq('id', locationId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating location:', error);
      throw error;
    }
  },

  async deleteLocation(locationId: string) {
    try {
      const { data, error } = await supabase
        .from('locations')
        .delete()
        .eq('id', locationId);

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error deleting location:', error);
      throw error;
    }
  },

  async shareLocation(email: string, latitude: number, longitude: number, senderName: string) {
    try {
      console.log('[apiService] Compartilhando localização via Edge Function:', { 
        email, 
        latitude, 
        longitude, 
        senderName 
      });

      const { data, error } = await supabase.functions.invoke('share-location', {
        body: {
          email,
          latitude,
          longitude,
          senderName,
          isRequest: false
        }
      });

      if (error) {
        console.error('[apiService] Erro da Edge Function:', error);
        throw error;
      }

      console.log('[apiService] Sucesso ao compartilhar localização:', data);
      return data;
    } catch (error) {
      console.error('[apiService] Error sharing location:', error);
      throw error; // Re-throw to let caller handle the error
    }
  },

  async getStudentDetails(studentId: string) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', studentId)
        .single();

      if (error) throw error;
      
      return {
        success: true,
        data: data
      };
    } catch (error) {
      console.error('Error fetching student details:', error);
      return {
        success: false,
        data: null
      };
    }
  }
};

