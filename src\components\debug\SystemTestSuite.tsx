
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { CheckCircle, XCircle, Clock, AlertTriangle, RefreshCw } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message: string;
  details?: any;
  duration?: number;
}

interface TestSuite {
  [key: string]: TestResult;
}

const SystemTestSuite: React.FC = () => {
  const [tests, setTests] = useState<TestSuite>({});
  const [isRunning, setIsRunning] = useState(false);
  const [summary, setSummary] = useState({ total: 0, passed: 0, failed: 0, pending: 0 });
  const { toast } = useToast();

  const updateTest = (testName: string, update: Partial<TestResult>) => {
    setTests(prev => ({
      ...prev,
      [testName]: { ...prev[testName], ...update }
    }));
  };

  const initializeTests = () => {
    const testSuite: TestSuite = {
      'database_connection': {
        name: 'Conexão com Banco de Dados',
        status: 'pending',
        message: 'Aguardando execução'
      },
      'profiles_integrity': {
        name: 'Integridade da Tabela Profiles',
        status: 'pending',
        message: 'Aguardando execução'
      },
      'guardian_relationships': {
        name: 'Relacionamentos Guardian-Student',
        status: 'pending',
        message: 'Aguardando execução'
      },
      'cpf_conflicts': {
        name: 'Conflitos de CPF',
        status: 'pending',
        message: 'Aguardando execução'
      },
      'user_authentication': {
        name: 'Autenticação de Usuários',
        status: 'pending',
        message: 'Aguardando execução'
      },
      'student_locations': {
        name: 'Sistema de Localizações',
        status: 'pending',
        message: 'Aguardando execução'
      },
      'family_invitations': {
        name: 'Sistema de Convites Familiares',
        status: 'pending',
        message: 'Aguardando execução'
      },
      'rpc_functions': {
        name: 'Funções RPC do Banco',
        status: 'pending',
        message: 'Aguardando execução'
      }
    };
    
    setTests(testSuite);
  };

  // Test 1: Database Connection
  const testDatabaseConnection = async () => {
    const startTime = Date.now();
    updateTest('database_connection', { status: 'running', message: 'Testando conexão...' });
    
    try {
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      if (error) throw error;
      
      updateTest('database_connection', {
        status: 'success',
        message: 'Conexão estabelecida com sucesso',
        duration: Date.now() - startTime
      });
    } catch (error: any) {
      updateTest('database_connection', {
        status: 'error',
        message: `Erro de conexão: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  };

  // Test 2: Profiles Integrity
  const testProfilesIntegrity = async () => {
    const startTime = Date.now();
    updateTest('profiles_integrity', { status: 'running', message: 'Verificando integridade...' });
    
    try {
      // Check for duplicate CPFs
      const { data: cpfDuplicates, error: cpfError } = await supabase
        .rpc('debug_cpf_conflicts');
      
      if (cpfError) throw cpfError;
      
      // Count total profiles
      const { count: totalProfiles, error: countError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });
      
      if (countError) throw countError;
      
      const conflicts = cpfDuplicates?.filter((c: any) => c.conflict_type !== 'match').length || 0;
      
      updateTest('profiles_integrity', {
        status: conflicts === 0 ? 'success' : 'error',
        message: conflicts === 0 
          ? `${totalProfiles} perfis, sem conflitos de CPF` 
          : `${conflicts} conflitos de CPF encontrados`,
        details: { totalProfiles, conflicts, duplicates: cpfDuplicates },
        duration: Date.now() - startTime
      });
    } catch (error: any) {
      updateTest('profiles_integrity', {
        status: 'error',
        message: `Erro na verificação: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  };

  // Test 3: Guardian Relationships
  const testGuardianRelationships = async () => {
    const startTime = Date.now();
    updateTest('guardian_relationships', { status: 'running', message: 'Verificando relacionamentos...' });
    
    try {
      // Count relationships
      const { count: totalRelationships, error: countError } = await supabase
        .from('student_guardian_relationships')
        .select('*', { count: 'exact', head: true });
      
      if (countError) throw countError;
      
      // Check for orphaned relationships
      const { data: orphanedCheck, error: orphanedError } = await supabase
        .rpc('verify_migration_integrity');
      
      if (orphanedError) throw orphanedError;
      
      const orphanedCount = orphanedCheck?.find((r: any) => r.check_name === 'orphaned_relationships')?.count_value || 0;
      
      updateTest('guardian_relationships', {
        status: orphanedCount === 0 ? 'success' : 'error',
        message: orphanedCount === 0 
          ? `${totalRelationships} relacionamentos válidos` 
          : `${orphanedCount} relacionamentos órfãos encontrados`,
        details: { totalRelationships, orphanedCount },
        duration: Date.now() - startTime
      });
    } catch (error: any) {
      updateTest('guardian_relationships', {
        status: 'error',
        message: `Erro na verificação: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  };

  // Test 4: CPF Conflicts
  const testCPFConflicts = async () => {
    const startTime = Date.now();
    updateTest('cpf_conflicts', { status: 'running', message: 'Verificando conflitos de CPF...' });
    
    try {
      const { data: conflicts, error } = await supabase
        .from('profiles')
        .select('cpf')
        .not('cpf', 'is', null)
        .neq('cpf', '');
      
      if (error) throw error;
      
      const cpfMap = new Map();
      let duplicateCount = 0;
      
      conflicts?.forEach((profile: any) => {
        if (cpfMap.has(profile.cpf)) {
          duplicateCount++;
        } else {
          cpfMap.set(profile.cpf, true);
        }
      });
      
      updateTest('cpf_conflicts', {
        status: duplicateCount === 0 ? 'success' : 'error',
        message: duplicateCount === 0 
          ? 'Nenhum conflito de CPF encontrado' 
          : `${duplicateCount} CPFs duplicados encontrados`,
        details: { totalCPFs: conflicts?.length, duplicates: duplicateCount },
        duration: Date.now() - startTime
      });
    } catch (error: any) {
      updateTest('cpf_conflicts', {
        status: 'error',
        message: `Erro na verificação: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  };

  // Test 5: User Authentication
  const testUserAuthentication = async () => {
    const startTime = Date.now();
    updateTest('user_authentication', { status: 'running', message: 'Verificando autenticação...' });
    
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) throw error;
      
      const isAuthenticated = !!user;
      const userType = user?.user_metadata?.user_type;
      
      updateTest('user_authentication', {
        status: 'success',
        message: isAuthenticated 
          ? `Usuário autenticado como ${userType}` 
          : 'Nenhum usuário autenticado',
        details: { isAuthenticated, userType, userId: user?.id },
        duration: Date.now() - startTime
      });
    } catch (error: any) {
      updateTest('user_authentication', {
        status: 'error',
        message: `Erro de autenticação: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  };

  // Test 6: Student Locations
  const testStudentLocations = async () => {
    const startTime = Date.now();
    updateTest('student_locations', { status: 'running', message: 'Verificando sistema de localizações...' });
    
    try {
      const { count: locationCount, error } = await supabase
        .from('locations')
        .select('*', { count: 'exact', head: true });
      
      if (error) throw error;
      
      updateTest('student_locations', {
        status: 'success',
        message: `${locationCount} localizações registradas no sistema`,
        details: { locationCount },
        duration: Date.now() - startTime
      });
    } catch (error: any) {
      updateTest('student_locations', {
        status: 'error',
        message: `Erro no sistema de localizações: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  };

  // Test 7: Family Invitations
  const testFamilyInvitations = async () => {
    const startTime = Date.now();
    updateTest('family_invitations', { status: 'running', message: 'Verificando convites familiares...' });
    
    try {
      const { count: invitationCount, error } = await supabase
        .from('family_invitations')
        .select('*', { count: 'exact', head: true });
      
      if (error) throw error;
      
      updateTest('family_invitations', {
        status: 'success',
        message: `${invitationCount} convites familiares no sistema`,
        details: { invitationCount },
        duration: Date.now() - startTime
      });
    } catch (error: any) {
      updateTest('family_invitations', {
        status: 'error',
        message: `Erro no sistema de convites: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  };

  // Test 8: RPC Functions
  const testRPCFunctions = async () => {
    const startTime = Date.now();
    updateTest('rpc_functions', { status: 'running', message: 'Testando funções RPC...' });
    
    try {
      // Test the main RPC function
      const { data, error } = await supabase.rpc('verify_migration_integrity');
      
      if (error) throw error;
      
      updateTest('rpc_functions', {
        status: 'success',
        message: 'Todas as funções RPC funcionando corretamente',
        details: { rpcResults: data },
        duration: Date.now() - startTime
      });
    } catch (error: any) {
      updateTest('rpc_functions', {
        status: 'error',
        message: `Erro nas funções RPC: ${error.message}`,
        duration: Date.now() - startTime
      });
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    initializeTests();
    
    try {
      await testDatabaseConnection();
      await testProfilesIntegrity();
      await testGuardianRelationships();
      await testCPFConflicts();
      await testUserAuthentication();
      await testStudentLocations();
      await testFamilyInvitations();
      await testRPCFunctions();
      
      toast({
        title: "Teste Concluído",
        description: "Todas as verificações foram executadas"
      });
    } catch (error) {
      toast({
        title: "Erro no Teste",
        description: "Houve um erro durante a execução dos testes",
        variant: "destructive"
      });
    } finally {
      setIsRunning(false);
    }
  };

  // Calculate summary
  useEffect(() => {
    const testArray = Object.values(tests);
    const total = testArray.length;
    const passed = testArray.filter(t => t.status === 'success').length;
    const failed = testArray.filter(t => t.status === 'error').length;
    const pending = testArray.filter(t => t.status === 'pending').length;
    
    setSummary({ total, passed, failed, pending });
  }, [tests]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      success: 'bg-green-100 text-green-800',
      error: 'bg-red-100 text-red-800',
      running: 'bg-blue-100 text-blue-800',
      pending: 'bg-gray-100 text-gray-800'
    };
    
    return (
      <Badge className={colors[status as keyof typeof colors]}>
        {status.toUpperCase()}
      </Badge>
    );
  };

  useEffect(() => {
    initializeTests();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Sistema de Testes Pós-Migração
            <Button 
              onClick={runAllTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              {isRunning ? 'Executando...' : 'Executar Todos os Testes'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold">{summary.total}</div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{summary.passed}</div>
              <div className="text-sm text-gray-600">Aprovados</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{summary.failed}</div>
              <div className="text-sm text-gray-600">Falharam</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-400">{summary.pending}</div>
              <div className="text-sm text-gray-600">Pendentes</div>
            </div>
          </div>
          
          <div className="space-y-3">
            {Object.entries(tests).map(([key, test]) => (
              <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <div>
                    <div className="font-medium">{test.name}</div>
                    <div className="text-sm text-gray-600">{test.message}</div>
                    {test.duration && (
                      <div className="text-xs text-gray-400">
                        Executado em {test.duration}ms
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(test.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Detailed Results */}
      {Object.values(tests).some(t => t.details) && (
        <Card>
          <CardHeader>
            <CardTitle>Detalhes dos Testes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(tests)
                .filter(([_, test]) => test.details)
                .map(([key, test]) => (
                  <div key={key} className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-medium">{test.name}</h4>
                    <pre className="text-sm bg-gray-100 p-2 rounded mt-2 overflow-auto">
                      {JSON.stringify(test.details, null, 2)}
                    </pre>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SystemTestSuite;

