import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';

export interface StudentInvitation {
  id: string;
  student_name: string;
  student_email: string;
  guardian_email: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  created_at: string;
  expires_at: string;
  accepted_at?: string;
  student_id?: string;
  guardian_id?: string;
}

export const useStudentInvitations = () => {
  const [invitations, setInvitations] = useState<StudentInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();

  const fetchInvitations = async () => {
    if (!user?.email) {
      console.log('[STUDENT_INVITATIONS] No user email available');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log('[STUDENT_INVITATIONS] Fetching invitations for guardian:', user.email);

      // Force fresh data by adding timestamp to query
      const timestamp = Date.now();
      console.log('[STUDENT_INVITATIONS] Cache-busting timestamp:', timestamp);

      // Clear any potential Supabase cache
      await supabase.auth.getSession(); // Refresh session

      const { data, error: queryError } = await supabase
        .from('family_invitations')
        .select(`
          id,
          student_name,
          student_email,
          guardian_email,
          status,
          created_at,
          expires_at,
          accepted_at,
          student_id,
          guardian_id
        `)
        .eq('guardian_email', user.email)
        .order('created_at', { ascending: false });

      if (queryError) {
        console.error('[STUDENT_INVITATIONS] Error fetching invitations:', queryError);
        
        // If it's a permission error (403/RLS blocking), treat as empty data
        if (queryError.code === 'PGRST301' || queryError.message?.includes('permission')) {
          console.log('[STUDENT_INVITATIONS] No invitations found for user (RLS permission)', user.email);
          setInvitations([]);
          return;
        }
        
        setError(queryError.message);
        return;
      }

      console.log('[STUDENT_INVITATIONS] Raw data received:', JSON.stringify(data, null, 2));
      console.log('[STUDENT_INVITATIONS] Found invitations:', data?.length || 0);
      
      const invitationsData = (data || []) as StudentInvitation[];
      
      // Log detailed status breakdown
      const statusCounts = invitationsData.reduce((acc, inv) => {
        acc[inv.status] = (acc[inv.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      console.log('[STUDENT_INVITATIONS] Status breakdown:', statusCounts);
      
      // Log each invitation individually
      invitationsData.forEach((inv, index) => {
        console.log(`[STUDENT_INVITATIONS] Invitation ${index + 1}:`, {
          id: inv.id,
          student_name: inv.student_name,
          status: inv.status,
          created_at: inv.created_at,
          accepted_at: inv.accepted_at,
          expires_at: inv.expires_at
        });
      });

      setInvitations(invitationsData);

    } catch (err: any) {
      console.error('[STUDENT_INVITATIONS] Unexpected error:', err);
      
      // Handle 403 Forbidden errors as empty state (user has no invitations)
      if (err?.status === 403 || err?.message?.includes('403') || err?.message?.includes('permission')) {
        console.log('[STUDENT_INVITATIONS] No invitations found for user (403 handled as empty)', user.email);
        setInvitations([]);
        setError(null);
      } else {
        setError('Erro ao carregar solicitações');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInvitations();
  }, [user?.email]);

  return {
    invitations,
    isLoading,
    error,
    refetch: fetchInvitations
  };
}; 