-- Migration: Fix activate_student_account RPC to update password in auth.users
-- Timestamp: **************

CREATE OR REPLACE FUNCTION public.activate_student_account(
  p_activation_token TEXT,
  p_new_password TEXT -- This parameter needs to be used
)
RETURNS TABLE(success BOOLEAN, message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_profile_record RECORD;
  -- Use this if calling auth.admin_update_user, not direct UPDATE on auth.users
  -- v_auth_update_result JSONB;
BEGIN
  -- <PERSON>car perfil pelo token de ativação
  SELECT * INTO v_profile_record
  FROM public.profiles
  WHERE activation_token = p_activation_token
  AND user_type = 'student' -- Ensure it's a student profile
  AND activation_expires_at > NOW() -- Check for expiration
  AND activated_at IS NULL; -- Check if already activated

  IF v_profile_record IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Token de ativação inválido, expirado ou a conta já foi ativada.'::TEXT;
    RETURN;
  END IF;

  -- Input validation for password (ensure it meets basic criteria if any)
  IF p_new_password IS NULL OR char_length(p_new_password) < 8 THEN
    -- Consider more specific password policies if they exist (e.g., regex for complexity)
    RETURN QUERY SELECT FALSE, 'Nova senha inválida. Deve ter pelo menos 8 caracteres.'::TEXT;
    RETURN;
  END IF;

  -- Atualizar senha no auth.users
  -- As a SECURITY DEFINER function (typically owned by 'postgres' or 'supabase_admin'),
  -- it has the necessary privileges to update auth.users directly.
  -- Supabase automatically handles hashing when the 'password' field is updated.
  UPDATE auth.users
  SET
    password = p_new_password,
    -- Optionally, update other fields like `password_last_updated_at` if Supabase doesn't do it automatically
    -- Or reset recovery tokens if any:
    -- recovery_token = NULL,
    -- recovery_sent_at = NULL,
    email_confirmed_at = COALESCE(email_confirmed_at, NOW()) -- Ensure email is marked confirmed if not already
  WHERE id = v_profile_record.user_id;

  -- Marcar conta como ativada no profiles
  UPDATE public.profiles
  SET
    activated_at = NOW(),
    requires_password_change = FALSE,
    activation_token = NULL, -- Clear the token as it's now used
    status = 'active', -- Explicitly set status to active
    registration_status = 'completed', -- Update overall registration status
    updated_at = NOW() -- Update timestamp for the profile
  WHERE user_id = v_profile_record.user_id;

  -- Log successful activation
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_account_activated', v_profile_record.user_id,
          jsonb_build_object('email', v_profile_record.email, 'activation_method', 'token_link')
         );

  RETURN QUERY SELECT TRUE, 'Conta ativada e senha atualizada com sucesso!'::TEXT;

EXCEPTION WHEN OTHERS THEN
    -- Log the error for easier debugging
    RAISE WARNING '[ACTIVATE_STUDENT_ACCOUNT_RPC] Error for token % (User ID %): %',
                  p_activation_token, v_profile_record.user_id, SQLERRM;
    RETURN QUERY SELECT FALSE, 'Erro interno ao processar a ativação da conta. Por favor, contate o suporte.'::TEXT;
END;
$$;

-- Grant permissions if not already covered by existing grants for this function signature
GRANT EXECUTE ON FUNCTION public.activate_student_account(TEXT, TEXT) TO authenticated;
-- 'authenticated' because it's called by the logged-out student via the ActivateAccount.tsx page,
-- but the function itself runs with definer's privileges.

COMMENT ON FUNCTION public.activate_student_account(TEXT, TEXT) IS
'Activates a student account using a token, updates their password directly in auth.users,
and updates their profile status. Called from the account activation page.';
