# Problema de Feedback Visual no Compartilhamento de Localização em Dispositivos Móveis

**Data:** 2025-05-05  
**Autor:** Equipe de Desenvolvimento  
**Status:** Em resolução  

## Descrição do Problema

Quando um estudante utiliza a função "Enviar Localização" através de um dispositivo móvel, após o clique no botão, o sistema apresenta uma tela em branco sem feedback visual. Embora a localização seja corretamente compartilhada e o email seja enviado com sucesso (como verificado nos logs do sistema), a experiência do usuário é prejudicada pela falta de feedback visual e pela interrupção do fluxo normal de navegação.

## Detalhes Técnicos

- **Componentes afetados:** 
  - `StudentDashboard.tsx`
  - `GuardianManager.tsx`
  - `api-service.ts`
  
- **Ambiente:** 
  - Dispositivos móveis (testado em iOS e Android)
  - Navegadores móveis (Chrome, Safari)

- **Comportamento observado:**
  1. Usuário faz login como estudante
  2. Navega até a dashboard
  3. Clica no botão "Enviar Localização"
  4. Compartilhamento ocorre com sucesso (confirmado pelos logs)
  5. **PROBLEMA:** Interface renderiza uma tela em branco em vez de mostrar confirmação e manter a navegação

- **Logs de console:**
  ```
  [API] Compartilhando localizaçã<NAME_EMAIL> de Sarah Rackel Ferreira Lima : lat=52.4746752, long=-0.966656
  [API] Enviando payload para Edge Function: Object
  [API] Compartilhamento bem-sucedido: Object
  ```

## Análise Preliminar

O problema parece estar relacionado à navegação pós-compartilhamento em dispositivos móveis. Os toasts implementados funcionam bem em desktop, mas em dispositivos móveis, uma combinação de fatores (possivelmente relacionados ao ciclo de vida da página e ao gerenciamento de eventos) causa o problema de tela em branco.

## Solução Proposta

Implementar uma abordagem de feedback visual que funcione universalmente, adotando as seguintes medidas:

1. **Bloqueio parcial da UI durante o compartilhamento:**
   - Usar um overlay modal (não navegacional) que permanece visível apenas durante o processamento
   - Garantir que este modal seja compatível com diferentes tamanhos de tela

2. **Feedback visual imediato pós-compartilhamento:**
   - Implementar uma notificação/alerta do tipo "Alert" (mais robusto que toast em mobile)
   - Garantir que o alerta seja apresentado ANTES de qualquer possível redirecionamento

3. **Modificação do fluxo de navegação:**
   - Remover qualquer redirecionamento automático após o compartilhamento
   - Incluir botão explícito "Voltar" ou "Continuar" após o compartilhamento

4. **Detecção e adaptação a dispositivos móveis:**
   - Implementar uma função `isMobileDevice()` para detectar dispositivos móveis
   - Aplicar comportamentos específicos baseados no tipo de dispositivo

## Implementação Técnica

### 1. Componente de Alerta Modal Universal

```tsx
// src/components/SharedLocationAlert.tsx
import React from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

interface SharedLocationAlertProps {
  isOpen: boolean;
  onClose: () => void;
  success: boolean;
  message: string;
  details?: string;
}

export const SharedLocationAlert: React.FC<SharedLocationAlertProps> = ({
  isOpen,
  onClose,
  success,
  message,
  details
}) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-[90vw] md:max-w-[500px]">
        <AlertDialogHeader>
          <AlertDialogTitle>
            {success ? '✅ Localização Compartilhada' : '❌ Erro no Compartilhamento'}
          </AlertDialogTitle>
          <AlertDialogDescription>{message}</AlertDialogDescription>
          {details && (
            <p className="text-sm text-muted-foreground mt-2">{details}</p>
          )}
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={onClose}>
            Continuar
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
```

### 2. Função para Detectar Dispositivos Móveis

```typescript
// src/lib/utils/device-detection.ts
export function isMobileDevice(): boolean {
  const userAgent = 
    typeof window !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
  
  return (
    /android|webos|iphone|ipad|ipod|blackberry|windows phone/i.test(userAgent)
  );
}
```

### 3. Modificações no StudentDashboard.tsx

```typescript
// Modificar a função shareLocationToGuardian para usar o novo componente de alerta
const shareLocationToGuardian = async (guardian: GuardianData, latitude?: number, longitude?: number): Promise<void> => {
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertSuccess, setAlertSuccess] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertDetails, setAlertDetails] = useState('');
  const isMobile = isMobileDevice();

  setSharingStatus(prev => ({ ...prev, [guardian.id]: 'loading' }));
  
  try {
    // ... código existente ...
    
    if (result.success) {
      setSharingStatus(prev => ({ ...prev, [guardian.id]: 'success' }));
      
      // Usar alerta em vez de toast para dispositivos móveis
      if (isMobile) {
        setAlertSuccess(true);
        setAlertMessage(`Localização enviada com sucesso para ${guardian.full_name || guardian.email}`);
        setAlertOpen(true);
      } else {
        toast({
          title: "Localização compartilhada",
          description: `Localização enviada com sucesso para ${guardian.full_name || guardian.email}`,
          variant: "default",
          duration: 3000,
        });
      }
    } else {
      throw new Error(result.message || 'Falha ao compartilhar localização');
    }
  } catch (error: any) {
    // ... código existente ...
    
    // Usar alerta em vez de toast para dispositivos móveis
    if (isMobile) {
      setAlertSuccess(false);
      setAlertMessage(error?.message || "Não foi possível compartilhar sua localização");
      setAlertDetails("Verifique sua conexão e tente novamente.");
      setAlertOpen(true);
    } else {
      toast({
        title: "Erro ao compartilhar",
        description: error?.message || "Não foi possível compartilhar sua localização",
        variant: "destructive",
        duration: 4000,
      });
    }
  }
};

// No componente de renderização, adicionar:
<SharedLocationAlert 
  isOpen={alertOpen}
  onClose={() => setAlertOpen(false)}
  success={alertSuccess}
  message={alertMessage}
  details={alertDetails}
/>
```

## Plano de Implementação

1. Criar componente `SharedLocationAlert.tsx`
2. Implementar função de detecção de dispositivos móveis
3. Modificar `StudentDashboard.tsx` e `GuardianManager.tsx` para usar a nova abordagem
4. Modificar `api-service.ts` para garantir que o retorno da função seja compatível
5. Testar em múltiplos dispositivos móveis e navegadores
6. Validar a experiência do usuário nos diferentes fluxos

## Métricas de Validação

- Verificar redução em taxas de abandono no fluxo de compartilhamento
- Monitorar tempo médio para completar o fluxo de compartilhamento
- Avaliar feedback dos usuários depois da implementação

## Referências

- [Documentação Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Melhores práticas para UX em dispositivos móveis](https://material.io/design/usability/accessibility.html)
- [React Context API para gerenciamento de estado](https://reactjs.org/docs/context.html)
