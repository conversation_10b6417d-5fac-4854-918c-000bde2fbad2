import React from 'react';

interface LastLoginInfoProps {
  lastLoginDate?: string;
  showWarning?: boolean;
}

export const LastLoginInfo: React.FC<LastLoginInfoProps> = ({ lastLoginDate, showWarning = true }) => {
  if (!lastLoginDate) {
    return <span className="text-gray-400">Nunca acessou</span>;
  }

  const loginDate = new Date(lastLoginDate);
  const daysSinceLogin = Math.floor((Date.now() - loginDate.getTime()) / (1000 * 60 * 60 * 24));

  return (
    <div>
      <span>Último acesso: {loginDate.toLocaleDateString('pt-BR')}</span>
      {showWarning && daysSinceLogin > 30 && (
        <span className="ml-2 text-red-500 text-xs">Inativo há {daysSinceLogin} dias</span>
      )}
    </div>
  );
};

export default LastLoginInfo;
