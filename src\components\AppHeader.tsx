
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { User, ArrowLeft, Home } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useUser } from "@/contexts/UnifiedAuthContext";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import LogoutButton from "./LogoutButton";
import { useResponsiveLayout } from "@/hooks/useResponsiveLayout";
import { cn } from "@/lib/utils";
import { LanguageSwitcher } from "./LanguageSwitcher";
import { useTranslation } from "react-i18next";

export const AppHeader = () => {
  const { user } = useUser();
  const { t } = useTranslation();
  const location = useLocation();
  const { device, textSizes, buttonStyles, getNavigationHeight } = useResponsiveLayout();
  
  if (!user) return null;
  
  const userType = user?.user_metadata?.user_type || 'student';
  const getDashboardLink = () => {
    switch(userType) {
      case 'parent':
        return '/parent-dashboard';
      case 'student':
        return '/student-dashboard';
      case 'developer':
        return '/dev-dashboard';
      case 'admin':
        return '/admin-dashboard';
      default:
        return '/dashboard';
    }
  };

  const getHelpLink = () => {
    switch(userType) {
      case 'parent':
        return '/ajuda/responsavel';
      case 'student':
        return '/ajuda/estudante';
      default:
        return '/ajuda/estudante';
    }
  };
  
  const dashboardLink = getDashboardLink();
  const isOnDashboard = location.pathname === dashboardLink;
  const isOnProfile = location.pathname === '/profile';
  
  // Enhanced dropdown width and positioning
  const getDropdownStyles = () => {
    const baseWidth = device.size === 'xxs' || device.size === 'xs' ? 'w-48' : 'w-56';
    const sideOffset = device.size === 'xxs' || device.size === 'xs' ? 4 : 8;
    
    return { width: baseWidth, sideOffset };
  };
  
  // Container padding with safe area support
  const getContainerPadding = () => {
    const basePadding = device.size === 'xxs' ? 'px-2' : device.size === 'xs' ? 'px-3' : 'px-4';
    const safeArea = device.hasNotch ? 'pt-safe-area-top' : '';
    
    return cn(basePadding, safeArea);
  };
  
  // Logo text sizing and truncation
  const getLogoStyles = () => {
    const baseSize = textSizes.subtitle;
    const maxWidth = device.size === 'xxs' ? 'max-w-[120px]' : 
                    device.size === 'xs' ? 'max-w-[160px]' : 
                    'max-w-[200px] sm:max-w-full';
    
    return cn(baseSize, 'font-bold text-primary truncate', maxWidth);
  };
  
  // User button styles
  const getUserButtonStyles = () => {
    const size = device.size === 'xxs' ? 'w-7 h-7' : 
                device.size === 'xs' ? 'w-8 h-8' : 
                'w-9 h-9';
    
    return cn('rounded-full', size);
  };
  
  // Menu item sizing
  const getMenuItemStyles = () => {
    return device.size === 'xxs' || (device.size === 'xs' && device.orientation === 'landscape') 
      ? 'text-xs py-1.5' 
      : '';
  };
  
  // User display name with proper truncation
  const getUserDisplayName = () => {
    const fullName = user?.user_metadata?.full_name;
    const email = user?.email;
    
    if (fullName) {
      // Truncate long names on small screens
      if (device.size === 'xxs' && fullName.length > 12) {
        return fullName.split(' ')[0]; // Just first name
      }
      if (device.size === 'xs' && fullName.length > 16) {
        return fullName.substring(0, 16) + '...';
      }
      return fullName;
    }
    
    if (email) {
      const username = email.split('@')[0];
      return device.size === 'xxs' && username.length > 10 
        ? username.substring(0, 10) + '...'
        : username;
    }
    
    return 'Usuário';
  };
  
  const dropdownStyles = getDropdownStyles();
  
  return (
    <header className="bg-white border-b shadow-sm sticky top-0 z-30 w-full">
      <div className={cn('container mx-auto', getContainerPadding())}>
        <div className={cn(
          'flex items-center justify-between',
          getNavigationHeight()
        )}>
          {/* Left side - Navigation */}
          <div className="flex items-center gap-2">
            <Link to="/" className={getLogoStyles()}>
              {userType === 'student' ? t('navigation.applicationMonitor') : t('navigation.monitoring')}
            </Link>
            
            {/* Navigation buttons */}
            <div className="hidden md:flex items-center gap-2 ml-4">
              {!isOnDashboard && (
                <Button
                  asChild
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Link to={dashboardLink}>
                    <Home className="h-4 w-4" />
                    {t('common.dashboard')}
                  </Link>
                </Button>
              )}
              
              {!isOnProfile && (
                <Button
                  asChild
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Link to="/profile">
                    <User className="h-4 w-4" />
                    {t('common.profile')}
                  </Link>
                </Button>
              )}
            </div>
          </div>
          
          {/* Right side - User Menu */}
          <div className="flex items-center gap-1 sm:gap-2">
            <LanguageSwitcher />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className={getUserButtonStyles()}
                >
                  <User className={buttonStyles.iconSize} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="end" 
                className={cn(dropdownStyles.width, 'z-50')}
                sideOffset={dropdownStyles.sideOffset}
              >
                <DropdownMenuLabel className={getMenuItemStyles()}>
                  <div className={cn(
                    'font-medium truncate',
                    device.size === 'xxs' ? 'text-xs' : 
                    device.size === 'xs' ? 'text-sm' : ''
                  )}>
                    {getUserDisplayName()}
                  </div>
                  <div className={cn(
                    'text-muted-foreground truncate',
                    device.size === 'xxs' ? 'text-[0.65rem]' : 'text-xs'
                  )}>
                    {user?.email}
                  </div>
                </DropdownMenuLabel>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem asChild className={getMenuItemStyles()}>
                  <Link to="/profile" className="cursor-pointer w-full">
                    {t('common.profile')}
                  </Link>
                </DropdownMenuItem>
                
                <DropdownMenuItem asChild className={getMenuItemStyles()}>
                  <Link
                    to={dashboardLink}
                    className="cursor-pointer w-full"
                  >
                    {t('common.dashboard')}
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem asChild className={getMenuItemStyles()}>
                  <Link
                    to={getHelpLink()}
                    className="cursor-pointer w-full"
                  >
                    {t('common.help')}
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
                
                <LogoutButton 
                  variant="ghost" 
                  size="sm" 
                  className={cn(
                    'w-full justify-start h-auto',
                    getMenuItemStyles(),
                    'p-2'
                  )}
                >
                  {t('common.logout')}
                </LogoutButton>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
};
