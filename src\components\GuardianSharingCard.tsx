
import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Mail, Phone, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { Guardian } from "@/types/auth";
import { cn } from "@/lib/utils";

interface GuardianSharingCardProps {
  guardian: Guardian;
  onShare: (guardian: Guardian) => Promise<void>;
  sharingStatus?: string;
  compact?: boolean;
}

const GuardianSharingCard: React.FC<GuardianSharingCardProps> = ({
  guardian,
  onShare,
  sharingStatus = 'idle',
  compact = false
}) => {
  const handleShare = () => {
    onShare(guardian);
  };

  const getStatusIcon = () => {
    switch (sharingStatus) {
      case 'loading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-600" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <MapPin className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (sharingStatus) {
      case 'loading':
        return 'Enviando...';
      case 'success':
        return 'Enviado!';
      case 'error':
        return 'Erro';
      default:
        return 'Compartilhar';
    }
  };

  const getStatusBadge = () => {
    if (sharingStatus === 'success') {
      return (
        <Badge variant="outline" className="border-green-500 text-green-700 bg-green-50">
          <CheckCircle className="h-3 w-3 mr-1" />
          Localização enviada
        </Badge>
      );
    }

    if (sharingStatus === 'error') {
      return (
        <Badge variant="outline" className="border-red-500 text-red-700 bg-red-50">
          <AlertCircle className="h-3 w-3 mr-1" />
          Falha no envio
        </Badge>
      );
    }

    return null;
  };

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <Mail className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <p className="font-medium text-sm">{guardian.full_name || guardian.email}</p>
            <p className="text-xs text-gray-500">{guardian.email}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {getStatusBadge()}
          <Button
            onClick={handleShare}
            disabled={sharingStatus === 'loading'}
            size="sm"
            variant={sharingStatus === 'success' ? 'outline' : 'default'}
            className={cn(
              "transition-all duration-200",
              sharingStatus === 'success' && "border-green-500 text-green-700"
            )}
          >
            {getStatusIcon()}
            <span className="ml-1 hidden sm:inline">{getStatusText()}</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Mail className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium">{guardian.full_name || 'Responsável'}</h3>
                <p className="text-sm text-gray-600">{guardian.email}</p>
              </div>
            </div>
            
            {guardian.phone && (
              <div className="flex items-center gap-2 text-sm text-gray-500 mb-3">
                <Phone className="h-4 w-4" />
                <span>{guardian.phone}</span>
              </div>
            )}

            {getStatusBadge()}
          </div>

          <Button
            onClick={handleShare}
            disabled={sharingStatus === 'loading'}
            size="sm"
            variant={sharingStatus === 'success' ? 'outline' : 'default'}
            className={cn(
              "transition-all duration-200 hover:scale-105",
              sharingStatus === 'success' && "border-green-500 text-green-700"
            )}
          >
            {getStatusIcon()}
            <span className="ml-2">{getStatusText()}</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default GuardianSharingCard;
