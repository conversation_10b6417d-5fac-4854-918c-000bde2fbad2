{"mcpServers": {"supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": ["list_projects", "get_project", "list_tables", "execute_sql"]}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "browserTools": {"command": "cmd", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "disabled": false, "autoApprove": []}, "browsermcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@browsermcp/mcp@latest"], "disabled": false, "autoApprove": []}, "Figma": {"command": "cmd", "args": ["/c", "npx", "-y", "mcp-remote", "http://127.0.0.1:3845/sse"], "disabled": false, "autoApprove": []}, "Linear": {"command": "cmd", "args": ["/c", "npx", "-y", "mcp-remote", "https://mcp.linear.app/sse"], "env": {}, "disabled": false, "autoApprove": []}}}