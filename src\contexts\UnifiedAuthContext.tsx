import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
// import { useAuthMonitoring } from '@/hooks/useAuthMonitoring'; // Temporarily disabled to fix auth loop

export interface ExtendedUser extends User {
  user_type?: string;
  full_name?: string;
  phone?: string;
  cpf?: string;
  status?: string;
  last_login_at?: string | null;
  login_count?: number;
}

export interface AuthContextType {
  user: ExtendedUser | null;
  session: Session | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
  signOut: async () => {},
  refreshUser: async () => {},
});

export const useUser = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useUser must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<ExtendedUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Auth monitoring temporarily disabled to fix auth loop
  // const { logAuthEvent } = useAuthMonitoring({ 
  //   enableRealTimeAlerts: true,
  //   logLevel: 'detailed'
  // });
  
  // Temporary no-op function
  const logAuthEvent = async (event: string, data?: any) => {
    console.log(`[AUTH] ${event}:`, data);
  };

  const fetchUserProfile = async (authUser: User): Promise<ExtendedUser> => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('full_name, user_type, phone, cpf, status, registration_status, last_login_at, login_count')
        .eq('user_id', authUser.id)
        .maybeSingle();

      if (error) {
        console.warn('[AUTH] Profile fetch error:', error);
      }

      if (!profile) {
        // Profile not found, return metadata only
        return {
          ...authUser,
          user_type: authUser.user_metadata?.user_type,
          full_name: authUser.user_metadata?.full_name,
          phone: authUser.user_metadata?.phone,
          cpf: authUser.user_metadata?.cpf,
          status: authUser.user_metadata?.status,
          last_login_at: authUser.user_metadata?.last_login_at,
          login_count: authUser.user_metadata?.login_count,
        };
      }

      // Check if profile is active
      if (profile.status === 'inactive' || profile.registration_status === 'pending') {
        await logAuthEvent('profile_status_warning', {
          user_id: authUser.id,
          status: profile.status,
          registration_status: profile.registration_status
        });
      }

      return {
        ...authUser,
        user_type: profile.user_type || authUser.user_metadata?.user_type,
        full_name: profile.full_name || authUser.user_metadata?.full_name,
        phone: profile.phone || authUser.user_metadata?.phone,
        cpf: profile.cpf || authUser.user_metadata?.cpf,
        status: profile.status,
        last_login_at: profile.last_login_at,
        login_count: profile.login_count,
      };
    } catch (error) {
      console.error('[AUTH] Failed to fetch user profile:', error);
      await logAuthEvent('profile_fetch_error', {
        user_id: authUser.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      // Fallback to user metadata
      return {
        ...authUser,
        user_type: authUser.user_metadata?.user_type,
        full_name: authUser.user_metadata?.full_name,
        phone: authUser.user_metadata?.phone,
        cpf: authUser.user_metadata?.cpf,
        status: authUser.user_metadata?.status,
        last_login_at: authUser.user_metadata?.last_login_at,
        login_count: authUser.user_metadata?.login_count,
      };
    }
  };

  const refreshUser = async () => {
    try {
      const { data: { user: authUser }, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('[AUTH] Error refreshing user:', error);
        setUser(null);
        return;
      }

      if (authUser) {
        const extendedUser = await fetchUserProfile(authUser);
        setUser(extendedUser);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('[AUTH] Failed to refresh user:', error);
      setUser(null);
    }
  };

  const signOut = async () => {
    try {
      await logAuthEvent('logout_initiated', { user_id: user?.id });
      
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('[AUTH] Sign out error:', error);
        throw error;
      }
      
      setUser(null);
      setSession(null);
      
      await logAuthEvent('logout_completed');
      
      // Redirect to login page
      window.location.href = '/login';
    } catch (error) {
      console.error('[AUTH] Failed to sign out:', error);
      // Force redirect even on error
      window.location.href = '/login';
      throw error;
    }
  };

  useEffect(() => {
    let mounted = true;
    let initComplete = false;

    const initializeAuth = async () => {
      if (initComplete) return; // Prevent multiple initializations
      
      try {
        console.log('[AUTH] Initializing authentication...');
        
        // Get initial session
        const { data: { session: initialSession }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('[AUTH] Error getting initial session:', error);
        }

        if (mounted && !initComplete) {
          initComplete = true;
          setSession(initialSession);
          
          if (initialSession?.user) {
            try {
              const extendedUser = await fetchUserProfile(initialSession.user);
              setUser(extendedUser);
              console.log('[AUTH] User authenticated:', extendedUser.email);
            } catch (profileError) {
              console.error('[AUTH] Profile fetch failed:', profileError);
              setUser(initialSession.user as ExtendedUser);
            }
          } else {
            setUser(null);
          }
          
          setIsLoading(false);
        }
      } catch (error) {
        console.error('[AUTH] Failed to initialize:', error);
        if (mounted) {
          setIsLoading(false);
          setUser(null);
          setSession(null);
        }
      }
    };

    // Set up auth state listener with refresh token error handling
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        if (!mounted) return;

        console.log('[AUTH] Auth state changed:', event);

        // Handle refresh token errors
        if (event === 'TOKEN_REFRESHED' && !newSession) {
          console.warn('[AUTH] Token refresh failed - signing out');
          setSession(null);
          setUser(null);
          setIsLoading(false);
          // Clear any stale tokens from localStorage
          localStorage.removeItem('supabase.auth.token');
          return;
        }

        // Handle sign out events
        if (event === 'SIGNED_OUT') {
          console.log('[AUTH] User signed out');
          setSession(null);
          setUser(null);
          setIsLoading(false);
          return;
        }

        // Save raw session & user immediately – avoid extra Supabase calls here to prevent
        // supabase-js onAuthStateChange dead-lock bug (see https://supabase.com/docs/guides/troubleshooting/why-is-my-supabase-api-call-not-returning-PGzXw0)
        setSession(newSession);
        setUser(newSession?.user as ExtendedUser ?? null);
        
        if (event !== 'INITIAL_SESSION') {
          setIsLoading(false);
        }
      }
    );

    initializeAuth();

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []); // Empty dependency array to prevent re-runs

  useEffect(() => {
    // Whenever we have a session and a bare user, enrich it with the profile
    // This is intentionally outside onAuthStateChange to avoid the dead-lock bug.
    if (!session?.user) return;
    // Skip if user is already extended (has user_type) to avoid redundant fetches
    if ((user as ExtendedUser | null)?.user_type) return;

    const enrich = async () => {
      try {
        const extended = await fetchUserProfile(session.user!);
        setUser(extended);
      } catch (err) {
        console.error('[AUTH] Failed to enrich user profile:', err);
      }
    };

    enrich();
  }, [session]);

  const value: AuthContextType = {
    user,
    session,
    isLoading,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Export AuthProvider as UnifiedAuthProvider for backwards compatibility
export const UnifiedAuthProvider = AuthProvider;
export { AuthContext };
