
import { useState, useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useUser } from '@/contexts/UnifiedAuthContext';
import { Guardian } from './types';
import { useLocationSharing } from './useLocationSharing';

export function useGuardianList() {
  const [guardians, setGuardians] = useState<Guardian[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useUser();
  const { 
    sharingStatus, 
    formatRelativeTime, 
    shareLocation, 
    resendEmail 
  } = useLocationSharing();

  useEffect(() => {
    if (user?.id) {
      console.log('[DB] Acessando guardians via RPC');
      fetchGuardians();
    }
  }, [user?.id]);

  const fetchGuardians = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      if (!user?.id) {
        setError('Usuário não autenticado');
        setGuardians([]);
        return;
      }

      // Chamar a função RPC com o objeto de parâmetros correto
      const { data, error } = await supabase.rpc(
        'get_student_guardians_from_relationships',
        { p_student_id: user.id }
      );

      if (error) {
        console.error('Error fetching guardians:', error);
        
        if (error.code === 'PGRST116') {
          setError('Nenhum responsável encontrado.');
        } else {
          setError('Não foi possível carregar os responsáveis: ' + error.message);
        }
        setGuardians([]);
      } else {
        console.log('Guardians loaded:', data);
        setGuardians(data || []);
      }
    } catch (error: any) {
      console.error('Error fetching guardians:', error);
      setError('Erro ao buscar os responsáveis');
      setGuardians([]);
    } finally {
      setIsLoading(false);
    }
  };

  const deleteGuardian = async (id: string) => {
    try {
      // Use student_guardian_relationships instead of guardians
      const { error } = await supabase
        .from('student_guardian_relationships')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Sucesso",
        description: "Responsável removido com sucesso"
      });

      fetchGuardians();
    } catch (error: any) {
      console.error('Error deleting guardian:', error);
      toast({
        title: "Erro",
        description: error?.message || "Não foi possível remover o responsável",
        variant: "destructive"
      });
    }
  };

  return {
    guardians,
    isLoading,
    error,
    sharingStatus,
    fetchGuardians,
    deleteGuardian,
    shareLocation,
    resendEmail,
    formatRelativeTime
  };
}

