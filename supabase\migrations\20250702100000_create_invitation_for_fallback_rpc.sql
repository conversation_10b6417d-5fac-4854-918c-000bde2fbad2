-- Migration: Create RPC function to generate an invitation record for student creation fallback
-- Timestamp: 20250702100000

CREATE OR REPLACE FUNCTION public.create_student_invitation_for_fallback(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_student_phone TEXT DEFAULT NULL,
  p_guardian_id UUID -- Ensure the client hook can pass this (e.g., from useUser())
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT,
  invitation_id UUID,
  temp_password TEXT,
  activation_token TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_clean_cpf TEXT;
  v_generated_temp_password TEXT;
  v_generated_activation_token TEXT;
  v_new_invitation_id UUID;
BEGIN
  -- Validate guardian_id
  IF p_guardian_id IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Guardian ID não fornecido. Usuário precisa estar autenticado.', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Validate student name
  IF p_student_name IS NULL OR TRIM(p_student_name) = '' THEN
    RETURN QUERY SELECT FALSE, 'Nome do estudante é obrigatório.', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Validate student email
  IF p_student_email IS NULL OR p_student_email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
      RETURN QUERY SELECT FALSE, 'Email do estudante inválido.', NULL::UUID, NULL::TEXT, NULL::TEXT;
      RETURN;
  END IF;

  -- Validate CPF
  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  IF char_length(v_clean_cpf) <> 11 THEN
    RETURN QUERY SELECT FALSE, 'CPF inválido. Deve conter 11 dígitos.', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Check if email already exists in auth.users (more critical than profiles for new auth creation)
  IF EXISTS (SELECT 1 FROM auth.users WHERE LOWER(email) = LOWER(p_student_email)) THEN
    RETURN QUERY SELECT FALSE, 'Este email já está registrado no sistema.', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Check if CPF already exists for another student profile
  IF EXISTS (
    SELECT 1 FROM public.profiles
    WHERE regexp_replace(cpf, '[^0-9]', '', 'g') = v_clean_cpf
    AND user_type = 'student'
    -- Optionally, add AND email != p_student_email if you want to allow same CPF with different email
  ) THEN
    RETURN QUERY SELECT FALSE, 'Este CPF já está associado a outro estudante.', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Generate temporary credentials and token
  -- Using a slightly more robust method for temp password generation
  v_generated_temp_password := array_to_string(array(
      select substr('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()', floor(random() * 70 + 1)::int, 1)
      from generate_series(1, 10)
  ), '');
  -- Ensure at least one special character if the random generation doesn't guarantee it
  IF v_generated_temp_password !~ '[!@#$%^&*()]' THEN
    v_generated_temp_password := v_generated_temp_password || '!';
  END IF;

  v_generated_activation_token := 'act_' || encode(gen_random_bytes(24), 'hex'); -- Consistent prefix, longer token

  -- Create family invitation record
  INSERT INTO public.family_invitations(
    id, guardian_id, student_name, student_email, student_cpf, student_phone,
    invitation_type, status, temp_password, activation_token, expires_at
  ) VALUES (
    gen_random_uuid(), p_guardian_id, p_student_name, p_student_email,
    v_clean_cpf, p_student_phone, 'guardian_to_student_fallback', 'pending_auth_creation',
    v_generated_temp_password, v_generated_activation_token, NOW() + interval '7 days'
  ) RETURNING id INTO v_new_invitation_id;

  IF v_new_invitation_id IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Falha ao criar convite na base de dados.', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Log this specific type of invitation creation
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_invitation_fallback_initiated', p_guardian_id,
          jsonb_build_object(
            'invitation_id', v_new_invitation_id,
            'student_email', p_student_email
          ),
          NOW());

  RETURN QUERY SELECT TRUE, 'Convite para fallback criado com sucesso.', v_new_invitation_id, v_generated_temp_password, v_generated_activation_token;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error in create_student_invitation_for_fallback: %', SQLERRM;
    RETURN QUERY SELECT FALSE, 'Erro interno ao criar convite: ' || SQLERRM, NULL::UUID, NULL::TEXT, NULL::TEXT;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.create_student_invitation_for_fallback(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_student_phone TEXT,
  p_guardian_id UUID
) TO authenticated;

COMMENT ON FUNCTION public.create_student_invitation_for_fallback IS
'Creates an invitation record in family_invitations specifically for the student account creation fallback flow.
Generates server-side temporary password and activation token.
Called by the client when the primary student creation Edge Function fails.';
