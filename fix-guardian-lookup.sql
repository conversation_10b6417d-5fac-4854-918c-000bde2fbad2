-- Create function to get guardians from student_guardian_relationships table
-- This replaces the old get_student_guardians_secure that looks in the wrong table

CREATE OR REPLACE FUNCTION public.get_student_guardians_from_relationships(
    p_student_id UUID
)
RETURNS TABLE(
    id TEXT,
    student_id UUID,
    email TEXT,
    full_name TEXT,
    phone TEXT,
    is_active BOOLEAN,
    created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Verify the caller is authorized to access this student's guardians
    IF auth.uid() <> p_student_id THEN
        RAISE EXCEPTION 'Unauthorized access to student guardians';
    END IF;
    
    -- Return guardians from the relationships table
    RETURN QUERY
    SELECT 
        sgr.id::TEXT as id,
        sgr.student_id,
        u.email,
        COALESCE(p.full_name, u.email) as full_name,
        p.phone,
        TRUE as is_active,
        sgr.created_at
    FROM public.student_guardian_relationships sgr
    JOIN auth.users u ON sgr.guardian_id = u.id
    LEFT JOIN public.profiles p ON sgr.guardian_id = p.user_id
    WHERE sgr.student_id = p_student_id
    ORDER BY sgr.created_at DESC;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_student_guardians_from_relationships(UUID) TO authenticated;

-- Add comment
COMMENT ON FUNCTION public.get_student_guardians_from_relationships(UUID) IS 'Get guardians for a student from the student_guardian_relationships table'; 