# 🔐 SISTEMA COMPLETO DE PERMISSÕES - LOCATE-FAMILY-CONNECT

**Data:** 28 de Dezembro de 2025  
**Sistema:** Locate-Family-Connect  
**Objetivo:** Definir arquitetura completa de permissões para todos os tipos de usuários  

---

## 🎯 **FILOSOFIA DAS PERMISSÕES**

### **🔒 Princípios Base:**
1. **Privacy by Design** - Mínimo acesso necessário por padrão
2. **Consent-Based** - Estudante/família controla quem vê o quê
3. **Context-Aware** - Permissões variam por situação e relacionamento
4. **Granular Control** - Configuração detalhada para cada tipo de dado
5. **Audit Trail** - Registro completo de acessos e mudanças

---

## 👥 **MATRIZ DE PERMISSÕES POR TIPO DE USUÁRIO**

### **🎓 ESTUDANTES**

#### **👶 Estudante Menor de Idade (< 18 anos)**
```yaml
Localização:
  ✅ compartilhar_localizacao_atual: true
  ✅ ver_proprio_historico: true (últimos 7 dias)
  ❌ desabilitar_compartilhamento: false
  ❌ excluir_historico: false

Responsáveis:
  ✅ convidar_responsaveis: true
  ✅ ver_responsaveis_atuais: true
  ❌ remover_responsavel_primario: false
  ⚠️ remover_responsavel_secundario: com_aprovacao_primario

Conta:
  ✅ atualizar_dados_basicos: true
  ✅ trocar_senha: true
  ❌ excluir_conta: false (precisa aprovação responsável)
  ❌ configuracoes_privacidade_avancadas: false

Notificações:
  ✅ configurar_notificacoes_proprias: true
  ❌ gerenciar_notificacoes_responsaveis: false
```

#### **👨‍🎓 Estudante Maior de Idade (≥ 18 anos)**
```yaml
Localização:
  ✅ controle_total_compartilhamento: true
  ✅ definir_horarios_compartilhamento: true
  ✅ criar_geofences_personalizados: true
  ✅ compartilhamento_granular_por_responsavel: true

Responsáveis:
  ✅ gerenciar_todos_responsaveis: true
  ✅ definir_niveis_acesso: true
  ✅ remover_qualquer_responsavel: true
  ✅ bloquear_responsavel: true

Conta:
  ✅ controle_total_conta: true
  ✅ excluir_conta_autonomamente: true
  ✅ exportar_dados: true
  ✅ configuracoes_avancadas_privacidade: true

Dados:
  ✅ ver_logs_acesso: true
  ✅ configurar_retencao_dados: true
  ✅ aprovar_integracao_terceiros: true
```

### **👨‍👩‍👧‍👦 RESPONSÁVEIS**

#### **👑 Responsável Primário**
```yaml
Definição: Primeiro responsável cadastrado OU designado pelo estudante

Localização:
  ✅ ver_localizacao_tempo_real: true
  ✅ historico_completo: true
  ✅ criar_geofences: true
  ✅ receber_alertas_geofence: true
  ✅ configurar_horarios_monitoramento: true

Gerenciamento:
  ✅ adicionar_responsaveis_secundarios: true
  ✅ gerenciar_permissoes_outros_responsaveis: true
  ✅ remover_responsaveis_secundarios: true
  ✅ designar_novo_responsavel_primario: true

Dados Estudante:
  ✅ ver_relatorios_completos: true
  ✅ configurar_dados_perfil: true (menor idade)
  ⚠️ configurar_dados_perfil: com_aprovacao (maior idade)
  ✅ solicitar_exclusao_conta: true (menor idade)

Emergência:
  ✅ acesso_localizacao_emergencia: true
  ✅ contatar_autoridades: true
  ✅ override_configuracoes_privacidade: true (situações críticas)
```

#### **👥 Responsável Secundário**
```yaml
Definição: Responsável adicional com permissões limitadas

Localização:
  ⚠️ ver_localizacao_tempo_real: se_autorizado_pelo_estudante
  ⚠️ historico_limitado: ultimos_7_dias
  ❌ criar_geofences: false
  ⚠️ receber_alertas: apenas_emergencia

Visualização:
  ✅ ver_informacoes_basicas_estudante: true
  ⚠️ relatorios_limitados: resumo_semanal
  ❌ gerenciar_outros_responsaveis: false
  ❌ alterar_configuracoes: false

Comunicação:
  ✅ receber_notificacoes_importantes: true
  ✅ comunicar_com_estudante: true
  ❌ alterar_configuracoes_notificacao: false
```

#### **⏰ Responsável Temporário**
```yaml
Definição: Acesso limitado por tempo (babá, transporte escolar, etc.)

Localização:
  ✅ ver_localizacao_atual: true
  ❌ historico: false
  ⚠️ alertas_basicos: apenas_durante_periodo_ativo

Duração:
  ⏰ acesso_temporario: data_inicio_fim
  ⏰ renovacao_automatica: false
  ⏰ notificacao_expiracao: 24h_antes

Limitações:
  ❌ configurar_qualquer_coisa: false
  ❌ ver_outros_responsaveis: false
  ✅ contato_emergencia_limitado: true
```

### **🏫 USUÁRIOS DO SISTEMA**

#### **🏫 Administrador Escolar**
```yaml
Gestão Institucional:
  ✅ criar_contas_estudantes_escola: true
  ✅ importar_dados_massa: true
  ✅ relatorios_institucionais: true
  ⚠️ dados_estudantes: apenas_dados_educacionais

Limitações Privacidade:
  ❌ ver_localizacao_estudantes: false
  ❌ ver_comunicacao_familiar: false
  ⚠️ dados_familia: apenas_contato_emergencia
  ❌ alterar_configuracoes_familia: false

Relatórios:
  ✅ estatisticas_uso_sistema: true
  ✅ relatorios_frequencia: true
  ❌ dados_localizacao_detalhados: false
```

#### **👨‍💻 Desenvolvedor/Suporte Técnico**
```yaml
Acesso Técnico:
  ✅ logs_sistema: true
  ✅ debugging_erros: true
  ⚠️ dados_usuarios: apenas_anonimizados
  ✅ configuracoes_sistema: true

Limitações Éticas:
  ❌ dados_localizacao_identificaveis: false
  ❌ comunicacao_privada: false
  ⚠️ acesso_conta_usuario: apenas_com_ticket_suporte
  ✅ dados_agregados_anonimos: true
```

---

## 🎯 **PERMISSÕES CONTEXTUAIS DETALHADAS**

### **📍 Localização - Níveis de Acesso**

#### **🟢 Nível 1: Tempo Real**
```yaml
Quem tem acesso:
  - Responsável Primário (sempre)
  - Responsável Secundário (se autorizado)
  - Responsável Temporário (durante período ativo)
  - Estudante (própria localização)

Dados incluídos:
  - Coordenadas atuais
  - Endereço atual
  - Timestamp última atualização
  - Status bateria dispositivo
```

#### **🔵 Nível 2: Histórico Recente (7 dias)**
```yaml
Quem tem acesso:
  - Responsável Primário
  - Estudante (próprio histórico)
  - Responsável Secundário (se configurado)

Dados incluídos:
  - Trajetos últimos 7 dias
  - Tempo permanência em locais
  - Padrões de movimento
  - Alertas geofence
```

#### **🟡 Nível 3: Histórico Estendido (30 dias)**
```yaml
Quem tem acesso:
  - Responsável Primário
  - Estudante Maior de Idade (próprio)

Dados incluídos:
  - Relatórios mensais
  - Análise padrões
  - Estatísticas movimento
  - Relatórios geofence
```

#### **🔴 Nível 4: Histórico Completo**
```yaml
Quem tem acesso:
  - Responsável Primário
  - Estudante Maior de Idade
  - Autoridades (com ordem judicial)

Dados incluídos:
  - Todo histórico armazenado
  - Dados para investigação
  - Backup dados
  - Logs sistema
```

### **📊 Dados Pessoais - Categorias**

#### **📋 Dados Básicos**
```yaml
Dados: nome, email, telefone, escola
Acesso:
  - Próprio usuário: total
  - Responsável Primário: leitura/edição
  - Responsável Secundário: apenas leitura
  - Escola: dados educacionais apenas
```

#### **🔒 Dados Sensíveis**
```yaml
Dados: CPF, endereço, dados médicos, situação familiar
Acesso:
  - Próprio usuário: total
  - Responsável Primário: conforme configuração
  - Escola: apenas se necessário
  - Outros: negado por padrão
```

#### **💬 Dados Comunicação**
```yaml
Dados: mensagens, notificações, histórico comunicação
Acesso:
  - Participantes da conversa
  - Responsável Primário (se menor idade)
  - Logs sistema (anonimizados)
```

---

## ⚙️ **CONFIGURAÇÕES AVANÇADAS**

### **🔔 Sistema de Notificações**

#### **📱 Níveis de Notificação**
```yaml
🚨 Emergência:
  - Todos os responsáveis
  - Notificação imediata
  - SMS + Push + Email
  - Não pode ser desabilitada

⚠️ Importante:
  - Responsáveis configurados
  - Conforme horário definido
  - Push + Email
  - Pode ser customizada

ℹ️ Informativa:
  - Apenas responsáveis que optaram
  - Horário comercial apenas
  - Push ou Email
  - Pode ser desabilitada
```

### **🗺️ Geofences Inteligentes**

#### **🏠 Tipos de Geofence**
```yaml
🏠 Casa:
  - Criado por: Estudante ou Responsável Primário
  - Alertas: Chegada/Saída
  - Quem recebe: Configurável

🏫 Escola:
  - Criado por: Sistema ou Escola
  - Alertas: Chegada/Saída horário escolar
  - Quem recebe: Responsáveis que optaram

⚠️ Zona Restrita:
  - Criado por: Responsável Primário apenas
  - Alertas: Entrada (imediato)
  - Quem recebe: Todos responsáveis

🎯 Local Frequente:
  - Criado por: Sistema (automático)
  - Alertas: Tempo permanência incomum
  - Quem recebe: Conforme configuração
```

### **⏰ Horários e Contextos**

#### **📅 Configurações Temporais**
```yaml
🌅 Horário Escolar:
  - Localização: Sempre compartilhada
  - Notificações: Ativas para chegada/saída
  - Responsáveis: Todos recebem

🌙 Período Noturno:
  - Localização: Apenas emergência
  - Notificações: Reduzidas
  - Privacidade: Aumentada

🎉 Fins de Semana:
  - Configuração: Relaxada
  - Responsáveis: Podem ter menos alertas
  - Estudante: Mais controle

🚨 Modo Emergência:
  - Triggered por: Botão pânico ou situação crítica
  - Override: Todas configurações privacidade
  - Notificação: Todos responsáveis + autoridades
```

---

## 🗄️ **ESTRUTURA DE BANCO PARA PERMISSÕES**

### **🔐 Tabela `user_permissions`**
```sql
CREATE TABLE user_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  permission_type VARCHAR(50) NOT NULL,
  permission_level VARCHAR(30) NOT NULL,
  granted_by_user_id UUID REFERENCES auth.users(id),
  granted_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  context JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **👥 Tabela `relationship_permissions`**
```sql
CREATE TABLE relationship_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  relationship_id UUID NOT NULL REFERENCES student_guardian_relationships(id),
  permission_category VARCHAR(50) NOT NULL, -- 'location', 'data', 'notifications', etc.
  permission_actions TEXT[] NOT NULL, -- ['read', 'write', 'delete', 'share']
  permission_scope JSONB, -- Detailed scope configuration
  time_restrictions JSONB, -- When these permissions apply
  emergency_override BOOLEAN DEFAULT false,
  created_by_user_id UUID REFERENCES auth.users(id),
  approved_by_user_id UUID REFERENCES auth.users(id),
  status VARCHAR(30) DEFAULT 'pending', -- pending, approved, denied, expired
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **📋 Tabela `permission_templates`**
```sql
CREATE TABLE permission_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  template_name VARCHAR(100) NOT NULL,
  user_type VARCHAR(30) NOT NULL, -- 'primary_guardian', 'secondary_guardian', etc.
  relationship_type VARCHAR(30),
  permission_config JSONB NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **📊 Valores para `permission_type`**
```sql
-- Localização
'location_realtime'          -- Ver localização atual
'location_history_7days'     -- Histórico 7 dias
'location_history_30days'    -- Histórico 30 dias  
'location_history_full'      -- Histórico completo
'geofence_create'           -- Criar geofences
'geofence_manage'           -- Gerenciar geofences

-- Dados Pessoais
'profile_read'              -- Ler dados perfil
'profile_write'             -- Editar dados perfil
'profile_sensitive'         -- Dados sensíveis
'data_export'               -- Exportar dados

-- Relacionamentos
'relationship_invite'       -- Convidar responsáveis
'relationship_manage'       -- Gerenciar responsáveis
'relationship_remove'       -- Remover responsáveis
'relationship_hierarchy'    -- Alterar hierarquia

-- Notificações
'notifications_configure'   -- Configurar notificações
'notifications_emergency'   -- Receber emergência
'notifications_routine'     -- Notificações rotina

-- Conta
'account_delete'            -- Excluir conta
'account_suspend'           -- Suspender conta
'account_settings'          -- Configurações conta

-- Sistema
'system_admin'              -- Administração sistema
'system_support'            -- Suporte técnico
'system_audit'              -- Auditoria sistema
```

---

## 🎯 **CENÁRIOS PRÁTICOS DE USO**

### **🏠 Cenário 1: Família Nuclear Simples**
```yaml
Participantes:
  - João (15 anos, estudante)
  - Maria (mãe, responsável primário)
  - Carlos (pai, responsável secundário)

Configuração:
  João:
    - Compartilha localização com ambos pais
    - Recebe notificações próprias
    - Pode convidar responsáveis adicionais
  
  Maria (Primário):
    - Vê localização tempo real João
    - Configura geofences
    - Gerencia permissões Carlos
    - Relatórios completos
  
  Carlos (Secundário):
    - Vê localização João (autorizado por Maria)
    - Recebe alertas emergência
    - Relatórios semanais básicos
    - Não pode alterar configurações
```

### **⚠️ Cenário 2: Família Separada**
```yaml
Participantes:
  - Ana (16 anos, estudante)
  - Mãe (responsável primário)
  - Pai (responsável secundário)
  - Madrasta (responsável temporário)

Configuração:
  Ana:
    - Controla quem vê localização quando
    - Define horários para cada responsável
    - Aprovação necessária para mudanças
  
  Mãe:
    - Acesso total durante semana
    - Configura horários escolares
    - Gerencia outros responsáveis
  
  Pai:
    - Acesso limitado aos fins de semana
    - Recebe apenas emergências durante semana
    - Não pode alterar configurações
  
  Madrasta:
    - Acesso apenas quando Ana está na casa do pai
    - Temporário (renovado mensalmente)
    - Apenas localização atual
```

### **🏫 Cenário 3: Estudante Maior de Idade**
```yaml
Participante:
  - Roberto (19 anos, universitário)

Configuração:
  Roberto:
    - Controle total sobre todos dados
    - Define se/quando/como compartilha localização
    - Pode adicionar/remover responsáveis livremente
    - Configura privacidade granular
    - Pode exportar/excluir dados
  
  Responsáveis (se convidados):
    - Apenas permissões que Roberto autorizar
    - Pode revogar acesso a qualquer momento
    - Configurações personalizadas por responsável
```

---

## 🛡️ **SISTEMA DE SEGURANÇA E AUDITORIA**

### **📋 Log de Acessos**
```sql
CREATE TABLE access_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  accessed_user_id UUID REFERENCES auth.users(id), -- Who's data was accessed
  permission_used VARCHAR(50) NOT NULL,
  action_taken VARCHAR(50) NOT NULL, -- 'read', 'write', 'delete', etc.
  data_accessed JSONB, -- What specific data
  ip_address INET,
  user_agent TEXT,
  success BOOLEAN DEFAULT true,
  failure_reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **🔍 Monitoramento Automático**
```yaml
Alertas Automáticos:
  🚨 Acesso Suspeito:
    - Login de localização incomum
    - Múltiplas tentativas acesso
    - Uso de permissões não usuais
  
  ⚠️ Mudanças Críticas:
    - Alteração responsável primário
    - Mudanças permissões importantes
    - Exclusão dados históricos
  
  📊 Relatórios Periódicos:
    - Quem acessou quais dados
    - Padrões de uso incomuns
    - Permissões não utilizadas
```

---

## 🎨 **INTERFACE DE CONFIGURAÇÃO**

### **👤 Dashboard de Permissões do Usuário**
```yaml
Seções:
  📍 Localização:
    - Toggle compartilhamento geral
    - Configurar por responsável
    - Definir horários
    - Geofences pessoais
  
  👥 Responsáveis:
    - Lista com permissões de cada um
    - Convites pendentes
    - Histórico alterações
    - Configuração por relacionamento
  
  🔔 Notificações:
    - Tipos de alerta
    - Frequência notificações
    - Canais preferidos
    - Configuração emergência
  
  🔒 Privacidade:
    - Retenção de dados
    - Configurações avançadas
    - Log de acessos
    - Exportar dados
```

### **👨‍👩‍👧‍👦 Dashboard do Responsável**
```yaml
Visão Geral:
  📊 Resumo Permissões:
    - O que posso ver/fazer
    - Limitações atuais
    - Solicitações pendentes
  
  👶 Por Estudante:
    - Permissões específicas
    - Últimos acessos
    - Configurações ativas
    - Histórico interações
  
  ⚙️ Configurações:
    - Notificações pessoais
    - Preferências relatórios
    - Configuração emergência
```

---

## ✅ **IMPLEMENTAÇÃO RECOMENDADA**

### **🏗️ Fase 1: Base (2 semanas)**
1. ✅ Criar tabelas permissões
2. ✅ Implementar sistema RLS baseado em permissões
3. ✅ Templates básicos de permissões
4. ✅ Middleware de autorização

### **🔧 Fase 2: Funcionalidades (3 semanas)**
1. ✅ Interface configuração permissões
2. ✅ Sistema notificações contextuais
3. ✅ Geofences com permissões
4. ✅ Logs e auditoria

### **🎨 Fase 3: Avançado (2 semanas)**
1. ✅ Permissões temporárias
2. ✅ Sistema emergência override
3. ✅ Relatórios permissões
4. ✅ Configurações granulares

### **🔍 Fase 4: Monitoramento (1 semana)**
1. ✅ Alertas automáticos
2. ✅ Dashboard auditoria
3. ✅ Métricas uso permissões
4. ✅ Otimizações performance

---

## 🏆 **BENEFÍCIOS ESPERADOS**

### **👨‍👩‍👧‍👦 Para Famílias:**
- ✅ **Controle granular** sobre privacidade
- ✅ **Flexibilidade** para diferentes contextos familiares
- ✅ **Transparência** sobre quem acessa o quê
- ✅ **Facilidade** de configuração

### **🏫 Para Instituições:**
- ✅ **Conformidade** com LGPD/GDPR
- ✅ **Separação clara** de responsabilidades
- ✅ **Auditoria completa** de acessos
- ✅ **Configuração** institucional

### **🔧 Para Desenvolvedores:**
- ✅ **Sistema robusto** e extensível
- ✅ **Debugging facilitado** com logs detalhados
- ✅ **Segurança by design**
- ✅ **Performance otimizada**

---

**🎯 Este sistema de permissões garante que cada usuário tenha exatamente o acesso necessário, mantendo a privacidade e segurança como prioridades máximas!** 