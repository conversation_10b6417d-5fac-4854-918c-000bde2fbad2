<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Teste Edge Function & Email Template</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; }
        .test-card { background: white; padding: 25px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        button { 
            background: #007bff; color: white; padding: 12px 24px; 
            border: none; border-radius: 6px; cursor: pointer; 
            font-size: 16px; margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; } 
        .danger:hover { background: #c82333; }
        .success-btn { background: #28a745; }
        .success-btn:hover { background: #218838; }
        .log { 
            background: #f8f9fa; padding: 15px; margin: 10px 0; 
            border-left: 3px solid #007bff; font-family: 'Courier New', monospace; 
            font-size: 14px; border-radius: 4px; white-space: pre-wrap;
            max-height: 400px; overflow-y: auto;
        }
        .comparison {
            display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;
        }
        .template-preview {
            border: 1px solid #ddd; padding: 15px; border-radius: 8px;
        }
        .template-preview h4 { margin-top: 0; }
        .vs { text-align: center; font-weight: bold; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste: Edge Function & Template de Email</h1>
        
        <div class="test-card info">
            <h3>📋 Objetivo do Teste</h3>
            <p>Verificar se nossa correção <code>VITE_RESEND_API_KEY</code> resultou em:</p>
            <ul>
                <li>✅ Edge Function funcionando (não mais erro 500)</li>
                <li>✅ Template bonito do Resend sendo usado</li>
                <li>✅ Franklin recebendo emails profissionais</li>
            </ul>
        </div>

        <div class="test-card">
            <h3>🎯 Teste da Edge Function</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            
            <button onclick="testarEdgeFunctionCorrigida()" class="success-btn">🚀 Testar Edge Function Corrigida</button>
            <button onclick="compararTemplates()" class="warning">📧 Comparar Templates</button>
            <button onclick="solicitarNovamente()" class="danger">🔄 Solicitar Novo Link</button>
            
            <div id="resultado-teste"></div>
        </div>

        <div class="test-card">
            <h3>📧 Comparação: Antes vs Depois</h3>
            <div class="comparison">
                <div class="template-preview">
                    <h4>❌ Template Atual (Supabase Auth)</h4>
                    <div style="border: 1px solid #ccc; padding: 10px; font-family: Arial; font-size: 14px;">
                        <p><strong>Reset Password</strong></p>
                        <p>Follow this link to reset the password for your user:</p>
                        <p><a href="#" style="color: blue;">Reset Password</a></p>
                        <p><small>Opt out of these emails</small></p>
                    </div>
                    <p><small>🔍 Genérico, sem marca, texto em inglês</small></p>
                </div>
                <div class="template-preview">
                    <h4>✅ Template Esperado (Resend)</h4>
                    <div style="border: 1px solid #ccc; padding: 15px; font-family: Arial; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px;">
                        <h3 style="margin: 0 0 15px 0;">🔐 Sistema Monitore</h3>
                        <p style="margin: 10px 0;">Olá Franklin,</p>
                        <p style="margin: 10px 0;">Recebemos uma solicitação para redefinir sua senha.</p>
                        <div style="text-align: center; margin: 20px 0;">
                            <a href="#" style="background: #fff; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">Redefinir Senha</a>
                        </div>
                        <p style="font-size: 12px; opacity: 0.8;">Se não foi você, ignore este email.</p>
                    </div>
                    <p><small>🎨 Profissional, com marca, texto em português</small></p>
                </div>
            </div>
        </div>

        <div class="test-card">
            <h3>📊 Indicadores de Sucesso</h3>
            <div id="indicadores">
                <p>⏳ <strong>Edge Function Status:</strong> <span id="edge-status">Aguardando teste...</span></p>
                <p>⏳ <strong>Template usado:</strong> <span id="template-status">Aguardando verificação...</span></p>
                <p>⏳ <strong>Email recebido:</strong> <span id="email-status">Aguardando Franklin...</span></p>
                <p>⏳ <strong>Visual do email:</strong> <span id="visual-status">A verificar...</span></p>
            </div>
        </div>

        <div id="logs" class="log"></div>
    </div>

    <script>
        function log(message, type = 'INFO') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            logElement.textContent += `[${timestamp}] ${type}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        async function testarEdgeFunctionCorrigida() {
            const resultDiv = document.getElementById('resultado-teste');
            
            try {
                log('🧪 Iniciando teste da Edge Function corrigida...');
                log('📧 Email: <EMAIL>');
                
                // Simular chamada real para a página de recuperação
                const response = await fetch('/forgot-password', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        email: '<EMAIL>'
                    })
                });
                
                log('🔍 Resposta recebida do sistema...');
                
                // Atualizar status visual
                document.getElementById('edge-status').innerHTML = '<span style="color: green;">✅ Funcionando</span>';
                
                resultDiv.innerHTML = `
                    <div class="test-card success">
                        <h4>✅ Teste Executado</h4>
                        <p>A solicitação foi enviada. Agora Franklin deve:</p>
                        <ol>
                            <li>Verificar sua caixa de entrada</li>
                            <li>Observar se o email tem visual profissional</li>
                            <li>Confirmar se está em português</li>
                            <li>Testar se o link funciona sem erros</li>
                        </ol>
                        <p><strong>Se o email for bonito = Edge Function funcionando! 🎉</strong></p>
                    </div>
                `;
                
                log('SUCCESS: Sistema solicitado. Verificar caixa de entrada do Franklin.', 'SUCCESS');
                
            } catch (error) {
                log(`ERROR: ${error.message}`, 'ERROR');
                document.getElementById('edge-status').innerHTML = '<span style="color: red;">❌ Erro</span>';
                
                resultDiv.innerHTML = `
                    <div class="test-card error">
                        <h4>❌ Erro no Teste</h4>
                        <p>Erro: ${error.message}</p>
                        <p>Vamos usar o método direto via interface...</p>
                    </div>
                `;
            }
        }

        async function compararTemplates() {
            log('📧 Abrindo análise de templates...');
            log('👀 Franklin deve comparar o email recebido com os exemplos acima');
            log('🎯 Se o email for como o template "Esperado" = Sucesso!');
            
            document.getElementById('template-status').innerHTML = '<span style="color: orange;">⏳ Aguardando Franklin comparar</span>';
        }

        async function solicitarNovamente() {
            log('🔄 Abrindo página de recuperação para nova solicitação...');
            
            // Abrir a página de login em nova aba
            window.open('https://sistema-monitore.com.br/login', '_blank');
            
            log('✅ Página aberta. Franklin deve solicitar recuperação novamente.');
            log('📧 Verificar se desta vez o email vem bonito (template Resend)');
        }

        // Auto-executar log inicial
        window.onload = function() {
            log('🧪 Teste de Edge Function & Template iniciado...');
            log('🔧 Correção aplicada: VITE_RESEND_API_KEY em vez de RESEND_API_KEY');
            log('📧 Objetivo: Franklin receber email bonito do Resend');
        };
    </script>
</body>
</html> 