
import { useDevice } from '@/hooks/use-mobile';

export const useMapControlsLayout = () => {
  const { type: deviceType, orientation } = useDevice();

  const getControlsPosition = () => {
    // Para tablet em modo retrato, mover para o lado esquerdo
    if (deviceType === 'tablet' && orientation === 'portrait') {
      return {
        containerClass: 'absolute bottom-4 left-4 z-30 flex flex-col gap-2',
        buttonSize: 'sm' as const,
        maxWidth: 'max-w-[140px]'
      };
    }
    
    // Para mobile, usar posição compacta no canto direito
    if (deviceType === 'mobile') {
      return {
        containerClass: 'absolute bottom-2 right-2 z-30 flex flex-col gap-1',
        buttonSize: 'sm' as const,
        maxWidth: 'max-w-[120px]'
      };
    }

    // Para tablet landscape e desktop, manter posição original
    return {
      containerClass: 'absolute bottom-4 right-4 z-30 flex flex-col gap-2',
      buttonSize: 'sm' as const,
      maxWidth: 'max-w-[160px]'
    };
  };

  const getLocationControlsPosition = () => {
    // Para tablet em modo retrato, posicionar no lado esquerdo
    if (deviceType === 'tablet' && orientation === 'portrait') {
      return {
        containerClass: 'absolute bottom-4 left-4 z-20 flex flex-col gap-2',
        buttonSize: 'sm' as const
      };
    }

    // Para mobile, usar posição mais compacta
    if (deviceType === 'mobile') {
      return {
        containerClass: 'absolute bottom-2 right-2 z-20 flex flex-col gap-1',
        buttonSize: 'sm' as const
      };
    }

    // Posição padrão para outros casos
    return {
      containerClass: 'absolute bottom-4 right-4 z-20 flex flex-col gap-2',
      buttonSize: 'sm' as const
    };
  };

  return {
    getControlsPosition,
    getLocationControlsPosition,
    deviceType,
    orientation
  };
};
