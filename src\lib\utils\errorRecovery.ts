/**
 * Comprehensive Error Recovery System
 * Handles database, API, and TypeScript compilation errors
 */

export interface ErrorRecoveryResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  method?: string;
  fallbackUsed?: boolean;
}

export interface RetryOptions {
  maxAttempts?: number;
  delayMs?: number;
  backoffMultiplier?: number;
  shouldRetry?: (error: any) => boolean;
}

/**
 * Generic retry mechanism with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<ErrorRecoveryResult<T>> {
  const {
    maxAttempts = 3,
    delayMs = 1000,
    backoffMultiplier = 2,
    shouldRetry = () => true
  } = options;

  let lastError: any;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const result = await operation();
      return {
        success: true,
        data: result,
        method: `attempt_${attempt}`
      };
    } catch (error: any) {
      lastError = error;
      console.warn(`[ErrorRecovery] Attempt ${attempt}/${maxAttempts} failed:`, error.message);
      
      if (attempt === maxAttempts || !shouldRetry(error)) {
        break;
      }
      
      // Exponential backoff delay
      const delay = delayMs * Math.pow(backoffMultiplier, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  return {
    success: false,
    error: lastError?.message || 'All retry attempts failed',
    method: 'retry_exhausted'
  };
}

/**
 * Safe property access with fallback
 */
export function safeGet<T>(
  obj: any,
  path: string,
  fallback: T
): T {
  try {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current == null || typeof current !== 'object') {
        return fallback;
      }
      current = current[key];
    }
    
    return current !== undefined ? current : fallback;
  } catch {
    return fallback;
  }
}

/**
 * Type-safe error handler
 */
export function handleError(error: unknown): {
  message: string;
  code?: string;
  type: string;
} {
  if (error instanceof Error) {
    return {
      message: error.message,
      code: (error as any).code,
      type: 'Error'
    };
  }
  
  if (typeof error === 'string') {
    return {
      message: error,
      type: 'string'
    };
  }
  
  if (error && typeof error === 'object') {
    const errorObj = error as any;
    return {
      message: errorObj.message || errorObj.error || 'Unknown error',
      code: errorObj.code,
      type: 'object'
    };
  }
  
  return {
    message: 'Unknown error occurred',
    type: 'unknown'
  };
}

/**
 * Database operation with fallback
 */
export async function withDatabaseFallback<T>(
  primaryOperation: () => Promise<T>,
  fallbackOperation: () => Promise<T>,
  options: {
    primaryName?: string;
    fallbackName?: string;
    shouldUseFallback?: (error: any) => boolean;
  } = {}
): Promise<ErrorRecoveryResult<T>> {
  const {
    primaryName = 'primary',
    fallbackName = 'fallback',
    shouldUseFallback = (error) => error.code === 'PGRST203' || error.code === '42883'
  } = options;

  try {
    console.log(`[ErrorRecovery] Trying ${primaryName} operation`);
    const result = await primaryOperation();
    return {
      success: true,
      data: result,
      method: primaryName,
      fallbackUsed: false
    };
  } catch (error: any) {
    console.warn(`[ErrorRecovery] ${primaryName} failed:`, error.message);
    
    if (shouldUseFallback(error)) {
      try {
        console.log(`[ErrorRecovery] Trying ${fallbackName} operation`);
        const fallbackResult = await fallbackOperation();
        return {
          success: true,
          data: fallbackResult,
          method: fallbackName,
          fallbackUsed: true
        };
      } catch (fallbackError: any) {
        console.error(`[ErrorRecovery] ${fallbackName} also failed:`, fallbackError.message);
        return {
          success: false,
          error: `Both ${primaryName} and ${fallbackName} failed: ${fallbackError.message}`,
          method: 'both_failed',
          fallbackUsed: true
        };
      }
    }
    
    return {
      success: false,
      error: error.message,
      method: primaryName,
      fallbackUsed: false
    };
  }
}

/**
 * API call with multiple endpoint fallback
 */
export async function withApiEndpointFallback<T>(
  endpoints: string[],
  requestOptions: RequestInit,
  responseHandler: (response: Response) => Promise<T>
): Promise<ErrorRecoveryResult<T>> {
  let lastError: any;
  
  for (let i = 0; i < endpoints.length; i++) {
    const endpoint = endpoints[i];
    
    try {
      console.log(`[ErrorRecovery] Trying API endpoint ${i + 1}/${endpoints.length}: ${endpoint}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(endpoint, {
        ...requestOptions,
        signal: controller.signal
      }).finally(() => clearTimeout(timeoutId));
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await responseHandler(response);
      return {
        success: true,
        data,
        method: `endpoint_${i + 1}`,
        fallbackUsed: i > 0
      };
      
    } catch (error: any) {
      lastError = error;
      console.warn(`[ErrorRecovery] Endpoint ${endpoint} failed:`, error.message);
      
      // Don't continue if it's an abort error and we have more endpoints
      if (error.name === 'AbortError' && i < endpoints.length - 1) {
        continue;
      }
    }
  }
  
  return {
    success: false,
    error: lastError?.message || 'All API endpoints failed',
    method: 'all_endpoints_failed',
    fallbackUsed: true
  };
}

/**
 * Worker operation with fallback
 */
export async function withWorkerFallback<T>(
  workerOperation: () => Promise<T>,
  mainThreadFallback: () => Promise<T>
): Promise<ErrorRecoveryResult<T>> {
  try {
    console.log('[ErrorRecovery] Trying worker operation');
    const result = await workerOperation();
    return {
      success: true,
      data: result,
      method: 'worker',
      fallbackUsed: false
    };
  } catch (error: any) {
    console.warn('[ErrorRecovery] Worker failed, using main thread:', error.message);
    
    try {
      const fallbackResult = await mainThreadFallback();
      return {
        success: true,
        data: fallbackResult,
        method: 'main_thread',
        fallbackUsed: true
      };
    } catch (fallbackError: any) {
      return {
        success: false,
        error: `Both worker and main thread failed: ${fallbackError.message}`,
        method: 'both_failed',
        fallbackUsed: true
      };
    }
  }
}
