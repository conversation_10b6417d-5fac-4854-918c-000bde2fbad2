
import { BaseService } from '../base/BaseService';
import { Student, StudentWithProfiles } from '@/types/auth';
import { StudentRPCResponse } from './types';

export class StudentRepository extends BaseService {
  
  async getStudentsByGuardianEmail(guardianEmail: string): Promise<Student[]> {
    try {
      // First, get the guardian ID from profiles table
      const { data: guardianProfile, error: guardianError } = await this.supabase
        .from('profiles')
        .select('id')
        .eq('email', guardianEmail)
        .single();
      
      if (guardianError || !guardianProfile) {
        console.error('Error fetching guardian profile:', guardianError);
        return [];
      }
      
      // Query student_guardian_relationships table
      const { data: relationships, error: relationshipsError } = await this.supabase
        .from('student_guardian_relationships')
        .select('student_id')
        .eq('guardian_id', guardianProfile.id.toString());
      
      if (relationshipsError) {
        console.error('Error fetching guardian relationships:', relationshipsError);
        throw relationshipsError;
      }
      
      if (!relationships || relationships.length === 0) {
        return [];
      }
      
      const studentIds = relationships
        .map(rel => rel.student_id)
        .filter(id => id !== null) as string[];
      
      if (studentIds.length === 0) {
        return [];
      }
      
      // Get student profiles
      const { data: profiles, error: profilesError } = await this.supabase
        .from('profiles')
        .select('*')
        .in('user_id', studentIds);
      
      if (profilesError) {
        console.error('Error fetching student profiles:', profilesError);
        throw profilesError;
      }
      
      return profiles?.map(profile => ({
        id: String(profile.id) || '',
        name: profile.full_name || '',
        email: profile.email || '',
        created_at: profile.created_at || '',
        status: 'active' as const,
        avatar_url: null,
        phone: profile.phone || null
      })) || [];
      
    } catch (error) {
      console.error('Error in getStudentsByGuardianEmail:', error);
      throw error;
    }
  }
  
  async getStudentsWithProfiles(guardianEmail: string): Promise<StudentWithProfiles[]> {
    const students = await this.getStudentsByGuardianEmail(guardianEmail);
    
    return students.map(student => ({
      ...student,
      user_profiles: {
        name: student.name,
        email: student.email
      }
    }));
  }
}

export const studentRepository = new StudentRepository();
