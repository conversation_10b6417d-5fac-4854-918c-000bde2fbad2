import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';
import { useTranslation } from 'react-i18next'; // Added

interface LogoutButtonProps {
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children?: React.ReactNode;
  'aria-label'?: string; // Added
}

export const LogoutButton: React.FC<LogoutButtonProps> = ({ 
  className = '', 
  variant = 'destructive',
  size = 'default',
  children,
  ...props
}) => {
  const navigate = useNavigate();
  const { signOut } = useUser();
  const { t } = useTranslation(); // Added

  const handleLogout = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      console.log('LogoutButton: Iniciando processo de logout...');
      
      // Call signOut from UserContext - it handles redirection
      await signOut();
    } catch (error) {
      console.error('LogoutButton: Erro ao fazer logout:', error);
      // signOut already handles redirection on error
    }
  };

  // Se children for fornecido, use-o; caso contrário, use o conteúdo padrão
  const buttonContent = children || (
    <>
      {size === 'icon' ? (
        <LogOut className="h-5 w-5" />
      ) : (
        <>
          <LogOut className="h-5 w-5 mr-2" />
          {t('common.logout')}
        </>
      )}
    </>
  );

  return (
    <Button 
      onClick={handleLogout} 
      className={className}
      variant={variant}
      size={size}
      aria-label={props['aria-label'] || t('common.logout')}
      {...props}
    >
      {buttonContent}
    </Button>
  );
};

export default LogoutButton;
