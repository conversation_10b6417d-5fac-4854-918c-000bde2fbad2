
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, CheckCircle, Database, Users, Settings, RefreshCw } from "lucide-react";

interface DiagnosticResult {
  name: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  details?: any;
}

const DiagnosticTool = () => {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runDiagnostics = async () => {
    setIsRunning(true);
    const diagnostics: DiagnosticResult[] = [];

    // Test 1: Database Connection
    try {
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      if (error) throw error;
      diagnostics.push({
        name: 'Database Connection',
        status: 'success',
        message: 'Successfully connected to database'
      });
    } catch (error: any) {
      diagnostics.push({
        name: 'Database Connection',
        status: 'error',
        message: `Database connection failed: ${error.message}`
      });
    }

    // Test 2: Authentication
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        diagnostics.push({
          name: 'Authentication',
          status: 'success',
          message: `Authenticated as: ${user.email}`
        });
      } else {
        diagnostics.push({
          name: 'Authentication',
          status: 'warning',
          message: 'No authenticated user'
        });
      }
    } catch (error: any) {
      diagnostics.push({
        name: 'Authentication',
        status: 'error',
        message: `Authentication check failed: ${error.message}`
      });
    }

    // Test 3: Student-Guardian Relationships (using correct table)
    try {
      const { data: relationships, error } = await supabase
        .from('student_guardian_relationships')
        .select('*')
        .limit(5);
      
      if (error) throw error;
      
      diagnostics.push({
        name: 'Student-Guardian Relationships',
        status: 'success',
        message: `Found ${relationships?.length || 0} relationships`,
        details: relationships
      });
    } catch (error: any) {
      diagnostics.push({
        name: 'Student-Guardian Relationships',
        status: 'error',
        message: `Failed to fetch relationships: ${error.message}`
      });
    }

    // Test 4: RPC Functions
    try {
      const { data, error } = await supabase.rpc('get_guardian_students');
      diagnostics.push({
        name: 'RPC Functions',
        status: error ? 'warning' : 'success',
        message: error ? `RPC function error: ${error.message}` : 'RPC functions accessible'
      });
    } catch (error: any) {
      diagnostics.push({
        name: 'RPC Functions',
        status: 'error',
        message: `RPC test failed: ${error.message}`
      });
    }

    // Test 5: Locations
    try {
      const { data: locations, error } = await supabase
        .from('locations')
        .select('*')
        .limit(5);
      
      if (error) throw error;
      
      diagnostics.push({
        name: 'Location Data',
        status: 'success',
        message: `Found ${locations?.length || 0} location records`,
        details: locations
      });
    } catch (error: any) {
      diagnostics.push({
        name: 'Location Data',
        status: 'error',
        message: `Failed to fetch locations: ${error.message}`
      });
    }

    setResults(diagnostics);
    setIsRunning(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                System Diagnostics
              </CardTitle>
              <CardDescription>
                Check system health and connectivity
              </CardDescription>
            </div>
            <Button onClick={runDiagnostics} disabled={isRunning}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isRunning ? 'animate-spin' : ''}`} />
              {isRunning ? 'Running...' : 'Run Diagnostics'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {results.map((result, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <h3 className="font-medium">{result.name}</h3>
                  </div>
                  <Badge className={getStatusColor(result.status)}>
                    {result.status.toUpperCase()}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-2">{result.message}</p>
                {result.details && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm font-medium">View Details</summary>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DiagnosticTool;

