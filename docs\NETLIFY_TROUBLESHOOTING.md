# Guia de Solução de Problemas no Deploy via Netlify

Este guia aborda passos básicos para investigar erros genéricos como **"Unidentified error"** ao publicar o frontend no Netlify.

## 1. Verificar logs de build
Acesse o painel do Netlify e abra os detalhes do deploy. Mesmo que a interface mostre apenas uma mensagem genérica, o log completo costuma indicar a etapa que falhou (ex.: dependência ausente ou comando incorreto).

## 2. Conferir variáveis de ambiente
Certifique-se de que todas as variáveis usadas no `vite.config.ts` e em `src/lib/supabase.ts` estejam configuradas em **Site settings → Environment variables**. Valores incorretos ou ausentes geralmente interrompem o build.

## 3. Executar o build localmente
Rode:
```bash
npm install
npm run build
```
Se o build local falhar, corrija os erros antes de tentar novamente no Netlify.

## 4. Verificar status da plataforma
Problemas temporários nos serviços do Netlify podem gerar erros sem detalhes. Consulte [status.netlify.com](https://status.netlify.com) e aguarde alguns minutos antes de reexecutar o deploy.

## 5. Contatar o suporte
Se nenhuma das etapas acima resolver, abra um ticket no suporte do Netlify informando o **Deploy ID** e o trecho relevante dos logs. Eles podem verificar problemas não visíveis pela interface.

Seguindo estes passos, a maioria dos erros genéricos de publicação costuma ser resolvida ou ao menos esclarecida.
