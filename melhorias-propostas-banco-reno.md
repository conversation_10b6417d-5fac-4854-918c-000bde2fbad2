# Melhorias Propostas para o Banco de Dados RENO

**Data:** 04/06/2025
**Autor:** <PERSON><PERSON>
**Projeto:** Locate-Family-Connect
**Atualizado em:** 04/06/2025 - 08:45

## 1. Status Atual

Com base na análise do banco de dados e dos requisitos do projeto, identificamos as seguintes oportunidades de melhoria para o banco de dados RENO:

### 1.1 Tabela Guardians
- ✅ Campo `cpf` já existe, mas é nullable e não tem validação específica de trigger
- ✅ Campo `is_active` já existe como boolean, mas precisamos de um campo `status` mais versátil
- ❌ Campo `birth_date` não existe e deve ser adicionado

### 1.2 Tabela Profiles
- ✅ Campo `cpf` já existe e tem validação rigorosa via trigger
- ✅ Campo `birth_date` já existe
- ❌ Campo `status` não existe formalmente (apenas registration_status)
- ❌ Falta monitoramento de atividade dos usuários (login, contadores)

## 2. Melhorias Implementadas

As seguintes melhorias foram implementadas com sucesso em 04/06/2025:

### 2.1 Tabela Guardians

| Campo        | Tipo  | Descrição                                    | Status      |
|--------------|-------|----------------------------------------------|-------------|
| birth_date   | DATE  | Data de nascimento do guardião               | ✅ Implementado  |
| status       | TEXT  | Status do guardião (active, inactive, etc)   | ✅ Implementado  |

**Índices adicionados:**
- `idx_guardians_status` - Índice para consultas por status - ✅ Implementado

### 2.2 Tabela Profiles

| Campo         | Tipo        | Descrição                                | Status        |
|---------------|-------------|------------------------------------------|---------------|  
| status        | TEXT        | Status formal do perfil                  | ✅ Implementado |
| last_login_at | TIMESTAMPTZ | Registro do último login                | ✅ Implementado |
| login_count   | INTEGER     | Contador de logins                      | ✅ Implementado |

**Índices adicionados:**
- `idx_profiles_status` - Otimiza consultas por status - ✅ Implementado
- `idx_profiles_user_type_status` - Otimiza consultas combinando tipo e status - ✅ Implementado

### 2.3 Melhorias de Validação

- ✅ **Validação de CPF**: A função `is_valid_cpf()` foi aprimorada para permitir exceções para contas administrativas e de desenvolvimento

## 3. Melhorias Pendentes

As seguintes melhorias ainda precisam ser implementadas:

### 3.1 Outras melhorias propostas:

- **Trigger para Atualização Automática**: Implementar trigger para atualizar automaticamente o campo `last_login_at` quando o usuário fizer login
- **Relatório de Atividade**: Tabela ou view para monitoramento de atividade dos usuários
- **Ajuste de RLS**: Revisar políticas para garantir proteção dos novos campos adicionados
- **Notificação de Inatividade**: Sistema para detectar e alertar sobre contas inativas

## 4. Desafios Encontrados e Superados

Durante a implementação, identificamos os seguintes desafios:

- **✅ Trigger de CPF**: A função `validate_cpf_trigger()` estava configurada para validar rigorosamente CPFs, causando erros em atualizações. Este problema foi resolvido com a implementação de exceções para usuários administrativos/desenvolvedores.

- **✅ CPFs Fictícios**: Dois perfis administrativos utilizavam CPFs fictícios (111.111.111-11 e 222.222.222-22) que não passavam na validação rigorosa. Implementamos uma solução que permite esses CPFs específicos para contas de sistema, mantendo a validação para usuários normais.

- **Dependências entre tabelas**: Alterações em `profiles` podem impactar outras tabelas e triggers, exigindo cuidado nas modificações.

- **Dados existentes**: Os guardiões estão com CPFs nulos, o que precisa ser considerado em futuras modificações que possam torná-los obrigatórios.

## 5. Plano de Ação Atualizado

Para implementar as melhorias pendentes de forma segura, recomendamos:

1. **Implementar trigger de last_login_at**: Criar um trigger que automaticamente atualize o campo last_login_at quando o usuário fizer login, aproveitando a estrutura já disponível no banco.

2. **Avaliar preenchimento de CPF para guardians**: Determinar se os CPFs dos guardiões devem ser preenchidos retroativamente ou mantidos como nulos.

3. **Validação de campos críticos**: Considerar adicionar validações para os novos campos, garantindo integridade dos dados.

4. **Atualização de documentação técnica**: Manter a documentação do banco atualizada com as novas estruturas e validações.

5. **Backup periódico**: Implementar uma rotina automatizada de backup do banco de dados.

6. **Monitoramento de performance**: Estabelecer métricas para monitorar a performance do banco após as alterações.

## 6. Recomendações Adicionais com Base no PRD

Considerando os objetivos e métricas descritos no PRD, recomendamos adicionar os seguintes campos:

1. **Em profiles e guardians:**
   - `preferences` (JSONB): Armazenar preferências do usuário como notificações, privacidade etc.
   - `device_info` (JSONB): Informações sobre o dispositivo mais usado
   - `notification_settings` (JSONB): Controles granulares de notificação

2. **Em locations:**
   - `metadata` (JSONB): Dados adicionais capturados (tipo de conexão, precisão, etc.)
   - `created_by` (UUID): Rastreamento de quem gerou o registro
   - `expiry_time` (TIMESTAMPTZ): Quando o compartilhamento expira

3. **Tabelas adicionais:**
   - `user_activity_logs`: Monitorar ações importantes no sistema
   - `notification_templates`: Templates para emails e notificações
   - `geofences`: Definir perímetros virtuais para alertas (conforme roadmap do PRD)

## 7. Conclusão

As melhorias propostas estão alinhadas com os objetivos do projeto conforme descritos no PRD e nos documentos de requisitos. A maior parte das melhorias inicialmente planejadas foram implementadas com sucesso em 04/06/2025:

✅ **Implementado**:
- Adição de campos birth_date e status na tabela guardians
- Adição de campos status, last_login_at e login_count na tabela profiles
- Novos índices para otimização de consultas
- Melhoria na validação de CPF para permitir exceções para contas de sistema

🔄 **Pendente**:
- Trigger para atualização automática de last_login_at
- Sistema de monitoramento de atividades
- Preenchimento dos CPFs dos guardiões
- Funcionalidades de geofencing mencionadas no roadmap do PRD

As melhorias já implementadas contribuem significativamente para o monitoramento, performance e segurança do sistema, além de possibilitar as funcionalidades planejadas para as próximas iterações do produto. Com esta base sólida, o sistema Locate-Family-Connect está bem posicionado para evoluir conforme as necessidades do projeto.
