{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "lib": ["ES2020", "DOM"], "types": [], "typeRoots": ["./node_modules/@types", "./"], "allowJs": true, "checkJs": false, "noEmit": true, "isolatedModules": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["**/*.ts", "**/*.js", "deno.d.ts"], "exclude": ["node_modules"]}