-- =============================================
-- SCRIPT: Correção da função RPC para usar 'profiles'
-- =============================================
-- A função estava falhando porque usa 'user_profiles' mas a tabela é 'profiles'

-- 1. Dropar função atual com problema
DROP FUNCTION IF EXISTS public.request_guardian_removal(UUID, TEXT);

-- 2. Recriar função corrigida usando 'profiles'
CREATE OR REPLACE FUNCTION public.request_guardian_removal(
    p_guardian_id UUID,
    p_reason TEXT DEFAULT 'Solicitação de remoção'
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_student_id UUID;
    v_student_name TEXT;
    v_guardian_name TEXT;
    v_guardian_email TEXT;
    v_request_id UUID;
    v_request_token UUID;
    v_relationship_exists BOOLEAN := FALSE;
BEGIN
    -- Verificar usuário atual
    v_student_id := auth.uid();
    
    IF v_student_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'message', 'Usuário não autenticado'
        );
    END IF;
    
    -- Verificar se existe relação (USANDO TABELA PROFILES!)
    SELECT COUNT(*) > 0 INTO v_relationship_exists
    FROM student_guardian_relationships sgr
    WHERE sgr.student_id = v_student_id 
      AND sgr.guardian_id = p_guardian_id;
    
    IF NOT v_relationship_exists THEN
        RETURN json_build_object(
            'success', false,
            'message', 'Relação não encontrada'
        );
    END IF;
    
    -- Buscar nomes (USANDO TABELA PROFILES!)
    SELECT s.full_name INTO v_student_name
    FROM profiles s 
    WHERE s.user_id = v_student_id;
    
    SELECT g.full_name, g.email INTO v_guardian_name, v_guardian_email
    FROM profiles g 
    WHERE g.user_id = p_guardian_id;
    
    -- Gerar IDs únicos
    v_request_id := uuid_generate_v4();
    v_request_token := uuid_generate_v4();
    
    -- Inserir solicitação
    INSERT INTO removal_requests (
        id,
        student_id,
        guardian_id,
        student_name,
        guardian_name,
        guardian_email,
        reason,
        status,
        request_token,
        created_at,
        expires_at
    ) VALUES (
        v_request_id,
        v_student_id,
        p_guardian_id,
        COALESCE(v_student_name, 'Estudante'),
        COALESCE(v_guardian_name, 'Responsável'),
        COALESCE(v_guardian_email, ''),
        p_reason,
        'pending',
        v_request_token,
        NOW(),
        NOW() + INTERVAL '7 days'
    );
    
    RETURN json_build_object(
        'success', true,
        'message', 'Solicitação criada com sucesso',
        'request_id', v_request_id,
        'token', v_request_token
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'message', 'Erro interno: ' || SQLERRM
        );
END;
$$;

-- 3. Verificar se a função foi criada
SELECT 
    'Função request_guardian_removal criada com sucesso' as status,
    COUNT(*) as function_count
FROM pg_proc p 
JOIN pg_namespace n ON p.pronamespace = n.oid 
WHERE n.nspname = 'public' 
AND p.proname = 'request_guardian_removal'; 