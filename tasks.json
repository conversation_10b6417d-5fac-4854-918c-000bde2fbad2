{"tasks": [{"id": 1, "title": "Fundamentos PWA e Funcionamento Offline", "steps": ["Criar public/manifest.json com nome, ícones e configurações", "Adicionar meta tags PWA em index.html", "Implementar public/sw.js e registrar via src/lib/sw-registration.ts e hook useServiceWorker.ts", "Desenvolver componentes NetworkStatus e OfflineBanner"]}, {"id": 2, "title": "Cache de Dados e Fila de Sincronização", "steps": ["Implementar offline-storage.ts usando IndexedDB", "Criar hook useOfflineData.ts", "Desenvolver sync-queue.ts e useSyncQueue.ts", "Integrar sincronização automática ao voltar a rede"]}, {"id": 3, "title": "Rastreamento em Segundo Plano e Geofencing", "steps": ["Estender useStudentLocation.ts para registrar localizações periódicas", "Permitir background tracking via Service Worker ou APIs nativas", "Implementar tabela e lógica de geofences", "Enviar notificações ou emails ao cruzar limites"]}, {"id": 4, "title": "Notificações Push e Botão de SOS", "steps": ["Configurar serviço de push e pedir permissão", "Criar componente de gerenciamento de notificações", "Implementar botão de SOS no dashboard do estudante"]}, {"id": 5, "title": "Otimizações Finais e Documentação", "steps": ["Cache de mapas do Mapbox para regiões frequentes", "Adicionar métricas de uso offline e relatórios de sincronização", "Revisar performance geral", "Atualizar README e guias com instruções PWA e offline"]}]}