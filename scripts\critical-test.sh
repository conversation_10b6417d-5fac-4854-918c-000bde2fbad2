
#!/bin/bash

# 🛡️ Critical Test - Testes de Funcionalidades Críticas
# Protocolo Anti-Quebra - Fase 2: Verificação Funcional

echo "🧪 [CRITICAL-TEST] Iniciando testes de funcionalidades críticas..."
echo "📅 Data: $(date)"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Contadores
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_SKIPPED=0

# Função para log
log_test() {
    local status=$1
    local test_name=$2
    local details=$3
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name"
        [ ! -z "$details" ] && echo "   📋 $details"
        ((TESTS_PASSED++))
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ FAIL${NC}: $test_name"
        [ ! -z "$details" ] && echo "   💥 $details"
        ((TESTS_FAILED++))
    elif [ "$status" = "SKIP" ]; then
        echo -e "${YELLOW}⏭️  SKIP${NC}: $test_name"
        [ ! -z "$details" ] && echo "   ⚠️  $details"
        ((TESTS_SKIPPED++))
    fi
}

# 1. Teste de Build Crítico
echo ""
echo -e "${BLUE}🏗️  TESTANDO BUILD CRÍTICO${NC}"
echo "================================"

if npm run build &> build.log; then
    log_test "PASS" "Build compilou sem erros"
    
    # Verificar arquivos essenciais no build
    if [ -f "dist/index.html" ]; then
        log_test "PASS" "index.html gerado"
    else
        log_test "FAIL" "index.html não encontrado no build"
    fi
    
    # Verificar se há arquivos JS
    JS_FILES=$(find dist -name "*.js" | wc -l)
    if [ $JS_FILES -gt 0 ]; then
        log_test "PASS" "Arquivos JavaScript gerados ($JS_FILES arquivos)"
    else
        log_test "FAIL" "Nenhum arquivo JavaScript encontrado"
    fi
    
    # Verificar se há arquivos CSS
    CSS_FILES=$(find dist -name "*.css" | wc -l)
    if [ $CSS_FILES -gt 0 ]; then
        log_test "PASS" "Arquivos CSS gerados ($CSS_FILES arquivos)"
    else
        log_test "FAIL" "Nenhum arquivo CSS encontrado"
    fi
    
else
    log_test "FAIL" "Build falhou - veja build.log para detalhes"
    cat build.log | tail -20
fi

# 2. Teste de Dependências Críticas
echo ""
echo -e "${BLUE}📦 TESTANDO DEPENDÊNCIAS CRÍTICAS${NC}"
echo "===================================="

CRITICAL_DEPS=(
    "react"
    "react-dom"
    "react-router-dom"
    "@supabase/supabase-js"
    "mapbox-gl"
    "tailwindcss"
    "vite"
)

for dep in "${CRITICAL_DEPS[@]}"; do
    if npm list "$dep" &> /dev/null; then
        VERSION=$(npm list "$dep" --depth=0 2>/dev/null | grep "$dep" | cut -d@ -f2)
        log_test "PASS" "Dependência $dep instalada" "Versão: $VERSION"
    else
        log_test "FAIL" "Dependência crítica ausente: $dep"
    fi
done

# 3. Teste de Arquivos de Contexto
echo ""
echo -e "${BLUE}🔒 TESTANDO CONTEXTOS DE AUTENTICAÇÃO${NC}"
echo "======================================"

AUTH_FILES=(
    "src/contexts/UnifiedAuthContext.tsx"
    "src/lib/supabase.ts"
    "src/lib/auth-redirects.ts"
)

for file in "${AUTH_FILES[@]}"; do
    if [ -f "$file" ]; then
        # Verificar se não há erros de sintaxe TypeScript
        if npx tsc --noEmit "$file" &> /dev/null; then
            log_test "PASS" "Arquivo de autenticação válido: $file"
        else
            log_test "FAIL" "Erro de TypeScript em: $file"
        fi
    else
        log_test "FAIL" "Arquivo de autenticação ausente: $file"
    fi
done

# 4. Teste de Componentes de Mapa
echo ""
echo -e "${BLUE}🗺️  TESTANDO COMPONENTES DE MAPA${NC}"
echo "=================================="

MAP_FILES=(
    "src/components/map/MapView.tsx"
    "src/components/map/MapFallback.tsx"
    "src/hooks/useMapInitialization.tsx"
    "src/hooks/useMapMarkers.tsx"
)

for file in "${MAP_FILES[@]}"; do
    if [ -f "$file" ]; then
        if npx tsc --noEmit "$file" &> /dev/null; then
            log_test "PASS" "Componente de mapa válido: $file"
        else
            log_test "FAIL" "Erro de TypeScript em: $file"
        fi
    else
        log_test "FAIL" "Componente de mapa ausente: $file"
    fi
done

# 5. Teste de Configuração de Ambiente
echo ""
echo -e "${BLUE}⚙️  TESTANDO CONFIGURAÇÃO DE AMBIENTE${NC}"
echo "====================================="

CONFIG_FILES=(
    "vite.config.ts"
    "tailwind.config.js"
    "tsconfig.json"
    "package.json"
)

for file in "${CONFIG_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_test "PASS" "Arquivo de configuração existe: $file"
        
        # Validação específica por tipo
        case "$file" in
            "package.json")
                if jq empty "$file" &> /dev/null; then
                    log_test "PASS" "package.json tem JSON válido"
                else
                    log_test "FAIL" "package.json tem JSON inválido"
                fi
                ;;
            "tsconfig.json")
                if npx tsc --noEmit --project "$file" &> /dev/null; then
                    log_test "PASS" "tsconfig.json válido"
                else
                    log_test "FAIL" "tsconfig.json inválido"
                fi
                ;;
        esac
    else
        log_test "FAIL" "Arquivo de configuração ausente: $file"
    fi
done

# 6. Teste de Rotas Críticas
echo ""
echo -e "${BLUE}🛣️  TESTANDO ROTAS CRÍTICAS${NC}"
echo "============================="

ROUTE_FILES=(
    "src/App.tsx"
    "src/pages/Login.tsx"
    "src/pages/StudentDashboard.tsx"
    "src/pages/ParentDashboard.tsx"
)

for file in "${ROUTE_FILES[@]}"; do
    if [ -f "$file" ]; then
        if npx tsc --noEmit "$file" &> /dev/null; then
            log_test "PASS" "Arquivo de rota válido: $file"
        else
            log_test "FAIL" "Erro de TypeScript em: $file"
        fi
    else
        log_test "FAIL" "Arquivo de rota ausente: $file"
    fi
done

# 7. Teste de Componentes de Formulário
echo ""
echo -e "${BLUE}📝 TESTANDO COMPONENTES DE FORMULÁRIO${NC}"
echo "======================================"

FORM_FILES=(
    "src/components/LoginForm.tsx"
    "src/components/RegisterForm.tsx"
    "src/components/auth/PasswordField.tsx"
)

for file in "${FORM_FILES[@]}"; do
    if [ -f "$file" ]; then
        if npx tsc --noEmit "$file" &> /dev/null; then
            log_test "PASS" "Componente de formulário válido: $file"
        else
            log_test "FAIL" "Erro de TypeScript em: $file"
        fi
    else
        log_test "FAIL" "Componente de formulário ausente: $file"
    fi
done

# 8. Teste de Jest (se disponível)
echo ""
echo -e "${BLUE}🧪 TESTANDO SUITE DE TESTES${NC}"
echo "============================="

if [ -f "jest.config.cjs" ]; then
    if npm test -- --passWithNoTests --silent &> jest.log; then
        log_test "PASS" "Suite de testes executada com sucesso"
    else
        log_test "FAIL" "Falha na suite de testes - veja jest.log"
    fi
else
    log_test "SKIP" "Jest não configurado"
fi

# Cleanup
rm -f build.log jest.log

# Resumo Final
echo ""
echo "📊 RESUMO DOS TESTES CRÍTICOS"
echo "=============================="
echo -e "${GREEN}✅ Testes passaram: $TESTS_PASSED${NC}"
echo -e "${YELLOW}⏭️  Testes ignorados: $TESTS_SKIPPED${NC}"
echo -e "${RED}❌ Testes falharam: $TESTS_FAILED${NC}"

# Determinar status final
if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 FUNCIONALIDADES CRÍTICAS OK - Sistema estável!${NC}"
    exit 0
else
    echo -e "\n${RED}🚨 FUNCIONALIDADES CRÍTICAS FALHARAM!${NC}"
    echo "💥 Não é seguro fazer mudanças - corrija os problemas primeiro"
    exit 1
fi
