# Erro de Permissão em `account_deletion_requests`

## Contexto
Ao acessar a página de perfil do responsável, o console exibe vários erros `403 (Forbidden)` relacionados à rota Supabase de `account_deletion_requests`.

```
permission denied for table account_deletion_requests
```

## <PERSON>ausa Provável
As políticas de Row Level Security (RLS) ou as permissões do usuário não permitem leitura da tabela. A função `get_guardian_deletion_requests` tenta acessar a tabela mas não possui privilégios suficientes.

## Impacto
Responsáveis não conseguem visualizar solicitações de exclusão de contas de estudantes.

## Recomendação
Solicitar ao administrador do Supabase que verifique:

1. As regras de RLS da tabela `account_deletion_requests`, garantindo permissão de seleção para o papel apropriado.
2. As permissões da função `get_guardian_deletion_requests`, que deve ser executada com privilégios suficientes (idealmente service role).

Com as políticas ajustadas, o frontend deve carregar corretamente as solicitações de exclusão.
