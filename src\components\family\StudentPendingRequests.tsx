import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Info, Clock, Mail } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { familyInvitationService } from '@/lib/services/family/FamilyInvitationService';
import { useToast } from '@/components/ui/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { cn } from '@/lib/utils';

interface StudentPendingRequest {
  invitation_id: string;
  guardian_email: string;
  invitation_token: string;
  created_at: string;
  expires_at: string;
}

export function StudentPendingRequests({ className = '' }: { className?: string }) {
  const [requests, setRequests] = useState<StudentPendingRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [processingTokens, setProcessingTokens] = useState<Set<string>>(new Set());
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  // Load pending requests
  const loadPendingRequests = async () => {
    try {
      setIsLoading(true);
      const pendingRequests = await familyInvitationService.getStudentPendingRequests();
      console.log('[StudentPendingRequests] Loaded requests:', pendingRequests);
      setRequests(pendingRequests);
    } catch (error) {
      console.error('[StudentPendingRequests] Error loading requests:', error);
      toast({
        title: t('studentPendingRequests.error', '❌ Error'),
        description: t('studentPendingRequests.loadError', 'Could not load pending requests.'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPendingRequests();
  }, []);

  const handleAccept = async (token: string) => {
    if (processingTokens.has(token)) return;

    setProcessingTokens(prev => new Set(prev).add(token));
    
    try {
      const result = await familyInvitationService.acceptStudentRequest(token);
      
      if (result.success) {
        toast({
          title: t('studentPendingRequests.requestAccepted', '✅ Request accepted!'),
          description: t('studentPendingRequests.guardianCanTrack', 'The guardian can now track your location.'),
          variant: "default",
        });
        
        // Refresh data
        queryClient.invalidateQueries({ queryKey: ['student-pending-requests'] });
        queryClient.invalidateQueries({ queryKey: ['guardians'] });
        
        // Reload the list to show updated state
        await loadPendingRequests();
        
      } else {
        toast({
          title: t('studentPendingRequests.acceptError', '❌ Error accepting'),
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('[StudentPendingRequests] Error accepting request:', error);
      toast({
        title: t('studentPendingRequests.error', '❌ Error'),
        description: t('studentPendingRequests.acceptRetry', 'Could not accept the request. Try again.'),
        variant: "destructive",
      });
    } finally {
      setProcessingTokens(prev => {
        const newSet = new Set(prev);
        newSet.delete(token);
        return newSet;
      });
    }
  };

  const handleReject = async (token: string) => {
    if (processingTokens.has(token)) return;

    setProcessingTokens(prev => new Set(prev).add(token));

    // Optimistic update: remove request locally before confirming with server
    setRequests(prev => prev.filter(r => r.invitation_token !== token));

    try {
      const result = await familyInvitationService.rejectStudentInvitation(token);

      if (result.success) {
        toast({
          title: t('studentPendingRequests.requestRejected', '🚫 Request rejected'),
          description: t('studentPendingRequests.guardianNotified', 'The guardian has been notified of the refusal.'),
          variant: "default",
        });

        // Refresh queries
        queryClient.invalidateQueries({ queryKey: ['student-pending-requests'] });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('[StudentPendingRequests] Error rejecting request:', error);
      toast({
        title: t('studentPendingRequests.rejectError', '❌ Error rejecting'),
        description: error instanceof Error ? error.message : t('studentPendingRequests.rejectFailed', 'Could not reject the request.'),
        variant: "destructive",
      });

      // Revert optimistic update
      await loadPendingRequests();
    } finally {
      setProcessingTokens(prev => {
        const newSet = new Set(prev);
        newSet.delete(token);
        return newSet;
      });
    }
  };

  const formatDate = (dateString: string) => {
    const locale = t('locale', 'en-GB');
    return new Date(dateString).toLocaleString(locale, {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <Card className={cn(className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {t('studentPendingRequests.title', 'Guardian Requests')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">{t('studentPendingRequests.loading', 'Loading requests...')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!requests || requests.length === 0) {
    return (
      <Card className={cn(className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {t('studentPendingRequests.title', 'Guardian Requests')}
          </CardTitle>
          <CardDescription>
            {t('studentPendingRequests.description', 'Requests from guardians who want to connect to your account')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              {t('studentPendingRequests.noPending', 'You have no pending guardian requests at the moment.')}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          {t('studentPendingRequests.titleWithCount', 'Guardian Requests ({{count}})', { count: requests.length })}
        </CardTitle>
        <CardDescription>
          {t('studentPendingRequests.description', 'Requests from guardians who want to connect to your account')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {requests.map((request) => {
          const isProcessing = processingTokens.has(request.invitation_token);
          
          return (
            <div key={request.invitation_id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-blue-500 mt-1" />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{request.guardian_email}</h4>
                  <p className="text-sm text-gray-600">
                    {t('studentPendingRequests.receivedAt', 'Request received on: {{date}}', { date: formatDate(request.created_at) })}
                  </p>
                  <p className="text-sm text-gray-600">
                    {t('studentPendingRequests.expiresAt', 'Expires on: {{date}}', { date: formatDate(request.expires_at) })}
                  </p>
                  <p className="text-sm text-gray-700 mt-2">
                    {t('studentPendingRequests.wantsToConnect', 'This person wants to connect as your guardian to track your location.')}
                  </p>
                </div>
              </div>
              
              <div className="flex gap-2 pt-2">
                <Button
                  onClick={() => handleAccept(request.invitation_token)}
                  disabled={isProcessing}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isProcessing ? t('common.processing', 'Processing...') : t('studentPendingRequests.accept', 'Accept')}
                </Button>
                <Button
                  onClick={() => handleReject(request.invitation_token)}
                  disabled={isProcessing}
                  size="sm"
                  variant="outline"
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  {isProcessing ? t('common.processing', 'Processing...') : t('studentPendingRequests.reject', 'Reject')}
                </Button>
              </div>
            </div>
          );
        })}
        
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>{t('studentPendingRequests.howItWorks', 'How it works?')}</strong>
            <br />
            • <strong>{t('studentPendingRequests.accept', 'Accept')}:</strong> {t('studentPendingRequests.acceptDescription', 'The guardian will be linked to your account and can track your location')}
            <br />
            • <strong>{t('studentPendingRequests.reject', 'Reject')}:</strong> {t('studentPendingRequests.rejectDescription', 'The request will be removed and the guardian will not have access')}
            <br />
            • {t('studentPendingRequests.autoExpire', 'Requests automatically expire in 7 days')}
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
} 