
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useStudentDetails } from '@/hooks/useStudentDetails';
import { useLocationData } from '@/hooks/useLocationData';
import LocationHistoryList from '@/components/student/LocationHistoryList';
import StudentMapSection from '@/components/student/StudentMapSection';
import { useDeviceType } from '@/hooks/use-mobile';
import 'mapbox-gl/dist/mapbox-gl.css';
import '@/styles/ios-map.css';



const StudentMap: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useUser();
  const navigate = useNavigate();
  const deviceType = useDeviceType();
  const [selectedStudent, setSelectedStudent] = useState<string | null>(
    id || null
  );
  const [isFullScreen, setIsFullScreen] = useState(false);
  
  // Detectar iOS - MELHORADO
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) || 
              (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  
  // Log específico para iOS - MELHORADO
  useEffect(() => {
    if (isIOS) {
      console.log('[StudentMap] 🍎 iOS detectado - aplicando otimizações específicas');
      console.log('[StudentMap] 🍎 Device info:', {
        deviceType,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
          orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
          devicePixelRatio: window.devicePixelRatio
        },
        safeArea: {
          top: getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-top)'),
          bottom: getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-bottom)')
        }
      });
    }
  }, [isIOS, deviceType]);
  
  // Fetch student details using our hook
  const { studentDetails } = useStudentDetails(
    selectedStudent, 
    user?.email
  );
  
  // Fetch location data using our hook
  const { locationData, loading, error } = useLocationData(
    selectedStudent || user?.id,
    user?.email,
    user?.user_metadata?.user_type
  );

  // Determine map title based on context
  const getMapTitle = () => {
    if (selectedStudent && selectedStudent !== user?.id) {
      return `Localização do ${studentDetails?.name || 'Estudante'}`;
    }
    return 'Minha Localização';
  };

  // Auto-enable full-screen for iOS mobile on mount
  useEffect(() => {
    const device = useDeviceType();
    console.log('[StudentMap] 🍎 Device detection:', { isIOS, device, userAgent: navigator.userAgent });
    if (isIOS && device === 'mobile') {
      setIsFullScreen(true);
      console.log('[StudentMap] 🍎 Auto-enabling full-screen mode for iOS mobile');
    }
  }, [isIOS, deviceType]);

  // Exit full-screen handler
  const handleExitFullScreen = () => {
    setIsFullScreen(false);
    console.log('[StudentMap] 🍎 Exiting full-screen mode');
  };

  // Container classes for full-screen iOS support
  const getContainerClasses = () => {
    const device = useDeviceType();

    if (isFullScreen && isIOS && device === 'mobile') {
      return 'ios-fullscreen-map-container';
    }

    return `${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'space-y-1' : 'space-y-3 md:space-y-6'} pb-16 md:pb-0`;
  };

  const getMapContainerClasses = () => {
    const device = useDeviceType();

    if (isFullScreen && isIOS && device === 'mobile') {
      return 'ios-fullscreen-map w-full h-full';
    }

    return 'w-full map-highlight map-responsive';
  };

  // Full-screen map configuration for iOS
  const getMapContainerStyle = () => {
    const device = useDeviceType();

    if (isFullScreen && isIOS && device === 'mobile') {
      console.log('[StudentMap] 🍎 Using full-screen mode for iOS mobile');
      return {
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        height: '100vh',
        width: '100vw'
      };
    }

    // Default responsive height for other devices
    if (device === 'mobile') {
      return {
        height: '75vh',
        minHeight: '400px'
      };
    }
    if (device === 'tablet') {
      return {
        height: '80vh',
        minHeight: '500px'
      };
    }
    return {
      height: '85vh',
      minHeight: '600px'
    };
  };

  // NOVO: Adicionar data attributes para debugging
  const getMapDataAttributes = () => {
    return {
      'data-ios': isIOS,
      'data-device': deviceType,
      'data-orientation': window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
      'data-fullscreen': isFullScreen
    };
  };

  // Determine user type with proper typing
  const getUserType = (): "student" | "parent" | "teacher" => {
    const userType = user?.user_metadata?.user_type;
    if (userType === 'student' || userType === 'parent' || userType === 'teacher') {
      return userType;
    }
    return 'student'; // default fallback
  };

  return (
    <div className={getContainerClasses()}>
      <div className={`flex ${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'flex-row items-center' : 'flex-col md:flex-row'} justify-between items-start md:items-center gap-1 md:gap-4`}>
        <div className="flex items-center gap-1 md:gap-4">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => navigate(-1)}
            className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'h-6 w-6' : 'h-7 w-7 md:h-9 md:w-9'} ${isIOS ? 'ios-map-button' : ''}`}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'text-base' : deviceType === 'mobile' ? 'text-lg' : 'text-xl md:text-2xl'} font-bold tracking-tight`}>
              {getMapTitle()}
            </h1>
            <p className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'text-xs' : 'text-xs md:text-sm'} text-gray-500`}>
              {selectedStudent && selectedStudent !== user?.id ? 
                `Localização atual e histórico de ${studentDetails?.name || 'estudante'}` :
                'Visualize e compartilhe sua localização atual'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Location history */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'py-1 px-2' : deviceType === 'mobile' ? 'py-2 px-3' : 'py-3 px-4'} border-b`}>
          <h2 className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'text-xs' : deviceType === 'mobile' ? 'text-sm' : 'text-base md:text-lg'} font-semibold`}>
            Histórico de Localizações
          </h2>
        </div>
        <div className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'px-1 py-0.5' : deviceType === 'mobile' ? 'px-2 py-1' : 'px-3 py-2 md:p-4'}`}>
          <LocationHistoryList
            locationData={locationData}
            loading={loading}
            error={error}
            userType={user?.user_metadata?.user_type}
            studentDetails={studentDetails ? { 
              id: selectedStudent || '', 
              name: studentDetails.name, 
              email: studentDetails.email 
            } : undefined}
            senderName={user?.user_metadata?.full_name}
          />
        </div>
      </div>

      {/* Full-screen map component for iOS, responsive for other devices */}
      <div
        className={getMapContainerClasses()}
        style={getMapContainerStyle()}
        {...getMapDataAttributes()}
      >
        <StudentMapSection
          title={getMapTitle()}
          selectedUserId={selectedStudent || user?.id}
          showControls={!selectedStudent || selectedStudent === user?.id}
          locations={locationData}
          userType={getUserType()}
          studentDetails={studentDetails}
          senderName={user?.user_metadata?.full_name}
          loading={loading}
          fullScreen={isFullScreen}
          onExitFullScreen={isFullScreen ? handleExitFullScreen : undefined}
          noDataContent={
            <div className="text-center p-3">
              <p className="text-gray-500 text-sm">Nenhuma localização disponível</p>
            </div>
          }
        />
      </div>
    </div>
  );
};

export default StudentMap;
