
-- 1. CORRIGIR PERMISSÕES DA TABELA auth_logs
-- Conceder per<PERSON><PERSON><PERSON>es necessárias para roles authenticated, anon, service_role
GRANT SELECT, INSERT ON public.auth_logs TO authenticated, anon, service_role;
GRANT USAGE ON SEQUENCE public.auth_logs_id_seq TO authenticated, anon, service_role;

-- 2. LIMPAR DADOS INCONSISTENTES DO USUÁRIO <EMAIL>
-- <PERSON><PERSON>, vamos verificar e corrigir o perfil deste usuário
DO $$
DECLARE
    v_user_id UUID;
    v_profile_exists BOOLEAN;
BEGIN
    -- Buscar ID do usuário <EMAIL>
    SELECT id INTO v_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Verificar se perfil existe
        SELECT EXISTS(
            SELECT 1 FROM public.profiles WHERE user_id = v_user_id
        ) INTO v_profile_exists;
        
        -- Se não existe, criar
        IF NOT v_profile_exists THEN
            INSERT INTO public.profiles (user_id, email, full_name, user_type, cpf)
            VALUES (
                v_user_id,
                '<EMAIL>',
                'Frank Weber',
                'parent',
                '111.111.111-11'
            );
        ELSE
            -- Se existe, garantir que está ativo
            UPDATE public.profiles 
            SET 
                status = 'active',
                registration_status = 'active',
                updated_at = NOW()
            WHERE user_id = v_user_id;
        END IF;
    END IF;
END $$;

-- 3. CORRIGIR QUALQUER PROBLEMA COM TRIGGERS OU FUNÇÕES
-- Verificar se existe alguma função problemática e recriar se necessário
DROP FUNCTION IF EXISTS public.log_auth_event(text, uuid, jsonb) CASCADE;

CREATE OR REPLACE FUNCTION public.log_auth_event(
    event_type TEXT,
    user_id UUID DEFAULT NULL,
    metadata JSONB DEFAULT '{}'::jsonb
) 
RETURNS void
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    BEGIN
        INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
        VALUES (event_type, user_id, metadata, now());
    EXCEPTION WHEN OTHERS THEN
        -- Silenciosamente ignorar erros de logging para não afetar autenticação
        NULL;
    END;
END;
$$ LANGUAGE plpgsql;

-- 4. REGISTRAR A CORREÇÃO NOS LOGS
INSERT INTO public.auth_logs (
    event_type,
    user_id,
    metadata,
    occurred_at
) VALUES (
    'login_fix_permissions_applied',
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    jsonb_build_object(
        'action', 'auth_logs_permissions_fixed_and_profiles_corrected',
        'emails', jsonb_build_array('<EMAIL>', '<EMAIL>'),
        'timestamp', NOW(),
        'changes', jsonb_build_array(
            'granted_auth_logs_permissions',
            'corrected_user_profiles',
            'recreated_log_function'
        )
    ),
    NOW()
);
