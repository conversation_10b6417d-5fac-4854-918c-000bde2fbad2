
import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AuthErrorHandlerProps {
  error: string;
  onRetry?: () => void;
  onClearError?: () => void;
}

const AuthErrorHandler: React.FC<AuthErrorHandlerProps> = ({ 
  error, 
  onRetry, 
  onClearError 
}) => {
  const getErrorDetails = (errorMessage: string) => {
    if (errorMessage.includes('Database error')) {
      return {
        title: 'Erro Temporário do Sistema',
        description: 'Detectamos um problema temporário. Nossa equipe já foi notificada.',
        showRetry: true,
        variant: 'destructive' as const
      };
    }
    
    if (errorMessage.includes('Invalid login credentials')) {
      return {
        title: 'Credenciais Inválidas',
        description: 'Email ou senha incorretos. Verifique seus dados e tente novamente.',
        showRetry: false,
        variant: 'destructive' as const
      };
    }
    
    if (errorMessage.includes('Email not confirmed')) {
      return {
        title: 'Email Não Confirmado',
        description: 'Verifique sua caixa de entrada e confirme seu email antes de fazer login.',
        showRetry: false,
        variant: 'default' as const
      };
    }
    
    return {
      title: 'Erro de Login',
      description: errorMessage,
      showRetry: true,
      variant: 'destructive' as const
    };
  };

  const errorDetails = getErrorDetails(error);

  return (
    <Alert variant={errorDetails.variant} className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        <div className="space-y-2">
          <div>
            <strong>{errorDetails.title}</strong>
          </div>
          <div className="text-sm">
            {errorDetails.description}
          </div>
          {errorDetails.showRetry && onRetry && (
            <div className="flex gap-2 mt-3">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onRetry}
                className="flex items-center gap-1"
              >
                <RefreshCw size={14} />
                Tentar Novamente
              </Button>
              {onClearError && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={onClearError}
                >
                  Limpar Erro
                </Button>
              )}
            </div>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
};

export default AuthErrorHandler;
