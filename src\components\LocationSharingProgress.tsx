
import React from 'react';
import { Progress } from "@/components/ui/progress";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Loader2, AlertCircle, MapPin } from "lucide-react";

interface LocationSharingProgressProps {
  isVisible: boolean;
  totalGuardians: number;
  successCount: number;
  errorCount: number;
  isComplete: boolean;
}

const LocationSharingProgress: React.FC<LocationSharingProgressProps> = ({
  isVisible,
  totalGuardians,
  successCount,
  errorCount,
  isComplete
}) => {
  if (!isVisible || totalGuardians === 0) {
    return null;
  }

  const progress = ((successCount + errorCount) / totalGuardians) * 100;
  const isProcessing = !isComplete && (successCount + errorCount) < totalGuardians;

  return (
    <Card className="fixed bottom-4 right-4 z-50 w-80 shadow-lg animate-fade-in">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          {isProcessing ? (
            <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
          ) : isComplete ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <MapPin className="h-4 w-4 text-gray-600" />
          )}
          Compartilhando Localização
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <Progress value={progress} className="h-2" />
        
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">
            {successCount + errorCount} de {totalGuardians} processados
          </span>
          <span className="font-medium">
            {Math.round(progress)}%
          </span>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span>{successCount} enviados</span>
          </div>
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <span>{errorCount} falharam</span>
          </div>
        </div>

        {isComplete && (
          <div className="pt-2 border-t">
            <p className="text-sm font-medium text-green-700">
              {successCount > 0 
                ? `Localização compartilhada com sucesso com ${successCount} responsável(is)!`
                : 'Não foi possível compartilhar a localização.'
              }
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LocationSharingProgress;
