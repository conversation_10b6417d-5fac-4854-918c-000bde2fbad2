import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  AlertTriangle, 
  Clock, 
  User, 
  Mail, 
  CheckCircle, 
  XCircle,
  MessageSquare,
  Calendar
} from 'lucide-react';
import { removalRequestService, RemovalRequest } from '@/lib/services/removal/RemovalRequestService';

interface RemovalRequestsPanelProps {
  onRequestProcessed?: () => void;
}

export const RemovalRequestsPanel: React.FC<RemovalRequestsPanelProps> = ({
  onRequestProcessed
}) => {
  const [requests, setRequests] = useState<RemovalRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<RemovalRequest | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    loadRequests();
  }, []);

  const loadRequests = async () => {
    try {
      setLoading(true);
      const data = await removalRequestService.getGuardianRemovalRequests();
      setRequests(data);
    } catch (error) {
      console.error('Erro ao carregar solicitações:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProcessRequest = async (request: RemovalRequest, action: 'approve' | 'reject') => {
    setSelectedRequest(request);
    setActionType(action);
    setShowConfirmDialog(true);
  };

  const confirmProcessRequest = async () => {
    if (!selectedRequest) return;

    setIsProcessing(true);
    try {
      const result = await removalRequestService.processRemovalRequest(
        selectedRequest.request_token,
        actionType
      );

      if (result.success) {
        setShowConfirmDialog(false);
        setSelectedRequest(null);
        await loadRequests(); // Recarregar lista
        if (onRequestProcessed) {
          onRequestProcessed();
        }
      }
    } catch (error) {
      console.error('Erro ao processar solicitação:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffMs = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) return 'Expirada';
    if (diffDays === 1) return '1 dia restante';
    return `${diffDays} dias restantes`;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Solicitações de Remoção
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (requests.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Solicitações de Remoção
          </CardTitle>
          <CardDescription>
            Nenhuma solicitação de remoção pendente
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Solicitações de Remoção
            <Badge variant="destructive" className="ml-2">
              {requests.length}
            </Badge>
          </CardTitle>
          <CardDescription>
            Estudantes solicitaram remoção do monitoramento familiar
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {requests.map((request) => (
              <div 
                key={request.id} 
                className="border border-orange-200 rounded-lg p-4 bg-orange-50"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-600" />
                      <span className="font-medium text-gray-900">
                        {request.student_name}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Mail className="h-4 w-4" />
                      <span>{request.student_email}</span>
                    </div>
                  </div>
                  
                  <Badge 
                    variant="outline" 
                    className="border-orange-300 text-orange-700"
                  >
                    <Clock className="h-3 w-3 mr-1" />
                    {getTimeRemaining(request.expires_at)}
                  </Badge>
                </div>

                {/* Motivo */}
                {request.reason && (
                  <div className="mb-3 p-3 bg-white rounded border">
                    <div className="flex items-start gap-2">
                      <MessageSquare className="h-4 w-4 text-gray-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">Motivo:</p>
                        <p className="text-sm text-gray-600 italic">"{request.reason}"</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Data da solicitação */}
                <div className="flex items-center gap-2 text-xs text-gray-500 mb-4">
                  <Calendar className="h-3 w-3" />
                  <span>Solicitado em: {formatDate(request.created_at)}</span>
                </div>

                {/* Ações */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleProcessRequest(request, 'approve')}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Aprovar Remoção
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleProcessRequest(request, 'reject')}
                    className="border-gray-300"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Manter Monitoramento
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Dialog de Confirmação */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {actionType === 'approve' ? (
                <CheckCircle className="h-5 w-5 text-red-500" />
              ) : (
                <XCircle className="h-5 w-5 text-green-500" />
              )}
              {actionType === 'approve' ? 'Confirmar Remoção' : 'Manter Monitoramento'}
            </DialogTitle>
            <DialogDescription className="text-left">
              {actionType === 'approve' ? (
                <>
                  Você está prestes a <strong className="text-red-600">remover permanentemente</strong> o 
                  estudante <strong>{selectedRequest?.student_name}</strong> do seu monitoramento.
                </>
              ) : (
                <>
                  Você vai <strong className="text-green-600">rejeitar a solicitação</strong> e 
                  manter <strong>{selectedRequest?.student_name}</strong> no seu monitoramento.
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {selectedRequest && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Detalhes da Solicitação:</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div><strong>Estudante:</strong> {selectedRequest.student_name}</div>
                  <div><strong>Email:</strong> {selectedRequest.student_email}</div>
                  {selectedRequest.reason && (
                    <div><strong>Motivo:</strong> "{selectedRequest.reason}"</div>
                  )}
                  <div><strong>Solicitado em:</strong> {formatDate(selectedRequest.created_at)}</div>
                </div>
              </div>
            )}

            <div className={`border rounded-lg p-3 ${
              actionType === 'approve' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
            }`}>
              <div className="text-sm">
                {actionType === 'approve' ? (
                  <>
                    <p className="font-medium text-red-700 mb-2">⚠️ Consequências da aprovação:</p>
                    <ul className="text-red-600 space-y-1 text-xs">
                      <li>• O estudante será removido permanentemente do seu dashboard</li>
                      <li>• Você não receberá mais notificações de localização</li>
                      <li>• O histórico de localizações será mantido</li>
                      <li>• Uma nova solicitação seria necessária para reconectar</li>
                    </ul>
                  </>
                ) : (
                  <>
                    <p className="font-medium text-green-700 mb-2">✅ Consequências da rejeição:</p>
                    <ul className="text-green-600 space-y-1 text-xs">
                      <li>• O estudante permanecerá no seu monitoramento</li>
                      <li>• Você continuará recebendo notificações de localização</li>
                      <li>• O estudante será informado da sua decisão</li>
                      <li>• Uma nova solicitação pode ser feita no futuro</li>
                    </ul>
                  </>
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              variant="outline" 
              onClick={() => setShowConfirmDialog(false)}
              disabled={isProcessing}
            >
              Cancelar
            </Button>
            <Button 
              onClick={confirmProcessRequest}
              disabled={isProcessing}
              className={actionType === 'approve' ? 
                'bg-red-600 hover:bg-red-700' : 
                'bg-green-600 hover:bg-green-700'
              }
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processando...
                </>
              ) : (
                <>
                  {actionType === 'approve' ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Confirmar Remoção
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      Manter Monitoramento
                    </>
                  )}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}; 