# 📧 Setup do Email de Confirmação - Fabio Leda Cunha

**Data:** 02/07/2025  
**Status:** ✅ Pronto para envio  
**Banco de dados:** ✅ Atualizado  

---

## 🎯 **DADOS DO ESTUDANTE**

| Campo | Valor |
|-------|-------|
| **Nome** | Fabio Leda Cunha |
| **Email** | <EMAIL> |
| **ID do Usuário** | d0992f66-70f9-4bd3-9fb5-d908e0c64ac5 |
| **Responsável** | <EMAIL> |
| **Status** | pending_activation |

---

## 🔑 **CREDENCIAIS ATUALIZADAS**

| Campo | Valor |
|-------|-------|
| **Email de Login** | <EMAIL> |
| **Senha Temporária** | FabioLeda2025! |
| **Token de Ativação** | fabio-leda-cunha-2025-token |
| **URL de Ativação** | https://sistema-monitore.com.br/activate-account?token=fabio-leda-cunha-2025-token |
| **Expira em** | 01/08/2025 (30 dias) |

---

## 📨 **CONFIGURAÇÃO DO EMAIL**

### Opção 1: SMTP Resend (Recomendado)
```
Servidor: smtp.resend.com
Porta: 465 (SSL/TLS)
Usuário: resend
Senha: [SUA_API_KEY_DO_RESEND]
De: <EMAIL>
Para: <EMAIL>
```

### Opção 2: API REST Resend
```
URL: https://api.resend.com/emails
Method: POST
Headers:
  Authorization: Bearer [SUA_API_KEY_DO_RESEND]
  Content-Type: application/json
```

---

## 🛠️ **SCRIPTS DISPONÍVEIS**

1. **`scripts/send-email-simple.ps1`** - Script PowerShell para SMTP
2. **`scripts/send-fabio-confirmation.ps1`** - Script para Edge Function
3. **`fabio-leda-cunha-credentials.txt`** - Credenciais em texto

---

## 📋 **COMO ENVIAR O EMAIL**

### Método 1: PowerShell + SMTP
1. Editar `scripts/send-email-simple.ps1`
2. Substituir `re_123456789_YOUR_API_KEY_HERE` pela API key real
3. Descomentar linha: `# $smtpClient.Send($emailMessage)`
4. Executar: `powershell -ExecutionPolicy Bypass -File scripts/send-email-simple.ps1`

### Método 2: Dashboard Resend
1. Acessar: https://resend.com/emails
2. Usar template de email abaixo
3. Enviar manualmente

---

## 📧 **TEMPLATE DO EMAIL**

**Assunto:** EduConnect - Confirme sua conta com novas credenciais

**Corpo:**
```
EduConnect - Sistema de Monitoramento Educacional

Olá Fabio Leda Cunha,

Suas credenciais foram atualizadas no sistema EduConnect!

SUAS NOVAS CREDENCIAIS:
Email: <EMAIL>
Senha temporária: FabioLeda2025!

LINK DE ATIVAÇÃO:
https://sistema-monitore.com.br/activate-account?token=fabio-leda-cunha-2025-token

PRÓXIMOS PASSOS:
1. Clique no link de ativação acima
2. Faça login com as credenciais fornecidas
3. Crie uma nova senha segura e permanente
4. Complete seu perfil no sistema
5. Comece a compartilhar sua localização

IMPORTANTE: Você precisará criar uma nova senha permanente no primeiro login.
Este link de ativação expira em 30 dias.

Se você não solicitou esta atualização, entre em contato conosco imediatamente.

EduConnect
Sistema de Monitoramento Educacional Seguro
Conectando famílias com segurança e confiança

Responsável vinculado: <EMAIL>
```

---

## ✅ **VALIDAÇÕES FEITAS**

- [x] Usuário existe no banco (`auth.users`)
- [x] Perfil existe no banco (`profiles`)
- [x] Token atualizado e válido
- [x] Senha atualizada no banco
- [x] Status definido como `pending_activation`
- [x] Flag `requires_password_change = true`
- [x] Data de expiração configurada (30 dias)
- [x] Script de email testado e funcionando

---

## 🔄 **PRÓXIMOS PASSOS APÓS ENVIO**

1. ✅ Verificar se o email foi entregue
2. ✅ Aguardar Fabio acessar o link de ativação
3. ✅ Confirmar se o login funcionou
4. ✅ Verificar se a senha foi alterada
5. ✅ Confirmar se o compartilhamento de localização foi iniciado

---

## 🚨 **TROUBLESHOOTING**

### Se o email não for entregue:
- Verificar configuração DNS do domínio
- Conferir se a API key do Resend está correta
- Verificar logs no dashboard do Resend

### Se o login falhar:
- Confirmar se o token não expirou
- Verificar se a senha está correta
- Checar se o usuário existe no banco

### Se a ativação falhar:
- Verificar se o token existe na tabela `profiles`
- Confirmar se o status está como `pending_activation`
- Checar se a data de expiração não passou

---

## 📞 **CONTATOS DE SUPORTE**

- **Sistema:** https://sistema-monitore.com.br
- **Dashboard:** https://sistema-monitore.com.br/parent-dashboard
- **Responsável:** <EMAIL>

---

**✨ Tudo pronto para enviar o email ao Fabio Leda Cunha! ✨** 