import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import NativeNotification from '../ui/NativeNotification';
import { Bell, WifiOff, MapPin, AlertTriangle } from 'lucide-react';

// Define the notification types
type NotificationType = 'default' | 'location' | 'offline' | 'alert';

// Define the notification action
interface NotificationAction {
  text: string;
  onClick: () => void;
  destructive?: boolean;
}

// Define the notification data
interface NotificationData {
  id: string;
  title: string;
  body: string;
  type: NotificationType;
  duration?: number;
  actions?: NotificationAction[];
}

// Define the notification context
interface NotificationContextType {
  showNotification: (
    title: string,
    body: string,
    options?: {
      type?: NotificationType;
      duration?: number;
      actions?: NotificationAction[];
    }
  ) => string;
  hideNotification: (id: string) => void;
  notifications: NotificationData[];
}

// Create the notification context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Create the notification provider
export function NotificationProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  // Show a notification
  const showNotification = useCallback(
    (
      title: string,
      body: string,
      options?: {
        type?: NotificationType;
        duration?: number;
        actions?: NotificationAction[];
      }
    ) => {
      const id = Date.now().toString();
      const notification: NotificationData = {
        id,
        title,
        body,
        type: options?.type || 'default',
        duration: options?.duration,
        actions: options?.actions,
      };

      setNotifications((prev) => [...prev, notification]);
      return id;
    },
    []
  );

  // Hide a notification
  const hideNotification = useCallback((id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  }, []);

  // Get the icon for a notification type
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'location':
        return <MapPin size={18} />;
      case 'offline':
        return <WifiOff size={18} />;
      case 'alert':
        return <AlertTriangle size={18} />;
      default:
        return <Bell size={18} />;
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        showNotification,
        hideNotification,
        notifications,
      }}
    >
      {children}

      {/* Render the notifications */}
      {notifications.map((notification) => (
        <NativeNotification
          key={notification.id}
          title={notification.title}
          body={notification.body}
          icon={getNotificationIcon(notification.type)}
          duration={notification.duration}
          actions={notification.actions}
          onClose={() => hideNotification(notification.id)}
        />
      ))}
    </NotificationContext.Provider>
  );
}

// Create a hook to use the notification context
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}