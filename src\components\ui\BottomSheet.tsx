import React, { useState, useEffect, useRef } from 'react';
import { Capacitor } from '@capacitor/core';
import { X } from 'lucide-react';
import { LocationData } from '@/types/database';

interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  location?: LocationData | null;
  children?: React.ReactNode;
}

const BottomSheet: React.FC<BottomSheetProps> = ({ 
  isOpen, 
  onClose, 
  location,
  children 
}) => {
  const [sheetHeight, setSheetHeight] = useState<number>(0);
  const [startY, setStartY] = useState<number | null>(null);
  const [currentY, setCurrentY] = useState<number | null>(null);
  const sheetRef = useRef<HTMLDivElement>(null);
  
  // Detect platform
  const isNative = Capacitor.isNativePlatform();
  const isIOS = isNative && Capacitor.getPlatform() === 'ios';
  const isAndroid = isNative && Capacitor.getPlatform() === 'android';
  
  // Set initial height based on content
  useEffect(() => {
    if (isOpen && sheetRef.current) {
      setSheetHeight(isNative ? 300 : 250); // Taller on native for better touch area
    } else {
      setSheetHeight(0);
    }
  }, [isOpen, isNative]);
  
  // Handle touch start
  const handleTouchStart = (e: React.TouchEvent) => {
    setStartY(e.touches[0].clientY);
  };
  
  // Handle touch move
  const handleTouchMove = (e: React.TouchEvent) => {
    if (startY === null) return;
    
    const currentTouchY = e.touches[0].clientY;
    const diff = currentTouchY - startY;
    
    // Only allow dragging down, not up past the initial height
    if (diff > 0) {
      setCurrentY(diff);
    }
  };
  
  // Handle touch end
  const handleTouchEnd = () => {
    if (currentY === null || startY === null) return;
    
    // If dragged more than 100px down, close the sheet
    if (currentY > 100) {
      onClose();
    } else {
      // Otherwise snap back
      setCurrentY(null);
    }
    
    setStartY(null);
  };
  
  // Calculate current position
  const getSheetStyle = () => {
    const translateY = currentY !== null ? currentY : 0;
    
    return {
      height: `${sheetHeight}px`,
      transform: `translateY(${translateY}px)`,
      transition: currentY !== null ? 'none' : 'all 0.3s ease-out'
    };
  };
  
  // Get platform-specific classes
  const getPlatformClasses = () => {
    let classes = 'bottom-sheet fixed bottom-0 left-0 right-0 bg-white rounded-t-xl shadow-lg z-50 overflow-hidden';
    
    if (isIOS) {
      classes += ' ios-bottom-sheet';
    } else if (isAndroid) {
      classes += ' android-bottom-sheet';
    }
    
    return classes;
  };
  
  if (!isOpen) return null;
  
  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 z-40 transition-opacity"
        onClick={onClose}
        style={{ opacity: currentY !== null ? 1 - (currentY / 300) : 1 }}
      />
      
      {/* Bottom Sheet */}
      <div
        ref={sheetRef}
        className={getPlatformClasses()}
        style={getSheetStyle()}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Handle */}
        <div className="w-full flex justify-center pt-2 pb-4">
          <div className="w-10 h-1 bg-gray-300 rounded-full" />
        </div>
        
        {/* Close button */}
        <button 
          className="absolute top-3 right-3 p-1 rounded-full bg-gray-100 hover:bg-gray-200"
          onClick={onClose}
        >
          <X size={18} />
        </button>
        
        {/* Content */}
        <div className="px-4 pb-8 overflow-auto">
          {location ? (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                {new Date(location.timestamp).toLocaleString()}
              </h3>
              
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-xs text-gray-500">Latitude</p>
                  <p className="font-mono">{location.latitude.toFixed(6)}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-xs text-gray-500">Longitude</p>
                  <p className="font-mono">{location.longitude.toFixed(6)}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-xs text-gray-500">User ID</p>
                  <p className="text-xs truncate">{location.user_id}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-xs text-gray-500">Student</p>
                  <p className="truncate">{location.student_name}</p>
                </div>
              </div>
              
              {location.address && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-xs text-gray-500">Address</p>
                  <div>{location.address}</div>
                </div>
              )}
            </div>
          ) : children ? (
            children
          ) : (
            <p className="text-center text-gray-500">No location data available</p>
          )}
        </div>
      </div>
    </>
  );
};

export default BottomSheet;