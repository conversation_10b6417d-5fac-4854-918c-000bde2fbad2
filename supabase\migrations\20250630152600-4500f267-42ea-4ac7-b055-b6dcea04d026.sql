
-- Adicionar campos necessários na tabela profiles para tracking de criação de contas
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS account_creation_method VARCHAR(50) DEFAULT 'unknown';
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS created_by_user_id UUID REFERENCES auth.users(id);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS requires_password_change BOOLEAN DEFAULT FALSE;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS activation_token VARCHAR(255);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS activation_expires_at TIMESTAMPTZ;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS activated_at TIMESTAMPTZ;

-- Criar índice para melhorar performance de busca por token de ativação
CREATE INDEX IF NOT EXISTS idx_profiles_activation_token ON public.profiles(activation_token);

-- Função RPC para criar estudante com relacionamento automático
CREATE OR REPLACE FUNCTION public.create_student_with_guardian(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_student_phone TEXT DEFAULT NULL,
  p_temp_password TEXT DEFAULT NULL
)
RETURNS TABLE(success BOOLEAN, message TEXT, student_id UUID, activation_token TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_guardian_id UUID;
  v_guardian_email TEXT;
  v_student_user_id UUID;
  v_clean_cpf TEXT;
  v_activation_token TEXT;
  v_existing_student_id UUID;
BEGIN
  -- Obter ID e email do responsável atual
  v_guardian_id := auth.uid();
  SELECT u.email INTO v_guardian_email FROM auth.users u WHERE u.id = v_guardian_id;
  
  IF v_guardian_id IS NULL OR v_guardian_email IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Usuário não autenticado'::TEXT, NULL::UUID, NULL::TEXT;
    RETURN;
  END IF;
  
  -- Limpar CPF
  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  
  -- Verificar se estudante já existe por CPF
  SELECT user_id INTO v_existing_student_id
  FROM public.profiles
  WHERE regexp_replace(cpf, '[^0-9]', '', 'g') = v_clean_cpf
  AND user_type = 'student';
  
  IF v_existing_student_id IS NOT NULL THEN
    -- Verificar se já existe relacionamento
    IF EXISTS (
      SELECT 1 FROM public.student_guardian_relationships 
      WHERE student_id = v_existing_student_id AND guardian_id = v_guardian_id
    ) THEN
      RETURN QUERY SELECT FALSE, 'Estudante já está vinculado à sua conta'::TEXT, NULL::UUID, NULL::TEXT;
      RETURN;
    ELSE
      -- Criar relacionamento com estudante existente
      INSERT INTO public.student_guardian_relationships (
        student_id, guardian_id, relationship_type, is_primary
      ) VALUES (
        v_existing_student_id, v_guardian_id, 'parent', TRUE
      );
      
      RETURN QUERY SELECT TRUE, 'Estudante vinculado com sucesso'::TEXT, v_existing_student_id, NULL::TEXT;
      RETURN;
    END IF;
  END IF;
  
  -- Gerar token de ativação
  v_activation_token := encode(gen_random_bytes(32), 'base64url');
  
  -- Esta função será complementada pela Edge Function que criará o usuário no auth.users
  -- Por enquanto, retornamos sucesso para que a Edge Function possa prosseguir
  
  RETURN QUERY SELECT TRUE, 'Preparado para criação via Edge Function'::TEXT, NULL::UUID, v_activation_token;
END;
$$;

-- Função para ativar conta de estudante
CREATE OR REPLACE FUNCTION public.activate_student_account(
  p_activation_token TEXT,
  p_new_password TEXT
)
RETURNS TABLE(success BOOLEAN, message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_student_id UUID;
  v_profile_record RECORD;
BEGIN
  -- Buscar perfil pelo token de ativação
  SELECT * INTO v_profile_record
  FROM public.profiles
  WHERE activation_token = p_activation_token
  AND activation_expires_at > NOW()
  AND activated_at IS NULL;
  
  IF v_profile_record IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Token de ativação inválido ou expirado'::TEXT;
    RETURN;
  END IF;
  
  -- Atualizar senha no auth.users (será feito via Edge Function)
  -- Marcar conta como ativada
  UPDATE public.profiles
  SET 
    activated_at = NOW(),
    requires_password_change = FALSE,
    activation_token = NULL
  WHERE user_id = v_profile_record.user_id;
  
  RETURN QUERY SELECT TRUE, 'Conta ativada com sucesso'::TEXT;
END;
$$;
