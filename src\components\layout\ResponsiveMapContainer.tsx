import React from 'react';
import { useDevice } from '@/hooks/useDevice';
import { cn } from '@/lib/utils';

interface ResponsiveMapContainerProps {
  children: React.ReactNode;
  className?: string;
  minHeight?: string;
  maxHeight?: string;
  fullScreen?: boolean;
}

const ResponsiveMapContainer: React.FC<ResponsiveMapContainerProps> = ({
  children,
  className,
  minHeight,
  maxHeight,
  fullScreen = false
}) => {
  const device = useDevice();

  // Calculate optimal height based on device and orientation
  const getContainerHeight = () => {
    // Full-screen mode for iOS mobile
    if (fullScreen && device.isIOS && device.type === 'mobile') {
      return 'h-full w-full';
    }

    if (device.type === 'mobile') {
      if (device.orientation === 'landscape') {
        return 'h-[40vh] min-h-[200px] max-h-[250px]';
      }
      // iOS específico: mais altura para evitar mapa apertado
      if (device.isIOS) {
        return 'h-[55vh] min-h-[350px]';
      }
      return 'h-[45vh] min-h-[300px]';
    }

    if (device.type === 'tablet') {
      if (device.orientation === 'landscape') {
        return 'h-[60vh] min-h-[350px] max-h-[500px]';
      }
      return 'h-[60vh] min-h-[400px]';
    }

    return 'h-[70vh] min-h-[500px]';
  };

  // Calculate padding and spacing
  const getContainerPadding = () => {
    // No padding for full-screen mode
    if (fullScreen && device.isIOS && device.type === 'mobile') {
      return 'p-0';
    }

    if (device.type === 'mobile' && device.orientation === 'landscape') {
      return 'p-1';
    }
    if (device.type === 'mobile') {
      // iOS específico: padding reduzido para maximizar área do mapa
      if (device.isIOS) {
        return 'p-1';
      }
      return 'p-2';
    }
    return 'p-4';
  };

  const getContainerClasses = () => {
    if (fullScreen && device.isIOS && device.type === 'mobile') {
      return 'w-full h-full relative overflow-hidden';
    }
    return 'w-full relative rounded-lg overflow-hidden';
  };

  return (
    <div
      className={cn(
        getContainerClasses(),
        getContainerHeight(),
        getContainerPadding(),
        className
      )}
      style={{
        minHeight: minHeight,
        maxHeight: maxHeight
      }}
    >
      {children}
    </div>
  );
};

export default ResponsiveMapContainer;