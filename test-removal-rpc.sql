-- Teste para verificar se as funções RPC estão funcionando
-- Execute no Supabase: Database > SQL Editor

-- 1. Verificar se as funções existem
SELECT 
    'Função request_guardian_removal' as funcao,
    EXISTS (SELECT 1 FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = 'public' AND p.proname = 'request_guardian_removal') as existe;

SELECT 
    'Função get_guardian_removal_requests' as funcao,
    EXISTS (SELECT 1 FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = 'public' AND p.proname = 'get_guardian_removal_requests') as existe;

-- 2. Verificar se a tabela existe
SELECT 
    'Tabela removal_requests' as objeto,
    EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'removal_requests') as existe;

-- 3. Verificar estrutura da tabela
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'removal_requests'
ORDER BY ordinal_position;

-- 4. Verificar permissões das funções
SELECT 
    p.proname as function_name,
    array_to_string(p.proacl, ', ') as permissions
FROM pg_proc p 
JOIN pg_namespace n ON p.pronamespace = n.oid 
WHERE n.nspname = 'public' 
AND p.proname LIKE '%removal%';

-- 5. Teste básico de autenticação (verificar se auth.uid() funciona)
SELECT 
    'User ID' as info,
    auth.uid() as value;

-- 6. Testar a função com usuário atual (se autenticado)
-- SELECT public.request_guardian_removal('00000000-0000-0000-0000-000000000000'::UUID, 'Teste'); 