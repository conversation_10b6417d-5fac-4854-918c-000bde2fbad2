
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface MultiChannelRequest {
  recipient_email: string;
  recipient_name?: string;
  latitude: number;
  longitude: number;
  sender_name?: string;
  preferences: {
    email_enabled: boolean;
    sms_enabled: boolean;
    whatsapp_enabled: boolean;
    phone_number?: string;
    preferred_method: string;
  };
}

interface ChannelResult {
  channel: string;
  success: boolean;
  error?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body: MultiChannelRequest = await req.json();
    console.log('Multi-channel sharing request:', body);

    const {
      recipient_email,
      recipient_name,
      latitude,
      longitude,
      sender_name,
      preferences
    } = body;

    const results: ChannelResult[] = [];
    const channels_used: string[] = [];
    const errors: string[] = [];

    // Determinar ordem dos canais baseada na preferência
    const orderedChannels = [preferences.preferred_method];
    if (preferences.email_enabled && !orderedChannels.includes('email')) {
      orderedChannels.push('email');
    }
    if (preferences.sms_enabled && !orderedChannels.includes('sms')) {
      orderedChannels.push('sms');
    }
    if (preferences.whatsapp_enabled && !orderedChannels.includes('whatsapp')) {
      orderedChannels.push('whatsapp');
    }

    // Tentar cada canal em ordem de preferência
    for (const channel of orderedChannels) {
      try {
        let success = false;

        if (channel === 'email' && preferences.email_enabled) {
          success = await sendEmail(recipient_email, recipient_name, latitude, longitude, sender_name);
        } else if (channel === 'sms' && preferences.sms_enabled && preferences.phone_number) {
          success = await sendSMS(preferences.phone_number, latitude, longitude, sender_name);
        } else if (channel === 'whatsapp' && preferences.whatsapp_enabled && preferences.phone_number) {
          success = await sendWhatsApp(preferences.phone_number, latitude, longitude, sender_name);
        }

        if (success) {
          channels_used.push(channel);
          results.push({ channel, success: true });
          // Para o canal preferido, se teve sucesso, não precisa tentar outros
          if (channel === preferences.preferred_method) {
            break;
          }
        } else {
          results.push({ channel, success: false, error: `Falha no envio via ${channel}` });
        }
      } catch (error: any) {
        console.error(`Erro no canal ${channel}:`, error);
        results.push({ channel, success: false, error: error.message });
        errors.push(`${channel}: ${error.message}`);
      }
    }

    // Se nenhum canal funcionou, usar email como fallback
    if (channels_used.length === 0 && !channels_used.includes('email')) {
      try {
        const emailSuccess = await sendEmail(recipient_email, recipient_name, latitude, longitude, sender_name);
        if (emailSuccess) {
          channels_used.push('email');
          results.push({ channel: 'email', success: true });
        }
      } catch (error: any) {
        console.error('Erro no fallback de email:', error);
        errors.push(`email (fallback): ${error.message}`);
      }
    }

    const response = {
      success: channels_used.length > 0,
      channels_used,
      results,
      errors: errors.length > 0 ? errors : undefined
    };

    console.log('Multi-channel response:', response);

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });

  } catch (error: any) {
    console.error("Erro na função send-multi-channel:", error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message,
        channels_used: [],
        results: []
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

async function sendEmail(
  recipient_email: string,
  recipient_name: string | undefined,
  latitude: number,
  longitude: number,
  sender_name: string | undefined
): Promise<boolean> {
  try {
    const googleMapsUrl = `https://www.google.com/maps?q=${latitude},${longitude}`;
    const appleMapsUrl = `https://maps.apple.com/?q=${latitude},${longitude}`;

    const emailResponse = await resend.emails.send({
      from: "EduConnect <<EMAIL>>",
      to: [recipient_email],
      subject: `${sender_name || 'Alguém'} compartilhou uma localização com você`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">📍 Localização Compartilhada</h2>
          <p>Olá ${recipient_name || 'você'},</p>
          <p><strong>${sender_name || 'Alguém'}</strong> compartilhou uma localização com você.</p>
          
          <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>Coordenadas:</strong> ${latitude}, ${longitude}</p>
            <p style="margin: 15px 0;">
              <a href="${googleMapsUrl}" target="_blank" style="background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">
                Abrir no Google Maps
              </a>
              <a href="${appleMapsUrl}" target="_blank" style="background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                Abrir no Apple Maps
              </a>
            </p>
          </div>
          
          <p style="color: #6b7280; font-size: 14px;">
            Esta localização foi compartilhada através do EduConnect.
          </p>
        </div>
      `,
    });

    console.log("Email enviado com sucesso:", emailResponse);
    return true;
  } catch (error: any) {
    console.error("Erro ao enviar email:", error);
    return false;
  }
}

async function sendSMS(
  phone_number: string,
  latitude: number,
  longitude: number,
  sender_name: string | undefined
): Promise<boolean> {
  // TODO: Implementar envio de SMS via Twilio
  // Por enquanto, simular sucesso para teste
  console.log(`SMS seria enviado para ${phone_number}: Localização de ${sender_name}: ${latitude}, ${longitude}`);
  return true;
}

async function sendWhatsApp(
  phone_number: string,
  latitude: number,
  longitude: number,
  sender_name: string | undefined
): Promise<boolean> {
  // TODO: Implementar envio de WhatsApp via Twilio
  // Por enquanto, simular sucesso para teste
  console.log(`WhatsApp seria enviado para ${phone_number}: Localização de ${sender_name}: ${latitude}, ${longitude}`);
  return true;
}

serve(handler);
