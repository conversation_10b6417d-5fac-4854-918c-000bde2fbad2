# Relatório de Limpeza de Tabelas – Banco *Reno*
> Data: 26-Jun-2025 • Autor: *Cascade*  
> Projeto: **Locate-Family-Connect / EduConnect**  
> Objetivo: documentar diagnóstico de tabelas vazias e recomendar ações ao DBA.

---

## 1. Contexto
Durante a revisão da base **Reno** foram identificadas diversas tabelas sem registros (`n_live_tup = 0`). O objetivo deste relatório é:

1. Confirmar se tais tabelas são realmente obsoletas ou apenas aguardam uso futuro.
2. Avaliar dependências (FK, código-fonte, funções, triggers) que possam impedir a remoção.
3. Recomendar ações (manter, renomear, remover) de forma segura.

## 2. Tabelas Vazias Detectadas (26-Jun-2025)
| Tabela | Linhas | Comentário |
|--------|-------:|------------|
| geofences | 0 | parte de feature “geofencing” não lançada |
| time_ranges | 0 | idem acima |
| student_location_history | 0 | histórico granular — substituída por `location_history` |
| student_permission_geofences | 0 | FK → `student_permissions.id`, não usada |
| student_permission_time_ranges | 0 | FK → `student_permissions.id`, não usada |
| webhook_events | 0 | planeja auditoria de webhooks |
| student_permissions | 0 | raiz de permissões de tutela |
| pending_shares | 0 | artefato inicial do fluxo de compartilhamento |
| user_communication_preferences | 0 | preferências de notificação (futura) |
| notification_logs | 0 | log de notificações push/email |
| guardians | 0 | **LEGADA** – suprida por `guardian_profiles` + `student_guardian_relationships` |
| email_logs | 0 | log de e-mails – feature ainda não habilitada |
| parent_confirmation_invites | 0 | convites pendentes – UI não implementada |

## 3. Análise de Dependências
Consulta FK entre tabelas vazias ou que apontem para elas:

| Origem (FK) | → Destino | Impacto |
|-------------|----------|---------|
| guardians.guardian_profile_id | → guardian_profiles.id | *Destino* contém 3 linhas → **sem risco** ao dropar `guardians` (on delete cascade N/A) |
| student_permission_geofences.permission_id | → student_permissions.id | ambos vazios |
| student_permission_geofences.geofence_id | → geofences.id | ambos vazios |
| student_permission_time_ranges.permission_id | → student_permissions.id | ambos vazios |
| student_permission_time_ranges.time_range_id | → time_ranges.id | ambos vazios |

Pesquisa em código (`src/`, `supabase/functions/`): nenhuma ocorrência de `FROM public.guardians` ou inserts nessas tabelas.

## 4. Conclusões
1. **`guardian_profiles`** é a fonte de verdade para dados de responsáveis (3 registros).  
   • Já referenciada em flows de vínculo pai-filho.  
   • **Manter**.
2. **`guardians`** é redundante, vazia e não referenciada.  
   • **Remover** para evitar confusão e simplificar schema.
3. Demais tabelas vazias compõem funcionalidades futuras.  
   • **Manter** até confirmação de cancelamento ou redesign.

## 5. Plano de Ação Recomendado
### 5.1  Migration SQL
```sql
-- 20250626_drop_guardians.sql
BEGIN;
DROP TABLE IF EXISTS public.guardians CASCADE; -- tabela vazia, sem dados de produção
COMMIT;
```
1. Salvar arquivo acima em `supabase/migrations/20250626_drop_guardians.sql`.
2. Executar `supabase db push` ou aplicar via CI de migrations.
3. Rodar testes E2E (autenticação, dashboard, vínculo familiar).
4. Monitorar logs por 24h para erros de referência à tabela removida.

### 5.2  Checklist para o DBA
- [ ] Revisar este relatório e aprovar remoção.  
- [ ] Garantir backup automatizado pré-migration.  
- [ ] Aplicar migration em branch `dev`, depois produção.  
- [ ] Confirmar ausência de erros SQL nos Edge Functions.  
- [ ] Documentar mudança no *Change Log*.

## 6. Próximos Passos
1. Avaliar roadmap das features de geofences / permissões; caso descartadas, planejar limpeza futura.
2. Criar view consolidada `v_guardians` baseada em `guardian_profiles` para leitura no frontend (caso julgado necessário).
3. Atualizar documentação ERD e gerar nova versão do inventário.

---
*FIM DO RELATÓRIO*
