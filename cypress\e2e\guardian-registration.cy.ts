/// <reference types="cypress" />

// Teste de criação de conta para RESPONSÁVEL (Guardian)
// Este teste intercepta a chamada de signup do Supabase para evitar criação real de conta

describe('Cadastro de Responsável', () => {
  const guardian = {
    name: 'KAROLINA CID BRITO',
    cpf: '99995727234',
    email: '<EMAIL>',
    password: 'Guardian2025!'
  };

  beforeEach(() => {
    // Intercepta signup no Supabase
    cy.intercept('POST', '**/auth/v1/signup*', (req) => {
      req.reply({
        statusCode: 200,
        body: {
          user: {
            id: 'test-uid',
            email: guardian.email,
            email_confirmed_at: null
          },
          session: null
        }
      });
    }).as('signUp');

    // Intercepta envio de e-mail via Resend e captura link de confirmação
    cy.intercept('POST', 'https://api.resend.com/emails', (req) => {
      // Simula resposta da API Resend
      req.reply({ statusCode: 200, body: { id: 'email-id' } });
    }).as('resendEmail');

    // Intercepta inserção de log
    cy.intercept('POST', '**/rest/v1/auth_logs', { statusCode: 201, body: {} }).as('logInsert');
  });

  it('deve registrar um novo responsável com sucesso', () => {
    cy.visit('http://localhost:4000/register');

    // Seleciona tipo Responsável
    cy.contains('Sou Responsável').click();
    cy.contains('button', /Continuar como Responsável/i).click();
    cy.contains('button', /Sim, confirmar como Responsável/i).click();

    // Preenche formulário
    cy.get('[data-cy="fullname-input"]').type(guardian.name);
    cy.get('[data-cy="cpf-input"]').type(guardian.cpf);
    cy.get('[data-cy="email-input"]').type(guardian.email);
    cy.get('[data-cy="password-input"]').type(guardian.password);
    cy.get('[data-cy="password-confirm-input"]').type(guardian.password);

    // Aceita consentimento LGPD (checkbox custom -> usar click)
    cy.get('#lgpd-consent').click({ force: true });

    // Envia formulário
    cy.get('[data-cy="submit-button"]').click();

    // Aguarda signup
    cy.wait('@signUp');

    // Aguarda envio do e-mail e valida conteúdo básico
    cy.wait('@resendEmail').then(({ request }) => {
      expect(request.body).to.have.property('to');
      expect(request.body.to[0]).to.eq(guardian.email);
      expect(request.body.subject).to.contain('Confirme sua conta');
    });
  });
}); 