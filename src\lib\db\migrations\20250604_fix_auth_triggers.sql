-- Migration: Correção completa para erros de autenticação
-- Data: 04/06/2025
-- Esta migração resolve conflitos entre triggers e funções de log que estão causando o erro 500

SET search_path = public, auth;

-- 1. Identificar e desativar triggers problemáticos na tabela auth.users
DO $$
DECLARE
    trigger_rec RECORD;
BEGIN
    FOR trigger_rec IN 
        SELECT trigger_name 
        FROM information_schema.triggers 
        WHERE event_object_schema = 'auth' AND event_object_table = 'users'
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS %I ON auth.users', trigger_rec.trigger_name);
        RAISE NOTICE 'Removido trigger: %', trigger_rec.trigger_name;
    END LOOP;
END
$$;

-- 2. Estabelecer uma versão única e segura da função log_auth_event
DROP FUNCTION IF EXISTS public.log_auth_event(text, uuid, jsonb);
CREATE OR REPLACE FUNCTION public.log_auth_event(
    event_type TEXT,
    user_id UUID DEFAULT NULL,
    metadata JSONB DEFAULT '{}'::jsonb
) 
RETURNS void
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Tratamento de erros para não interferir na autenticação
    BEGIN
        -- Garantir que a tabela auth_logs tem os campos necessários
        INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
        VALUES (event_type, user_id, metadata, now());
    EXCEPTION WHEN OTHERS THEN
        -- Log silencioso para não afetar a autenticação
        -- Note que este erro está sendo silenciado deliberadamente para não interferir no login
        NULL;
    END;
END;
$$ LANGUAGE plpgsql;

-- 3. Garantir que a tabela auth_logs tem a estrutura correta
DO $$
BEGIN
    -- Adicionar coluna event_type se estiver faltando
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'auth_logs' AND column_name = 'event_type'
    ) THEN
        -- Verificar se existe coluna event que pode ser renomeada
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = 'auth_logs' AND column_name = 'event'
        ) THEN
            ALTER TABLE public.auth_logs RENAME COLUMN event TO event_type;
        ELSE
            ALTER TABLE public.auth_logs ADD COLUMN event_type TEXT;
        END IF;
    END IF;
    
    -- Adicionar outras colunas necessárias se estiverem faltando
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'auth_logs' AND column_name = 'user_id'
    ) THEN
        ALTER TABLE public.auth_logs ADD COLUMN user_id UUID;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'auth_logs' AND column_name = 'occurred_at'
    ) THEN
        ALTER TABLE public.auth_logs ADD COLUMN occurred_at TIMESTAMPTZ DEFAULT now();
    END IF;
END;
$$;

-- 4. Conceder permissões adequadas para a função e tabela
GRANT EXECUTE ON FUNCTION public.log_auth_event(TEXT, UUID, JSONB) TO authenticated, anon, service_role;
GRANT ALL ON public.auth_logs TO service_role;
GRANT SELECT, INSERT ON public.auth_logs TO authenticated;
GRANT USAGE ON SEQUENCE public.auth_logs_id_seq TO authenticated, service_role;

-- 5. Criar um trigger seguro para rastreamento de login (opcional, comentado para evitar problemas)
-- Descomente apenas após testar que a autenticação funciona sem este trigger
/*
CREATE OR REPLACE FUNCTION public.track_user_login()
RETURNS TRIGGER AS $$
BEGIN
    -- Não fazer nada para operações de DELETE
    IF (TG_OP = 'DELETE') THEN
        RETURN OLD;
    END IF;
    
    -- Para operações UPDATE que envolvem login (verificar última autenticação)
    IF (TG_OP = 'UPDATE' AND NEW.last_sign_in_at IS NOT NULL AND 
       (OLD.last_sign_in_at IS NULL OR NEW.last_sign_in_at > OLD.last_sign_in_at)) THEN
        -- Chamar a função de log de forma segura
        PERFORM public.log_auth_event(
            'user_login',
            NEW.id,
            jsonb_build_object('email', NEW.email)
        );
    END IF;
    
    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    -- Nunca falhar a operação principal por causa do logging
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;
*/

-- 6. Registrar a execução desta migração
BEGIN
    INSERT INTO public.auth_logs (event_type, metadata, occurred_at)
    VALUES (
        'fix_auth_triggers_applied', 
        jsonb_build_object(
            'migration', '20250604_fix_auth_triggers.sql',
            'description', 'Resolução de conflitos em funções e triggers de autenticação'
        ), 
        now()
    );
EXCEPTION WHEN OTHERS THEN
    -- Ignorar erros ao registrar esta migração
    NULL;
END;

-- Comentários para documentação
COMMENT ON FUNCTION public.log_auth_event(TEXT, UUID, JSONB) IS 'Função segura para registrar eventos de autenticação sem interferir no processo de login';
