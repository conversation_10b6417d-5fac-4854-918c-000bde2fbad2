import { useState, useEffect, useCallback } from 'react';
import {
  getOfflineData,
  setOfflineData,
  removeOfflineData,
} from '@/lib/offline/offline-storage';

export interface OfflineDataHook<T> {
  data: T;
  loading: boolean;
  error: string | null;
  save: (value: T, ttl?: number) => Promise<void>;
  remove: () => Promise<void>;
}

export function useOfflineData<T>(
  store: string,
  key: string,
  initialValue: T
): OfflineDataHook<T> {
  const [data, setData] = useState<T>(initialValue);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    getOfflineData<T>(store, key)
      .then((value) => {
        if (mounted && value !== null) {
          setData(value);
        }
      })
      .catch((err) => {
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Failed to load data');
        }
      })
      .finally(() => {
        if (mounted) {
          setLoading(false);
        }
      });

    return () => {
      mounted = false;
    };
  }, [store, key]);

  const save = useCallback(
    async (value: T, ttl?: number) => {
      try {
        await setOfflineData(store, key, value, ttl);
        setData(value);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to save data');
      }
    },
    [store, key]
  );

  const remove = useCallback(async () => {
    try {
      await removeOfflineData(store, key);
      setData(initialValue);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove data');
    }
  }, [store, key, initialValue]);

  return {
    data,
    loading,
    error,
    save,
    remove,
  };
}
