export const getCommitHash = (): string => {
  // Durante o build, o Vite pode injetar o commit hash via variável de ambiente
  const gitCommit = import.meta.env.VITE_GIT_COMMIT || 
                   import.meta.env.GIT_COMMIT ||
                   'dev-build';
  
  // Se for um hash completo, pega apenas os primeiros 7 caracteres
  if (gitCommit.length > 7 && gitCommit !== 'dev-build') {
    return gitCommit.substring(0, 7);
  }
  
  return gitCommit;
};

export const getBuildInfo = () => {
  return {
    commit: getCommitHash(),
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    buildTime: import.meta.env.VITE_BUILD_TIME || new Date().toISOString()
  };
};