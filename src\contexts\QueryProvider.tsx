
import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Configurações otimizadas para o QueryClient
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Configurações globais para queries
      staleTime: 1 * 60 * 1000, // 1 minuto
      gcTime: 5 * 60 * 1000, // 5 minutos (novo nome para cacheTime)
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
      retry: (failureCount, error) => {
        // Retry strategy mais inteligente
        if (failureCount >= 3) return false;
        if (error?.message?.includes('Network')) return true;
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

export const QueryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};
