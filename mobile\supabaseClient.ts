import 'react-native-url-polyfill/auto'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { createClient } from '@supabase/supabase-js'
import { SUPABASE_URL, SUPABASE_KEY } from './env'

// Verificar se as variáveis de ambiente estão definidas
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('Variáveis de ambiente do Supabase não estão definidas!');
  console.log('SUPABASE_URL:', SUPABASE_URL);
  console.log('SUPABASE_KEY:', SUPABASE_KEY ? 'Definido (valor oculto)' : 'Não definido');
}

// Criar cliente Supabase com configurações específicas para mobile
export const supabase = createClient(
  SUPABASE_URL,
  SUPABASE_KEY,
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  }
);