
import React from 'react';
import { Outlet } from 'react-router-dom';
import { AppHeader } from '@/components/AppHeader';
import { MobileNavigation } from '@/components/MobileNavigation';
import { useUser } from '@/contexts/UnifiedAuthContext';

const DashboardLayout: React.FC = () => {
  const { user } = useUser();
  const userType = user?.user_metadata?.user_type || 'student';
  
  const getDashboardLink = () => {
    switch(userType) {
      case 'parent':
        return '/parent-dashboard';
      case 'student':
        return '/student-dashboard';
      case 'developer':
        return '/dev-dashboard';
      case 'admin':
        return '/admin-dashboard';
      default:
        return '/dashboard';
    }
  };

  return (
    <div className="min-h-screen flex flex-col w-full">
      {/* Header */}
      <AppHeader />
      
      {/* Main content */}
      <main className="flex-1 container mx-auto px-4 py-8 pb-20 md:pb-8">
        <Outlet />
      </main>
      
      {/* Mobile Navigation */}
      <MobileNavigation 
        userType={userType}
        dashboardLink={getDashboardLink()}
      />
    </div>
  );
};

export default DashboardLayout;
