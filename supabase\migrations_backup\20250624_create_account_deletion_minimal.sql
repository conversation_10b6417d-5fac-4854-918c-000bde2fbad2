-- ==========================================
-- SISTEMA DE SOLICITAÇÕES DE EXCLUSÃO - VERSÃO MINIMAL
-- Data: 24/06/2025
-- Objetivo: Apenas criar tabela sem RPC (contorna quota)
-- ==========================================

-- 1. CRIAR TABELA APENAS
CREATE TABLE IF NOT EXISTS "public"."account_deletion_requests" (
    "id" UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    "student_id" UUID NOT NULL,
    "student_email" TEXT NOT NULL,
    "student_name" TEXT NOT NULL,
    "reason" TEXT,
    "status" TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    "requested_at" TIMESTAMPTZ DEFAULT now(),
    "processed_at" TIMESTAMPTZ,
    "processed_by_guardian_email" TEXT,
    "guardian_notes" TEXT,
    "created_at" TIMESTAMPTZ DEFAULT now(),
    "updated_at" TIMESTAMPTZ DEFAULT now()
);

-- 2. INDICES BÁSICOS
CREATE INDEX IF NOT EXISTS "idx_account_deletion_requests_student_id" 
ON "public"."account_deletion_requests"("student_id");

CREATE INDEX IF NOT EXISTS "idx_account_deletion_requests_status" 
ON "public"."account_deletion_requests"("status");

-- 3. RLS SIMPLES
ALTER TABLE "public"."account_deletion_requests" ENABLE ROW LEVEL SECURITY;

-- Política básica para leitura
DROP POLICY IF EXISTS "Enable read access for authenticated users" 
ON "public"."account_deletion_requests";

CREATE POLICY "Enable read access for authenticated users"
ON "public"."account_deletion_requests"
FOR SELECT
TO authenticated
USING (true);

-- Política básica para insert
DROP POLICY IF EXISTS "Enable insert for authenticated users" 
ON "public"."account_deletion_requests";

CREATE POLICY "Enable insert for authenticated users"
ON "public"."account_deletion_requests"
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Política básica para update
DROP POLICY IF EXISTS "Enable update for authenticated users" 
ON "public"."account_deletion_requests";

CREATE POLICY "Enable update for authenticated users"
ON "public"."account_deletion_requests"
FOR UPDATE
TO authenticated
USING (true);

-- COMENTÁRIO: RPC functions removidas para contornar limitações de quota
-- O hook JavaScript fará queries diretas 