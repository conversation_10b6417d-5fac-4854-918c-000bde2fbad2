import { useState, useEffect } from 'react';

export type MapStyle =
  | 'streets-v12'
  | 'satellite-streets-v12'
  | 'light-v11'
  | 'dark-v11'
  | 'outdoors-v12';

const STORAGE_KEY = 'map-style';
const VALID_STYLES: MapStyle[] = [
  'streets-v12',
  'satellite-streets-v12',
  'light-v11',
  'dark-v11',
  'outdoors-v12',
];

export function useMapStyle(initial: MapStyle = 'streets-v12') {
  const [style, setStyle] = useState<MapStyle>(() => {
    if (typeof window === 'undefined') return initial;
    const stored = localStorage.getItem(STORAGE_KEY) as MapStyle | null;
    if (stored && VALID_STYLES.includes(stored as MapStyle)) {
      return stored as MapStyle;
    }
    // Limpar valor inválido
    if (stored) localStorage.removeItem(STORAGE_KEY);
    return initial;
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEY, style);
    }
  }, [style]);

  return [style, setStyle] as const;
}
