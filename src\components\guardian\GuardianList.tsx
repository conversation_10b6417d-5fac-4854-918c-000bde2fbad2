import React from 'react';
import { Plus } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Mail, Check, AlertTriangle } from 'lucide-react';
import { EmptyState } from './GuardianListStates';
import ProfileStatusBadge from '@/components/profile/ProfileStatusBadge';

interface Guardian {
  id: string;
  full_name: string | null;
  email: string;
  phone: string | null;
  cpf?: string | null;
  birth_date?: string | null;
  status?: string;
  is_active: boolean;
  created_at: string;
}

interface GuardianListProps {
  guardians: Guardian[];
  loading: boolean;
  onAddClick: () => void;
  onRemoveGuardian: (id: string) => Promise<void>;
  onSendInvite: (email: string, name: string | null) => Promise<void>;
}

const GuardianList: React.FC<GuardianListProps> = ({
  guardians,
  loading,
  onAddClick,
  onRemoveG<PERSON>ian,
  onSendInvite
}) => {

  const handleRemoveClick = (guardian: Guardian) => {
    // Mostrar mensagem explicativa sobre o novo processo
    alert(`🛡️ NOVO SISTEMA DE PROTEÇÃO

Para sua segurança, agora a remoção de responsáveis funciona assim:

✅ Estudante solicita remoção → Responsável aprova

Por favor, acesse o Student Dashboard e clique em "Solicitar Remoção" no card do responsável.

O responsável receberá um email e uma notificação no dashboard para aprovar ou rejeitar a solicitação.`);
  };

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array(3).fill(0).map((_, i) => (
          <div key={i} className="overflow-hidden">
            <div className="p-6">
              <Skeleton className="h-6 w-32 mb-2" />
              <Skeleton className="h-4 w-64 mb-4" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              <div className="flex justify-between mt-6">
                <Skeleton className="h-9 w-32" />
                <Skeleton className="h-9 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (guardians.length === 0) {
    return <EmptyState onAddClick={onAddClick} />;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {guardians.map((guardian) => (
        <Card key={guardian.id} className="dark:bg-zinc-900 dark:text-white">
          <CardHeader>
            <CardTitle className="dark:text-white">{guardian.full_name || "Responsável"}</CardTitle>
            <div className="text-sm text-muted-foreground dark:text-gray-200 flex items-center gap-2">
              {guardian.email}
              {guardian.status && <ProfileStatusBadge status={guardian.status} />}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground dark:text-gray-200">
              {guardian.phone && (
                <p className="dark:text-white">Telefone: {guardian.phone}</p>
              )}
              {guardian.cpf && (
                <p className="dark:text-white">CPF: {guardian.cpf.slice(0,3)}.***.***-{guardian.cpf.slice(-2)}</p>
              )}
              {guardian.birth_date && (
                <p className="dark:text-white">Nascimento: {new Date(guardian.birth_date).toLocaleDateString()}</p>
              )}
              <p className="dark:text-white">Adicionado em: {new Date(guardian.created_at).toLocaleDateString()}</p>
            </div>
          </CardContent>
          <CardFooter className="flex flex-wrap justify-between items-center gap-2">
            <Button 
              variant={guardian.is_active ? "secondary" : "outline"}
              size="sm"
              disabled={guardian.is_active}
              onClick={() => onSendInvite(guardian.email, guardian.full_name)}
              className={guardian.is_active ? "cursor-not-allowed" : "dark:border-white dark:text-white"}
            >
              {guardian.is_active ? (
                <>
                  <Check className="mr-2 h-4 w-4" /> Conectado
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" /> Convidar
                </>
              )}
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleRemoveClick(guardian)}
              className="border-orange-200 text-orange-700 hover:bg-orange-50 dark:border-white dark:text-white"
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              Solicitar Remoção
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

export default GuardianList;
