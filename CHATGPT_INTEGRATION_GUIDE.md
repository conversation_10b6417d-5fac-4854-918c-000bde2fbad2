# Integração do ChatGPT com EduConnect

Este guia apresenta um passo a passo simples para integrar o ChatGPT ao sistema EduConnect. A integração permite usar modelos da OpenAI para responder dúvidas comuns de usuários ou auxiliar em fluxos de atendimento.

## 1. Objetivos da Integração
- Responder perguntas frequentes de pais, alunos e professores
- Auxiliar na navegação das páginas do EduConnect
- Fornecer orientações rápidas sobre funcionalidades específicas

## 2. Estrutura Básica
1. **API da OpenAI**: utilize a chave de API para enviar mensagens ao modelo ChatGPT.
2. **Servidor de Intermediação**: configure um pequeno servidor em Node.js ou Python para receber solicitações do frontend e encaminhá-las à API da OpenAI.
3. **Componente React**: crie um formulário de chat no frontend onde o usuário digita a pergunta e visualiza a resposta.

```
Frontend (React) → Servidor de Intermediação → API OpenAI → Resposta → Frontend
```

## 3. Exemplo de Servidor (Node.js)
```javascript
import express from 'express';
import { Configuration, OpenAIApi } from 'openai';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
app.use(express.json());

const configuration = new Configuration({ apiKey: process.env.OPENAI_API_KEY });
const openai = new OpenAIApi(configuration);

app.post('/chat', async (req, res) => {
  const { message } = req.body;
  const completion = await openai.createChatCompletion({
    model: 'gpt-4',
    messages: [{ role: 'user', content: message }]
  });
  res.json(completion.data.choices[0].message);
});

app.listen(3001, () => {
  console.log('ChatGPT server running on port 3001');
});
```

## 4. Componente React Simplificado
```tsx
import { useState } from 'react';
import axios from 'axios';

export function ChatGPTWidget() {
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');

  const sendQuestion = async () => {
    const { data } = await axios.post('/chat', { message: question });
    setAnswer(data.content);
  };

  return (
    <div className="space-y-2">
      <textarea
        value={question}
        onChange={(e) => setQuestion(e.target.value)}
        className="w-full border p-2"
      />
      <button onClick={sendQuestion} className="bg-blue-600 text-white px-4 py-2">
        Perguntar
      </button>
      {answer && <p className="mt-2">{answer}</p>}
    </div>
  );
}
```

## 5. Considerações de Segurança
- Armazene a chave `OPENAI_API_KEY` em variáveis de ambiente.
- Implemente limites de uso e autenticação para evitar abusos.
- Monitore os logs do servidor de intermediação para detectar problemas.

## 6. Próximos Passos
1. Personalizar o prompt para refletir a linguagem da instituição.
2. Integrar o componente de chat em páginas como `ParentDashboard` ou `StudentDashboard`.
3. Criar respostas pré-definidas para perguntas comuns e fallback para ChatGPT.

---

Consulte esta documentação sempre que precisar reconfigurar ou ajustar a integração do ChatGPT com o EduConnect.
