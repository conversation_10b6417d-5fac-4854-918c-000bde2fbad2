import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-site-url',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders, status: 204 });
  }

  try {
    const {
      student_name,
      student_email,
      guardian_name,
      guardian_email,
      temp_password,
      activation_token,
      invitation_id,
      expires_at
    } = await req.json()

    console.log('[SEND_CREDENTIALS] Processing for:', student_email, 'invitation:', invitation_id)

    // Validate required fields
    if (!student_name || !student_email || !temp_password) {
      throw new Error('Dados obrigatórios ausentes para envio do email');
    }

    // Initialize Supabase Admin Client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase configuration missing');
    }

    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Create user in auth.users using Admin API
    console.log('[SEND_CREDENTIALS] Creating auth user...')
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: student_email,
      password: temp_password,
      email_confirm: true,
      user_metadata: {
        full_name: student_name,
        user_type: 'student',
        created_by_edge_function: true,
        invitation_id: invitation_id
      }
    })

    if (authError || !authUser.user) {
      console.error('[SEND_CREDENTIALS] Auth creation error:', authError)
      throw new Error(`Falha ao criar usuário: ${authError?.message || 'Unknown error'}`)
    }

    console.log('[SEND_CREDENTIALS] Auth user created:', authUser.user.id)

    // Complete student account creation via RPC (if invitation_id provided)
    // This step is CRITICAL for the activation token to be valid.
    if (invitation_id) {
      console.log('[SEND_CREDENTIALS] Completing account creation via RPC with invitation ID:', invitation_id);
      const { data: rpcResult, error: rpcError } = await supabaseAdmin
        .rpc('complete_student_account_creation', {
          p_invitation_id: invitation_id,
          p_auth_user_id: authUser.user.id
        });

      if (rpcError || (rpcResult && rpcResult.length > 0 && rpcResult[0].success === false)) {
        console.error('[SEND_CREDENTIALS] Critical RPC "complete_student_account_creation" failed:', rpcError || rpcResult[0].message);
        // Clean up the auth user if profile setup failed? This could be complex.
        // For now, throw an error to prevent sending a useless activation email.
        // await supabaseAdmin.auth.admin.deleteUser(authUser.user.id); // Consider implications
        throw new Error(`Falha ao finalizar configuração da conta do estudante (RPC): ${rpcError?.message || rpcResult?.[0]?.message || 'Erro desconhecido no RPC'}`);
      } else {
        console.log('[SEND_CREDENTIALS] RPC "complete_student_account_creation" successful:', rpcResult);
      }
    } else {
      // This path should ideally not be taken if the client always calls the new RPC first.
      // If it is, activation will likely fail as the token won't be in the profiles table correctly.
      console.warn('[SEND_CREDENTIALS] No invitation_id provided to send-student-credentials. Activation will likely fail.');
      // Depending on policy, could throw new Error here too.
      // For now, allowing it to proceed but with high chance of activation failure.
      // To be stricter: throw new Error('Falha crítica: invitation_id não fornecido para configuração da conta.');
    }

    // Construct activation URL
    const activationUrl = `${Deno.env.get('SITE_URL') || 'https://sistema-monitore.com.br'}/activate-account?token=${activation_token}`

    // Enhanced email template
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2563eb; margin: 0;">EduConnect</h1>
          <p style="color: #6b7280; margin: 5px 0;">Sistema de Monitoramento Educacional</p>
        </div>
        
        <h2 style="color: #2563eb;">✅ Sua conta foi criada com sucesso!</h2>
        
        <p>Olá <strong>${student_name}</strong>,</p>
        
        <p>Seu responsável <strong>${guardian_name || 'Responsável'}</strong> ${guardian_email ? `(${guardian_email})` : ''} criou uma conta para você no sistema EduConnect.</p>
        
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
          <h3 style="margin-top: 0; color: #1e293b;">🔑 Suas credenciais de acesso:</h3>
          <p style="margin: 10px 0;"><strong>Email:</strong> ${student_email}</p>
          <p style="margin: 10px 0;"><strong>Senha temporária:</strong> <code style="background: #e2e8f0; padding: 4px 8px; border-radius: 4px; font-family: monospace; font-size: 16px;">${temp_password}</code></p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${activationUrl}" 
             style="background-color: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;">
            🚀 Ativar Minha Conta
          </a>
        </div>
        
        <div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #f59e0b;">
          <p style="margin: 0; color: #92400e;">
            <strong>⚠️ Importante:</strong> Você precisará alterar sua senha no primeiro login. 
            Este link expira em 7 dias${expires_at ? ` (${new Date(expires_at).toLocaleDateString('pt-BR')})` : ''}.
          </p>
        </div>
        
        <div style="margin: 30px 0; padding: 20px; background-color: #f1f5f9; border-radius: 6px;">
          <h4 style="margin-top: 0; color: #1e293b;">📋 Próximos passos:</h4>
          <ol style="color: #475569; line-height: 1.6;">
            <li>Clique no botão "🚀 Ativar Minha Conta" acima</li>
            <li>Faça login com suas credenciais</li>
            <li>Crie uma nova senha segura</li>
            <li>Complete seu perfil</li>
            <li>Conecte-se com seu responsável</li>
          </ol>
        </div>
        
        <div style="background-color: #dbeafe; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #2563eb;">
          <p style="margin: 0; color: #1e40af;">
            <strong>🔒 Privacidade:</strong> Seus dados estão protegidos e só serão compartilhados com responsáveis autorizados por você.
          </p>
        </div>
        
        <p style="color: #6b7280; font-size: 14px;">Se você não solicitou esta conta, pode ignorar este email com segurança.</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
        <div style="text-align: center;">
          <p style="color: #6b7280; font-size: 14px; margin: 0;">
            <strong>EduConnect</strong><br>
            Sistema de Monitoramento Educacional Seguro<br>
            <em>Conectando famílias com segurança e confiança</em>
          </p>
        </div>
      </div>
    `

    // Send email using Resend
    const resendApiKey = Deno.env.get('RESEND_API_KEY')
    if (!resendApiKey) {
      console.error('[SEND_CREDENTIALS] RESEND_API_KEY not configured');
      throw new Error('RESEND_API_KEY not configured')
    }

    console.log('[SEND_CREDENTIALS] Sending email via Resend API...');

    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'EduConnect <<EMAIL>>',
        to: [student_email],
        subject: `✅ EduConnect - Sua conta foi criada com sucesso!`,
        html: emailHtml,
        reply_to: guardian_email || '<EMAIL>',
        tags: [
          { name: 'category', value: 'student_credentials' },
          { name: 'user_type', value: 'student' },
          { name: 'invitation_id', value: invitation_id || 'unknown' }
        ]
      }),
    })

    const emailResult = await emailResponse.json()

    if (!emailResponse.ok) {
      console.error('[SEND_CREDENTIALS] Resend API error:', emailResult)
      throw new Error(`Erro no envio do email: ${emailResult.message || 'API error'}`)
    }

    console.log('[SEND_CREDENTIALS] Email sent successfully:', emailResult.id)

    // Log success to database
    if (invitation_id) {
      await supabaseAdmin.from('auth_logs').insert({
        event_type: 'student_credentials_sent',
        user_id: authUser.user.id,
        metadata: {
          invitation_id: invitation_id,
          email_id: emailResult.id,
          recipient: student_email,
          guardian_email: guardian_email
        }
      })
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Conta criada e email enviado com sucesso',
        auth_user_id: authUser.user.id,
        email_id: emailResult.id,
        recipient: student_email,
        invitation_id: invitation_id
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('[SEND_CREDENTIALS] Error:', error)
    const errorMessage = error instanceof Error ? error.message : 'Erro ao criar conta e enviar email'
    
    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage,
        details: error instanceof Error ? error.stack : 'Unknown error'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
