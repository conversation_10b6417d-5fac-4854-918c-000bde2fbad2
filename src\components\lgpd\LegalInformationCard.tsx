
import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Shield } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const LegalInformationCard: React.FC = () => {
  const { t } = useTranslation();
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {t('lgpd.legalInfo.title')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>{t('lgpd.legalInfo.legalBasis')}</strong> {t('lgpd.legalInfo.consent')} {t('lgpd.legalInfo.dataCollection')} {t('lgpd.legalInfo.processing')} {t('lgpd.legalInfo.personalData')} {t('lgpd.legalInfo.consentDescription')}
            </AlertDescription>
          </Alert>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">{t('lgpd.legalInfo.purposesTitle')}</h4>
              <ul className="list-disc list-inside space-y-1 text-foreground dark:text-white">
                <li>{t('lgpd.legalInfo.purposes.auth')}</li>
                <li>{t('lgpd.legalInfo.purposes.location')}</li>
                <li>{t('lgpd.legalInfo.purposes.security')}</li>
                <li>{t('lgpd.legalInfo.purposes.experience')}</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">{t('lgpd.legalInfo.rightsTitle')}</h4>
              <ul className="list-disc list-inside space-y-1 text-foreground dark:text-white">
                <li>{t('lgpd.legalInfo.rights.access')}</li>
                <li>{t('lgpd.legalInfo.rights.correct')}</li>
                <li>{t('lgpd.legalInfo.rights.delete')}</li>
                <li>{t('lgpd.legalInfo.rights.portability')}</li>
                <li>{t('lgpd.legalInfo.rights.revoke')}</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 rounded border-l-4 border-blue-400 dark:bg-zinc-800 dark:border-blue-400">
            <p className="text-sm text-foreground dark:text-white">
              <strong>{t('lgpd.legalInfo.privacyQuestions')}</strong><br />
              {t('lgpd.legalInfo.contact')}<br />
              {t('lgpd.legalInfo.responseTime')}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LegalInformationCard;
