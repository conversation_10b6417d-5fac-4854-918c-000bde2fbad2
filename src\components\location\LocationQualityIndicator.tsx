
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  MapPin, 
  Target, 
  Wifi, 
  Satellite,
  RefreshCw,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { GeolocationResult } from '@/lib/services/location/AdvancedGeolocationService';
import { cn } from '@/lib/utils';

interface LocationQualityIndicatorProps {
  result: GeolocationResult | null;
  qualityIndicator: 'excellent' | 'good' | 'fair' | 'poor' | null;
  isLoading: boolean;
  isRefining: boolean;
  canImprove: boolean;
  onImproveAccuracy: () => void;
  onRefresh: () => void;
  className?: string;
  compact?: boolean;
}

const LocationQualityIndicator: React.FC<LocationQualityIndicatorProps> = ({
  result,
  qualityIndicator,
  isLoading,
  isRefining,
  canImprove,
  onImproveAccuracy,
  onRefresh,
  className,
  compact = false
}) => {
  if (!result && !isLoading) return null;

  const getQualityConfig = () => {
    switch (qualityIndicator) {
      case 'excellent':
        return {
          color: 'bg-green-500',
          textColor: 'text-green-700',
          bgColor: 'bg-green-50',
          icon: CheckCircle,
          label: 'Excelente',
          description: 'Localização muito precisa'
        };
      case 'good':
        return {
          color: 'bg-blue-500',
          textColor: 'text-blue-700',
          bgColor: 'bg-blue-50',
          icon: Target,
          label: 'Boa',
          description: 'Precisão adequada'
        };
      case 'fair':
        return {
          color: 'bg-yellow-500',
          textColor: 'text-yellow-700',
          bgColor: 'bg-yellow-50',
          icon: AlertTriangle,
          label: 'Regular',
          description: 'Precisão limitada'
        };
      case 'poor':
        return {
          color: 'bg-red-500',
          textColor: 'text-red-700',
          bgColor: 'bg-red-50',
          icon: AlertTriangle,
          label: 'Baixa',
          description: 'Precisão insuficiente'
        };
      default:
        return {
          color: 'bg-gray-500',
          textColor: 'text-gray-700',
          bgColor: 'bg-gray-50',
          icon: Clock,
          label: 'Obtendo...',
          description: 'Processando localização'
        };
    }
  };

  const getSourceIcon = () => {
    if (!result) return MapPin;
    
    switch (result.source) {
      case 'gps_high':
      case 'gps_medium':
      case 'gps_low':
        return Satellite;
      case 'ip_premium':
      case 'ip_fallback':
        return Wifi;
      case 'hybrid':
        return Target;
      default:
        return MapPin;
    }
  };

  const getSourceLabel = () => {
    if (!result) return 'GPS';
    
    switch (result.source) {
      case 'gps_high':
        return 'GPS Alta Precisão';
      case 'gps_medium':
        return 'GPS Médio';
      case 'gps_low':
        return 'GPS Baixo';
      case 'ip_premium':
        return 'IP Premium';
      case 'ip_fallback':
        return 'IP Aproximado';
      case 'hybrid':
        return 'Híbrido GPS+IP';
      default:
        return 'Localização';
    }
  };

  const config = getQualityConfig();
  const SourceIcon = getSourceIcon();
  const QualityIcon = config.icon;

  if (compact) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <div className={cn('flex items-center gap-1 px-2 py-1 rounded-md text-xs', config.bgColor, config.textColor)}>
          <QualityIcon className="h-3 w-3" />
          <span>{config.label}</span>
          {result && (
            <span className="opacity-75">±{Math.round(result.accuracy)}m</span>
          )}
        </div>
        
        {canImprove && (
          <Button
            variant="outline"
            size="sm"
            onClick={onImproveAccuracy}
            disabled={isRefining}
            className="h-6 px-2 text-xs"
          >
            {isRefining ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <TrendingUp className="h-3 w-3" />
            )}
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className={cn('w-3 h-3 rounded-full', config.color)} />
            <div>
              <div className="flex items-center gap-2">
                <QualityIcon className={cn('h-4 w-4', config.textColor)} />
                <span className="font-medium text-sm">{config.label}</span>
                <Badge variant="outline" className="text-xs">
                  <SourceIcon className="h-3 w-3 mr-1" />
                  {getSourceLabel()}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {config.description}
              </p>
            </div>
          </div>
          
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={isLoading || isRefining}
              className="h-7 px-2"
            >
              <RefreshCw className={cn('h-3 w-3', (isLoading || isRefining) && 'animate-spin')} />
            </Button>
            
            {canImprove && (
              <Button
                variant="outline"
                size="sm"
                onClick={onImproveAccuracy}
                disabled={isRefining}
                className="h-7 px-2"
              >
                {isRefining ? (
                  <RefreshCw className="h-3 w-3 animate-spin" />
                ) : (
                  <TrendingUp className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>

        {result && (
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="text-muted-foreground">Precisão:</span>
              <span className="ml-1 font-medium">±{Math.round(result.accuracy)}m</span>
            </div>
            <div>
              <span className="text-muted-foreground">Confiança:</span>
              <span className="ml-1 font-medium capitalize">{result.confidence}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Qualidade:</span>
              <span className="ml-1 font-medium">{result.quality_score}/100</span>
            </div>
            <div>
              <span className="text-muted-foreground">Fonte:</span>
              <span className="ml-1 font-medium">{getSourceLabel()}</span>
            </div>
          </div>
        )}

        {result?.address && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-xs text-muted-foreground">
              <MapPin className="h-3 w-3 inline mr-1" />
              {result.address}
            </p>
          </div>
        )}

        {isLoading && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <RefreshCw className="h-3 w-3 animate-spin" />
              <span>Obtendo localização de alta precisão...</span>
            </div>
          </div>
        )}

        {isRefining && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Target className="h-3 w-3" />
              <span>Refinando precisão...</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LocationQualityIndicator;
