import { Redis } from 'ioredis';
import { CircuitBreaker, CircuitBreakerState } from '@/lib/utils/circuit-breaker';

// Interfaces
interface LocationData {
  id: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: string;
  studentId: string;
  address?: string;
}

interface NotificationData {
  type: string;
  studentId?: string;
  guardianId?: string;
  timestamp: string;
  data?: any;
}

interface RedisHealthMetrics {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency: number;
  hitRate: number;
  memoryUsage?: string;
  connectedClients?: number;
  circuitBreakerState: CircuitBreakerState;
}

class RedisService {
  private client: Redis | null = null;
  private isConnected = false;
  private circuitBreaker: CircuitBreaker;
  private subscriptionClient: Redis | null = null;
  private publishClient: Redis | null = null;

  // Queue for failed notifications
  private notificationQueue: Array<{
    channel: string;
    notification: NotificationData;
    attempts: number;
    nextAttempt: number;
  }> = [];

  // Metrics
  private queuedMessages = 0;
  private retriedMessages = 0;
  private queueInterval: NodeJS.Timeout | null = null;
  
  // Métricas
  private cacheHits = 0;
  private cacheMisses = 0;
  private lastLatency = 0;

  constructor() {
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 5,
      resetTimeout: 30000, // 30 segundos
      name: 'redis-service',
      onStateChange: (newState, oldState, metrics) => {
        console.log(`[Redis] Circuit breaker: ${oldState} → ${newState}`, metrics);
      }
    });

    this.initializeClients();

    // Periodically process queued notifications
    this.queueInterval = setInterval(
      () => this.processNotificationQueue(),
      5000
    );
  }

  private async initializeClients(): Promise<void> {
    // Importar env após a classe estar definida para evitar circular imports
    const { env } = await import('@/env');
    
    const redisUrl = env.REDIS_URL;

    try {
      // Cliente principal para cache
      this.client = new Redis(redisUrl, {
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        retryStrategy: (times) => Math.min(times * 50, 2000)
      });

      // Cliente para publicação
      this.publishClient = new Redis(redisUrl);

      // Cliente para subscrição (dedicado)
      this.subscriptionClient = new Redis(redisUrl);

      // Event handlers
      this.setupEventHandlers();

      // Conectar
      await this.client.connect();
      this.isConnected = true;
      
      console.log('[Redis] Todos os clientes conectados com sucesso');
    } catch (error) {
      console.error('[Redis] Erro na inicialização:', error);
      this.isConnected = false;
    }
  }

  private setupEventHandlers(): void {
    if (!this.client) return;

    this.client.on('connect', () => {
      console.log('[Redis] Cliente principal conectado');
      this.isConnected = true;
      this.processNotificationQueue();
    });

    this.client.on('error', (error) => {
      console.error('[Redis] Erro no cliente principal:', error);
      this.isConnected = false;
    });

    this.client.on('ready', () => {
      console.log('[Redis] Cliente principal pronto');
      this.processNotificationQueue();
    });
  }

  /**
   * Método principal para cache com circuit breaker
   */
  async getCachedData<T>(
    cacheKey: string,
    fetchFunction: () => Promise<T>,
    ttl: number = 300
  ): Promise<T> {
    return await this.circuitBreaker.execute(
      async () => {
        const startTime = Date.now();
        
        // Tentar buscar no cache
        const cachedData = await this.client!.get(cacheKey);
        
        this.lastLatency = Date.now() - startTime;
        
        if (cachedData) {
          this.cacheHits++;
          console.log(`[Redis] Cache hit para: ${cacheKey}`);
          return JSON.parse(cachedData);
        }

        // Cache miss - buscar dados frescos
        this.cacheMisses++;
        console.log(`[Redis] Cache miss para: ${cacheKey}`);
        
        const freshData = await fetchFunction();
        
        // Armazenar no cache
        await this.client!.set(cacheKey, JSON.stringify(freshData), 'EX', ttl);
        
        return freshData;
      },
      // Fallback: buscar direto da fonte
      async () => {
        console.log(`[Redis] Circuit breaker ativo - usando fallback para: ${cacheKey}`);
        return await fetchFunction();
      }
    );
  }

  /**
   * Cache de dados com circuit breaker
   */
  async cacheData(
    cacheKey: string,
    data: any,
    ttl: number = 300
  ): Promise<boolean> {
    return await this.circuitBreaker.execute(
      async () => {
        await this.client!.set(cacheKey, JSON.stringify(data), 'EX', ttl);
        console.log(`[Redis] Dados armazenados no cache: ${cacheKey}`);
        return true;
      },
      async () => {
        console.log(`[Redis] Falha ao armazenar no cache: ${cacheKey}`);
        return false; // Operação silenciosa
      }
    );
  }

  /**
   * Invalidar cache
   */
  async invalidateCache(pattern: string): Promise<number> {
    return await this.circuitBreaker.execute(
      async () => {
        const keys = await this.client!.keys(pattern);
        if (keys.length > 0) {
          const deleted = await this.client!.del(...keys);
          console.log(`[Redis] ${deleted} chaves invalidadas com padrão: ${pattern}`);
          return deleted;
        }
        return 0;
      },
      async () => {
        console.log(`[Redis] Falha ao invalidar cache com padrão: ${pattern}`);
        return 0;
      }
    );
  }

  /**
   * Sistema de notificações em tempo real
   */
  async publishNotification(
    channel: string,
    notification: NotificationData
  ): Promise<boolean> {
    return await this.circuitBreaker.execute(
      async () => {
        if (!this.publishClient) throw new Error('Cliente de publicação não disponível');
        
        const result = await this.publishClient.publish(
          channel,
          JSON.stringify(notification)
        );
        
        console.log(`[Redis] Notificação publicada no canal ${channel}:`, notification);
        return result > 0;
      },
      async () => {
        console.warn(`[Redis] Falha ao publicar notificação no canal: ${channel}`);
        this.enqueueNotification(channel, notification);
        return false;
      }
    );
  }

  /**
   * Subscrever a um canal de notificações
   */
  async subscribe(
    channel: string,
    callback: (message: NotificationData) => void
  ): Promise<void> {
    if (!this.subscriptionClient) {
      console.error('[Redis] Cliente de subscrição não disponível');
      return;
    }

    await this.subscriptionClient.subscribe(channel);
    
    this.subscriptionClient.on('message', (receivedChannel, message) => {
      if (receivedChannel === channel) {
        try {
          const notification: NotificationData = JSON.parse(message);
          callback(notification);
        } catch (error) {
          console.error('[Redis] Erro ao parsear notificação:', error);
        }
      }
    });

    console.log(`[Redis] Subscrito ao canal: ${channel}`);
  }

  /**
   * Métodos específicos para localizações
   */
  async getLatestLocation(studentId: string): Promise<LocationData | null> {
    return await this.getCachedData(
      `student:location:${studentId}:latest`,
      async () => null, // Fallback retorna null se não houver cache
      900 // 15 minutos TTL
    );
  }

  async setLatestLocation(studentId: string, location: LocationData): Promise<void> {
    const cacheKey = `student:location:${studentId}:latest`;
    await this.cacheData(cacheKey, location, 900);

    // Publicar notificação de atualização
    await this.publishNotification('guardian:notifications', {
      type: 'location_update',
      studentId,
      timestamp: new Date().toISOString(),
      data: location
    });
  }

  async invalidateStudentLocation(studentId: string): Promise<void> {
    await this.invalidateCache(`student:location:${studentId}:*`);
  }

  /**
   * Cache de lista de estudantes por responsável
   */
  async getCachedStudentsList(guardianId: string): Promise<any[] | null> {
    return await this.getCachedData(
      `guardian:students:${guardianId}`,
      async () => null,
      1800 // 30 minutos
    );
  }

  async setCachedStudentsList(guardianId: string, students: any[]): Promise<void> {
    await this.cacheData(`guardian:students:${guardianId}`, students, 1800);
  }

  /**
   * Adiciona notificação à fila para retry
   */
  private enqueueNotification(channel: string, notification: NotificationData): void {
    this.notificationQueue.push({
      channel,
      notification,
      attempts: 0,
      nextAttempt: Date.now()
    });
    this.queuedMessages++;
    console.log(`[Redis] Notificação enfileirada para ${channel}`);
  }

  /**
   * Processa fila de notificações com backoff exponencial
   */
  private async processNotificationQueue(): Promise<void> {
    if (!this.isHealthy() || this.notificationQueue.length === 0 || !this.publishClient) {
      return;
    }

    const now = Date.now();

    for (let i = 0; i < this.notificationQueue.length; ) {
      const item = this.notificationQueue[i];

      if (now < item.nextAttempt) {
        i++;
        continue;
      }

      try {
        const result = await this.publishClient.publish(
          item.channel,
          JSON.stringify(item.notification)
        );
        if (result > 0) {
          console.log(`[Redis] Notificação reenviada para ${item.channel}:`, item.notification);
          this.notificationQueue.splice(i, 1);
          this.retriedMessages++;
          continue;
        }
      } catch (error) {
        console.warn('[Redis] Falha ao reenviar notificação:', error);
      }

      item.attempts++;
      const delay = Math.min(2 ** item.attempts * 1000, 60000);
      item.nextAttempt = Date.now() + delay;
      i++;
    }
  }

  getNotificationQueueMetrics() {
    return {
      queued: this.queuedMessages,
      retried: this.retriedMessages,
      pending: this.notificationQueue.length
    };
  }

  /**
   * Health check com métricas detalhadas
   */
  async healthCheck(): Promise<RedisHealthMetrics> {
    try {
      const startTime = Date.now();
      
      await this.circuitBreaker.execute(
        async () => {
          if (!this.client) throw new Error('Cliente Redis não disponível');
          const result = await this.client.ping();
          if (result !== 'PONG') throw new Error('Redis ping falhou');
        },
        async () => {
          throw new Error('Circuit breaker aberto');
        }
      );

      const latency = Date.now() - startTime;
      const hitRate = this.cacheHits / (this.cacheHits + this.cacheMisses) || 0;

      // Informações do servidor (se disponível)
      let memoryUsage, connectedClients;
      try {
        const info = await this.client?.info('memory');
        const clients = await this.client?.info('clients');
        memoryUsage = info?.match(/used_memory_human:(.+)/)?.[1]?.trim();
        connectedClients = parseInt(clients?.match(/connected_clients:(\d+)/)?.[1] || '0');
      } catch (error) {
        console.warn('[Redis] Não foi possível obter métricas do servidor:', error);
      }

      return {
        status: 'healthy',
        latency,
        hitRate: Math.round(hitRate * 100) / 100,
        memoryUsage,
        connectedClients,
        circuitBreakerState: this.circuitBreaker.getState()
      };

    } catch (error) {
      console.error('[Redis] Health check falhou:', error);
      
      return {
        status: this.circuitBreaker.getState() === 'OPEN' ? 'unhealthy' : 'degraded',
        latency: -1,
        hitRate: -1,
        circuitBreakerState: this.circuitBreaker.getState()
      };
    }
  }

  /**
   * Obter métricas do circuit breaker
   */
  getCircuitBreakerMetrics() {
    return this.circuitBreaker.getMetrics();
  }

  /**
   * Resetar circuit breaker manualmente
   */
  resetCircuitBreaker(): void {
    this.circuitBreaker.reset();
  }

  /**
   * Desconectar todos os clientes
   */
  async disconnect(): Promise<void> {
    try {
      await Promise.all([
        this.client?.quit(),
        this.publishClient?.quit(),
        this.subscriptionClient?.quit()
      ]);
      
      this.client = null;
      this.publishClient = null;
      this.subscriptionClient = null;
      this.isConnected = false;

      if (this.queueInterval) {
        clearInterval(this.queueInterval);
        this.queueInterval = null;
      }
      
      console.log('[Redis] Todos os clientes desconectados');
    } catch (error) {
      console.error('[Redis] Erro ao desconectar:', error);
    }
  }

  /**
   * Verificar se está conectado
   */
  isHealthy(): boolean {
    return this.isConnected && this.circuitBreaker.getState() !== 'OPEN';
  }
}

// Singleton instance
export const redisService = new RedisService();

// Backward compatibility
export { redisService as default };
