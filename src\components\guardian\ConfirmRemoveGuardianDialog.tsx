import React, { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Shield, Mail, Clock } from 'lucide-react';

interface ConfirmRemoveGuardianDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  guardianName: string | null;
  guardianEmail: string;
  isLoading?: boolean;
}

const ConfirmRemoveGuardianDialog: React.FC<ConfirmRemoveGuardianDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  guardianName,
  guardianEmail,
  isLoading = false
}) => {
  const [confirmationText, setConfirmationText] = useState('');
  const [step, setStep] = useState<'warning' | 'confirmation'>('warning');
  
  const expectedText = 'CONFIRMAR REMOÇÃO';
  const canProceed = confirmationText.toUpperCase().trim() === expectedText;
  
  const handleClose = () => {
    setConfirmationText('');
    setStep('warning');
    onClose();
  };
  
  const handleNext = () => {
    setStep('confirmation');
  };
  
  const handleConfirm = () => {
    if (canProceed) {
      onConfirm();
      handleClose();
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent className="max-w-md">
        {step === 'warning' ? (
          <>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2 text-destructive">
                <AlertTriangle className="h-5 w-5" />
                Remover Responsável
              </AlertDialogTitle>
              <div className="space-y-4 text-left">
                <AlertDialogDescription>Você está prestes a remover:</AlertDialogDescription>
                <div className="bg-muted p-3 rounded-md">
                  <p className="font-semibold">{guardianName || 'Responsável'}</p>
                  <p className="text-sm text-muted-foreground">{guardianEmail}</p>
                </div>
              </div>
            </AlertDialogHeader>
            
            <div className="space-y-3">
              <Alert variant="destructive">
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  <strong>Consequências da remoção:</strong>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>• Responsável não poderá mais acompanhar sua localização</li>
                    <li>• Histórico de compartilhamento será perdido</li>
                    <li>• Responsável será notificado da remoção</li>
                    <li>• Ação é irreversível (precisa reenviar convite)</li>
                  </ul>
                </AlertDescription>
              </Alert>
              
              <Alert>
                <Mail className="h-4 w-4" />
                <AlertDescription>
                  <strong>O que acontece depois:</strong>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>• Responsável recebe e-mail de notificação</li>
                    <li>• Você pode reconvidá-lo posteriormente se necessário</li>
                    <li>• Seus pais/responsáveis podem questionar esta ação</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </div>

            <AlertDialogFooter>
              <AlertDialogCancel onClick={handleClose}>
                Cancelar
              </AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleNext}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Continuar
              </AlertDialogAction>
            </AlertDialogFooter>
          </>
        ) : (
          <>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2 text-destructive">
                <Clock className="h-5 w-5" />
                Confirmação Final
              </AlertDialogTitle>
              <AlertDialogDescription>
                Para confirmar a remoção, digite exatamente:
              </AlertDialogDescription>
            </AlertDialogHeader>
            
            <div className="space-y-4">
              <div className="bg-muted p-2 rounded text-center font-mono text-sm">
                {expectedText}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirmation">Digite a confirmação:</Label>
                <Input
                  id="confirmation"
                  value={confirmationText}
                  onChange={(e) => setConfirmationText(e.target.value)}
                  placeholder="Digite a confirmação..."
                  className={confirmationText && !canProceed ? "border-destructive" : ""}
                />
                {confirmationText && !canProceed && (
                  <p className="text-sm text-destructive">
                    Texto não confere. Digite exatamente como mostrado acima.
                  </p>
                )}
              </div>
              
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Última chance!</strong> Esta ação não pode ser desfeita.
                  O responsável "{guardianName || guardianEmail}" será removido permanentemente.
                </AlertDescription>
              </Alert>
            </div>

            <AlertDialogFooter>
              <AlertDialogCancel onClick={handleClose}>
                Cancelar
              </AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleConfirm}
                disabled={!canProceed || isLoading}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90 disabled:opacity-50"
              >
                {isLoading ? 'Removendo...' : 'Confirmar Remoção'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </>
        )}
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ConfirmRemoveGuardianDialog; 