import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  User, 
  Mail, 
  MessageSquare,
  Calendar,
  AlertTriangle,
  Loader
} from 'lucide-react';
import { removalRequestService, RemovalRequest } from '@/lib/services/removal/RemovalRequestService';

export const ProcessRemovalPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [request, setRequest] = useState<RemovalRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [processed, setProcessed] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const token = searchParams.get('token');
  const action = searchParams.get('action') as 'approve' | 'reject' | null;

  useEffect(() => {
    if (!token || !action || !['approve', 'reject'].includes(action)) {
      setError('Link inválido ou parâmetros ausentes');
      setLoading(false);
      return;
    }

    loadRequest();
  }, [token, action]);

  const loadRequest = async () => {
    if (!token) return;

    try {
      setLoading(true);
      const requestData = await removalRequestService.getRemovalRequestByToken(token);
      
      if (!requestData) {
        setError('Solicitação não encontrada ou expirada');
      } else {
        setRequest(requestData);
      }
    } catch (error) {
      console.error('Erro ao carregar solicitação:', error);
      setError('Erro ao carregar dados da solicitação');
    } finally {
      setLoading(false);
    }
  };

  const handleProcess = async () => {
    if (!token || !action || !request) return;

    setProcessing(true);
    try {
      const result = await removalRequestService.processRemovalRequest(token, action);
      
      if (result.success) {
        setProcessed(true);
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error('Erro ao processar solicitação:', error);
      setError('Erro ao processar solicitação');
    } finally {
      setProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffMs = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) return 'Expirada';
    if (diffDays === 1) return '1 dia restante';
    return `${diffDays} dias restantes`;
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
              <p className="text-gray-600">Carregando solicitação...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-2" />
            <CardTitle className="text-red-600">Erro</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => navigate('/guardian-dashboard')} 
              className="w-full"
            >
              Ir para Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Success state
  if (processed) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            {action === 'approve' ? (
              <>
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                <CardTitle className="text-green-600">Remoção Aprovada</CardTitle>
                <CardDescription>
                  O estudante foi removido do seu monitoramento com sucesso.
                </CardDescription>
              </>
            ) : (
              <>
                <XCircle className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                <CardTitle className="text-blue-600">Solicitação Rejeitada</CardTitle>
                <CardDescription>
                  O estudante permanecerá no seu monitoramento.
                </CardDescription>
              </>
            )}
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => navigate('/guardian-dashboard')} 
              className="w-full"
            >
              Ir para Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Main content
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            {action === 'approve' ? (
              <CheckCircle className="h-8 w-8 text-red-500" />
            ) : (
              <XCircle className="h-8 w-8 text-green-500" />
            )}
            <CardTitle className="text-2xl">
              {action === 'approve' ? 'Confirmar Remoção' : 'Manter Monitoramento'}
            </CardTitle>
          </div>
          <CardDescription>
            {action === 'approve' ? 
              'Você está prestes a remover permanentemente este estudante do seu monitoramento.' :
              'Você vai rejeitar a solicitação e manter este estudante no seu monitoramento.'
            }
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {request && (
            <>
              {/* Detalhes da solicitação */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-3">Detalhes da Solicitação</h3>
                
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <User className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">{request.student_name}</p>
                      <p className="text-sm text-gray-600">Estudante</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-gray-500" />
                    <div>
                      <p>{request.student_email}</p>
                      <p className="text-sm text-gray-600">Email do estudante</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-gray-500" />
                    <div>
                      <p>{formatDate(request.created_at)}</p>
                      <p className="text-sm text-gray-600">Data da solicitação</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-gray-500" />
                    <div>
                      <Badge 
                        variant="outline" 
                        className="border-orange-300 text-orange-700"
                      >
                        {getTimeRemaining(request.expires_at)}
                      </Badge>
                      <p className="text-sm text-gray-600">Tempo restante</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Motivo */}
              {request.reason && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <MessageSquare className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900 mb-2">Motivo informado:</h4>
                      <p className="text-blue-700 italic">"{request.reason}"</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Consequências */}
              <div className={`border rounded-lg p-4 ${
                action === 'approve' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
              }`}>
                <div className="text-sm">
                  {action === 'approve' ? (
                    <>
                      <p className="font-medium text-red-700 mb-3">⚠️ Consequências da aprovação:</p>
                      <ul className="text-red-600 space-y-2">
                        <li className="flex items-start gap-2">
                          <span>•</span>
                          <span>O estudante será removido permanentemente do seu dashboard</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span>•</span>
                          <span>Você não receberá mais notificações de localização deste estudante</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span>•</span>
                          <span>O histórico de localizações será mantido para consulta</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span>•</span>
                          <span>Uma nova solicitação seria necessária para reconectar</span>
                        </li>
                      </ul>
                    </>
                  ) : (
                    <>
                      <p className="font-medium text-green-700 mb-3">✅ Consequências da rejeição:</p>
                      <ul className="text-green-600 space-y-2">
                        <li className="flex items-start gap-2">
                          <span>•</span>
                          <span>O estudante permanecerá no seu monitoramento</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span>•</span>
                          <span>Você continuará recebendo notificações de localização</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span>•</span>
                          <span>O estudante será informado da sua decisão</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span>•</span>
                          <span>Uma nova solicitação pode ser feita no futuro</span>
                        </li>
                      </ul>
                    </>
                  )}
                </div>
              </div>

              {/* Ações */}
              <div className="flex gap-3 pt-4">
                <Button 
                  variant="outline" 
                  onClick={() => navigate('/guardian-dashboard')}
                  className="flex-1"
                  disabled={processing}
                >
                  Cancelar
                </Button>
                <Button 
                  onClick={handleProcess}
                  disabled={processing}
                  className={`flex-1 ${action === 'approve' ? 
                    'bg-red-600 hover:bg-red-700' : 
                    'bg-green-600 hover:bg-green-700'
                  }`}
                >
                  {processing ? (
                    <>
                      <Loader className="h-4 w-4 animate-spin mr-2" />
                      Processando...
                    </>
                  ) : (
                    <>
                      {action === 'approve' ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Confirmar Remoção
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 mr-2" />
                          Manter Monitoramento
                        </>
                      )}
                    </>
                  )}
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Export default for lazy loading
export default ProcessRemovalPage; 