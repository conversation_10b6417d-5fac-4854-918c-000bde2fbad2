import React, { useState } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { UserType } from '@/lib/auth-redirects';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, InfoIcon } from "lucide-react";
import { Link } from "react-router-dom";

export interface ForgotPasswordFormProps {
  userType: UserType;
  onBackToLogin: () => void;
  variant?: 'login' | 'register';
}

const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  userType,
  onBackToLogin,
  variant,
}) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiError, setApiError] = useState<any>(null);
  const [detailedLogging, setDetailedLogging] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setApiError(null);
    
    if (!email) {
      toast({
        title: "Campo obrigatório",
        description: "Por favor, insira seu email.",
        variant: "destructive",
      });
      setLoading(false);
      return;
    }

    try {
      console.log(`[FORGOT_PASSWORD] Iniciando processo de recuperação para: ${email}`);
      
      // Ativar logs detalhados para diagnóstico
      if (detailedLogging) {
        console.log("[FORGOT_PASSWORD] Configuração do Supabase:", {
          hasAuth: !!supabase.auth,
          hasEdgeFunctions: !!supabase.functions
        });
      }

      // Primeiro, tentar usar a Edge Function personalizada
      try {
        console.log('[FORGOT_PASSWORD] Tentando Edge Function send-password-reset...');
        
        const { data: edgeResponse, error: edgeError } = await supabase.functions.invoke('send-password-reset', {
          body: { email }
        });

        if (edgeError) {
          console.warn('[FORGOT_PASSWORD] Edge Function falhou:', edgeError);
          throw edgeError;
        }

        console.log('[FORGOT_PASSWORD] Edge Function bem-sucedida:', edgeResponse);
        
        setSent(true);
        toast({
          title: "Link de recuperação enviado",
          description: "Verifique sua caixa de entrada (e a pasta de spam) para redefinir sua senha.",
        });
        
        return;
        
      } catch (edgeError) {
        console.warn('[FORGOT_PASSWORD] Edge Function não disponível, usando Supabase Auth padrão...');
        
        // Fallback para o método padrão do Supabase Auth
        const currentUrl = window.location.origin;
        console.log(`[FORGOT_PASSWORD] Usando URL base para recuperação: ${currentUrl}`);
        
        const { error: authError } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${currentUrl}/reset-password`,
        });

        if (authError) {
          console.error('[FORGOT_PASSWORD] Erro Supabase Auth:', authError);
          throw authError;
        }
        
        console.log('[FORGOT_PASSWORD] Recuperação via Supabase Auth bem-sucedida');
        
        setSent(true);
        toast({
          title: "Link de recuperação enviado",
          description: "Verifique sua caixa de entrada (e a pasta de spam) para redefinir sua senha.",
        });
      }
      
    } catch (error: any) {
      console.error('[FORGOT_PASSWORD] Erro geral:', error);
      setApiError(error);
      
      let errorMessage = "Não foi possível enviar o link de recuperação. Verifique o email e tente novamente.";
      
      if (error.message?.includes('rate limit') || error.status === 429) {
        errorMessage = 'Muitas solicitações. Aguarde alguns minutos e tente novamente.';
      } else if (error.message?.includes('network') || error.message?.includes('Network Error')) {
        errorMessage = 'Problema de conexão. Verifique sua internet e tente novamente.';
      } else if (error.message?.includes('User not found')) {
        errorMessage = 'Email não encontrado. Verifique se está correto ou cadastre-se primeiro.';
      } else if (error.message?.includes('Invalid email')) {
        errorMessage = 'Email inválido. Verifique o formato do email.';
      }
      
      setError(errorMessage);
      
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleDetailedLogging = () => {
    setDetailedLogging(prev => !prev);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 p-4" data-cy="forgot-password-form">
      {error && (
        <Alert variant="destructive" className="mb-4" data-cy="email-error-message">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {apiError && detailedLogging && (
        <Alert variant="destructive" className="mb-4 text-xs">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Erro técnico:</strong> {JSON.stringify(apiError, null, 2)}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Resto do formulário */}
      {!sent ? (
        <>
          <div className="space-y-2">
            <label htmlFor={`recovery${userType === 'student' ? 'Student' : 'Parent'}Email`} className="block text-sm font-medium text-gray-700">
              E-mail
            </label>
            <Input
              id={`recovery${userType === 'student' ? 'Student' : 'Parent'}Email`}
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Digite seu e-mail cadastrado"
              required
              data-cy="recovery-email-input"
            />
          </div>
          
          <Button type="submit" className="w-full" disabled={loading} data-cy="send-recovery-button">
            {loading ? 'Enviando...' : 'Enviar link de recuperação'}
          </Button>
          
          <div className="bg-blue-50 text-blue-800 p-3 rounded-md text-sm flex items-start">
            <InfoIcon className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p>Se você não receber o email, verifique:</p>
              <ul className="list-disc pl-5 mt-1">
                <li>Se o email está correto</li>
                <li>A pasta de spam/lixo eletrônico</li>
                <li>Se o email está cadastrado no sistema</li>
              </ul>
              <div className="mt-2 text-xs text-blue-700 flex justify-between items-center">
                <Link to="/email-diagnostic" className="underline" data-cy="email-diagnostic-link">
                  Diagnóstico do sistema de email
                </Link>
                <button
                  type="button" 
                  onClick={toggleDetailedLogging}
                  className="text-xs text-blue-600"
                >
                  {detailedLogging ? 'Desativar logs' : 'Ativar logs detalhados'}
                </button>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="text-center py-4" data-cy="success-message">
          <p className="mb-4">Link de recuperação enviado para <strong>{email}</strong></p>
          <p className="text-sm text-gray-600 mb-4">
            Verifique sua caixa de entrada (e também a pasta de spam) e siga as instruções para redefinir sua senha.
          </p>
          <div className="space-y-3">
            <Button onClick={() => { setSent(false); setEmail(''); }} variant="outline" className="mt-2">
              Tentar com outro email
            </Button>
            
            <div>
              <Link to="/password-reset-test" className="text-xs text-blue-600 block mt-2">
                Não recebeu? Execute o teste de diagnóstico completo
              </Link>
            </div>
          </div>
        </div>
      )}
      
      <div className="text-center mt-4">
        <button
          type="button"
          onClick={onBackToLogin}
          className="text-sm text-blue-600 hover:underline focus:outline-none"
          data-cy="back-to-login-button"
        >
          Voltar para o login
        </button>
      </div>
    </form>
  );
};

export default ForgotPasswordForm;

