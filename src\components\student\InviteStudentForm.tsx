
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { validateCPF, formatCPF } from '@/lib/utils/cpf-validator';
import { useInviteStudent } from '@/hooks/useInviteStudent';
import { useTranslation } from 'react-i18next';

const formSchema = z.object({
  cpf: z.string()
    .min(1, { message: "CPF é obrigatório." })
    .refine((cpf) => {
      const validation = validateCPF(cpf);
      return validation.isValid;
    }, {
      message: "CPF inválido. Verifique os dígitos informados.",
    }),
  name: z.string().min(3, {
    message: 'Nome deve ter pelo menos 3 caracteres',
  }),
  email: z.string().optional().refine((email) => {
    // Se vazio ou undefined, é válido (opcional)
    if (!email || email.trim() === '') return true;
    // Se preenchido, deve ser um email válido
    return z.string().email().safeParse(email).success;
  }, {
    message: 'Email inválido',
  }),
  phone: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export function InviteStudentForm({ onStudentAdded }: { onStudentAdded?: () => void }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { inviteStudent, isLoading } = useInviteStudent();
  const { t, i18n } = useTranslation();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      cpf: '',
      name: '',
      email: '',
      phone: '',
    },
  });

  const showCpf = i18n.language !== 'en-GB';

  const handleCPFChange = (field: any) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCPF(e.target.value);
    field.onChange(formatted);
  };

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      const result = await inviteStudent({
        studentName: data.name,
        studentEmail: data.email || '',
        studentCpf: data.cpf,
        studentPhone: data.phone || ''
      });
      
      if (result.success) {
        form.reset();
        if (onStudentAdded) {
          onStudentAdded();
        }
      }
    } catch (error) {
      console.error('Erro ao processar convite:', error);
      toast({
        title: 'Erro',
        description: 'Houve um erro ao processar o convite',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {showCpf && (
          <FormField
            control={form.control}
            name="cpf"
            render={({ field }) => (
              <FormItem>
                <FormLabel>CPF</FormLabel>
                <FormControl>
                  <Input
                    placeholder="000.000.000-00"
                    {...field}
                    onChange={handleCPFChange(field)}
                    maxLength={14}
                  />
                </FormControl>
                <FormMessage />
                <p className="text-xs text-gray-500">
                  {t('student.cpfHint', 'O CPF será usado para identificar o estudante no sistema.')}
                </p>
              </FormItem>
            )}
          />
        )}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('student.studentName')}</FormLabel>
              <FormControl>
                <Input placeholder={t('student.studentName')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email (opcional)</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
              <p className="text-xs text-gray-500">
                {t('student.inviteEmailHint')}
              </p>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('student.studentPhone')}</FormLabel>
              <FormControl>
                <Input placeholder="(00) 00000-0000" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" disabled={isLoading || isSubmitting} className="w-full">
          {isLoading || isSubmitting ? t('student.sendingInvite') : t('student.inviteButton')}
        </Button>
        <p className="text-xs text-muted-foreground text-center">
          {t('student.inviteEmailHint')}
        </p>
      </form>
    </Form>
  );
}

export default InviteStudentForm;
