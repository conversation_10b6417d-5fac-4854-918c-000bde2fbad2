/* iOS Map Optimizations - Específico para melhorar a experiência do mapa no iOS */

/* Otimizações gerais para iOS */
@supports (-webkit-touch-callout: none) {
  /* Detecta dispositivos iOS */
  
  /* Container principal do dashboard - remove padding desnecessário */
  .student-dashboard-container {
    padding: 0;
    margin: 0;
  }
  
  /* Mapa responsivo - ocupa máximo espaço no iOS */
  .map-responsive {
    width: 100vw !important;
    height: calc(100vh - 120px) !important; /* Considera header e tabs */
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 0 !important; /* Remove bordas arredondadas em tela cheia */
  }
  
  /* Container das tabs - otimizado para iOS */
  .ios-tabs-container {
    height: calc(100vh - 80px) !important;
    width: 100vw !important;
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* Conteúdo das tabs - usa toda altura disponível */
  .ios-tab-content {
    height: calc(100vh - 140px) !important;
    width: 100vw !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* Header das tabs - compacto no iOS */
  .ios-tabs-header {
    padding: 8px 16px !important;
    height: 60px !important;
    flex-shrink: 0 !important;
  }
  
  /* Mapbox container - otimizações específicas */
  .mapboxgl-map {
    width: 100vw !important;
    height: 100% !important;
    border-radius: 0 !important;
  }
  
  /* Controles do mapa - posicionamento otimizado para iOS */
  .mapboxgl-ctrl-top-right {
    top: 10px !important;
    right: 10px !important;
  }
  
  /* Remove margens e paddings que limitam o mapa */
  .map-container-ios {
    width: 100vw !important;
    height: 100% !important;
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
  }
}

/* Otimizações específicas para iPhone */
@media screen and (max-width: 428px) and (-webkit-min-device-pixel-ratio: 2) {
  /* iPhone específico */
  
  .map-responsive {
    width: 100vw !important;
    height: calc(100vh - 100px) !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
  }
  
  /* Container principal - remove limitações de largura */
  .student-dashboard-mobile {
    width: 100vw !important;
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* Seções de informação - compactas em iPhone */
  .student-info-mobile {
    padding: 8px !important;
    margin: 4px !important;
  }
  
  /* Tabs - otimizadas para iPhone */
  .mobile-tabs {
    font-size: 14px !important;
    padding: 6px 12px !important;
  }
}

/* Otimizações para iPad */
@media screen and (min-width: 768px) and (max-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
  .map-responsive {
    width: 100% !important;
    height: calc(100vh - 160px) !important;
    max-width: none !important;
  }
  
  .ios-tabs-container {
    height: calc(100vh - 120px) !important;
  }
}

/* Correções para Safari iOS - viewport height issues */
@supports (-webkit-appearance: none) {
  .map-responsive {
    height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 120px) !important;
  }
  
  /* Suporte para safe areas do iOS */
  .ios-safe-area {
    padding-top: env(safe-area-inset-top) !important;
    padding-bottom: env(safe-area-inset-bottom) !important;
    padding-left: env(safe-area-inset-left) !important;
    padding-right: env(safe-area-inset-right) !important;
  }
}

/* Otimizações de performance para iOS */
.map-responsive,
.mapboxgl-map {
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
  -webkit-perspective: 1000 !important;
  perspective: 1000 !important;
}

/* Remove bounce effect no iOS */
body {
  -webkit-overflow-scrolling: touch !important;
  overflow-scrolling: touch !important;
}

/* Otimizações para touch no iOS */
.map-responsive,
.mapboxgl-canvas {
  touch-action: pan-x pan-y !important;
  -webkit-user-select: none !important;
  user-select: none !important;
}
