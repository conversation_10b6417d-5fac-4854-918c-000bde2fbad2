import React from 'react';

interface ProfileStatusBadgeProps {
  status?: string;
}

const statusColors: Record<string, string> = {
  active: 'bg-green-500',
  inactive: 'bg-yellow-500',
  suspended: 'bg-red-500',
  pending: 'bg-blue-500',
};

export const ProfileStatusBadge: React.FC<ProfileStatusBadgeProps> = ({ status = 'active' }) => {
  const color = statusColors[status] || 'bg-gray-500';
  return (
    <span
      className={`inline-flex items-center rounded-full border border-transparent px-2.5 py-0.5 text-xs font-semibold text-white ${color}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

export default ProfileStatusBadge;
