http://localhost:4000/profile

Voltar
Perfil do Usuário
Informações Pessoais
Nome Completo
Luciana <PERSON>rreira
Email
<EMAIL>
Telefone
+5592985880080
CPF
***********
Tipo de Usuário
Responsável

Salvar Alterações
Preferências de Comunicação
Email
Sempre ativo para notificações importantes


SMS
Mensagens de texto para seu celular


WhatsApp
Mensagens via WhatsApp Business


Método Preferido
Email
Canal usado primeiro quando você compartilhar localização

Salvar Preferências
💡 Dica Importante
Se o estudante já possui cadastro no sistema, use a seção "Vínculos Familiares" abaixo para se conectar a ele. O formulário de "Adicionar Estudante" é apenas para criar novos usuários.

Estudantes Vinculados
Maurício Williams Ferreira
<EMAIL>


Vínculos Familiares
Vincular Estudante Existente
Solicitações Enviadas (1)
<PERSON> Lima
<EMAIL>
Solicitação enviada em: 25/06/2025, 16:21
Expira em: 02/07/2025, 16:21
Aguardando resposta
O estudante precisa aceitar sua solicitação

ℹ️ Como funciona:

Você enviou uma solicitação de vínculo. O estudante receberá uma notificação e poderá aceitar ou rejeitar sua solicitação. Quando ele responder, você será notificado do resultado.

Como funciona
Para se vincular a um estudante que já possui cadastro:

Use o botão "Vincular Estudante Existente" acima
Informe o email do estudante (obrigatório)
Adicione o CPF para validação extra (recomendado)
O estudante receberá uma notificação para aceitar o vínculo
Após aceitar, você poderá visualizar e gerenciar as informações dele
⚠️ Importante: Este processo é para estudantes que JÁ possuem conta no sistema. Para criar um novo estudante, use o menu "Adicionar Estudante".

Solicitações de Exclusão
Nenhuma solicitação pendente
Não há solicitações de exclusão aguardando sua aprovação.

Quando seus estudantes solicitarem exclusão de conta, aparecerão aqui.

Excluir Minha Conta
Remove permanentemente todos os seus dados. Esta ação não pode ser desfeita.

Excluir Minha Conta



hook.js:608 ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition. Error Component Stack
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
warnOnce @ react-router-dom.js?v=1b56d892:4394
logDeprecation @ react-router-dom.js?v=1b56d892:4397
logV6DeprecationWarnings @ react-router-dom.js?v=1b56d892:4400
(anonymous) @ react-router-dom.js?v=1b56d892:5272
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
commitPassiveMountOnFiber @ chunk-W6L2VRDA.js?v=1b56d892:18156
commitPassiveMountEffects_complete @ chunk-W6L2VRDA.js?v=1b56d892:18129
commitPassiveMountEffects_begin @ chunk-W6L2VRDA.js?v=1b56d892:18119
commitPassiveMountEffects @ chunk-W6L2VRDA.js?v=1b56d892:18109
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19490
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
hook.js:608 ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath. Error Component Stack
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
warnOnce @ react-router-dom.js?v=1b56d892:4394
logDeprecation @ react-router-dom.js?v=1b56d892:4397
logV6DeprecationWarnings @ react-router-dom.js?v=1b56d892:4403
(anonymous) @ react-router-dom.js?v=1b56d892:5272
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
commitPassiveMountOnFiber @ chunk-W6L2VRDA.js?v=1b56d892:18156
commitPassiveMountEffects_complete @ chunk-W6L2VRDA.js?v=1b56d892:18129
commitPassiveMountEffects_begin @ chunk-W6L2VRDA.js?v=1b56d892:18119
commitPassiveMountEffects @ chunk-W6L2VRDA.js?v=1b56d892:18109
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19490
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
client.ts:17 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
overrideMethod @ hook.js:608
_GoTrueClient @ @supabase_supabase-js.js?v=1b56d892:5040
SupabaseAuthClient @ @supabase_supabase-js.js?v=1b56d892:6906
_initSupabaseAuthClient @ @supabase_supabase-js.js?v=1b56d892:7102
SupabaseClient @ @supabase_supabase-js.js?v=1b56d892:6975
createClient @ @supabase_supabase-js.js?v=1b56d892:7142
(anonymous) @ client.ts:17
hook.js:608 "featureNamespace place-A of featureset place-labels's selector is not associated to the same source, skip this selector
overrideMethod @ hook.js:608
pt @ mapbox-gl.js?v=1b56d892:1456
setFeaturesetSelectors @ mapbox-gl.js?v=1b56d892:23121
s2 @ mapbox-gl.js?v=1b56d892:22524
_load @ mapbox-gl.js?v=1b56d892:22541
(anonymous) @ mapbox-gl.js?v=1b56d892:22455
useAccountDeletionRequests.ts:175 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at commitPassiveMountOnFiber (chunk-W6L2VRDA.js?v=1b56d892:18156:19)
    at commitPassiveMountEffects_complete (chunk-W6L2VRDA.js?v=1b56d892:18129:17)
    at commitPassiveMountEffects_begin (chunk-W6L2VRDA.js?v=1b56d892:18119:15)
    at commitPassiveMountEffects (chunk-W6L2VRDA.js?v=1b56d892:18109:11)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19490:11)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11) Error Component Stack
    at AccountDeletionRequestsList (AccountDeletionRequestsList.tsx:29:65)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ProfilePage (ProfilePage.tsx:30:20)
    at ProtectedRoute (ProtectedRoute.tsx:12:3)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Outlet (react-router-dom.js?v=1b56d892:4495:26)
    at main (<anonymous>)
    at div (<anonymous>)
    at DashboardLayout (<anonymous>)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Routes (react-router-dom.js?v=1b56d892:4559:5)
    at Suspense (<anonymous>)
    at div (<anonymous>)
    at Router (react-router-dom.js?v=1b56d892:4502:15)
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
fetchRequests @ useAccountDeletionRequests.ts:175
(anonymous) @ useAccountDeletionRequests.ts:268
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
commitPassiveMountOnFiber @ chunk-W6L2VRDA.js?v=1b56d892:18156
commitPassiveMountEffects_complete @ chunk-W6L2VRDA.js?v=1b56d892:18129
commitPassiveMountEffects_begin @ chunk-W6L2VRDA.js?v=1b56d892:18119
commitPassiveMountEffects @ chunk-W6L2VRDA.js?v=1b56d892:18109
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19490
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
useAccountDeletionRequests.ts:175 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at invokePassiveEffectMountInDEV (chunk-W6L2VRDA.js?v=1b56d892:18324:19)
    at invokeEffectsInDev (chunk-W6L2VRDA.js?v=1b56d892:19701:19)
    at commitDoubleInvokeEffectsInDEV (chunk-W6L2VRDA.js?v=1b56d892:19686:15)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19503:13)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11)
    at flushSyncCallbacks (chunk-W6L2VRDA.js?v=1b56d892:9119:30)
overrideMethod @ hook.js:608
fetchRequests @ useAccountDeletionRequests.ts:175
(anonymous) @ useAccountDeletionRequests.ts:268
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
invokePassiveEffectMountInDEV @ chunk-W6L2VRDA.js?v=1b56d892:18324
invokeEffectsInDev @ chunk-W6L2VRDA.js?v=1b56d892:19701
commitDoubleInvokeEffectsInDEV @ chunk-W6L2VRDA.js?v=1b56d892:19686
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19503
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
hook.js:608 React DevTools detected duplicate welcome "message" events from the content script.
overrideMethod @ hook.js:608
hook.js:608 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at commitPassiveMountOnFiber (chunk-W6L2VRDA.js?v=1b56d892:18156:19)
    at commitPassiveMountEffects_complete (chunk-W6L2VRDA.js?v=1b56d892:18129:17)
    at commitPassiveMountEffects_begin (chunk-W6L2VRDA.js?v=1b56d892:18119:15)
    at commitPassiveMountEffects (chunk-W6L2VRDA.js?v=1b56d892:18109:11)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19490:11)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11) Error Component Stack
    at AccountDeletionRequestsList (AccountDeletionRequestsList.tsx:29:65)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ProfilePage (ProfilePage.tsx:30:20)
    at ProtectedRoute (ProtectedRoute.tsx:12:3)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Outlet (react-router-dom.js?v=1b56d892:4495:26)
    at main (<anonymous>)
    at div (<anonymous>)
    at DashboardLayout (<anonymous>)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Routes (react-router-dom.js?v=1b56d892:4559:5)
    at Suspense (<anonymous>)
    at div (<anonymous>)
    at Router (react-router-dom.js?v=1b56d892:4502:15)
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
hook.js:608 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at commitPassiveMountOnFiber (chunk-W6L2VRDA.js?v=1b56d892:18156:19)
    at commitPassiveMountEffects_complete (chunk-W6L2VRDA.js?v=1b56d892:18129:17)
    at commitPassiveMountEffects_begin (chunk-W6L2VRDA.js?v=1b56d892:18119:15)
    at commitPassiveMountEffects (chunk-W6L2VRDA.js?v=1b56d892:18109:11)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19490:11)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11) Error Component Stack
    at AccountDeletionRequestsList (AccountDeletionRequestsList.tsx:29:65)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ProfilePage (ProfilePage.tsx:30:20)
    at ProtectedRoute (ProtectedRoute.tsx:12:3)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Outlet (react-router-dom.js?v=1b56d892:4495:26)
    at main (<anonymous>)
    at div (<anonymous>)
    at DashboardLayout (<anonymous>)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Routes (react-router-dom.js?v=1b56d892:4559:5)
    at Suspense (<anonymous>)
    at div (<anonymous>)
    at Router (react-router-dom.js?v=1b56d892:4502:15)
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
hook.js:608 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at invokePassiveEffectMountInDEV (chunk-W6L2VRDA.js?v=1b56d892:18324:19)
    at invokeEffectsInDev (chunk-W6L2VRDA.js?v=1b56d892:19701:19)
    at commitDoubleInvokeEffectsInDEV (chunk-W6L2VRDA.js?v=1b56d892:19686:15)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19503:13)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11)
    at flushSyncCallbacks (chunk-W6L2VRDA.js?v=1b56d892:9119:30)
overrideMethod @ hook.js:608
VM5626 backendManager.js:1 React DevTools detected duplicate welcome "message" events from the content script.
overrideMethod @ hook.js:608
welcome @ VM5626 backendManager.js:1
(anonymous) @ main.tsx:88
(anonymous) @ main.tsx:107
postMessage
handleMessageFromDevtools @ proxy.js:1
useAccountDeletionRequests.ts:175 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at commitPassiveMountOnFiber (chunk-W6L2VRDA.js?v=1b56d892:18156:19)
    at commitPassiveMountEffects_complete (chunk-W6L2VRDA.js?v=1b56d892:18129:17)
    at commitPassiveMountEffects_begin (chunk-W6L2VRDA.js?v=1b56d892:18119:15)
    at commitPassiveMountEffects (chunk-W6L2VRDA.js?v=1b56d892:18109:11)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19490:11)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11) Error Component Stack
    at AccountDeletionRequestsList (AccountDeletionRequestsList.tsx:29:65)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ProfilePage (ProfilePage.tsx:30:20)
    at ProtectedRoute (ProtectedRoute.tsx:12:3)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Outlet (react-router-dom.js?v=1b56d892:4495:26)
    at main (<anonymous>)
    at div (<anonymous>)
    at DashboardLayout (<anonymous>)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Routes (react-router-dom.js?v=1b56d892:4559:5)
    at Suspense (<anonymous>)
    at div (<anonymous>)
    at Router (react-router-dom.js?v=1b56d892:4502:15)
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
fetchRequests @ useAccountDeletionRequests.ts:175
(anonymous) @ useAccountDeletionRequests.ts:268
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
commitPassiveMountOnFiber @ chunk-W6L2VRDA.js?v=1b56d892:18156
commitPassiveMountEffects_complete @ chunk-W6L2VRDA.js?v=1b56d892:18129
commitPassiveMountEffects_begin @ chunk-W6L2VRDA.js?v=1b56d892:18119
commitPassiveMountEffects @ chunk-W6L2VRDA.js?v=1b56d892:18109
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19490
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
useAccountDeletionRequests.ts:175 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at commitPassiveMountOnFiber (chunk-W6L2VRDA.js?v=1b56d892:18156:19)
    at commitPassiveMountEffects_complete (chunk-W6L2VRDA.js?v=1b56d892:18129:17)
    at commitPassiveMountEffects_begin (chunk-W6L2VRDA.js?v=1b56d892:18119:15)
    at commitPassiveMountEffects (chunk-W6L2VRDA.js?v=1b56d892:18109:11)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19490:11)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11) Error Component Stack
    at AccountDeletionRequestsList (AccountDeletionRequestsList.tsx:29:65)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ProfilePage (ProfilePage.tsx:30:20)
    at ProtectedRoute (ProtectedRoute.tsx:12:3)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Outlet (react-router-dom.js?v=1b56d892:4495:26)
    at main (<anonymous>)
    at div (<anonymous>)
    at DashboardLayout (<anonymous>)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Routes (react-router-dom.js?v=1b56d892:4559:5)
    at Suspense (<anonymous>)
    at div (<anonymous>)
    at Router (react-router-dom.js?v=1b56d892:4502:15)
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
fetchRequests @ useAccountDeletionRequests.ts:175
(anonymous) @ useAccountDeletionRequests.ts:268
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
commitPassiveMountOnFiber @ chunk-W6L2VRDA.js?v=1b56d892:18156
commitPassiveMountEffects_complete @ chunk-W6L2VRDA.js?v=1b56d892:18129
commitPassiveMountEffects_begin @ chunk-W6L2VRDA.js?v=1b56d892:18119
commitPassiveMountEffects @ chunk-W6L2VRDA.js?v=1b56d892:18109
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19490
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
useAccountDeletionRequests.ts:175 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at invokePassiveEffectMountInDEV (chunk-W6L2VRDA.js?v=1b56d892:18324:19)
    at invokeEffectsInDev (chunk-W6L2VRDA.js?v=1b56d892:19701:19)
    at commitDoubleInvokeEffectsInDEV (chunk-W6L2VRDA.js?v=1b56d892:19686:15)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19503:13)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11)
    at flushSyncCallbacks (chunk-W6L2VRDA.js?v=1b56d892:9119:30)
overrideMethod @ hook.js:608
fetchRequests @ useAccountDeletionRequests.ts:175
(anonymous) @ useAccountDeletionRequests.ts:268
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
invokePassiveEffectMountInDEV @ chunk-W6L2VRDA.js?v=1b56d892:18324
invokeEffectsInDev @ chunk-W6L2VRDA.js?v=1b56d892:19701
commitDoubleInvokeEffectsInDEV @ chunk-W6L2VRDA.js?v=1b56d892:19686
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19503
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
useAccountDeletionRequests.ts:175 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at commitPassiveMountOnFiber (chunk-W6L2VRDA.js?v=1b56d892:18156:19)
    at commitPassiveMountEffects_complete (chunk-W6L2VRDA.js?v=1b56d892:18129:17)
    at commitPassiveMountEffects_begin (chunk-W6L2VRDA.js?v=1b56d892:18119:15)
    at commitPassiveMountEffects (chunk-W6L2VRDA.js?v=1b56d892:18109:11)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19490:11)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11) Error Component Stack
    at AccountDeletionRequestsList (AccountDeletionRequestsList.tsx:29:65)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ProfilePage (ProfilePage.tsx:30:20)
    at ProtectedRoute (ProtectedRoute.tsx:12:3)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Outlet (react-router-dom.js?v=1b56d892:4495:26)
    at main (<anonymous>)
    at div (<anonymous>)
    at DashboardLayout (<anonymous>)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Routes (react-router-dom.js?v=1b56d892:4559:5)
    at Suspense (<anonymous>)
    at div (<anonymous>)
    at Router (react-router-dom.js?v=1b56d892:4502:15)
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
fetchRequests @ useAccountDeletionRequests.ts:175
(anonymous) @ useAccountDeletionRequests.ts:268
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
commitPassiveMountOnFiber @ chunk-W6L2VRDA.js?v=1b56d892:18156
commitPassiveMountEffects_complete @ chunk-W6L2VRDA.js?v=1b56d892:18129
commitPassiveMountEffects_begin @ chunk-W6L2VRDA.js?v=1b56d892:18119
commitPassiveMountEffects @ chunk-W6L2VRDA.js?v=1b56d892:18109
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19490
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
useAccountDeletionRequests.ts:175 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at commitPassiveMountOnFiber (chunk-W6L2VRDA.js?v=1b56d892:18156:19)
    at commitPassiveMountEffects_complete (chunk-W6L2VRDA.js?v=1b56d892:18129:17)
    at commitPassiveMountEffects_begin (chunk-W6L2VRDA.js?v=1b56d892:18119:15)
    at commitPassiveMountEffects (chunk-W6L2VRDA.js?v=1b56d892:18109:11)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19490:11)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11) Error Component Stack
    at AccountDeletionRequestsList (AccountDeletionRequestsList.tsx:29:65)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ProfilePage (ProfilePage.tsx:30:20)
    at ProtectedRoute (ProtectedRoute.tsx:12:3)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Outlet (react-router-dom.js?v=1b56d892:4495:26)
    at main (<anonymous>)
    at div (<anonymous>)
    at DashboardLayout (<anonymous>)
    at RenderedRoute (react-router-dom.js?v=1b56d892:4089:5)
    at Routes (react-router-dom.js?v=1b56d892:4559:5)
    at Suspense (<anonymous>)
    at div (<anonymous>)
    at Router (react-router-dom.js?v=1b56d892:4502:15)
    at BrowserRouter (react-router-dom.js?v=1b56d892:5248:5)
    at UnifiedAuthProvider (UnifiedAuthContext.tsx:36:80)
    at QueryClientProvider (@tanstack_react-query.js?v=1b56d892:2826:3)
    at QueryProvider (QueryProvider.tsx:27:63)
    at ErrorBoundary (ErrorBoundary.tsx:13:1)
    at App (<anonymous>)
    at ErrorBoundary (main.tsx:20:5)
overrideMethod @ hook.js:608
fetchRequests @ useAccountDeletionRequests.ts:175
(anonymous) @ useAccountDeletionRequests.ts:268
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
commitPassiveMountOnFiber @ chunk-W6L2VRDA.js?v=1b56d892:18156
commitPassiveMountEffects_complete @ chunk-W6L2VRDA.js?v=1b56d892:18129
commitPassiveMountEffects_begin @ chunk-W6L2VRDA.js?v=1b56d892:18119
commitPassiveMountEffects @ chunk-W6L2VRDA.js?v=1b56d892:18109
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19490
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384
useAccountDeletionRequests.ts:175 [DIAGNÓSTICO] Erro na RPC, tentando fallback: Error: Forçando fallback para testar
    at fetchRequests (useAccountDeletionRequests.ts:139:15)
    at useAccountDeletionRequests.ts:268:5
    at commitHookEffectListMount (chunk-W6L2VRDA.js?v=1b56d892:16915:34)
    at invokePassiveEffectMountInDEV (chunk-W6L2VRDA.js?v=1b56d892:18324:19)
    at invokeEffectsInDev (chunk-W6L2VRDA.js?v=1b56d892:19701:19)
    at commitDoubleInvokeEffectsInDEV (chunk-W6L2VRDA.js?v=1b56d892:19686:15)
    at flushPassiveEffectsImpl (chunk-W6L2VRDA.js?v=1b56d892:19503:13)
    at flushPassiveEffects (chunk-W6L2VRDA.js?v=1b56d892:19447:22)
    at performSyncWorkOnRoot (chunk-W6L2VRDA.js?v=1b56d892:18868:11)
    at flushSyncCallbacks (chunk-W6L2VRDA.js?v=1b56d892:9119:30)
overrideMethod @ hook.js:608
fetchRequests @ useAccountDeletionRequests.ts:175
(anonymous) @ useAccountDeletionRequests.ts:268
commitHookEffectListMount @ chunk-W6L2VRDA.js?v=1b56d892:16915
invokePassiveEffectMountInDEV @ chunk-W6L2VRDA.js?v=1b56d892:18324
invokeEffectsInDev @ chunk-W6L2VRDA.js?v=1b56d892:19701
commitDoubleInvokeEffectsInDEV @ chunk-W6L2VRDA.js?v=1b56d892:19686
flushPassiveEffectsImpl @ chunk-W6L2VRDA.js?v=1b56d892:19503
flushPassiveEffects @ chunk-W6L2VRDA.js?v=1b56d892:19447
performSyncWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18868
flushSyncCallbacks @ chunk-W6L2VRDA.js?v=1b56d892:9119
commitRootImpl @ chunk-W6L2VRDA.js?v=1b56d892:19432
commitRoot @ chunk-W6L2VRDA.js?v=1b56d892:19277
finishConcurrentRender @ chunk-W6L2VRDA.js?v=1b56d892:18805
performConcurrentWorkOnRoot @ chunk-W6L2VRDA.js?v=1b56d892:18718
workLoop @ chunk-W6L2VRDA.js?v=1b56d892:197
flushWork @ chunk-W6L2VRDA.js?v=1b56d892:176
performWorkUntilDeadline @ chunk-W6L2VRDA.js?v=1b56d892:384


