
import { useState, useEffect } from 'react';
import { serviceWorkerManager } from '@/lib/offline/service-worker-registration';

export interface ServiceWorkerState {
  isRegistered: boolean;
  isUpdating: boolean;
  updateAvailable: boolean;
  registration: ServiceWorkerRegistration | null;
  error: string | null;
}

export function useServiceWorker() {
  const [state, setState] = useState<ServiceWorkerState>({
    isRegistered: false,
    isUpdating: false,
    updateAvailable: false,
    registration: null,
    error: null
  });

  useEffect(() => {
    // Verificar se service worker é suportado
    if (!serviceWorkerManager.isSupported()) {
      setState(prev => ({
        ...prev,
        error: 'Service Worker não suportado neste navegador'
      }));
      return;
    }

    // Registrar service worker
    const registerSW = async () => {
      try {
        setState(prev => ({ ...prev, isUpdating: true }));
        
        const registration = await serviceWorkerManager.register();
        
        setState(prev => ({
          ...prev,
          isRegistered: !!registration,
          registration,
          isUpdating: false,
          error: registration ? null : 'Falha ao registrar Service Worker'
        }));
      } catch (error) {
        setState(prev => ({
          ...prev,
          isUpdating: false,
          error: error instanceof Error ? error.message : 'Erro desconhecido'
        }));
      }
    };

    // Listener para atualizações disponíveis
    const handleUpdateAvailable = (event: Event) => {
      const customEvent = event as CustomEvent;
      setState(prev => ({
        ...prev,
        updateAvailable: true,
        registration: customEvent.detail.registration
      }));
    };

    // Registrar listeners
    window.addEventListener('sw-update-available', handleUpdateAvailable);
    
    // Iniciar registro
    registerSW();

    return () => {
      window.removeEventListener('sw-update-available', handleUpdateAvailable);
    };
  }, []);

  // Ações disponíveis
  const actions = {
    // Aplicar atualização
    applyUpdate: () => {
      if (state.registration?.waiting) {
        serviceWorkerManager.skipWaiting();
        // Recarregar página após breve delay
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    },

    // Verificar por atualizações
    checkForUpdates: async () => {
      if (!state.registration) return;
      
      setState(prev => ({ ...prev, isUpdating: true }));
      
      try {
        await serviceWorkerManager.update();
      } catch (error) {
        console.error('Erro ao verificar atualizações:', error);
      } finally {
        setState(prev => ({ ...prev, isUpdating: false }));
      }
    },

    // Desregistrar service worker
    unregister: async () => {
      const success = await serviceWorkerManager.unregister();
      if (success) {
        setState({
          isRegistered: false,
          isUpdating: false,
          updateAvailable: false,
          registration: null,
          error: null
        });
      }
      return success;
    }
  };

  return {
    ...state,
    ...actions
  };
}
