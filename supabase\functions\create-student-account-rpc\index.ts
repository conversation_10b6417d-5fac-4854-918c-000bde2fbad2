
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders, status: 204 });
  }

  try {
    console.log('[RPC_CREATE_STUDENT] Starting request processing');
    
    // PHASE 1: DIAGNOSTIC - Check environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    console.log('[RPC_CREATE_STUDENT] Environment check:', {
      hasUrl: !!supabaseUrl,
      hasServiceKey: !!supabaseServiceKey,
      urlLength: supabaseUrl?.length || 0,
      keyLength: supabaseServiceKey?.length || 0
    });

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('[RPC_CREATE_STUDENT] Missing environment variables');
      throw new Error('Server configuration error: Missing required environment variables');
    }

    // Create Supabase admin client with explicit service role
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('[RPC_CREATE_STUDENT] Supabase admin client created');

    // PHASE 1: DIAGNOSTIC - Test basic database connection
    try {
      const { data: testData, error: testError } = await supabaseAdmin
        .from('profiles')
        .select('count')
        .limit(1);
      
      console.log('[RPC_CREATE_STUDENT] Database connection test:', {
        success: !testError,
        error: testError?.message,
        hasData: !!testData
      });

      if (testError) {
        console.error('[RPC_CREATE_STUDENT] Database connection failed:', testError);
        throw new Error(`Database connection failed: ${testError.message}`);
      }
    } catch (dbTestError) {
      console.error('[RPC_CREATE_STUDENT] Database test error:', dbTestError);
      throw new Error(`Database test failed: ${dbTestError.message}`);
    }

    const { 
      student_name, 
      student_email, 
      student_cpf, 
      student_phone,
      guardian_id,
      temp_password
    } = await req.json();

    console.log('[RPC_CREATE_STUDENT] Creating student via RPC:', {
      student_name,
      student_email,
      guardian_id
    });

    // Enhanced validation
    if (!student_name?.trim() || !student_email?.trim() || !student_cpf?.trim() || !guardian_id?.trim() || !temp_password?.trim()) {
      console.error('[RPC_CREATE_STUDENT] Missing required fields');
      throw new Error('Todos os campos obrigatórios devem ser preenchidos');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(student_email.trim())) {
      console.error('[RPC_CREATE_STUDENT] Invalid email format:', student_email);
      throw new Error('Formato de email inválido');
    }

    // Clean and validate CPF
    const cleanCpf = student_cpf.replace(/\D/g, '');
    if (cleanCpf.length !== 11) {
      console.error('[RPC_CREATE_STUDENT] Invalid CPF length:', cleanCpf.length);
      throw new Error('CPF deve ter 11 dígitos');
    }

    // Generate activation token
    const activationToken = `act_${Math.random().toString(36).substr(2, 12)}_${Date.now()}`;
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();

    // Create auth user
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: student_email.trim(),
      password: temp_password,
      user_metadata: {
        full_name: student_name.trim(),
        user_type: 'student',
        created_by_guardian: true
      },
      email_confirm: true
    });

    if (authError) {
      console.error('[RPC_CREATE_STUDENT] Auth error:', authError);
      throw new Error(`Auth error: ${authError.message}`);
    }

    if (!authUser.user) {
      throw new Error('Falha na criação do usuário');
    }

    console.log('[RPC_CREATE_STUDENT] Auth user created:', authUser.user.id);

    // PHASE 2: POLICY ENHANCEMENT - Create profile with minimal required data first
    const profileData = {
      user_id: authUser.user.id,
      full_name: student_name.trim(),
      email: student_email.trim(),
      cpf: cleanCpf,
      phone: student_phone?.trim() || null,
      user_type: 'student'
    };

    console.log('[RPC_CREATE_STUDENT] Creating profile with minimal data...');

    const { data: profileResult, error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert(profileData)
      .select()
      .single();

    if (profileError) {
      console.error('[RPC_CREATE_STUDENT] Profile error:', {
        error: profileError,
        code: profileError.code,
        message: profileError.message,
        details: profileError.details,
        hint: profileError.hint
      });
      await supabaseAdmin.auth.admin.deleteUser(authUser.user.id);
      throw new Error(`Profile error: ${profileError.message}`);
    }

    console.log('[RPC_CREATE_STUDENT] Profile created successfully:', profileResult);

    // Update profile with additional fields
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({
        account_creation_method: 'created_by_guardian_rpc',
        created_by_user_id: guardian_id,
        requires_password_change: true,
        activation_token: activationToken,
        activation_expires_at: expiresAt,
        status: 'pending_activation',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', authUser.user.id);

    if (updateError) {
      console.warn('[RPC_CREATE_STUDENT] Profile update failed, but continuing:', updateError);
    }

    // Create relationship
    const { error: relationshipError } = await supabaseAdmin
      .from('student_guardian_relationships')
      .insert({
        student_id: authUser.user.id,
        guardian_id: guardian_id,
        relationship_type: 'parent',
        is_primary: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (relationshipError) {
      console.error('[RPC_CREATE_STUDENT] Relationship error:', relationshipError);
      await supabaseAdmin.auth.admin.deleteUser(authUser.user.id);
      throw new Error(`Relationship error: ${relationshipError.message}`);
    }

    console.log('[RPC_CREATE_STUDENT] Relationship created successfully');

    // Log successful creation
    try {
      await supabaseAdmin.from('auth_logs').insert({
        event_type: 'rpc_student_account_created',
        user_id: authUser.user.id,
        metadata: {
          created_by_guardian: guardian_id,
          student_email,
          created_at: new Date().toISOString()
        },
        occurred_at: new Date().toISOString()
      });
    } catch (logError) {
      console.warn('[RPC_CREATE_STUDENT] Logging failed:', logError);
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Estudante criado com sucesso via RPC',
        student_id: authUser.user.id,
        activation_token: activationToken
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('[RPC_CREATE_STUDENT] Error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
