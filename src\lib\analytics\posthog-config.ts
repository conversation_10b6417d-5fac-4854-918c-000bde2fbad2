import posthog from 'posthog-js';
import { env } from '@/env';

export const initPostHog = () => {
  if (!env.VITE_POSTHOG_KEY) {
    console.warn('[PostHog] API Key não configurado - analytics desabilitado');
    return;
  }

  posthog.init(env.VITE_POSTHOG_KEY, {
    api_host: env.VITE_POSTHOG_HOST || 'https://app.posthog.com',
    
    // Configurações de privacidade
    opt_out_capturing_by_default: false,
    capture_pageview: true,
    capture_pageleave: true,
    
    // Configurações específicas do EduConnect
    autocapture: {
      // Capturar cliques em elementos importantes
      dom_event_allowlist: ['click', 'change', 'submit'],
      // Não capturar elementos sensíveis
      css_selector_allowlist: [
        '[data-track]',
        '.btn',
        '.card',
        '[role="button"]'
      ]
    },
    
    // Session recording (opcional)
    disable_session_recording: env.MODE !== 'production',
    
    // Feature flags
    bootstrap: {
      featureFlags: {
        'new-dashboard-ui': false,
        'advanced-location-tracking': false,
        'beta-features': false
      }
    },
    
    // Debug em desenvolvimento
    debug: env.MODE === 'development',
    
    // Configurações de captura
    property_blacklist: [
      // Não enviar dados sensíveis
      '$password',
      '$email',
      '$phone',
      'token',
      'api_key'
    ]
  });

  console.log('[PostHog] Analytics inicializado');
};

// Event tracking functions específicas do EduConnect
export const trackUserAction = (action: string, properties?: Record<string, any>) => {
  posthog.capture(action, {
    ...properties,
    timestamp: new Date().toISOString(),
    app_version: import.meta.env.VITE_APP_VERSION || '1.0.0'
  });
};

export const trackLocationShare = (method: 'manual' | 'automatic', studentId?: string) => {
  trackUserAction('location_shared', {
    sharing_method: method,
    student_id: studentId ? 'present' : 'none', // Não enviar IDs reais
    feature: 'location_tracking'
  });
};

export const trackAuthEvent = (event: 'login' | 'logout' | 'signup', userType?: string) => {
  trackUserAction(`auth_${event}`, {
    user_type: userType,
    feature: 'authentication'
  });
};

export const trackGuardianAction = (action: 'invite_sent' | 'student_added' | 'request_processed') => {
  trackUserAction(`guardian_${action}`, {
    feature: 'family_management'
  });
};

export const trackMapInteraction = (interaction: 'zoom' | 'pan' | 'marker_click' | 'filter_applied') => {
  trackUserAction(`map_${interaction}`, {
    feature: 'map_interaction'
  });
};

export const trackFeatureUsage = (feature: string, context?: Record<string, any>) => {
  trackUserAction('feature_used', {
    feature_name: feature,
    ...context
  });
};

// Identificação de usuário (sem dados sensíveis)
export const identifyUser = (userId: string, properties?: { role?: string; plan?: string }) => {
  posthog.identify(userId, {
    ...properties,
    app_name: 'educonnect',
    platform: 'web'
  });
};

// Feature flags
export const getFeatureFlag = (flag: string): boolean => {
  return posthog.isFeatureEnabled(flag) || false;
};

export const getAllFeatureFlags = (): Record<string, any> => {
  // PostHog doesn't expose a direct method to get all flags
  // Use individual getFeatureFlag() calls instead
  return {};
};

// Page tracking
export const trackPageView = (pageName: string, properties?: Record<string, any>) => {
  posthog.capture('$pageview', {
    $current_url: window.location.href,
    page_name: pageName,
    ...properties
  });
};

// Experimentos A/B
export const getExperimentVariant = (experimentKey: string): string => {
  return posthog.getFeatureFlag(experimentKey) as string || 'control';
};

// Cleanup
export const resetUser = () => {
  posthog.reset();
}; 