
/**
 * Sync Queue - Manages offline actions queue for synchronization
 * Integrates with existing IndexedDB infrastructure
 */

import { offlineDB } from './indexed-db-manager';

export type SyncActionType = 
  | 'location_save'
  | 'location_share'
  | 'profile_update'
  | 'guardian_add'
  | 'guardian_remove'
  | 'settings_update';

export interface SyncAction {
  id: string;
  type: SyncActionType;
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  userId: string;
  priority: 'high' | 'medium' | 'low';
  status: 'pending' | 'syncing' | 'failed' | 'completed';
  error?: string;
}

export interface SyncQueueStats {
  totalPending: number;
  totalFailed: number;
  totalCompleted: number;
  byType: Record<SyncActionType, number>;
  byPriority: Record<string, number>;
}

export class SyncQueue {
  private initialized = false;
  private readonly STORE_NAME = 'sync_queue';

  async init(): Promise<void> {
    if (this.initialized) return;
    
    try {
      await offlineDB.init();
      this.initialized = true;
      console.log('[SyncQueue] Initialized successfully');
    } catch (error) {
      console.error('[SyncQueue] Failed to initialize:', error);
      throw error;
    }
  }

  async enqueue(action: Omit<SyncAction, 'id' | 'timestamp' | 'retryCount' | 'status'>): Promise<string> {
    await this.ensureInitialized();
    
    const syncAction: SyncAction = {
      ...action,
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      retryCount: 0,
      status: 'pending'
    };

    const key = `action_${syncAction.id}`;
    await offlineDB.set(this.STORE_NAME, key, syncAction);
    
    console.log(`[SyncQueue] Enqueued action: ${syncAction.type} (${syncAction.id})`);
    return syncAction.id;
  }

  async dequeue(priority?: SyncAction['priority']): Promise<SyncAction | null> {
    await this.ensureInitialized();
    
    const actions = await this.getPendingActions();
    
    if (actions.length === 0) return null;

    // Sort by priority and timestamp
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    const sortedActions = actions.sort((a, b) => {
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return a.timestamp - b.timestamp;
    });

    const action = priority 
      ? sortedActions.find(a => a.priority === priority) || sortedActions[0]
      : sortedActions[0];

    if (action) {
      // Mark as syncing
      await this.updateActionStatus(action.id, 'syncing');
      return action;
    }

    return null;
  }

  async updateActionStatus(
    actionId: string, 
    status: SyncAction['status'], 
    error?: string
  ): Promise<void> {
    await this.ensureInitialized();
    
    const key = `action_${actionId}`;
    const action = await offlineDB.get<SyncAction>(this.STORE_NAME, key);
    
    if (!action) {
      console.warn(`[SyncQueue] Action not found: ${actionId}`);
      return;
    }

    const updatedAction: SyncAction = {
      ...action,
      status,
      error,
      ...(status === 'failed' && { retryCount: action.retryCount + 1 })
    };

    await offlineDB.set(this.STORE_NAME, key, updatedAction);
    console.log(`[SyncQueue] Updated action ${actionId} status: ${status}`);
  }

  async retryAction(actionId: string): Promise<boolean> {
    await this.ensureInitialized();
    
    const key = `action_${actionId}`;
    const action = await offlineDB.get<SyncAction>(this.STORE_NAME, key);
    
    if (!action) return false;

    if (action.retryCount >= action.maxRetries) {
      console.log(`[SyncQueue] Action ${actionId} exceeded max retries`);
      return false;
    }

    await this.updateActionStatus(actionId, 'pending');
    return true;
  }

  async removeAction(actionId: string): Promise<void> {
    await this.ensureInitialized();
    
    const key = `action_${actionId}`;
    await offlineDB.delete(this.STORE_NAME, key);
    console.log(`[SyncQueue] Removed action: ${actionId}`);
  }

  async getPendingActions(): Promise<SyncAction[]> {
    await this.ensureInitialized();
    
    const keys = await offlineDB.getAllKeys(this.STORE_NAME);
    const actions: SyncAction[] = [];
    
    for (const key of keys) {
      const action = await offlineDB.get<SyncAction>(this.STORE_NAME, key);
      if (action && action.status === 'pending') {
        actions.push(action);
      }
    }
    
    return actions;
  }

  async getFailedActions(): Promise<SyncAction[]> {
    await this.ensureInitialized();
    
    const keys = await offlineDB.getAllKeys(this.STORE_NAME);
    const actions: SyncAction[] = [];
    
    for (const key of keys) {
      const action = await offlineDB.get<SyncAction>(this.STORE_NAME, key);
      if (action && action.status === 'failed' && action.retryCount < action.maxRetries) {
        actions.push(action);
      }
    }
    
    return actions;
  }

  async getStats(): Promise<SyncQueueStats> {
    await this.ensureInitialized();
    
    const keys = await offlineDB.getAllKeys(this.STORE_NAME);
    const stats: SyncQueueStats = {
      totalPending: 0,
      totalFailed: 0,
      totalCompleted: 0,
      byType: {} as Record<SyncActionType, number>,
      byPriority: { high: 0, medium: 0, low: 0 }
    };
    
    for (const key of keys) {
      const action = await offlineDB.get<SyncAction>(this.STORE_NAME, key);
      if (!action) continue;
      
      // Count by status
      if (action.status === 'pending') stats.totalPending++;
      else if (action.status === 'failed') stats.totalFailed++;
      else if (action.status === 'completed') stats.totalCompleted++;
      
      // Count by type
      stats.byType[action.type] = (stats.byType[action.type] || 0) + 1;
      
      // Count by priority
      stats.byPriority[action.priority]++;
    }
    
    return stats;
  }

  async clearCompleted(): Promise<number> {
    await this.ensureInitialized();
    
    const keys = await offlineDB.getAllKeys(this.STORE_NAME);
    let cleared = 0;
    
    for (const key of keys) {
      const action = await offlineDB.get<SyncAction>(this.STORE_NAME, key);
      if (action && action.status === 'completed') {
        await offlineDB.delete(this.STORE_NAME, key);
        cleared++;
      }
    }
    
    console.log(`[SyncQueue] Cleared ${cleared} completed actions`);
    return cleared;
  }

  async clear(): Promise<void> {
    await this.ensureInitialized();
    await offlineDB.clear(this.STORE_NAME);
    console.log('[SyncQueue] Queue cleared');
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.init();
    }
  }
}

// Singleton instance
export const syncQueue = new SyncQueue();

// Re-export the sync manager from its own file
export { syncManager } from './sync-manager';
