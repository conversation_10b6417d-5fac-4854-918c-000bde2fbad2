-- =====================================================
-- ATUALIZAR TABELA FAMILY_INVITATIONS 
-- Para suportar convites enviados por responsáveis
-- =====================================================

-- Adicionar campos necessários para convites de responsáveis
ALTER TABLE public.family_invitations 
ADD COLUMN IF NOT EXISTS guardian_id uuid REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS student_cpf text,
ADD COLUMN IF NOT EXISTS student_phone text;

-- Tornar campos opcionais quando necessário
ALTER TABLE public.family_invitations 
ALTER COLUMN student_id DROP NOT NULL,
ALTER COLUMN guardian_email DROP NOT NULL,
ALTER COLUMN student_email DROP NOT NULL;

-- <PERSON><PERSON><PERSON> índice para melhor performance
CREATE INDEX IF NOT EXISTS idx_family_invitations_guardian_id 
ON public.family_invitations(guardian_id);

CREATE INDEX IF NOT EXISTS idx_family_invitations_student_cpf 
ON public.family_invitations(student_cpf);
