
describe('Complete User Flow Tests', () => {
  const testUsers = {
    student: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      type: 'student'
    },
    parent: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      type: 'parent'
    }
  };

  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
  });

  describe('Authentication Flow', () => {
    it('should complete login flow for student', () => {
      cy.visit('/login');
      
      // Verify login page elements
      cy.get('[data-cy="login-page"]').should('be.visible');
      cy.get('[data-cy="email-input"]').should('be.visible');
      cy.get('[data-cy="password-input"]').should('be.visible');
      cy.get('[data-cy="submit-button"]').should('be.visible');
      
      // Attempt login
      cy.get('[data-cy="email-input"]').type(testUsers.student.email);
      cy.get('[data-cy="password-input"]').type(testUsers.student.password);
      cy.get('[data-cy="submit-button"]').click();
      
      // Should redirect or show error (depending on user existence)
      cy.url().should('not.equal', `${Cypress.config().baseUrl}/login`);
    });

    it('should complete registration flow', () => {
      cy.visit('/register');
      
      // Verify registration form
      cy.get('[data-cy="register-form"]').should('be.visible');
      cy.get('[data-cy="auth-tabs"]').should('be.visible');
      
      // Select student type
      cy.get('[data-cy="auth-tabs"]').contains('Estudante').click();
      
      // Fill registration form
      cy.get('[data-cy="fullname-input"]').type('Test Student');
      cy.get('[data-cy="email-input"]').type(`test-new-${Date.now()}@example.com`);
      cy.get('[data-cy="password-input"]').type(testUsers.student.password);
      cy.get('[data-cy="password-confirm-input"]').type(testUsers.student.password);
      
      // Submit registration
      cy.get('[data-cy="submit-button"]').click();
      
      // Should show success or redirect
      cy.url().should('not.equal', `${Cypress.config().baseUrl}/register`);
    });
  });

  describe('Dashboard Access', () => {
    it('should protect dashboard routes when not authenticated', () => {
      // Try to access student dashboard without login
      cy.visit('/student-dashboard', { failOnStatusCode: false });
      cy.url().should('include', '/login');
      
      // Try to access parent dashboard without login
      cy.visit('/parent-dashboard', { failOnStatusCode: false });
      cy.url().should('include', '/login');
    });

    it('should allow access to dashboard when authenticated', () => {
      // Mock authentication state
      cy.window().then((win) => {
        win.localStorage.setItem('supabase.auth.token', JSON.stringify({
          access_token: 'mock-token',
          user: {
            id: 'mock-user-id',
            email: testUsers.student.email,
            user_metadata: { user_type: 'student' }
          }
        }));
      });
      
      cy.visit('/student-dashboard');
      cy.get('[data-cy="dashboard-container"]').should('be.visible');
    });
  });

  describe('Navigation Flow', () => {
    it('should navigate between pages correctly', () => {
      cy.visit('/');
      
      // Navigate to login
      cy.contains('Login').click();
      cy.url().should('include', '/login');
      
      // Navigate to register
      cy.get('[data-cy="register-link"]').click();
      cy.url().should('include', '/register');
      
      // Navigate back to login
      cy.get('[data-cy="login-link"]').click();
      cy.url().should('include', '/login');
    });
  });

  describe('Form Validation', () => {
    it('should validate login form inputs', () => {
      cy.visit('/login');
      
      // Try to submit empty form
      cy.get('[data-cy="submit-button"]').click();
      cy.get('body').should('contain.text', 'obrigatório');
      
      // Try invalid email
      cy.get('[data-cy="email-input"]').type('invalid-email');
      cy.get('[data-cy="submit-button"]').click();
      cy.get('body').should('contain.text', 'Email');
    });

    it('should validate registration form inputs', () => {
      cy.visit('/register');
      
      // Try to submit empty form
      cy.get('[data-cy="submit-button"]').click();
      cy.get('body').should('contain.text', 'obrigatório');
      
      // Test password confirmation mismatch
      cy.get('[data-cy="password-input"]').type('password1');
      cy.get('[data-cy="password-confirm-input"]').type('password2');
      cy.get('[data-cy="submit-button"]').click();
      cy.get('body').should('contain.text', 'coincidem');
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', () => {
      // Intercept auth requests to simulate network error
      cy.intercept('POST', '**/auth/v1/token*', {
        statusCode: 500,
        body: { error: 'Network error' }
      }).as('authError');
      
      cy.visit('/login');
      cy.get('[data-cy="email-input"]').type(testUsers.student.email);
      cy.get('[data-cy="password-input"]').type(testUsers.student.password);
      cy.get('[data-cy="submit-button"]').click();
      
      // Should show error message
      cy.wait('@authError');
      cy.get('body').should('contain.text', 'erro');
    });

    it('should handle invalid credentials', () => {
      cy.intercept('POST', '**/auth/v1/token*', {
        statusCode: 400,
        body: { error: 'Invalid login credentials' }
      }).as('invalidCreds');
      
      cy.visit('/login');
      cy.get('[data-cy="email-input"]').type('<EMAIL>');
      cy.get('[data-cy="password-input"]').type('wrongpassword');
      cy.get('[data-cy="submit-button"]').click();
      
      cy.wait('@invalidCreds');
      cy.get('body').should('contain.text', 'incorretos');
    });
  });
});
