{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# Guia do Desenvolvedor - EduConnect\n", "\n", "## Inicialização do Servidor\n", "> **Importante**: Antes de iniciar o servidor, verifique se já está em execução. Nem sempre é necessário executar `npm run dev`. Se for indispensável, utilize o comando kill port para liberar a porta 8080.\n", "\n", "Este guia fornece instruções detalhadas para configurar o ambiente de desenvolvimento do projeto EduConnect.\n", "\n", "## 📋 <PERSON>ndice\n", "\n", "1. [Pré-requisitos](#pre-requisitos)\n", "2. [Finalização do Projeto](#finalizacao-do-projeto)\n", "3. [Configuração Inicial](#configuracao-inicial)\n", "4. [Executando o Projeto](#executando-o-projeto)\n", "5. [Fluxo de Trabalho Diário](#fluxo-de-trabalho)\n", "6. [<PERSON><PERSON><PERSON>](#comandos-uteis)\n", "7. [Solução de Problemas](#solucao-de-problemas)\n", "8. [Dicas e Boas Práticas](#dicas-e-boas-praticas)\n", "\n", "## 🛠️ Pré-requisitos <a name=\"pre-requisitos\"></a>\n", "\n", "### Regra de Proteção do arquivo .env\n", "\n", "**Regra Fundamental:**\n", "NUNCA altere o arquivo `.env` automaticamente. Todas as alterações devem ser feitas manualmente pelo desenvolvedor.\n", "\n", "**Justificativa:**\n", "- O arquivo `.env` contém credenciais sensíveis e configurações críticas\n", "- Alterações automáticas podem comprometer a segurança\n", "- Cada ambiente possui configurações específicas\n", "- Chaves de API e tokens de acesso são únicos para cada desenvolvedor\n", "\n", "### Ferramentas Necessárias\n", "\n", "- Node.js (versão LTS mais recente)\n", "- npm (gerenciador de paco<PERSON>)\n", "- Git\n", "- <PERSON> de código (VS Code recomendado)\n", "- <PERSON><PERSON> (opcional, para containers)\n", "\n", "## 🎯 Finalização do Projeto <a name=\"finalizacao-do-projeto\"></a>\n", "\n", "Antes de finalizar seu trabalho:\n", "\n", "1. Execute `npm run build` para verificar erros\n", "2. Veri<PERSON><PERSON> os logs do console\n", "3. Faça commit das alterações\n", "4. Atualize a documentação se necessário\n", "\n", "## ⚙️ Configuração Inicial <a name=\"configuracao-inicial\"></a>\n", "\n", "1. <PERSON><PERSON> o repositório:"], "id": "38358366c1963a76"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "a648af7d30dfcafe"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["cd educonnect\n", "npm install"], "id": "fab079cbed7e4ac5"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "npm run dev", "id": "fb8f48b45918d14c"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "git pull origin main", "id": "79fb60373536cb33"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "npm install", "id": "642602bb80032f41"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["sudo lsof -i :8080\n", "kill -9 PID"], "id": "9b1b8953f178b4dc"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["rm -rf node_modules\n", "npm install"], "id": "a313b98f78807a11"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["\n", "```"], "id": "1e4f923305466e13"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}