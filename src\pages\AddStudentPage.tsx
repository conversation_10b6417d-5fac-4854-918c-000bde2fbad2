
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { useTranslation } from 'react-i18next';
import { ArrowLeft } from "lucide-react";
import { useUser } from '@/contexts/UnifiedAuthContext';
import { formatCPF, validateCPF } from '@/lib/utils/cpf-validator';

// Define tipos adequados sem recursão infinita
interface StudentFormData {
  cpf: string;
  name: string;
  email?: string;
  phone?: string;
  school?: string;
}

interface AddStudentProps {
  onSuccess?: () => void;
}

// Componente de formulário sem recursão infinita de tipos
const AddStudent = ({ onSuccess }: AddStudentProps) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<StudentFormData>({
    cpf: '',
    name: '',
    email: '',
    phone: '',
    school: ''
  });
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Implementação do envio do formulário
      console.log('Form submitted:', formData);
      
      toast({
        title: t('student.studentAdded'),
        description: t('student.studentAdded')
      });
      
      if (onSuccess) onSuccess();
    } catch (error: any) {
      console.error('Error adding student:', error);
      toast({
        variant: 'destructive',
        title: t('student.addError'),
        description: error.message || t('student.addError')
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'cpf') {
      const formatted = formatCPF(value);
      setFormData(prev => ({ ...prev, [name]: formatted }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Conteúdo do formulário */}
      <div className="space-y-4">
        <div>
          <label htmlFor="cpf" className="block text-sm font-medium mb-1">CPF</label>
          <input
            type="text"
            id="cpf"
            name="cpf"
            value={formData.cpf}
            onChange={handleInputChange}
            required
            maxLength={14}
            placeholder="000.000.000-00"
            className="w-full p-2 border rounded-md"
          />
          <p className="text-xs text-gray-500 mt-1">
            O CPF será usado para identificar o estudante no sistema.
          </p>
        </div>
        
        <div>
          <label htmlFor="name" className="block text-sm font-medium mb-1">Nome completo</label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            required
            className="w-full p-2 border rounded-md"
          />
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-1">Email (opcional)</label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email || ''}
            onChange={handleInputChange}
            className="w-full p-2 border rounded-md"
          />
        </div>
        
        <div>
          <label htmlFor="phone" className="block text-sm font-medium mb-1">Telefone</label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            className="w-full p-2 border rounded-md"
          />
        </div>
        
        <div>
          <label htmlFor="school" className="block text-sm font-medium mb-1">Escola</label>
          <input
            type="text"
            id="school"
            name="school"
            value={formData.school}
            onChange={handleInputChange}
            className="w-full p-2 border rounded-md"
          />
        </div>
      </div>
      
      <Button type="submit" disabled={loading} className="w-full">
        {loading ? t('common.processing') : t('student.addStudent')}
      </Button>
    </form>
  );
};

// Página principal que usa o componente AddStudent
const AddStudentPage: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const { t } = useTranslation();
  
  // Redirecionar para login se usuário não estiver autenticado
  if (!user) {
    return <div>{t('common.loading')}</div>;
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="content-overlay rounded-xl bg-transparent p-4 md:p-8 w-full max-w-2xl mx-auto">
        {/* Conteúdo original do AddStudentPage */}
        <div className="mb-6">
          <Button variant="ghost" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back')}
          </Button>
        </div>
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>{t('student.addStudent')}</CardTitle>
            <CardDescription>{t('student.addStudentDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <AddStudent onSuccess={() => navigate('/dashboard')} />
          </CardContent>
          <CardFooter className="flex justify-between">
            {/* Footer conteúdo se necessário */}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default AddStudentPage;
