# 🤖 Diretrizes para AI Assistant - EduConnect

**Objetivo:** Protocolos para trabalhar com AI assistants de forma eficiente, evitando erros e duplicações no projeto.

---

## 🚫 **PROTOCOLO ANTI-ERRO - REGRAS FUNDAMENTAIS**

### ⚠️ **REGRA ZERO - NUNCA ASSUMIR**
```
🚫 NUNCA afirme problemas sem evidências concretas
✅ SEMPRE verifique com ferramentas antes de concluir
```

### 🔍 **PROTOCOLO DE VERIFICAÇÃO OBRIGATÓRIO**

#### **Antes de afirmar qualquer problema:**
1. **EXECUTE comandos de verificação**
2. **COLETE evidências reais**
3. **CORRELACIONE com status atual**
4. **SE FUNCIONA = questione por que assumir que está quebrado**

#### **Comandos de Verificação Padrão:**
```bash
# Verificar estrutura real
ls src/pages/
find src/ -name "*.tsx" | head -10

# Verificar funcionamento
npm run build    # Erros reais?
npm run dev      # Carrega sem erro?
npm test         # Testes passando?

# Status do servidor
netstat -ano | findstr :8080
curl http://localhost:8080 # Responde?
```

### 📊 **MATRIZ DE DECISÃO**

| **Evidência** | **Ação** |
|---------------|----------|
| ✅ Servidor rodando + Build OK | ❌ NÃO assuma problemas |
| ✅ Arquivos existem | ❌ NÃO diga que faltam |
| ✅ Aplicação carrega | ❌ NÃO invente imports quebrados |
| ❌ Erro real mostrado | ✅ Analise e corrija |

### 🚨 **PALAVRAS-CHAVE DE ALERTA**
**Quando AI usar estes termos, PARE:**
- "parece que..."
- "provavelmente..."
- "deve estar..."
- "é possível que..."

**Exija:** *"Verifique isso com comandos antes de continuar!"*

---

## 🚫 **PROTOCOLO ANTI-DUPLICAÇÃO**

### 🔍 **VERIFICAÇÃO OBRIGATÓRIA ANTES DE CRIAR QUALQUER ARQUIVO**

#### **Comandos obrigatórios ANTES de criar:**
```bash
# Mapear funcionalidades existentes
find src/ -name "*.tsx" -o -name "*.ts" | grep -i [termo-relacionado]
grep -r "função_similar" src/
find . -name "*[nome-similar]*"

# Para componentes de autenticação:
ls src/components/ | grep -i auth
ls src/pages/ | grep -i login
ls src/contexts/ | grep -i auth

# Para hooks:
ls src/hooks/ | grep -i [funcionalidade]

# Para serviços:
find src/lib/ -name "*service*"
find src/lib/ -name "*api*"

# Para tipos:
ls src/types/
grep -r "interface.*Auth" src/
```

### 📋 **CHECKLIST OBRIGATÓRIO ANTES DE CRIAR ARQUIVOS**

```markdown
ANTES de criar QUALQUER arquivo novo:
- [ ] Executei busca por funcionalidade similar?
- [ ] Verifiquei se já existe implementação?
- [ ] Analisei estrutura atual do projeto?
- [ ] Identifiquei onde essa função JÁ pode estar?
- [ ] Considerei ESTENDER ao invés de DUPLICAR?
```

### 🎯 **ESTRATÉGIA: EXTEND-FIRST, CREATE-LAST**

#### **Ordem de Prioridade:**
1. **🔧 REUTILIZAR** - Usar o que já existe
2. **📈 ESTENDER** - Melhorar/expandir existente  
3. **🔄 REFATORAR** - Reorganizar código atual
4. **➕ CRIAR** - Só se realmente necessário

### 📊 **MATRIZ DE DECISÃO ANTI-DUPLICAÇÃO**

| **Situação** | **Ação** |
|--------------|----------|
| 🟢 Função já existe exatamente | ❌ NÃO criar - usar existente |
| 🟡 Função similar existe | 🔧 Estender/melhorar existente |
| 🟡 Estrutura parcial existe | 📈 Completar o que falta |
| 🔴 Nada similar encontrado | ✅ Criar, mas documentar bem |

---

## 🎯 **METHODOLOGY: EVIDENCE-FIRST**
```
❌ Velha Forma: Análise → Conclusão → Verificação
✅ Nova Forma: Verificação → Evidências → Análise → Conclusão
```

---

## 📝 **COMO USAR ESTE DOCUMENTO**

### **No início de cada conversa com AI:**
1. Referencie este documento: *"Siga as diretrizes em docs/diretrizes-ai-assistant.md"*
2. Lembre das regras principais: *"Não assuma problemas, verifique evidências primeiro"*
3. Cobre cumprimento: *"Antes de criar arquivos, mapeie o que já existe"*

### **Durante o trabalho:**
- Use este documento como checklist
- Aponte violações das regras
- Exija evidências antes de conclusões

### **Exemplos de comandos para dar ao AI:**
```
"Analise o projeto, mas primeiro execute comandos para verificar o estado real"
"Antes de criar componente X, me mostre se já existe algo similar"
"Prove que há problema Y com evidências concretas"
```

---

## 📚 **HISTÓRICO DE MELHORIAS**

### **30/05/2025:**
- ❌ **Erro:** Assumiu imports quebrados sem verificar arquivos existentes
- ✅ **Correção:** Implementado protocolo de verificação obrigatória
- ✅ **Aprendizado:** Sempre correlacionar evidências com conclusões

### **Template para próximos erros:**
- ❌ **Erro:** [Descrever erro]
- ✅ **Correção:** [Descrever correção]
- ✅ **Aprendizado:** [Lição aprendida]

---

## 🎯 **MANTRA OPERACIONAL**
> **"Se está funcionando, prove que está quebrado. Se está quebrado, prove com evidências."**

---

**📅 Última Atualização:** 30 de Maio de 2025  
**🎯 Objetivo:** Zero erros por assumption, zero duplicação desnecessária 