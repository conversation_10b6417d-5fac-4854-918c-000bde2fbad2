-- ==========================================
-- VERIFICAR TABELAS VAZIAS E COM DADOS
-- Data: 24/06/2025
-- Objetivo: Identificar estado real de todas as tabelas
-- ==========================================

-- 1. CONTAGEM DE TODAS AS TABELAS PRINCIPAIS
SELECT 'profiles' as tabela, COUNT(*) as registros FROM public.profiles
UNION ALL
SELECT 'guardians' as tabela, COUNT(*) as registros FROM public.guardians  
UNION ALL
SELECT 'students' as tabela, COUNT(*) as registros FROM public.students
UNION ALL
SELECT 'account_deletion_requests' as tabela, COUNT(*) as registros FROM public.account_deletion_requests
UNION ALL
SELECT 'locations' as tabela, COUNT(*) as registros FROM public.locations
UNION ALL
SELECT 'student_guardian_relationships' as tabela, COUNT(*) as registros FROM public.student_guardian_relationships
ORDER BY registros DESC;

-- 2. VERIFICAR ESTRUTURA DA TABELA GUARDIANS
SELECT 
    'ESTRUTURA_GUARDIANS' as info,
    column_name, 
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'guardians'
ORDER BY ordinal_position;

-- 3. VERIFICAR SE EXISTE TABELA DE RELACIONAMENTOS ALTERNATIVA
SELECT 
    'TABELAS_RELACIONAMENTO' as info,
    table_name
FROM information_schema.tables
WHERE table_schema = 'public' 
  AND table_name LIKE '%guardian%'
  OR table_name LIKE '%relationship%'
  OR table_name LIKE '%student%'
ORDER BY table_name;

-- 4. DADOS DO FRANK NA TABELA PROFILES
SELECT 
    'FRANK_PROFILE' as info,
    id, email, full_name, user_type, user_id, created_at
FROM public.profiles
WHERE email = '<EMAIL>';

-- 5. DADOS DOS ESTUDANTES MENCIONADOS  
SELECT 
    'ESTUDANTES_MENCIONADOS' as info,
    id, email, full_name, user_type, user_id, created_at
FROM public.profiles
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
);

-- 6. VERIFICAR TABELA STUDENTS (pode ter os dados)
SELECT 
    'STUDENTS_TABLE' as info,
    COUNT(*) as total_registros
FROM public.students;

-- Se students tem dados, mostrar alguns:
SELECT 
    'STUDENTS_SAMPLE' as info,
    id, email, full_name, guardian_email, created_at
FROM public.students
LIMIT 10; 