
import React from 'react';
import { useTranslation } from 'react-i18next';
import { But<PERSON> } from '@/components/ui/button';
import { User, PlusCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { InviteStudentForm } from '@/components/student/InviteStudentForm';

interface DashboardHeaderProps {
  userName: string;
  onGoToProfile: () => void;
  onSignOut: () => void;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  userName,
  onGoToProfile,
  onSignOut
}) => {
  const { t } = useTranslation();
  return (
    <>
      <div className="absolute top-4 right-4 flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onGoToProfile}
          className="flex items-center gap-2"
        >
          <User className="h-4 w-4" />
          {t('common.profile')}
        </Button>
        <Button
          onClick={onSignOut}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
          data-cy="logout-button"
        >
          {t('common.logout')}
        </Button>
      </div>
      
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex items-center gap-2 text-gray-600">
          <User className="h-5 w-5" />
          <span className="font-medium">{userName}</span>
        </div>
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">{t('dashboard.parent.title')}</h1>
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                {t('dashboard.parent.addStudent')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
              <DialogTitle>{t('student.addStudent')}</DialogTitle>
              </DialogHeader>
              <InviteStudentForm onStudentAdded={() => {}} />
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  );
};
