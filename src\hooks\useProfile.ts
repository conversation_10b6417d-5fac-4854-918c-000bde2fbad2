
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useToast } from '@/hooks/use-toast';

interface Profile {
  id: number;
  user_id: string;
  full_name: string;
  email: string;
  phone: string;
  user_type: string;
  cpf: string;
  birth_date?: string;
  status?: string;
  last_login_at?: string | null;
  login_count?: number;
  created_at: string;
  updated_at: string;
}

interface ProfileInput {
  full_name: string;
  email: string;
  phone: string;
  user_type: string;
  cpf: string;
  birth_date?: string;
}

export const useProfile = () => {
  const { user } = useUser();
  const { toast } = useToast();
  const { t } = useTranslation();
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchProfile = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      console.log('[useProfile] Fetching profile for user:', user.id);
      
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('[useProfile] Error fetching profile:', error);
        return;
      }

      console.log('[useProfile] Profile data fetched:', data);
      setProfile(data);
    } catch (error) {
      console.error('[useProfile] Exception fetching profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (profileData: ProfileInput): Promise<boolean> => {
    console.log('[useProfile] Updating profile with data:', profileData);
    
    if (!user) {
      console.error('[useProfile] No user available for profile update');
      return false;
    }

    try {
      // Validar campos obrigatórios
      if (!profileData.full_name || !profileData.email || !profileData.user_type) {
        console.error('[useProfile] Missing required fields:', {
          full_name: !!profileData.full_name,
          email: !!profileData.email,
          user_type: !!profileData.user_type
        });
        toast({
          variant: 'destructive',
          title: t('profile.updateErrorTitle'),
          description: t('validation.missingFields')
        });
        return false;
      }

      // Validar e formatar telefone
      let formattedPhone = null;
      if (profileData.phone?.trim()) {
        const phoneValue = profileData.phone.trim();
        if (!phoneValue.startsWith('+')) {
          const cleanPhone = phoneValue.replace(/\D/g, '');
          if (cleanPhone.length >= 10 && cleanPhone.length <= 11) {
            formattedPhone = `+55${cleanPhone}`;
          }
        } else {
          if (/^\+[1-9][0-9]{7,14}$/.test(phoneValue)) {
            formattedPhone = phoneValue;
          }
        }
      }

      const updateData = {
        full_name: profileData.full_name.trim(),
        email: profileData.email.trim(),
        phone: formattedPhone,
        user_type: profileData.user_type,
        cpf: profileData.cpf?.trim() || '',
        birth_date: profileData.birth_date || null
      };

      console.log('[useProfile] Final update data:', updateData);

      // Primeiro, tentar atualizar se já existe
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', user.id)
        .single();

      let error;
      if (existingProfile) {
        // Atualizar perfil existente
        ({ error } = await supabase
          .from('profiles')
          .update({
            full_name: updateData.full_name,
            email: updateData.email,
            phone: updateData.phone,
            user_type: updateData.user_type,
            cpf: updateData.cpf,
            birth_date: updateData.birth_date,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id));
      } else {
        // Criar novo perfil
        ({ error } = await supabase
          .from('profiles')
          .insert({
            user_id: user.id,
            ...updateData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }));
      }

      if (error) {
        console.error('[useProfile] Supabase error:', error);
        toast({
          variant: 'destructive',
          title: t('profile.updateErrorTitle'),
          description: `${t('profile.updateErrorDescription')}: ${error.message}`
        });
        return false;
      }

      console.log('[useProfile] Profile updated successfully');

      toast({
        title: t('profile.updateSuccessTitle'),
        description: t('profile.updateSuccessDescription')
      });

      await fetchProfile();
      return true;
    } catch (error: any) {
      console.error('[useProfile] Unexpected error:', error);
      toast({
        variant: 'destructive',
        title: t('profile.updateErrorTitle'),
        description: `${t('profile.updateErrorDescription')}: ${error.message || t('errors.serverError')}`
      });
      return false;
    }
  };

  useEffect(() => {
    fetchProfile();
  }, [user]);

  return {
    profile,
    isLoading,
    updateProfile,
    refetch: fetchProfile
  };
};
