
import { pgTable, serial, text, varchar, integer } from 'drizzle-orm/pg-core';

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: text('email').notNull(),
  user_type: text('user_type').notNull(),
  createdAt: text('created_at').notNull(),
  updatedAt: text('updated_at').notNull()
});

export const profiles = pgTable('profiles', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id),
  full_name: text('full_name').notNull(),
  phone: varchar('phone', { length: 20 }),
  email: text('email').notNull(),
  user_type: text('user_type').notNull(),
  country_code: varchar('country_code', { length: 2 }).notNull().default('BR'),
  cpf: varchar('cpf', { length: 14 }), // CPF field
  national_id: varchar('national_id', { length: 255 }), // National ID field
  createdAt: text('created_at').notNull(),
  updatedAt: text('updated_at').notNull()
});

export const guardians = pgTable('guardians', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id),
  nome_completo: text('nome_completo').notNull(),
  email: text('email').notNull(),
  telefone: varchar('telefone', { length: 20 }),
  tipo_vinculo: text('tipo_vinculo').notNull(), // 'PARENT' or 'GUARDIAN'
  pais: text('pais').notNull(), // 'BR', 'UK', 'US', 'PT'
  cpf: text('cpf'), // Campo CPF adicionado
  createdAt: text('created_at').notNull(),
  updatedAt: text('updated_at').notNull()
});

export const schema = {
  users,
  profiles,
  guardians
};
