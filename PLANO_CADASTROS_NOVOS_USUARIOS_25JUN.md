# 📋 PLANO CADASTROS DE NOVOS USUÁRIOS - 25 JUNHO 2025

## 🎯 OBJETIVO PRINCIPAL
**"Permitir cadastro e onboarding de novos usuários (estudantes e responsáveis) de forma independente"**

---

## 📊 STATUS ATUAL (24/06/2025)
✅ **Student Dashboard** - 100% funcional para Maurício  
✅ **Account Deletion System** - Completo e funcionando  
✅ **Parent Dashboard** - Account deletion requests funcionais  
✅ **RPC Corrections** - get_student_guardians_from_relationships corrigida  

**PRÓXIMO PASSO:** Expandir para novos usuários independentes

---

## 🔧 TAREFAS ESPECÍFICAS - 25/06/2025

### **1. ANÁLISE DO SISTEMA DE CADASTRO ATUAL** ⏱️ *30min*
- [ ] Verificar fluxo de registro existente
- [ ] Identificar bottlenecks para novos usuários  
- [ ] Documentar processo atual vs ideal
- [ ] Mapear tabelas: profiles, students, auth

### **2. REGISTRO DE NOVOS ESTUDANTES** ⏱️ *2h*
- [ ] Interface de cadastro self-service
- [ ] Validação de dados (CPF, email, telefone)
- [ ] Criação automática de perfil na tabela `profiles`
- [ ] Envio de email de confirmação
- [ ] Integration com Supabase Auth

### **3. SISTEMA DE CONVITE PARA RESPONSÁVEIS** ⏱️ *2h*
- [ ] Estudante pode convidar responsáveis via email
- [ ] Link de convite com token único
- [ ] Responsável aceita convite e cria conta
- [ ] Vinculação automática student ↔ guardian
- [ ] Email templates profissionais

### **4. ONBOARDING WORKFLOW** ⏱️ *1h*
- [ ] Tutorial primeiro login
- [ ] Configuração inicial de preferências
- [ ] Teste de funcionalidades básicas
- [ ] Welcome messages
- [ ] Guided tour do dashboard

### **5. VALIDAÇÕES E SEGURANÇA** ⏱️ *1h*
- [ ] RLS policies para novos usuários
- [ ] Validação de emails únicos
- [ ] Prevenção de spam/cadastros falsos
- [ ] Rate limiting para convites
- [ ] Token expiration (24h)

---

## 🚀 FUNCIONALIDADES ESPECÍFICAS

### **📝 CADASTRO DE ESTUDANTE:**
```
✅ Formulário: Nome, Email, Telefone, Data Nascimento
✅ Validação: Email único, formato correto
✅ Criação: profile + students table
✅ Email: Confirmação de cadastro
✅ Redirect: Student Dashboard
```

### **👥 CONVITE DE RESPONSÁVEL:**
```
✅ Interface: "Adicionar Responsável" 
✅ Input: Email do responsável
✅ Envio: Link de convite por email
✅ Token: Único e temporário (24h)
✅ Registro: Responsável cria conta via link
✅ Vinculação: Automática após confirmação
```

### **🔄 FLUXO COMPLETO:**
```
1. Estudante se cadastra → Student Dashboard
2. Convida responsável → Email enviado  
3. Responsável clica link → Cadastro responsável
4. Responsável confirma → Parent Dashboard
5. Ambos conectados → Sistema funcional
```

---

## 📅 CRONOGRAMA - 25 JUNHO 2025

### **MANHÃ (9h-12h):**
- **09h00-09h30:** Análise sistema atual
- **09h30-11h00:** Formulário cadastro estudante  
- **11h00-12h00:** Backend estudante + validações

### **TARDE (14h-17h):**
- **14h00-16h00:** Sistema convite responsáveis
- **16h00-17h00:** Email templates + links

### **FINAL (17h-18h):**
- **17h00-17h30:** Teste completo do fluxo
- **17h30-18h00:** Documentação e commit

---

## 🎯 RESULTADO ESPERADO

**"Qualquer pessoa pode se cadastrar como estudante, convidar responsáveis, e ter o sistema funcionando independentemente dos dados hardcoded atuais"**

### **TESTE DE SUCESSO:**
- [ ] Novo email → Cadastro estudante → Dashboard funcional
- [ ] Estudante convida email X → X recebe link → X se cadastra → Parent Dashboard funcional  
- [ ] Sistema escalável para múltiplos usuários

---

## 📂 ARQUIVOS ALVO

### **Frontend:**
- `src/pages/Register.tsx` - Melhorar formulário
- `src/components/student/InviteGuardianForm.tsx` - Sistema convites
- `src/hooks/useStudentRegistration.ts` - Hook cadastro
- `src/hooks/useGuardianInvite.ts` - Hook convites

### **Backend:**
- `supabase/migrations/` - Novas tabelas se necessário
- `supabase/functions/` - Edge functions para emails
- RLS policies para novos usuários

### **Email:**
- Templates de convite
- Templates de confirmação
- Sistema de notificações

---

## 🔧 TECNOLOGIAS

- **Frontend:** React + TypeScript + Tailwind
- **Backend:** Supabase (Auth + Database + Edge Functions)
- **Email:** Supabase + Resend
- **Validação:** Zod + React Hook Form
- **Estado:** TanStack Query

---

## 📋 ACCEPTANCE CRITERIA

### **Estudante pode se cadastrar:**
- [ ] Formulário intuitivo e validado
- [ ] Email de confirmação enviado
- [ ] Perfil criado automaticamente
- [ ] Redirecionamento para dashboard

### **Estudante pode convidar responsável:**
- [ ] Interface simples para convite
- [ ] Email com link único enviado
- [ ] Link tem expiração (24h)
- [ ] Responsável consegue se cadastrar via link

### **Responsável pode aceitar convite:**
- [ ] Link funcional do email
- [ ] Formulário de cadastro pré-preenchido
- [ ] Vinculação automática com estudante
- [ ] Acesso ao Parent Dashboard

### **Sistema é seguro:**
- [ ] RLS policies aplicadas
- [ ] Emails únicos validados
- [ ] Rate limiting implementado
- [ ] Tokens seguros e expiráveis

---

## 🚀 IMPACTO ESPERADO

**ANTES:** Sistema funciona apenas para dados hardcoded (Maurício + Frank)
**DEPOIS:** Sistema escalável para qualquer número de usuários reais

**MÉTRICAS DE SUCESSO:**
- Tempo de onboarding < 5 minutos
- Taxa de conversão convite → cadastro > 80%
- Zero erros críticos no fluxo
- UX intuitiva e profissional

---

*Plano criado em: 24 Junho 2025 21:00*  
*Status: READY TO IMPLEMENT*  
*Prioridade: HIGH* 