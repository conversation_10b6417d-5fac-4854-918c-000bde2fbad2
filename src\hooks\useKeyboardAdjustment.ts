import { useEffect } from 'react';
import { Keyboard } from '@capacitor/keyboard';

/**
 * Adjusts the body position when the virtual keyboard is shown on mobile.
 * This prevents important elements from being hidden.
 */
export function useKeyboardAdjustment() {
  useEffect(() => {
    let showListener: any;
    let hideListener: any;

    const setupListeners = async () => {
      showListener = await Keyboard.addListener('keyboardWillShow', info => {
        document.body.style.transform = `translateY(-${info.keyboardHeight / 2}px)`;
      });
      hideListener = await Keyboard.addListener('keyboardWillHide', () => {
        document.body.style.transform = 'translateY(0)';
      });
    };

    setupListeners();

    return () => {
      if (showListener) showListener.remove();
      if (hideListener) hideListener.remove();
    };
  }, []);
}
