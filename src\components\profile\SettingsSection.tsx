
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CommunicationPreferences } from '@/components/communication/CommunicationPreferences';
import { ThemeSettings } from './settings/ThemeSettings';
import { NotificationSettings } from './settings/NotificationSettings';
import { PrivacySettings } from './settings/PrivacySettings';
import { InterfaceSettings } from './settings/InterfaceSettings';
import { AccountSettings } from './settings/AccountSettings';
import { Button } from '@/components/ui/button';
import { LifeBuoy } from 'lucide-react';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useTranslation } from 'react-i18next';

const SettingsSection: React.FC = () => {
  const { user } = useUser();
  const { t } = useTranslation();
  const userType = user?.user_metadata?.user_type || 'student';

  const helpLink = userType === 'parent'
    ? '/ajuda/responsavel'
    : '/ajuda/estudante';
  return (
    <div className="space-y-6">
      {/* Comunicação */}
      <CommunicationPreferences />
      
      {/* Tema */}
      <ThemeSettings />
      
      {/* Notificações */}
      <NotificationSettings />
      
      {/* Privacidade */}
      <PrivacySettings />
      
      {/* Interface */}
      <InterfaceSettings />
      
      {/* Conta */}
      <AccountSettings />

      {/* Help */}
      <Card className="bg-transparent border-0 shadow-none">
        <CardHeader className="bg-transparent border-0 shadow-none">
          <CardTitle className="bg-transparent">{t('settings.help.title','Need Help?')}</CardTitle>
        </CardHeader>
        <CardContent className="bg-transparent border-0 shadow-none">
          <Button asChild variant="outline" className="flex items-center gap-2">
            <Link to={helpLink} className="w-full">
              <LifeBuoy className="h-4 w-4" />
              {t('settings.help.button','Help Center')}
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default SettingsSection;
