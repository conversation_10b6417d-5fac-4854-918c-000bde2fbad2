# Native Features Implementation

This document provides an overview of the native features implemented in the Monitore MVP app using Capacitor. These features enhance the mobile experience by making the app feel more native on both iOS and Android devices.

## Overview

The following native features have been implemented:

1. **Native Gestures and Haptic Feedback**
   - Map interactions with haptic feedback
   - Platform-specific gesture handling

2. **Platform-Specific Styling**
   - iOS and Android specific UI components
   - Adaptive styling based on platform detection

3. **Offline Capabilities**
   - Map caching for offline use
   - Location data persistence
   - Network status detection and handling

4. **Native Notifications**
   - Local notifications for location updates
   - In-app notifications with native styling
   - Platform-specific notification behaviors

5. **Native-like UI Components**
   - Bottom sheet with touch gestures
   - Platform-specific UI elements

## Implementation Details

### Capacitor Configuration

The `capacitor.config.ts` file has been configured with platform-specific settings:

```typescript
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.monitore.app',
  appName: 'Monitore',
  webDir: 'dist',
  server: {
    androidScheme: 'https',
    iosScheme: 'https',
    hostname: 'app.monitore.com',
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#FFFFFF',
      androidSplashResourceName: 'splash',
      androidScaleType: 'CENTER_CROP',
    },
    Keyboard: {
      resize: 'body',
      style: 'dark',
      resizeOnFullScreen: true,
    },
    StatusBar: {
      style: 'dark',
      backgroundColor: '#FFFFFF',
    },
    Haptics: {
      selectionStart: true,
      selectionChanged: true,
      selectionEnd: true,
    },
    LocalNotifications: {
      smallIcon: 'ic_stat_icon_config_sample',
      iconColor: '#488AFF',
      sound: 'beep.wav',
    },
    CapacitorHttp: {
      enabled: true,
    },
  },
  ios: {
    contentInset: 'automatic',
    preferredContentMode: 'mobile',
    backgroundColor: '#FFFFFF',
  },
  android: {
    backgroundColor: '#FFFFFF',
  },
};

export default config;
```

### Native Map Features

The `useNativeMapFeatures` hook adds haptic feedback and optimizes gestures for the map:

```typescript
// src/hooks/useNativeMapFeatures.ts
import { useEffect, useRef } from 'react';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { isPlatform } from '@ionic/react';
import mapboxgl from 'mapbox-gl';

export function useNativeMapFeatures(map: mapboxgl.Map | null) {
  const isIOS = isPlatform('ios');
  const isAndroid = isPlatform('android');
  
  useEffect(() => {
    if (!map) return;
    
    // Add haptic feedback for map interactions
    map.on('zoomstart', async () => {
      await Haptics.impact({ style: ImpactStyle.Light });
    });
    
    map.on('zoomend', async () => {
      await Haptics.impact({ style: ImpactStyle.Light });
    });
    
    map.on('dragstart', async () => {
      await Haptics.impact({ style: ImpactStyle.Light });
    });
    
    map.on('dragend', async () => {
      await Haptics.impact({ style: ImpactStyle.Light });
    });
    
    map.on('click', async () => {
      await Haptics.impact({ style: ImpactStyle.Medium });
    });
    
    // Platform-specific optimizations
    if (isIOS) {
      // iOS-specific map settings
      map.dragRotate.disable();
      map.touchZoomRotate.disableRotation();
    } else if (isAndroid) {
      // Android-specific map settings
      map.dragRotate.disable();
      map.touchZoomRotate.disableRotation();
    }
    
    return () => {
      // Clean up event listeners
      map.off('zoomstart');
      map.off('zoomend');
      map.off('dragstart');
      map.off('dragend');
      map.off('click');
    };
  }, [map, isIOS, isAndroid]);
}
```

### Offline Capabilities

The `useOfflineMap` hook provides offline functionality for the map:

```typescript
// src/hooks/useOfflineMap.ts
import { useState, useEffect } from 'react';
import { Network } from '@capacitor/network';
import { Storage } from '@capacitor/storage';
import mapboxgl from 'mapbox-gl';

export function useOfflineMap(map: mapboxgl.Map | null) {
  const [isOnline, setIsOnline] = useState(true);
  const [cachedLocations, setCachedLocations] = useState<any[]>([]);
  
  // Network status monitoring
  useEffect(() => {
    const checkNetworkStatus = async () => {
      const status = await Network.getStatus();
      setIsOnline(status.connected);
    };
    
    checkNetworkStatus();
    
    const networkListener = Network.addListener('networkStatusChange', (status) => {
      setIsOnline(status.connected);
    });
    
    return () => {
      networkListener.remove();
    };
  }, []);
  
  // Load cached locations when offline
  useEffect(() => {
    if (!isOnline && map) {
      loadCachedLocations();
    }
  }, [isOnline, map]);
  
  // Cache current map view for offline use
  const cacheCurrentMapRegion = async () => {
    if (!map) return;
    
    try {
      const bounds = map.getBounds();
      const center = map.getCenter();
      const zoom = map.getZoom();
      
      await Storage.set({
        key: 'offlineMapRegion',
        value: JSON.stringify({
          bounds: {
            north: bounds.getNorth(),
            east: bounds.getEast(),
            south: bounds.getSouth(),
            west: bounds.getWest(),
          },
          center: {
            lng: center.lng,
            lat: center.lat,
          },
          zoom,
          timestamp: new Date().toISOString(),
        }),
      });
      
      return true;
    } catch (error) {
      console.error('Failed to cache map region:', error);
      return false;
    }
  };
  
  // Cache locations for offline use
  const cacheLocations = async (locations: any[]) => {
    try {
      await Storage.set({
        key: 'cachedLocations',
        value: JSON.stringify(locations),
      });
      
      return true;
    } catch (error) {
      console.error('Failed to cache locations:', error);
      return false;
    }
  };
  
  // Load cached locations
  const loadCachedLocations = async () => {
    try {
      const { value } = await Storage.get({ key: 'cachedLocations' });
      
      if (value) {
        const locations = JSON.parse(value);
        setCachedLocations(locations);
        return locations;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to load cached locations:', error);
      return [];
    }
  };
  
  // Load cached map region
  const loadCachedMapRegion = async () => {
    try {
      const { value } = await Storage.get({ key: 'offlineMapRegion' });
      
      if (value && map) {
        const region = JSON.parse(value);
        map.jumpTo({
          center: region.center,
          zoom: region.zoom,
        });
        
        return region;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to load cached map region:', error);
      return null;
    }
  };
  
  return {
    isOnline,
    cachedLocations,
    cacheCurrentMapRegion,
    cacheLocations,
    loadCachedLocations,
    loadCachedMapRegion,
  };
}
```

### Native Notifications

The `useNativeNotifications` hook provides native notification functionality:

```typescript
// src/hooks/useNativeNotifications.ts
import { useEffect } from 'react';
import { LocalNotifications } from '@capacitor/local-notifications';
import { isPlatform } from '@ionic/react';

export function useNativeNotifications() {
  const isIOS = isPlatform('ios');
  const isAndroid = isPlatform('android');
  
  useEffect(() => {
    // Request permission for notifications
    const requestPermissions = async () => {
      const { display } = await LocalNotifications.requestPermissions();
      return display === 'granted';
    };
    
    requestPermissions();
    
    return () => {
      // Clean up if needed
    };
  }, []);
  
  // Send a location update notification
  const sendLocationUpdateNotification = async (title: string, body: string) => {
    try {
      await LocalNotifications.schedule({
        notifications: [
          {
            title,
            body,
            id: new Date().getTime(),
            schedule: { at: new Date(Date.now()) },
            sound: isIOS ? 'beep.aiff' : 'beep.wav',
            smallIcon: isAndroid ? 'ic_stat_location' : undefined,
            actionTypeId: 'LOCATION_UPDATE',
            extra: {
              type: 'location_update',
            },
          },
        ],
      });
      
      return true;
    } catch (error) {
      console.error('Failed to send notification:', error);
      return false;
    }
  };
  
  // Send an offline mode notification
  const sendOfflineModeNotification = async (isOffline: boolean) => {
    try {
      const title = isOffline ? 'Modo Offline' : 'Conexão Restaurada';
      const body = isOffline
        ? 'Você está no modo offline. Algumas funcionalidades podem estar limitadas.'
        : 'Sua conexão foi restaurada. Todas as funcionalidades estão disponíveis.';
      
      await LocalNotifications.schedule({
        notifications: [
          {
            title,
            body,
            id: new Date().getTime(),
            schedule: { at: new Date(Date.now()) },
            sound: null, // No sound for connection status
            smallIcon: isAndroid ? 'ic_stat_network' : undefined,
            actionTypeId: 'NETWORK_STATUS',
            extra: {
              type: 'network_status',
              isOffline,
            },
          },
        ],
      });
      
      return true;
    } catch (error) {
      console.error('Failed to send offline notification:', error);
      return false;
    }
  };
  
  return {
    sendLocationUpdateNotification,
    sendOfflineModeNotification,
  };
}
```

### Bottom Sheet Component

The `BottomSheet` component provides a native-like bottom sheet:

```tsx
// src/components/ui/BottomSheet.tsx
import React, { useState, useRef, useEffect } from 'react';
import { isPlatform } from '@ionic/react';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import '../styles/native-map.css';

interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  snapPoints?: number[];
}

export const BottomSheet: React.FC<BottomSheetProps> = ({
  isOpen,
  onClose,
  children,
  snapPoints = [25, 50, 90],
}) => {
  const [currentSnapPoint, setCurrentSnapPoint] = useState(0);
  const sheetRef = useRef<HTMLDivElement>(null);
  const startY = useRef(0);
  const currentY = useRef(0);
  const isIOS = isPlatform('ios');
  
  useEffect(() => {
    if (isOpen) {
      setCurrentSnapPoint(0);
    }
  }, [isOpen]);
  
  const handleTouchStart = (e: React.TouchEvent) => {
    startY.current = e.touches[0].clientY;
    currentY.current = startY.current;
    
    if (sheetRef.current) {
      sheetRef.current.style.transition = 'none';
    }
  };
  
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!sheetRef.current) return;
    
    const touchY = e.touches[0].clientY;
    const deltaY = touchY - startY.current;
    currentY.current = touchY;
    
    if (deltaY < 0) {
      // Moving up
      const nextHeight = Math.min(
        window.innerHeight * (snapPoints[currentSnapPoint] / 100) - deltaY,
        window.innerHeight * (snapPoints[snapPoints.length - 1] / 100)
      );
      sheetRef.current.style.height = `${nextHeight}px`;
    } else if (deltaY > 0) {
      // Moving down
      const nextHeight = Math.max(
        window.innerHeight * (snapPoints[currentSnapPoint] / 100) - deltaY,
        0
      );
      sheetRef.current.style.height = `${nextHeight}px`;
    }
  };
  
  const handleTouchEnd = async () => {
    if (!sheetRef.current) return;
    
    const deltaY = currentY.current - startY.current;
    
    // Determine which snap point to go to
    if (Math.abs(deltaY) > 20) {
      if (deltaY > 0) {
        // Swiped down
        if (currentSnapPoint === 0) {
          // Close the sheet
          onClose();
          await Haptics.impact({ style: ImpactStyle.Medium });
        } else {
          // Go to previous snap point
          setCurrentSnapPoint(currentSnapPoint - 1);
          await Haptics.impact({ style: ImpactStyle.Light });
        }
      } else {
        // Swiped up
        if (currentSnapPoint < snapPoints.length - 1) {
          // Go to next snap point
          setCurrentSnapPoint(currentSnapPoint + 1);
          await Haptics.impact({ style: ImpactStyle.Light });
        }
      }
    }
    
    // Reset transition
    if (sheetRef.current) {
      sheetRef.current.style.transition = 'height 0.3s ease-out';
      sheetRef.current.style.height = `${window.innerHeight * (snapPoints[currentSnapPoint] / 100)}px`;
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="bottom-sheet-overlay">
      <div
        className={`bottom-sheet ${isIOS ? 'ios-sheet' : 'android-sheet'}`}
        ref={sheetRef}
        style={{
          height: `${window.innerHeight * (snapPoints[currentSnapPoint] / 100)}px`,
          transition: 'height 0.3s ease-out',
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className="bottom-sheet-handle" />
        <div className="bottom-sheet-content">{children}</div>
      </div>
    </div>
  );
};
```

### Platform-Specific Styling

The `native-map.css` file contains platform-specific styles:

```css
/* src/styles/native-map.css */
/* Common styles */
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* iOS specific styles */
.ios-map-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
}

.ios-map-container .map-control-button {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.ios-sheet {
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.ios-sheet .bottom-sheet-handle {
  width: 36px;
  height: 5px;
  background-color: #D1D1D6;
  border-radius: 2.5px;
}

/* Android specific styles */
.android-map-container {
  font-family: Roboto, 'Segoe UI', sans-serif;
}

.android-map-container .map-control-button {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background-color: #FFFFFF;
}

.android-sheet {
  border-radius: 8px 8px 0 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
}

.android-sheet .bottom-sheet-handle {
  width: 32px;
  height: 4px;
  background-color: #BDBDBD;
  border-radius: 2px;
}

/* Offline controls */
.offline-indicator {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 10;
}

.offline-download-button {
  position: absolute;
  bottom: 80px;
  right: 10px;
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

/* Bottom sheet styles */
.bottom-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.bottom-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bottom-sheet-handle {
  margin-top: 10px;
  margin-bottom: 10px;
}

.bottom-sheet-content {
  width: 100%;
  padding: 0 16px 16px 16px;
  overflow-y: auto;
}
```

## Usage

### Integrating Native Features in MapView

The `MapView` component integrates all the native features:

```tsx
// src/components/map/MapView.tsx
import React, { useRef, useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { isPlatform } from '@ionic/react';
import { useNativeMapFeatures } from '../../hooks/useNativeMapFeatures';
import { useOfflineMap } from '../../hooks/useOfflineMap';
import { useNativeNotifications } from '../../hooks/useNativeNotifications';
import { BottomSheet } from '../ui/BottomSheet';
import { MapControlButtons } from './MapControlButtons';
import '../../styles/native-map.css';

mapboxgl.accessToken = 'your-mapbox-token';

interface MapViewProps {
  initialLocation?: { lat: number; lng: number };
}

export const MapView: React.FC<MapViewProps> = ({ initialLocation }) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [bottomSheetOpen, setBottomSheetOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<any>(null);
  const isIOS = isPlatform('ios');
  
  // Use custom hooks
  useNativeMapFeatures(map.current);
  const {
    isOnline,
    cachedLocations,
    cacheCurrentMapRegion,
    cacheLocations,
    loadCachedMapRegion,
  } = useOfflineMap(map.current);
  const { sendLocationUpdateNotification, sendOfflineModeNotification } = useNativeNotifications();
  
  // Initialize map
  useEffect(() => {
    if (map.current || !mapContainer.current) return;
    
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v11',
      center: initialLocation ? [initialLocation.lng, initialLocation.lat] : [-46.6333, -23.5505],
      zoom: 12,
    });
    
    map.current.on('load', () => {
      // Load cached map region if offline
      if (!isOnline) {
        loadCachedMapRegion();
      }
    });
    
    map.current.on('click', (e) => {
      // Show bottom sheet with location details
      setSelectedLocation({
        lat: e.lngLat.lat,
        lng: e.lngLat.lng,
        timestamp: new Date().toISOString(),
      });
      setBottomSheetOpen(true);
    });
    
    return () => {
      map.current?.remove();
      map.current = null;
    };
  }, [initialLocation, isOnline, loadCachedMapRegion]);
  
  // Handle online/offline status changes
  useEffect(() => {
    sendOfflineModeNotification(!isOnline);
  }, [isOnline, sendOfflineModeNotification]);
  
  // Handle location sharing
  const handleShareLocation = async () => {
    if (!map.current) return;
    
    const center = map.current.getCenter();
    const newLocation = {
      lat: center.lat,
      lng: center.lng,
      timestamp: new Date().toISOString(),
    };
    
    // Cache the location for offline use
    const locations = [...cachedLocations, newLocation];
    await cacheLocations(locations);
    
    // Send notification
    sendLocationUpdateNotification(
      'Localização Compartilhada',
      `Sua localização foi compartilhada com sucesso às ${new Date().toLocaleTimeString()}.`
    );
  };
  
  // Handle map download for offline use
  const handleDownloadMap = async () => {
    const success = await cacheCurrentMapRegion();
    
    if (success) {
      sendLocationUpdateNotification(
        'Mapa Salvo',
        'Esta região do mapa foi salva para uso offline.'
      );
    }
  };
  
  return (
    <div
      className={`map-container ${isIOS ? 'ios-map-container' : 'android-map-container'}`}
      ref={mapContainer}
    >
      {!isOnline && (
        <div className="offline-indicator">Modo Offline</div>
      )}
      
      <MapControlButtons
        onShareLocation={handleShareLocation}
        onDownloadMap={handleDownloadMap}
      />
      
      <BottomSheet
        isOpen={bottomSheetOpen}
        onClose={() => setBottomSheetOpen(false)}
        snapPoints={[25, 50, 90]}
      >
        {selectedLocation && (
          <div>
            <h3>Detalhes da Localização</h3>
            <p>Latitude: {selectedLocation.lat.toFixed(6)}</p>
            <p>Longitude: {selectedLocation.lng.toFixed(6)}</p>
            <p>
              Horário: {new Date(selectedLocation.timestamp).toLocaleTimeString()}
            </p>
          </div>
        )}
      </BottomSheet>
    </div>
  );
};
```

## Testing

For testing the native features, refer to the [Native Features Test Plan](./native-features-test-plan.md) document.

## Extending Native Features

### Adding New Haptic Feedback Patterns

To add new haptic feedback patterns, use the Capacitor Haptics API:

```typescript
import { Haptics, ImpactStyle, NotificationType } from '@capacitor/haptics';

// For UI element impacts
await Haptics.impact({ style: ImpactStyle.Light }); // Light impact
await Haptics.impact({ style: ImpactStyle.Medium }); // Medium impact
await Haptics.impact({ style: ImpactStyle.Heavy }); // Heavy impact

// For notifications
await Haptics.notification({ type: NotificationType.Success }); // Success notification
await Haptics.notification({ type: NotificationType.Warning }); // Warning notification
await Haptics.notification({ type: NotificationType.Error }); // Error notification

// For vibration patterns
await Haptics.vibrate(); // Default vibration
await Haptics.vibrate({ duration: 1000 }); // 1 second vibration
```

### Adding New Native Notifications

To add new notification types, extend the `useNativeNotifications` hook:

```typescript
// Add a new notification function
const sendCustomNotification = async (title: string, body: string, data: any) => {
  try {
    await LocalNotifications.schedule({
      notifications: [
        {
          title,
          body,
          id: new Date().getTime(),
          schedule: { at: new Date(Date.now()) },
          sound: isIOS ? 'custom.aiff' : 'custom.wav',
          smallIcon: isAndroid ? 'ic_stat_custom' : undefined,
          actionTypeId: 'CUSTOM_ACTION',
          extra: {
            type: 'custom_notification',
            ...data,
          },
        },
      ],
    });
    
    return true;
  } catch (error) {
    console.error('Failed to send custom notification:', error);
    return false;
  }
};
```

### Adding New Platform-Specific Styles

To add new platform-specific styles, extend the CSS files:

```css
/* For iOS */
.ios-custom-component {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

/* For Android */
.android-custom-component {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background-color: #FFFFFF;
}
```

## Conclusion

These native features enhance the mobile experience of the Monitore MVP app by making it feel more native on both iOS and Android devices. The implementation follows platform-specific design patterns and leverages Capacitor's native APIs for haptic feedback, notifications, and offline capabilities.

For any questions or issues, please refer to the [Native Features Test Plan](./native-features-test-plan.md) document or contact the development team.