import { Selector, t } from 'testcafe';

// Page Object Model
class AuthPage {
  emailInput: Selector;
  passwordInput: Selector;
  loginButton: Selector;
  signupLink: Selector;
  errorMessage: Selector;
  logoutButton: Selector;
  dashboardTitle: Selector;

  constructor() {
    this.emailInput = Selector('[data-testid="email-input"]');
    this.passwordInput = Selector('[data-testid="password-input"]');
    this.loginButton = Selector('[data-testid="login-button"]');
    this.signupLink = Selector('[data-testid="signup-link"]');
    this.errorMessage = Selector('[data-testid="error-message"]');
    this.logoutButton = Selector('[data-testid="logout-button"]');
    this.dashboardTitle = Selector('[data-testid="dashboard-title"]');
  }

  async login(email: string, password: string) {
    await t
      .typeText(this.emailInput, email)
      .typeText(this.passwordInput, password)
      .click(this.loginButton);
  }

  async logout() {
    await t.click(this.logoutButton);
  }
}

const authPage = new AuthPage();

// Configuração dos testes
fixture('EduConnect - Fluxo de Autenticação')
  .page('http://localhost:4000/login')
  .beforeEach(async t => {
    // Limpar localStorage/sessionStorage
    await t.eval(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  });

// Testes de autenticação
test('Deve fazer login com credenciais válidas', async t => {
  await authPage.login('<EMAIL>', 'password123');
  
  await t
    .expect(authPage.dashboardTitle.exists).ok('Dashboard deve ser exibido após login')
    .expect(Selector('body').textContent).contains('Dashboard', 'Texto do dashboard deve estar presente');
});

test('Deve exibir erro com credenciais inválidas', async t => {
  await authPage.login('<EMAIL>', 'wrongpassword');
  
  await t
    .expect(authPage.errorMessage.exists).ok('Mensagem de erro deve ser exibida')
    .expect(authPage.errorMessage.textContent).contains('credenciais', 'Erro deve mencionar credenciais inválidas');
});

test('Deve validar campos obrigatórios', async t => {
  await t.click(authPage.loginButton);
  
  await t
    .expect(authPage.emailInput.hasAttribute('required')).ok('Campo email deve ser obrigatório')
    .expect(authPage.passwordInput.hasAttribute('required')).ok('Campo senha deve ser obrigatório');
});

test('Deve redirecionar para signup', async t => {
  await t.click(authPage.signupLink);
  
  await t
    .expect(t.eval(() => window.location.pathname)).eql('/signup', 'Deve redirecionar para página de cadastro');
});

test('Deve fazer logout corretamente', async t => {
  // Primeiro fazer login
  await authPage.login('<EMAIL>', 'password123');
  await t.expect(authPage.dashboardTitle.exists).ok();
  
  // Depois logout
  await authPage.logout();
  
  await t
    .expect(t.eval(() => window.location.pathname)).eql('/login', 'Deve redirecionar para login após logout');
});

// Testes de responsividade
test('Deve funcionar em dispositivos móveis', async t => {
  await t.resizeWindow(375, 667); // iPhone SE
  
  await t
    .expect(authPage.emailInput.visible).ok('Campo email deve ser visível em mobile')
    .expect(authPage.passwordInput.visible).ok('Campo senha deve ser visível em mobile')
    .expect(authPage.loginButton.visible).ok('Botão login deve ser visível em mobile');
  
  await authPage.login('<EMAIL>', 'password123');
  await t.expect(authPage.dashboardTitle.exists).ok('Dashboard deve funcionar em mobile');
});

// Testes de acessibilidade
test('Deve ter elementos acessíveis', async t => {
  await t
    .expect(authPage.emailInput.hasAttribute('aria-label')).ok('Campo email deve ter aria-label')
    .expect(authPage.passwordInput.hasAttribute('aria-label')).ok('Campo senha deve ter aria-label')
    .expect(authPage.loginButton.hasAttribute('role')).ok('Botão deve ter role definido');
});

// Teste de performance
test('Deve carregar rapidamente', async t => {
  const startTime = Date.now();
  
  await t.expect(authPage.emailInput.exists).ok('Página deve carregar', { timeout: 5000 });
  
  const loadTime = Date.now() - startTime;
  await t.expect(loadTime).lt(3000, 'Página deve carregar em menos de 3 segundos');
}); 