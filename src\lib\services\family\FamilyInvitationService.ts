// @ts-nocheck
import { BaseService } from '../base/BaseService';

export interface FamilyInvitation {
  invitation_id: string;
  student_name: string;
  student_email: string;
  invitation_token: string;
  created_at: string;
  expires_at: string;
}

export interface InvitationResponse {
  success: boolean;
  message: string;
  invitation_id?: string;
}

/**
 * Service for managing family invitations and connections
 */
export class FamilyInvitationService extends BaseService {
  
  /**
   * Request connection to a student (guardian side)
   */
  async requestStudentConnection(studentEmail: string, studentCpf?: string): Promise<InvitationResponse> {
    try {
      await this.getCurrentUser();

      // @ts-ignore - Ignorar erro de tipo temporariamente
      const { data, error } = await this.supabase
        .rpc('request_student_connection', {
          p_student_email: studentEmail.trim(),
          p_student_cpf: studentCpf?.trim() || null
        });

      if (error) {
        console.error('[FamilyInvitationService] Error requesting connection:', error);
        this.showError('Erro ao solicitar conexão com estudante');
        return { success: false, message: error.message };
      }

      if (!data || (Array.isArray(data) && data.length === 0)) {
        return { success: false, message: 'Resposta inválida do servidor' };
      }

      const result = data[0];
      
      if (result.success) {
        this.showSuccess(result.message);
      } else {
        this.showError(result.message);
      }

      return {
        success: result.success,
        message: result.message,
        invitation_id: result.invitation_id
      };
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in requestStudentConnection:', error);
      this.showError('Erro ao processar solicitação');
      return { success: false, message: 'Erro interno do sistema' };
    }
  }

  /**
   * Send invitation to guardian (student side)
   */
  async sendInvitationToGuardian(guardianEmail: string): Promise<InvitationResponse> {
    try {
      await this.getCurrentUser();

      // @ts-ignore
      const { data, error } = await this.supabase
        .rpc('send_family_invitation', {
          p_guardian_email: guardianEmail.trim()
        });

      if (error) {
        console.error('[FamilyInvitationService] Error sending invitation:', error);
        this.showError('Erro ao enviar convite');
        return { success: false, message: error.message };
      }

      // @ts-ignore
      if (!data || data.length === 0) {
        return { success: false, message: 'Resposta inválida do servidor' };
      }

      const result = data[0];
      
      if (result.success) {
        this.showSuccess(result.message);
      } else {
        this.showError(result.message);
      }

      return {
        success: result.success,
        message: result.message,
        invitation_id: result.invitation_id
      };
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in sendInvitationToGuardian:', error);
      this.showError('Erro ao processar convite');
      return { success: false, message: 'Erro interno do sistema' };
    }
  }

  /**
   * Accept family invitation using token
   */
  async acceptInvitation(invitationToken: string): Promise<InvitationResponse> {
    try {
      await this.getCurrentUser();

      // @ts-ignore
      const { data, error } = await this.supabase
        .rpc('accept_family_invitation', {
          p_invitation_token: invitationToken.trim()
        });

      if (error) {
        console.error('[FamilyInvitationService] Error accepting invitation:', error);
        this.showError('Erro ao aceitar convite');
        return { success: false, message: error.message };
      }

      // @ts-ignore
      if (!data || data.length === 0) {
        return { success: false, message: 'Resposta inválida do servidor' };
      }

      const result = data[0];
      
      if (result.success) {
        this.showSuccess(result.message);
      } else {
        this.showError(result.message);
      }

      return {
        success: result.success,
        message: result.message
      };
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in acceptInvitation:', error);
      this.showError('Erro ao processar convite');
      return { success: false, message: 'Erro interno do sistema' };
    }
  }

  /**
   * Accept student request (student accepts guardian's request to connect)
   */
  async acceptStudentRequest(invitationToken: string): Promise<InvitationResponse> {
    try {
      await this.getCurrentUser();

      const { data, error } = await this.supabase
        .rpc('accept_student_request', {
          p_invitation_token: invitationToken.trim()
        });

      if (error) {
        console.error('[FamilyInvitationService] Error accepting student request:', error);
        this.showError('Erro ao aceitar solicitação');
        return { success: false, message: error.message };
      }

      if (!data || data.length === 0) {
        return { success: false, message: 'Resposta inválida do servidor' };
      }

      const result = data[0];
      
      if (result.success) {
        this.showSuccess(result.message);
      } else {
        this.showError(result.message);
      }

      return {
        success: result.success,
        message: result.message
      };
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in acceptStudentRequest:', error);
      this.showError('Erro ao processar solicitação');
      return { success: false, message: 'Erro interno do sistema' };
    }
  }

  /**
   * Reject student invitation (student rejects guardian's request)
   */
  async rejectStudentInvitation(invitationToken: string): Promise<InvitationResponse> {
    try {
      await this.getCurrentUser();

      const { data, error } = await this.supabase
        .rpc('reject_student_invitation', {
          p_invitation_token: invitationToken.trim()
        });

      if (error) {
        console.error('[FamilyInvitationService] Error rejecting student invitation:', error);
        this.showError('Erro ao rejeitar solicitação');
        return { success: false, message: error.message };
      }

      if (!data || data.length === 0) {
        return { success: false, message: 'Resposta inválida do servidor' };
      }

      const result = data[0];

      if (result.success) {
        this.showSuccess(result.message);
      } else {
        this.showError(result.message);
      }

      return {
        success: result.success,
        message: result.message
      };
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in rejectStudentInvitation:', error);
      this.showError('Erro ao processar solicitação');
      return { success: false, message: 'Erro interno do sistema' };
    }
  }

  /**
   * Get pending invitations for current guardian
   */
  async getPendingInvitations(): Promise<FamilyInvitation[]> {
    try {
      await this.getCurrentUser();

      // @ts-ignore - Ignorar erro de tipo temporariamente
      const { data, error } = await this.supabase
        .rpc('get_guardian_pending_invitations');

      if (error) {
        console.error('[FamilyInvitationService] Error fetching invitations:', error);
        this.showError('Erro ao buscar convites pendentes');
        return [];
      }

      // @ts-ignore
      return data || [];
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in getPendingInvitations:', error);
      this.showError('Erro ao acessar convites');
      return [];
    }
  }

  /**
   * Get pending requests for current student (from guardians who want to connect)
   */
  async getStudentPendingRequests(): Promise<any[]> {
    try {
      await this.getCurrentUser();

      const { data, error } = await this.supabase
        .rpc('get_student_pending_requests');

      if (error) {
        console.error('[FamilyInvitationService] Error fetching student requests:', error);
        this.showError('Erro ao buscar solicitações pendentes');
        return [];
      }

      return data || [];
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in getStudentPendingRequests:', error);
      this.showError('Erro ao acessar solicitações');
      return [];
    }
  }

  /**
   * Get family invitations from the table directly (for monitoring)
   */
  async getFamilyInvitations(): Promise<any[]> {
    try {
      await this.getCurrentUser();

      // @ts-ignore
      const { data, error } = await this.supabase
        .from('family_invitations')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('[FamilyInvitationService] Error fetching family invitations:', error);
        return [];
      }

      return data || [];
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in getFamilyInvitations:', error);
      return [];
    }
  }

  /**
   * Cleanup expired invitations (admin function)
   */
  async cleanupExpiredInvitations(): Promise<number> {
    try {
      await this.getCurrentUser();

      // @ts-ignore
      const { data, error } = await this.supabase
        .rpc('cleanup_expired_family_invitations');

      if (error) {
        console.error('[FamilyInvitationService] Error cleaning up invitations:', error);
        return 0;
      }

      // @ts-ignore
      return data || 0;
    } catch (error: any) {
      console.error('[FamilyInvitationService] Error in cleanupExpiredInvitations:', error);
      return 0;
    }
  }
}

export const familyInvitationService = new FamilyInvitationService(); 