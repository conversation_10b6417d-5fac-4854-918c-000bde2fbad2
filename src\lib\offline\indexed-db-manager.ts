
/**
 * IndexedDB Manager for offline data storage
 * Extended with sync queue support
 */

interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // Time To Live in milliseconds
  version: number;
}

interface CacheConfig {
  dbName: string;
  version: number;
  stores: string[];
}

export class IndexedDBManager {
  private db: IDBDatabase | null = null;
  private config: CacheConfig;

  constructor(config: CacheConfig) {
    this.config = config;
  }

  async init(): Promise<void> {
    if (this.db) return;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.dbName, this.config.version);

      request.onerror = () => {
        console.error('[IndexedDB] Failed to open database:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('[IndexedDB] Database opened successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        this.config.stores.forEach(storeName => {
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, { keyPath: 'key' });
            store.createIndex('timestamp', 'timestamp', { unique: false });
            console.log(`[IndexedDB] Created store: ${storeName}`);
          }
        });
      };
    });
  }

  async set<T>(storeName: string, key: string, data: T, ttl: number = 24 * 60 * 60 * 1000): Promise<void> {
    if (!this.db) await this.init();

    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      version: 1
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put({ key, ...cacheItem });

      request.onsuccess = () => {
        console.log(`[IndexedDB] Stored ${key} in ${storeName}`);
        resolve();
      };

      request.onerror = () => {
        console.error(`[IndexedDB] Failed to store ${key}:`, request.error);
        reject(request.error);
      };
    });
  }

  async get<T>(storeName: string, key: string): Promise<T | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onsuccess = () => {
        const result = request.result;
        
        if (!result) {
          resolve(null);
          return;
        }

        // Check if data has expired
        const now = Date.now();
        const isExpired = (now - result.timestamp) > result.ttl;

        if (isExpired) {
          console.log(`[IndexedDB] Data expired for ${key}, removing...`);
          this.delete(storeName, key);
          resolve(null);
          return;
        }

        console.log(`[IndexedDB] Retrieved ${key} from ${storeName}`);
        resolve(result.data);
      };

      request.onerror = () => {
        console.error(`[IndexedDB] Failed to get ${key}:`, request.error);
        reject(request.error);
      };
    });
  }

  async delete(storeName: string, key: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onsuccess = () => {
        console.log(`[IndexedDB] Deleted ${key} from ${storeName}`);
        resolve();
      };

      request.onerror = () => {
        console.error(`[IndexedDB] Failed to delete ${key}:`, request.error);
        reject(request.error);
      };
    });
  }

  async clear(storeName: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => {
        console.log(`[IndexedDB] Cleared store ${storeName}`);
        resolve();
      };

      request.onerror = () => {
        console.error(`[IndexedDB] Failed to clear ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  async getAllKeys(storeName: string): Promise<string[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAllKeys();

      request.onsuccess = () => {
        resolve(request.result as string[]);
      };

      request.onerror = () => {
        console.error(`[IndexedDB] Failed to get keys from ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  async cleanExpired(storeName: string): Promise<number> {
    if (!this.db) await this.init();
    
    let cleanedCount = 0;
    const now = Date.now();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.openCursor();

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        
        if (cursor) {
          const item = cursor.value;
          const isExpired = (now - item.timestamp) > item.ttl;
          
          if (isExpired) {
            cursor.delete();
            cleanedCount++;
          }
          
          cursor.continue();
        } else {
          console.log(`[IndexedDB] Cleaned ${cleanedCount} expired items from ${storeName}`);
          resolve(cleanedCount);
        }
      };

      request.onerror = () => {
        console.error(`[IndexedDB] Failed to clean expired items:`, request.error);
        reject(request.error);
      };
    });
  }
}

// Singleton instance for the app with sync queue support
export const offlineDB = new IndexedDBManager({
  dbName: 'EduConnectOffline',
  version: 2, // Bumped version for sync queue support
  stores: [
    'locations',      // Student locations
    'profiles',       // User profiles
    'guardians',      // Guardian relationships
    'settings',       // App settings
    'api_cache',      // API response cache
    'sync_queue'      // Sync actions queue
  ]
});
