import { useEffect, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { Capacitor } from '@capacitor/core';

interface UseNativeMapFeaturesProps {
  map: mapboxgl.Map | null;
  mapLoaded: boolean;
  onLocationUpdate?: () => void;
}

/**
 * Hook to enhance map with native-like features for mobile devices
 * - Adds haptic feedback for interactions
 * - Optimizes touch gestures
 * - Adds platform-specific behaviors
 */
export function useNativeMapFeatures({
  map,
  mapLoaded,
  onLocationUpdate
}: UseNativeMapFeaturesProps) {
  const isNative = Capacitor.isNativePlatform();
  const isIOS = Capacitor.getPlatform() === 'ios';
  const isAndroid = Capacitor.getPlatform() === 'android';

  // Trigger haptic feedback based on intensity
  const triggerHaptic = useCallback(async (intensity: 'light' | 'medium' | 'heavy') => {
    if (!isNative) return;
    
    try {
      let style: ImpactStyle;
      switch (intensity) {
        case 'light':
          style = ImpactStyle.Light;
          break;
        case 'medium':
          style = ImpactStyle.Medium;
          break;
        case 'heavy':
          style = ImpactStyle.Heavy;
          break;
        default:
          style = ImpactStyle.Medium;
      }
      
      await Haptics.impact({ style });
    } catch (error) {
      console.error('Haptic feedback error:', error);
    }
  }, [isNative]);

  // Trigger success haptic pattern
  const triggerSuccessHaptic = useCallback(async () => {
    if (!isNative) return;
    
    try {
      // Create a success pattern with multiple impacts
      await Haptics.impact({ style: ImpactStyle.Light });
      setTimeout(async () => {
        await Haptics.impact({ style: ImpactStyle.Medium });
      }, 100);
    } catch (error) {
      console.error('Success haptic feedback error:', error);
    }
  }, [isNative]);

  // Trigger error haptic pattern
  const triggerErrorHaptic = useCallback(async () => {
    if (!isNative) return;
    
    try {
      // Create an error pattern with multiple impacts
      await Haptics.impact({ style: ImpactStyle.Heavy });
      setTimeout(async () => {
        await Haptics.impact({ style: ImpactStyle.Heavy });
      }, 150);
    } catch (error) {
      console.error('Error haptic feedback error:', error);
    }
  }, [isNative]);

  // Add haptic feedback to map interactions
  useEffect(() => {
    if (!map || !mapLoaded || !isNative) return;

    console.log('[useNativeMapFeatures] Adding haptic feedback to map interactions');

    // Haptic feedback for map interactions
    const handleZoomStart = () => triggerHaptic('light');
    const handleZoomEnd = () => triggerHaptic('medium');
    const handleDragStart = () => triggerHaptic('light');
    const handleDragEnd = () => triggerHaptic('medium');
    const handleClick = () => triggerHaptic('medium');
    
    // Add event listeners
    map.on('zoomstart', handleZoomStart);
    map.on('zoomend', handleZoomEnd);
    map.on('dragstart', handleDragStart);
    map.on('dragend', handleDragEnd);
    map.on('click', handleClick);

    // Add haptic feedback for marker clicks
    const markers = document.querySelectorAll('.custom-marker');
    markers.forEach(marker => {
      marker.addEventListener('click', () => triggerHaptic('medium'));
    });

    // Cleanup
    return () => {
      map.off('zoomstart', handleZoomStart);
      map.off('zoomend', handleZoomEnd);
      map.off('dragstart', handleDragStart);
      map.off('dragend', handleDragEnd);
      map.off('click', handleClick);
      
      markers.forEach(marker => {
        marker.removeEventListener('click', () => triggerHaptic('medium'));
      });
    };
  }, [map, mapLoaded, isNative, triggerHaptic]);

  // Optimize touch gestures for the map
  useEffect(() => {
    if (!map || !mapLoaded || !isNative) return;

    console.log('[useNativeMapFeatures] Optimizing touch gestures for map');

    // Get the map container and canvas
    const container = map.getContainer();
    const canvas = map.getCanvas();

    if (!container || !canvas) return;

    // Prevent default touch behaviors that might interfere with map
    container.style.touchAction = 'none';
    
    // Add platform-specific styles
    if (isIOS) {
      // iOS-specific optimizations
      (container.style as any)['-webkit-overflow-scrolling'] = 'touch';
      (canvas.style as any)['-webkit-tap-highlight-color'] = 'transparent';
    } else if (isAndroid) {
      // Android-specific optimizations
      container.style.overscrollBehavior = 'none';
    }

    // Optimize double-tap to zoom
    let lastTap = 0;
    const handleDoubleTap = (e: TouchEvent) => {
      const currentTime = new Date().getTime();
      const tapLength = currentTime - lastTap;
      
      if (tapLength < 300 && tapLength > 0) {
        // Double tap detected
        e.preventDefault();
        triggerHaptic('medium');
        
        if (e.touches && e.touches[0]) {
          const point = {
            x: e.touches[0].clientX,
            y: e.touches[0].clientY
          };
          
          // Convert point to map coordinates
          const containerRect = container.getBoundingClientRect();
          const mapPoint = new mapboxgl.Point(
            point.x - containerRect.left,
            point.y - containerRect.top
          );
          
          // Get the geographic coordinates
          const lngLat = map.unproject(mapPoint);
          
          // Zoom in to the point
          map.flyTo({
            center: lngLat,
            zoom: map.getZoom() + 1,
            speed: 1.5,
            essential: true
          });
        }
      }
      
      lastTap = currentTime;
    };

    // Add event listener for double-tap
    canvas.addEventListener('touchend', handleDoubleTap, { passive: false });

    // Cleanup
    return () => {
      canvas.removeEventListener('touchend', handleDoubleTap);
    };
  }, [map, mapLoaded, isNative, isIOS, isAndroid, triggerHaptic]);

  // Add haptic feedback for location updates
  useEffect(() => {
    if (!onLocationUpdate || !isNative) return;

    const originalOnLocationUpdate = onLocationUpdate;
    
    // Override the location update function to add haptic feedback
    const enhancedOnLocationUpdate = async () => {
      await triggerSuccessHaptic();
      originalOnLocationUpdate();
    };

    // Replace the original function with our enhanced version
    onLocationUpdate = enhancedOnLocationUpdate;

    // No cleanup needed as we're not adding event listeners
  }, [onLocationUpdate, isNative, triggerSuccessHaptic]);

  return {
    triggerHaptic,
    triggerSuccessHaptic,
    triggerErrorHaptic,
    isNative,
    isIOS,
    isAndroid
  };
}