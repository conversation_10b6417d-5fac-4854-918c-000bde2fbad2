/**
 * Codex Local Shell - Wrapper Se<PERSON>ro
 * 
 * Este script implementa a interface Local Shell para Codex CLI,
 * seguindo os protocolos de segurança do Locate-Family-Connect.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Diretório raiz do projeto
const PROJECT_ROOT = path.resolve(__dirname, '..');

// Lista de comandos permitidos (whitelist)
const ALLOWED_COMMANDS = [
  'npm', 'node', 'ls', 'dir', 'cat', 'echo', 'git', 'find', 'grep',
  'supabase', 'prettier', 'eslint', 'tsc', 'vite'
];

// Lista de caminhos protegidos (não permitir alterações)
const PROTECTED_PATHS = [
  '.git', 'node_modules', '.env', '.env.local', 
  'supabase/.branches', 'supabase/.temp'
];

// Lista de comandos que precisam de confirmação
const CONFIRM_COMMANDS = [
  'rm', 'del', 'rmdir', 'mv', 'move', 'ren', 'rename'
];

/**
 * Verifica se o comando é permitido
 */
function isCommandAllowed(command) {
  const mainCommand = command.split(' ')[0];
  return ALLOWED_COMMANDS.includes(mainCommand);
}

/**
 * Verifica se o comando acessa caminhos protegidos
 */
function accessesProtectedPath(command) {
  return PROTECTED_PATHS.some(path => command.includes(path));
}

/**
 * Verifica se o comando precisa de confirmação
 */
function needsConfirmation(command) {
  const mainCommand = command.split(' ')[0];
  return CONFIRM_COMMANDS.includes(mainCommand);
}

/**
 * Executa o comando com garantias de segurança
 */
async function executeCommand(command, cwd, timeout = 30000) {
  return new Promise((resolve) => {
    console.log(`\n🔍 Verificando comando: ${command}`);
    
    // Verificações de segurança
    if (!isCommandAllowed(command)) {
      console.log(`⛔ Comando não permitido: ${command}`);
      return resolve({
        output: `Erro de Segurança: O comando "${command}" não está na lista de permitidos.`,
        exitCode: 1
      });
    }
    
    if (accessesProtectedPath(command)) {
      console.log(`⛔ Caminho protegido detectado no comando: ${command}`);
      return resolve({
        output: `Erro de Segurança: O comando acessa caminhos protegidos.`,
        exitCode: 1
      });
    }
    
    if (needsConfirmation(command)) {
      console.log(`⚠️ Comando potencialmente perigoso: ${command}`);
      return resolve({
        output: `Confirmação necessária: Execute este comando manualmente.`,
        exitCode: 1
      });
    }
    
    // Garantir que o diretório de trabalho está dentro do projeto
    const workDir = path.resolve(cwd || PROJECT_ROOT);
    if (!workDir.startsWith(PROJECT_ROOT)) {
      return resolve({
        output: `Erro de Segurança: Diretório fora do projeto não é permitido.`,
        exitCode: 1
      });
    }
    
    console.log(`✅ Comando verificado, executando: ${command}`);
    
    // Preparar comando para execução
    const parts = command.split(' ');
    const cmd = parts[0];
    const args = parts.slice(1);
    
    // Executar com timeout
    const proc = spawn(cmd, args, {
      cwd: workDir,
      shell: true,
      timeout
    });
    
    let output = '';
    
    proc.stdout.on('data', (data) => {
      const chunk = data.toString();
      output += chunk;
      console.log(chunk);
    });
    
    proc.stderr.on('data', (data) => {
      const chunk = data.toString();
      output += chunk;
      console.error(chunk);
    });
    
    proc.on('close', (code) => {
      console.log(`🏁 Comando finalizado com código: ${code}`);
      resolve({
        output,
        exitCode: code
      });
    });
    
    // Force timeout if needed
    setTimeout(() => {
      if (proc.connected) {
        proc.kill();
        resolve({
          output: output + '\n[TIMEOUT] Comando excedeu o tempo limite.',
          exitCode: 124 // Tradicional código para timeout
        });
      }
    }, timeout);
  });
}

// Interface para Codex CLI
async function handleLocalShellRequest(callId, args) {
  const { command, working_directory, timeout_ms } = args;
  
  // Log para auditoria
  const logEntry = {
    timestamp: new Date().toISOString(),
    command,
    workingDir: working_directory,
    timeout: timeout_ms
  };
  
  fs.appendFileSync(
    path.join(PROJECT_ROOT, 'logs', 'codex-commands.log'),
    JSON.stringify(logEntry) + '\n'
  );
  
  // Executar com garantias
  const result = await executeCommand(
    command.join(' '), 
    working_directory,
    timeout_ms || 30000
  );
  
  // Formato esperado pelo Codex
  return {
    type: "local_shell_call_output",
    call_id: callId,
    output: result.output
  };
}

// Exportar para uso com Codex CLI
module.exports = {
  handleLocalShellRequest,
  executeCommand
};

// Interface de linha de comando para testes
if (require.main === module) {
  // Criar diretório de logs se não existir
  const logsDir = path.join(PROJECT_ROOT, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  
  const command = process.argv.slice(2).join(' ');
  if (command) {
    executeCommand(command, process.cwd())
      .then(result => {
        console.log('\nResultado:', result);
        process.exit(result.exitCode);
      });
  } else {
    console.log('Uso: node codex-local-shell.js <comando>');
  }
}
