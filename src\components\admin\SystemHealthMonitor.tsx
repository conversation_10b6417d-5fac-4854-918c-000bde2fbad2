
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface SystemStatus {
  edgeFunctions: 'healthy' | 'degraded' | 'down';
  database: 'healthy' | 'degraded' | 'down';
  email: 'healthy' | 'degraded' | 'down';
  lastCheck: Date;
}

export const SystemHealthMonitor: React.FC = () => {
  const [status, setStatus] = useState<SystemStatus>({
    edgeFunctions: 'healthy',
    database: 'healthy', 
    email: 'healthy',
    lastCheck: new Date()
  });
  const [isChecking, setIsChecking] = useState(false);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  const checkSystemHealth = async () => {
    setIsChecking(true);
    
    try {
      const newStatus: SystemStatus = {
        edgeFunctions: 'healthy',
        database: 'healthy',
        email: 'healthy',
        lastCheck: new Date()
      };

      // Test database connection
      try {
        await supabase.from('profiles').select('count').limit(1);
        newStatus.database = 'healthy';
      } catch (error) {
        console.error('Database check failed:', error);
        newStatus.database = 'down';
      }

      // Test Edge Functions
      try {
        const { error } = await supabase.functions.invoke('create-student-account', {
          body: { test: true }
        });
        // If we get a structured error (not network error), the function is reachable
        newStatus.edgeFunctions = error ? 'degraded' : 'healthy';
      } catch (error) {
        console.error('Edge function check failed:', error);
        newStatus.edgeFunctions = 'down';
      }

      // Check recent activity
      try {
        const { data: logs } = await supabase
          .from('auth_logs')
          .select('*')
          .order('occurred_at', { ascending: false })
          .limit(10);
        
        setRecentActivity(logs || []);
      } catch (error) {
        console.error('Activity check failed:', error);
      }

      setStatus(newStatus);
    } catch (error) {
      console.error('Health check failed:', error);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 60000); // Check every minute
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'degraded':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'down':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === 'healthy' ? 'default' : status === 'degraded' ? 'secondary' : 'destructive';
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Status do Sistema</CardTitle>
          <Button 
            onClick={checkSystemHealth} 
            disabled={isChecking}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                {getStatusIcon(status.database)}
                <span className="font-medium">Banco de Dados</span>
              </div>
              {getStatusBadge(status.database)}
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                {getStatusIcon(status.edgeFunctions)}
                <span className="font-medium">Edge Functions</span>
              </div>
              {getStatusBadge(status.edgeFunctions)}
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                {getStatusIcon(status.email)}
                <span className="font-medium">Sistema de Email</span>
              </div>
              {getStatusBadge(status.email)}
            </div>
          </div>
          
          <div className="text-sm text-gray-500">
            Última verificação: {status.lastCheck.toLocaleString('pt-BR')}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Atividade Recente</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {recentActivity.length > 0 ? (
              recentActivity.map((log, index) => (
                <div key={index} className="flex items-center justify-between p-2 border-b last:border-b-0">
                  <div>
                    <span className="font-medium">{log.event_type}</span>
                    {log.metadata && (
                      <span className="text-sm text-gray-500 ml-2">
                        {JSON.stringify(log.metadata).slice(0, 50)}...
                      </span>
                    )}
                  </div>
                  <span className="text-sm text-gray-400">
                    {new Date(log.occurred_at).toLocaleTimeString('pt-BR')}
                  </span>
                </div>
              ))
            ) : (
              <p className="text-gray-500">Nenhuma atividade recente encontrada</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemHealthMonitor;
