import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import LogoutButton from '@/components/LogoutButton';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface StudentDashboardHeaderProps {
  userFullName: string;
  className?: string;
}

const StudentDashboardHeader: React.FC<StudentDashboardHeaderProps> = ({
  userFullName,
  className
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <div className={cn('flex items-center justify-between mb-4', className)}>
      <div>
        <h1 className="text-xl font-semibold">
          {t('profile.greeting', { name: userFullName })}
        </h1>
        <p className="text-sm text-muted-foreground">
          {t('studentDashboard.title')}
        </p>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" size="sm" aria-label={t('studentDashboard.profile')} onClick={() => navigate('/profile')}>
          {t('studentDashboard.profile')}
        </Button>
        <LogoutButton variant="destructive" size="sm" aria-label={t('common.logout')} />
      </div>
    </div>
  );
};

export default StudentDashboardHeader;
