import React, { useEffect, useState } from 'react';
import { RefreshCw, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { clearAppCache } from '@/lib/utils/cache-manager';

interface BlankScreenDetectorProps {
  children: React.ReactNode;
  timeout?: number; // Timeout em ms para detectar tela branca
}

const BlankScreenDetector: React.FC<BlankScreenDetectorProps> = ({ 
  children, 
  timeout = 5000 
}) => {
  const [isBlankScreen, setIsBlankScreen] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  useEffect(() => {
    let hasChecked = false;
    
    const checkForBlankScreen = () => {
      if (hasChecked) return; // Evitar múltiplas verificações
      hasChecked = true;
      
      // Verificar se existe conteúdo visível na página
      const bodyContent = document.body.innerText.trim();
      const hasVisibleElements = document.querySelectorAll('main, [role="main"], .dashboard, .auth, [data-testid]').length > 0;
      const hasReactContent = document.querySelectorAll('[data-reactroot], #root > *').length > 0;
      
      // Se não há conteúdo de texto significativo nem elementos principais
      if (bodyContent.length < 100 && !hasVisibleElements && !hasReactContent) {
        console.warn('[BlankScreenDetector] Possible blank screen detected');
        setIsBlankScreen(true);
      } else {
        setIsBlankScreen(false);
      }
    };

    // Verificar após o timeout especificado
    const timer = setTimeout(() => {
      checkForBlankScreen();
    }, timeout);

    // ⚠️ REMOVIDO MutationObserver que estava causando loop infinito
    // O observer será reabilitado apenas se necessário no futuro

    return () => {
      clearTimeout(timer);
    };
  }, [timeout, retryCount]);

  const handleRecovery = async () => {
    console.log('[BlankScreenDetector] Attempting recovery...');
    
    if (retryCount < maxRetries) {
      try {
        // Primeiro, tentar limpar cache e recarregar
        await clearAppCache(false);
        
        // Enviar mensagem para o service worker limpar cache
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          const messageChannel = new MessageChannel();
          navigator.serviceWorker.controller.postMessage(
            { type: 'CLEAR_CACHE' },
            [messageChannel.port2]
          );
          
          messageChannel.port1.onmessage = (event) => {
            if (event.data.type === 'CACHE_CLEARED') {
              console.log('[BlankScreenDetector] Service worker cache cleared');
              setTimeout(() => {
                window.location.reload();
              }, 500);
            }
          };
        } else {
          // Se não há service worker, apenas recarregar
          setTimeout(() => {
            window.location.reload();
          }, 500);
        }
        
        setRetryCount(prev => prev + 1);
      } catch (error) {
        console.error('[BlankScreenDetector] Recovery failed:', error);
        window.location.reload();
      }
    } else {
      // Se excedeu tentativas, fazer reload forçado
      console.log('[BlankScreenDetector] Max retries exceeded, forcing reload');
      window.location.reload();
    }
  };

  const handleClearData = async () => {
    try {
      // Limpar todos os dados da aplicação
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }
      
      // Limpar localStorage
      localStorage.clear();
      
      // Limpar sessionStorage
      sessionStorage.clear();
      
      // Recarregar página
      window.location.href = '/login';
    } catch (error) {
      console.error('[BlankScreenDetector] Failed to clear data:', error);
      window.location.href = '/login';
    }
  };

  if (isBlankScreen) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
          <AlertTriangle className="mx-auto h-12 w-12 text-amber-500 mb-4" />
          
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Problema Detectado
          </h2>
          
          <p className="text-gray-600 mb-6">
            A aplicação parece estar com problemas para carregar. 
            Isso pode ser devido ao cache do navegador.
          </p>
          
          <div className="space-y-3">
            <Button 
              onClick={handleRecovery}
              className="w-full flex items-center justify-center gap-2"
              disabled={retryCount >= maxRetries}
            >
              <RefreshCw className="h-4 w-4" />
              {retryCount < maxRetries 
                ? `Tentar Novamente (${retryCount + 1}/${maxRetries})` 
                : 'Limite de Tentativas Atingido'
              }
            </Button>
            
            <Button 
              onClick={handleClearData}
              variant="outline" 
              className="w-full"
            >
              Limpar Dados e Ir para Login
            </Button>
          </div>
          
          <p className="text-xs text-gray-500 mt-4">
            Se o problema persistir, entre em contato com o suporte
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default BlankScreenDetector; 