
import React, { useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Shield, Eye, Download, Trash2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getLegalPolicy } from '@/utils/policy';

interface LGPDConsentProps {
  onConsentChange: (consented: boolean) => void;
  consented: boolean;
  userType: 'student' | 'parent';
}

const LGPDConsent: React.FC<LGPDConsentProps> = ({ onConsentChange, consented, userType }) => {
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const { t, i18n } = useTranslation();
  const policy = getLegalPolicy(i18n.language);

  const getDataCollectionInfo = () => {
    if (userType === 'student') {
      return {
        purpose: t('legal.dataCollection.student.purpose', 'allow secure sharing of your location with your guardians'),
        data: [
          t('legal.dataCollection.data.fullName', 'Full name'),
          t('legal.dataCollection.data.email', 'Email'),
          t('legal.dataCollection.data.phone', 'Phone (optional)'),
          t('legal.dataCollection.data.gpsLocation', 'GPS location data')
        ],
        retention: t('legal.dataCollection.retention', '24 months after last account activity')
      };
    } else {
      return {
        purpose: t('legal.dataCollection.parent.purpose', 'allow you to monitor the location of students under your responsibility'),
        data: [
          t('legal.dataCollection.data.fullName', 'Full name'),
          t('legal.dataCollection.data.email', 'Email'),
          t('legal.dataCollection.data.phone', 'Phone (optional)'),
          t('legal.dataCollection.data.studentLinks', 'Student linking data')
        ],
        retention: t('legal.dataCollection.retention', '24 months after last account activity')
      };
    }
  };

  const info = getDataCollectionInfo();

  return (
    <div className="space-y-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-start space-x-3">
        <Shield className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <h3 className="font-medium text-gray-900 mb-2">
            {t(`legal.${policy.toLowerCase()}.title`, 
              policy === 'LGPD' ? 'Personal Data Protection (LGPD)' : 'Personal Data Protection (UK GDPR)'
            )}
          </h3>
          
          <div className="text-sm text-gray-700 space-y-2 mb-4">
            <p>
              <strong>{t('legal.purpose', 'Purpose')}:</strong> {t('legal.purposeDescription', 'We collect your data to {{purpose}}.', { purpose: info.purpose })}
            </p>
            
            <div>
              <strong>{t('legal.dataCollected', 'Data collected')}:</strong>
              <ul className="list-disc list-inside ml-2 mt-1">
                {info.data.map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
            
            <p>
              <strong>{t('legal.legalBasis', 'Legal basis')}:</strong> {t(`legal.${policy.toLowerCase()}.legalBasis`, 
                policy === 'LGPD' ? 'Consent (Art. 7, I of LGPD)' : 'Consent (Art. 6(1)(a) of UK GDPR)'
              )}
            </p>
            
            <p>
              <strong>{t('legal.retention', 'Retention')}:</strong> {info.retention}
            </p>
            
            <div className="bg-white p-3 rounded border-l-4 border-blue-400">
              <p className="text-sm font-medium text-blue-800 mb-1">{t('legal.yourRights', 'Your rights')}:</p>
              <div className="flex flex-wrap gap-2 text-xs">
                <span className="flex items-center gap-1 bg-blue-100 px-2 py-1 rounded">
                  <Eye className="h-3 w-3" />
                  {t('legal.rights.access', 'Access data')}
                </span>
                <span className="flex items-center gap-1 bg-blue-100 px-2 py-1 rounded">
                  <Download className="h-3 w-3" />
                  {t('legal.rights.export', 'Export data')}
                </span>
                <span className="flex items-center gap-1 bg-blue-100 px-2 py-1 rounded">
                  <Trash2 className="h-3 w-3" />
                  {t('legal.rights.delete', 'Delete data')}
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="lgpd-consent"
              checked={consented}
              onCheckedChange={(checked) => onConsentChange(!!checked)}
            />
            <label 
              htmlFor="lgpd-consent" 
              className="text-sm text-gray-700 cursor-pointer flex-1"
            >
              {t('legal.consentText', 'I agree with the processing of my personal data as described and authorize location sharing when necessary.')} 
              <Dialog open={showPrivacyPolicy} onOpenChange={setShowPrivacyPolicy}>
                <DialogTrigger asChild>
                  <Button variant="link" className="p-0 h-auto text-blue-600 underline">
                    {t('legal.viewFullPolicy', 'View full policy')}
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh]">
                  <DialogHeader>
                    <DialogTitle>{t('legal.privacyPolicy.title', 'Privacy Policy - EduConnect')}</DialogTitle>
                    <DialogDescription>
                      {t('legal.privacyPolicy.description', 'Detailed information about personal data processing')}
                    </DialogDescription>
                  </DialogHeader>
                  <ScrollArea className="h-96">
                    <div className="space-y-4 text-sm">
                      <section>
                        <h4 className="font-semibold mb-2">{t('legal.privacyPolicy.controller.title', '1. Data Controller Identification')}</h4>
                        <p>{t('legal.privacyPolicy.controller.name', 'EduConnect - Family Location Platform')}</p>
                        <p>{t('legal.privacyPolicy.controller.contact', 'Privacy contact: <EMAIL>')}</p>
                      </section>
                      
                      <section>
                        <h4 className="font-semibold mb-2">{t('legal.privacyPolicy.dataCollected.title', '2. Data Collected')}</h4>
                        <div>{t('legal.privacyPolicy.dataCollected.intro', 'We collect only the data necessary for platform operation:')}</div>
                        <ul className="list-disc list-inside ml-4">
                          <li>{t('legal.privacyPolicy.dataCollected.identification', 'Identification data: name, email')}</li>
                          <li>{t('legal.privacyPolicy.dataCollected.contact', 'Contact data: phone (optional)')}</li>
                          <li>{t('legal.privacyPolicy.dataCollected.location', 'Location data: GPS coordinates when shared')}</li>
                          <li>{t('legal.privacyPolicy.dataCollected.navigation', 'Navigation data: access logs for security')}</li>
                        </ul>
                      </section>
                      
                      <section>
                        <h4 className="font-semibold mb-2">{t('legal.privacyPolicy.purposes.title', '3. Processing Purposes')}</h4>
                        <ul className="list-disc list-inside ml-4">
                          <li>{t('legal.privacyPolicy.purposes.authentication', 'Authentication and access control')}</li>
                          <li>{t('legal.privacyPolicy.purposes.locationSharing', 'Secure location sharing')}</li>
                          <li>{t('legal.privacyPolicy.purposes.communication', 'Communication about security and updates')}</li>
                          <li>{t('legal.privacyPolicy.purposes.improvement', 'User experience improvement')}</li>
                        </ul>
                      </section>
                      
                      <section>
                        <h4 className="font-semibold mb-2">{t('legal.privacyPolicy.sharing.title', '4. Data Sharing')}</h4>
                        <div>{t('legal.privacyPolicy.sharing.intro', 'Your data is shared only:')}</div>
                        <ul className="list-disc list-inside ml-4">
                          <li>{t('legal.privacyPolicy.sharing.authorized', 'With authorized guardians/students')}</li>
                          <li>{t('legal.privacyPolicy.sharing.legal', 'To comply with legal obligations')}</li>
                          <li>{t('legal.privacyPolicy.sharing.noSale', 'We never sell your data to third parties')}</li>
                        </ul>
                      </section>
                      
                      <section>
                        <h4 className="font-semibold mb-2">{t('legal.privacyPolicy.rights.title', '5. Your Rights')}</h4>
                        <div>{t('legal.privacyPolicy.rights.intro', 'You can at any time:')}</div>
                        <ul className="list-disc list-inside ml-4">
                          <li>{t('legal.privacyPolicy.rights.access', 'Access your personal data')}</li>
                          <li>{t('legal.privacyPolicy.rights.correct', 'Correct incorrect information')}</li>
                          <li>{t('legal.privacyPolicy.rights.delete', 'Request account deletion')}</li>
                          <li>{t('legal.privacyPolicy.rights.export', 'Export your data')}</li>
                          <li>{t('legal.privacyPolicy.rights.revoke', 'Revoke consent')}</li>
                        </ul>
                      </section>
                      
                      <section>
                        <h4 className="font-semibold mb-2">{t('legal.privacyPolicy.security.title', '6. Security')}</h4>
                        <p>{t('legal.privacyPolicy.security.description', 'We implement technical and organizational measures to protect your data against unauthorized access, loss or leakage.')}</p>
                      </section>
                      
                      <section>
                        <h4 className="font-semibold mb-2">{t('legal.privacyPolicy.contact.title', '7. Contact')}</h4>
                        <p>{t('legal.privacyPolicy.contact.intro', 'To exercise your rights or clarify privacy questions:')}</p>
                        <p>{t('legal.privacyPolicy.contact.email', 'Email: <EMAIL>')}</p>
                        <p>{t('legal.privacyPolicy.contact.response', 'Response within 15 business days')}</p>
                      </section>
                    </div>
                  </ScrollArea>
                </DialogContent>
              </Dialog>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LGPDConsent;
