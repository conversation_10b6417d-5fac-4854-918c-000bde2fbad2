# Banco de Dados – Pro<PERSON><PERSON> “Reno” (`rsvjnndhbyyxktbczlnk`)

## 1. Visão Geral do Projeto
| Atributo | Valor |
|----------|-------|
| ID do Projeto | `rsvjnndhbyyxktbczlnk` |
| Nome        | Reno |
| Organização | `eadcljarbdqclulzxedr` |
| Região      | eu-west-2 |
| Status      | ACTIVE_HEALTHY |
| PostgreSQL  | 15.8 (release channel ga) |
| Criado em   | 2025-03-31 08:29:39 UTC |
| Atualizado em | 26-Jun-2025 08:52 BST |

---

## 2. Extensões PostgreSQL Instaladas
| Extensão | Versão | Schema (se diferente de public) | Descrição resumida |
|----------|--------|---------------------------------|--------------------|
| pgsodium          | 3.1.8  | `pgsodium`  | Criptografia libsodium |
| uuid-ossp         | 1.1    | `extensions`| Geração de UUIDs |
| timescaledb       | 2.16.1 | — | Time-series |
| pg_trgm           | 1.6    | — | Índices trigrama |
| pg_net            | 0.14.0 | — | HTTP assíncrono |
| index_advisor     | 0.2.0  | — | Recomendação de índices |
| tablefunc, file_fdw, pg_cron, unaccent, … *(+ outras 25)* |

---

## 3. Histórico de Migrações
Total de ≈ 60 migrações aplicadas (2024-04 → 2025-06). Principais marcos:

| Versão | Nome / Descrição curta |
|--------|------------------------|
| 20240424000000 | fix_is_valid_phone_search_path |
| 20250522030321 | fix_get_student_locations_with_names |
| 20250526071645 | add_created_at_to_locations |
| 20250601035323 | 83872b10-b5a9-4214-aadb-78dd609a99ee |
| 20250617083252 | list_all_policies |
| 20250623030216 | create_trigger_set_timestamp |
| 20250623041011 | 20240320000000_add_student_permissions |
*(lista completa disponível via `supabase migrations list`)*

---

## 4. Inventário de Tabelas (schema `public`)

### 4.1  profiles
| Coluna | Tipo | Nulo | Default / Constraints |
|--------|------|------|-----------------------|
| id            | int4    | N | `nextval('profiles_id_seq')` (PK) |
| user_id       | uuid    | S | FK → `auth.users.id` |
| full_name     | text    | N | — |
| phone         | varchar | S | Regex `^\\+[1-9][0-9]{7,14}$` |
| user_type     | text    | N | — |
| created_at    | timestamptz | N | `now()` |
| updated_at    | timestamptz | N | trigger `set_timestamp` |

Relacionamentos:
• FK `user_id` → `auth.users(id)`

---

### 4.2  locations
| Coluna | Tipo | Nulo | Descrição |
|--------|------|------|-----------|
| id          | uuid         | N | PK |
| student_id  | uuid         | N | FK → `students.id` |
| lat, lng    | numeric(9,6) | N | Coordenadas |
| accuracy_m  | numeric(5,2) | S | — |
| timestamp   | timestamptz  | N | Horário da amostra |
| created_at  | timestamptz  | N | Trigger `set_timestamp` |

Índices: BTREE (pk), GIST (geolocalização), `idx_locations_student_time`.

RLS habilitado ✔️.

---

### 4.*  Outras Tabelas Principais
| Tabela | Notas rápidas |
|--------|---------------|
| guardians                 | Dados dos responsáveis |
| students                  | Perfil de estudante |
| geofences                 | Geometrias POLYGON |
| time_ranges               | Janelas de tempo |
| student_permissions       | Permissões por horário/geofence |
| location_history          | Histórico comprimido (TimescaleDB) |
| audit_logs                | Auditoria de mutações |
| Tabelas ponte             | `student_permission_*` |

---

## 5. Políticas RLS Ativas (seleção)
| Tabela | Policy | Comando | Regra (simplificada) |
|--------|--------|---------|----------------------|
| locations | select_own_locations | SELECT | `guardian_students` lookup |
| guardians | authenticated_select_guardians | SELECT | `auth.role()='authenticated'` |
| profiles  | authenticated_select_profiles  | SELECT | idem |
| auth.users | authenticated_select_auth_users | SELECT | colunas públicas |

---

## 6. Funções & Triggers
| Tipo | Nome | Finalidade |
|------|------|------------|
| Trigger | set_timestamp | Atualiza `created_at` / `updated_at` |
| RPC     | get_student_locations_with_names_v3 | Última posição + nome |
| RPC     | list_all_policies | Lista RLS |
| Trigger | audit_logs_trigger | Auditoria |

---

## 7. Observações Operacionais
1. **RLS** em todas as tabelas sensíveis.
2. **Indexação**: Índices críticos presentes.
3. **TimescaleDB** para `location_history`.
4. **Criptografia**: `pgsodium` disponível.
5. **Auditoria**: Trigger `audit_logs_trigger`.
6. **Edge Functions**: Catálogo não recuperado automaticamente – ver repositório ou CLI.

---

## 8. Próximos Passos Recomendados
1. Revisar políticas RLS.
2. Rodar `supabase advisor`.
3. Documentar retention/compression TimescaleDB.
4. Versionar novas migrações conforme fluxo.

---

Documento gerado em 25 Jun 2025 10:36 BST.


estas duas tabelas tem a mesma funcionalide?  guardians → guardian_profiles elas esta sendo usadas intercabilvemente pela semelhanca de nomes de modo confuso pelo frontend de modo que seria recomendavel deletar a "guardian" e manter a "guardian_profiles"? substuindo a "guardian" no frontend por "guardian_profiles"? 