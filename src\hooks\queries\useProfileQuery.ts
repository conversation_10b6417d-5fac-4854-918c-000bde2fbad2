
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';

interface Profile {
  id: string | number;
  full_name: string;
  email: string;
  phone: string;
  user_type: string;
  cpf: string;
  birth_date?: string;
  created_at: string;
  updated_at: string;
}

interface ProfileInput {
  full_name: string;
  email: string;
  phone: string;
  user_type: string;
  cpf: string;
  birth_date?: string;
}

export const useProfileQuery = () => {
  const { user, refreshUser } = useUser();
  const { toast } = useToast();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  // Query para buscar perfil com timeout e fallbacks
  const {
    data: profile,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['profile', user?.id],
    queryFn: async (): Promise<Profile | null> => {
      if (!user?.id) {
        console.log('[useProfileQuery] No user ID available');
        return null;
      }

      console.log('[useProfileQuery] Fetching profile for user:', user.id);
      
      // Timeout para evitar loading infinito
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile query timeout')), 10000);
      });

      try {
        const queryPromise = supabase
          .from('profiles')
          .select('*')
          .eq('user_id', user.id)
          .single();

        const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

        if (error && error.code !== 'PGRST116') {
          console.error('[useProfileQuery] Error fetching profile:', error);
          // Retornar perfil baseado nos dados do usuário como fallback
          return {
            id: user.id,
            full_name: user.user_metadata?.full_name || '',
            email: user.email || '',
            phone: user.user_metadata?.phone || '',
            user_type: user.user_metadata?.user_type || '',
            cpf: user.user_metadata?.cpf || '',
            birth_date: user.user_metadata?.birth_date || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
        }

        console.log('[useProfileQuery] Profile data fetched:', data);
        return data;
      } catch (timeoutError) {
        console.warn('[useProfileQuery] Query timeout, using fallback data');
        // Fallback para dados do usuário
        return {
          id: user.id,
          full_name: user.user_metadata?.full_name || '',
          email: user.email || '',
          phone: user.user_metadata?.phone || '',
          user_type: user.user_metadata?.user_type || '',
          cpf: user.user_metadata?.cpf || '',
          birth_date: user.user_metadata?.birth_date || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }
    },
    enabled: !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
    retryDelay: 1000,
  });

  // Mutation para atualizar perfil
  const updateProfileMutation = useMutation({
    mutationFn: async (profileData: ProfileInput): Promise<boolean> => {
      console.log('[useProfileQuery] Updating profile with data:', profileData);
      
      if (!user) {
        console.error('[useProfileQuery] No user available for profile update');
        throw new Error('No user available');
      }

      // Validar campos obrigatórios
      if (!profileData.full_name || !profileData.email || !profileData.user_type) {
        console.error('[useProfileQuery] Missing required fields');
        throw new Error('Campos obrigatórios não informados');
      }

      // Validar e formatar telefone
      let formattedPhone = null;
      if (profileData.phone?.trim()) {
        const phoneValue = profileData.phone.trim();
        if (!phoneValue.startsWith('+')) {
          const cleanPhone = phoneValue.replace(/\D/g, '');
          if (cleanPhone.length >= 10 && cleanPhone.length <= 11) {
            formattedPhone = `+55${cleanPhone}`;
          }
        } else {
          if (/^\+[1-9][0-9]{7,14}$/.test(phoneValue)) {
            formattedPhone = phoneValue;
          }
        }
      }

      const updateData = {
        full_name: profileData.full_name.trim(),
        email: profileData.email.trim(),
        phone: formattedPhone,
        user_type: profileData.user_type,
        cpf: profileData.cpf?.trim() || '',
        birth_date: profileData.birth_date || null
      };

      console.log('[useProfileQuery] Final update data:', updateData);

      // Atualizar via upsert direto na tabela profiles
      const { error } = await supabase
        .from('profiles')
        .upsert(updateData)
        .eq('user_id', user.id);

      if (error) {
        console.error('[useProfileQuery] RPC error:', error);
        throw error;
      }

      console.log('[useProfileQuery] Profile updated successfully');
      return true;
    },
    onSuccess: async () => {
      await refreshUser();
      toast({
        title: t('profile.updateSuccessTitle'),
        description: t('profile.updateSuccessDescription')
      });
      // Invalidar e refetch do perfil
      queryClient.invalidateQueries({ queryKey: ['profile', user?.id] });
    },
    onError: (error: any) => {
      console.error('[useProfileQuery] Mutation error:', error);
      toast({
        variant: 'destructive',
        title: t('profile.updateErrorTitle'),
        description: error.message || t('profile.updateErrorDescription')
      });
    },
  });

  return {
    profile,
    isLoading,
    error: error?.message || null,
    updateProfile: updateProfileMutation.mutate,
    isUpdating: updateProfileMutation.isPending,
    refetch
  };
};
