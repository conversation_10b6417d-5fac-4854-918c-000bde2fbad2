import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  console.log('[CREATE_STUDENT_COMPLETE] 🚀 Iniciando fluxo COMPLETO baseado em RPC direto');
  console.log('[CREATE_STUDENT_COMPLETE] 📋 Request method:', req.method);
  console.log('[CREATE_STUDENT_COMPLETE] 📋 Request headers:', Object.fromEntries(req.headers.entries()));

  if (req.method === 'OPTIONS') {
    console.log('[CREATE_STUDENT_COMPLETE] ✅ Handling CORS preflight');
    return new Response(null, { headers: corsHeaders, status: 204 });
  }

  try {
    // PASSO 1: Configuração do Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Configuração do Supabase ausente');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('[CREATE_STUDENT_COMPLETE] ✅ Cliente Supabase configurado');

    // PASSO 2: Parse dos dados de entrada
    const requestBody = await req.json();
    console.log('[CREATE_STUDENT_COMPLETE] 📝 Request body completo:', requestBody);
    
    const {
      student_name,
      student_email,
      student_cpf,
      student_phone,
      guardian_id,
      guardian_email,
      guardian_name
    } = requestBody;

    // Validação detalhada dos dados de entrada
    console.log('[CREATE_STUDENT_COMPLETE] 🔍 Validando dados de entrada...');
    if (!student_name || !student_email || !student_cpf || !guardian_id) {
      const missingFields = [];
      if (!student_name) missingFields.push('student_name');
      if (!student_email) missingFields.push('student_email');
      if (!student_cpf) missingFields.push('student_cpf');
      if (!guardian_id) missingFields.push('guardian_id');
      
      console.error('[CREATE_STUDENT_COMPLETE] ❌ Campos obrigatórios ausentes:', missingFields);
      throw new Error(`Campos obrigatórios ausentes: ${missingFields.join(', ')}`);
    }

    console.log('[CREATE_STUDENT_COMPLETE] 📝 Dados validados:', {
      student_name,
      student_email,
      student_cpf: student_cpf?.substring(0, 3) + '***',
      guardian_id: guardian_id?.substring(0, 8) + '***',
      has_phone: !!student_phone,
      has_guardian_email: !!guardian_email,
      has_guardian_name: !!guardian_name
    });

    // PASSO 3: Chamar RPC direto para validação e preparação
    console.log('[CREATE_STUDENT_COMPLETE] 🔄 Chamando RPC create_student_account_direct...');
    console.log('[CREATE_STUDENT_COMPLETE] 📋 Parâmetros RPC:', {
      p_student_name: student_name,
      p_student_email: student_email,
      p_student_cpf: student_cpf?.substring(0, 3) + '***',
      p_guardian_id: guardian_id?.substring(0, 8) + '***',
      p_student_phone: student_phone || 'null'
    });
    
    const { data: rpcResult, error: rpcError } = await supabase.rpc('create_student_account_direct', {
      p_student_name: student_name,
      p_student_email: student_email,
      p_student_cpf: student_cpf,
      p_guardian_id: guardian_id,
      p_student_phone: student_phone || null
    });

    console.log('[CREATE_STUDENT_COMPLETE] 📊 RPC Response:', {
      success: !rpcError && rpcResult?.[0]?.success,
      data: rpcResult,
      error: rpcError,
      hasResult: !!rpcResult,
      resultLength: rpcResult?.length || 0
    });

    if (rpcError || !rpcResult?.[0]?.success) {
      console.error('[CREATE_STUDENT_COMPLETE] ❌ RPC falhou:');
      console.error('[CREATE_STUDENT_COMPLETE] ❌ RPC Error:', rpcError);
      console.error('[CREATE_STUDENT_COMPLETE] ❌ RPC Result:', rpcResult);
      
      const errorMessage = rpcError?.message || rpcResult?.[0]?.message || 'Erro no RPC de validação';
      console.error('[CREATE_STUDENT_COMPLETE] ❌ Error Message:', errorMessage);
      throw new Error(errorMessage);
    }

    const { student_id, temp_password, activation_token, message } = rpcResult[0];
    
    // Se foi apenas vinculação de estudante existente
    if (message.includes('vinculado')) {
      console.log('[CREATE_STUDENT_COMPLETE] ✅ Estudante existente vinculado');
      return new Response(
        JSON.stringify({
          success: true,
          message: message,
          student_id: student_id,
          type: 'existing_student_linked'
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
          status: 200 
        }
      );
    }

    // PASSO 4: Criar usuário na tabela auth.users usando Admin API
    console.log('[CREATE_STUDENT_COMPLETE] 🔑 Criando usuário auth com ID:', student_id);
    console.log('[CREATE_STUDENT_COMPLETE] 🔑 Dados para criação:', {
      email: student_email.trim(),
      has_password: !!temp_password,
      password_length: temp_password?.length || 0,
      user_metadata_keys: ['full_name', 'user_type', 'created_by']
    });

    const { data: newUser, error: createUserError } = await supabase.auth.admin.createUser({
      email: student_email.trim(),
      password: temp_password,
      email_confirm: true,
      user_metadata: {
        full_name: student_name.trim(),
        user_type: 'student',
        created_by: 'guardian_invitation'
      }
    });

    console.log('[CREATE_STUDENT_COMPLETE] 📊 CreateUser Response:', {
      success: !createUserError && !!newUser.user,
      user_id: newUser?.user?.id,
      user_email: newUser?.user?.email,
      error: createUserError
    });

    if (createUserError || !newUser.user) {
      console.error('[CREATE_STUDENT_COMPLETE] ❌ Erro ao criar usuário auth:');
      console.error('[CREATE_STUDENT_COMPLETE] ❌ CreateUser Error:', createUserError);
      console.error('[CREATE_STUDENT_COMPLETE] ❌ CreateUser Data:', newUser);
      throw new Error(`Erro ao criar usuário: ${createUserError?.message}`);
    }

    console.log('[CREATE_STUDENT_COMPLETE] ✅ Usuário auth criado:', newUser.user.id);

    // PASSO 5: Criar perfil do estudante usando RPC para evitar problemas de RLS
    console.log('[CREATE_STUDENT_COMPLETE] 📝 Criando perfil via RPC...');
    const { data: profileResult, error: profileError } = await supabase.rpc('create_profile_from_edge_function', {
      p_user_id: newUser.user.id,
      p_full_name: student_name.trim(),
      p_user_type: 'student',
      p_cpf: student_cpf.replace(/\D/g, ''),
      p_email: student_email.trim(),
      p_phone: student_phone?.trim() || null,
      p_account_creation_method: 'guardian_invitation_complete',
      p_created_by_user_id: guardian_id,
      p_requires_password_change: true,
      p_activation_token: activation_token,
      p_activation_expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      p_status: 'pending_activation',
      p_registration_status: 'pending'
    });

    console.log('[CREATE_STUDENT_COMPLETE] 📊 Profile creation result:', profileResult);

    if (profileError || !profileResult?.[0]?.success) {
      console.error('[CREATE_STUDENT_COMPLETE] ❌ Erro ao criar perfil:', profileError || profileResult?.[0]?.message);
      // Cleanup: remover usuário auth criado
      await supabase.auth.admin.deleteUser(newUser.user.id);
      throw new Error(`Erro ao criar perfil: ${profileError?.message || profileResult?.[0]?.message || 'Unknown error'}`);
    }

    console.log('[CREATE_STUDENT_COMPLETE] ✅ Perfil criado com sucesso');

    // PASSO 6: Criar relacionamento guardian-student
    const { error: relationshipError } = await supabase
      .from('student_guardian_relationships')
      .insert({
        student_id: newUser.user.id,
        guardian_id: guardian_id,
        relationship_type: 'parent',
        is_primary: true
      });

    if (relationshipError) {
      console.error('[CREATE_STUDENT_COMPLETE] ❌ Erro ao criar relacionamento:', relationshipError);
      // Cleanup completo
      await supabase.auth.admin.deleteUser(newUser.user.id);
      throw new Error(`Erro ao criar relacionamento: ${relationshipError.message}`);
    }

    // PASSO 7: Atualizar status da invitation
    await supabase
      .from('family_invitations')
      .update({ status: 'auth_created' })
      .eq('guardian_id', guardian_id)
      .eq('student_email', student_email)
      .eq('invitation_type', 'direct_rpc_creation');

    // PASSO 8: Enviar email com credenciais diretamente
    console.log('[CREATE_STUDENT_COMPLETE] 📧 Enviando email com credenciais...');
    
    try {
      const resendApiKey = Deno.env.get('RESEND_API_KEY');
      if (!resendApiKey) {
        console.warn('[CREATE_STUDENT_COMPLETE] ⚠️ RESEND_API_KEY não configurado - prosseguindo sem email');
        // Não falha o processo, apenas pula o envio de email
        console.log('[CREATE_STUDENT_COMPLETE] ✅ Conta criada (email será enviado quando RESEND_API_KEY for configurado)');
        
        // PASSO 9: Log final e retorno (sem email)
        await supabase
          .from('auth_logs')
          .insert({
            event_type: 'student_account_created_complete_no_email',
            user_id: newUser.user.id,
            metadata: {
              method: 'complete_rpc_flow',
              guardian_id: guardian_id,
              student_email: student_email,
              email_skipped: 'RESEND_API_KEY not configured'
            },
            occurred_at: new Date().toISOString()
          });

        return new Response(
          JSON.stringify({
            success: true,
            message: `Conta de ${student_name} criada com sucesso (email pendente)`,
            student_id: newUser.user.id,
            activation_token: activation_token,
            email_sent: false,
            type: 'new_student_created'
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
            status: 200 
          }
        );
      }

      const activationUrl = `${Deno.env.get('SITE_URL') || 'https://sistema-monitore.com.br'}/activate-account?token=${activation_token}`;

      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">EduConnect</h1>
            <p style="color: #6b7280; margin: 5px 0;">Sistema de Monitoramento Educacional</p>
          </div>
          
          <h2 style="color: #2563eb;">✅ Sua conta foi criada com sucesso!</h2>
          
          <p>Olá <strong>${student_name}</strong>,</p>
          
          <p>Seu responsável <strong>${guardian_name || 'Responsável'}</strong> ${guardian_email ? `(${guardian_email})` : ''} criou uma conta para você no sistema EduConnect.</p>
          
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
            <h3 style="margin-top: 0; color: #1e293b;">🔑 Suas credenciais de acesso:</h3>
            <p style="margin: 10px 0;"><strong>Email:</strong> ${student_email}</p>
            <p style="margin: 10px 0;"><strong>Senha temporária:</strong> <code style="background: #e2e8f0; padding: 4px 8px; border-radius: 4px; font-family: monospace; font-size: 16px;">${temp_password}</code></p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${activationUrl}" 
               style="background-color: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;">
              🚀 Ativar Minha Conta
            </a>
          </div>
          
          <div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #f59e0b;">
            <p style="margin: 0; color: #92400e;">
              <strong>⚠️ Importante:</strong> Você precisará alterar sua senha no primeiro login. 
              Este link expira em 7 dias.
            </p>
          </div>
          
          <p style="color: #6b7280; font-size: 14px;">Se você não solicitou esta conta, pode ignorar este email com segurança.</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          <div style="text-align: center;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
              <strong>EduConnect</strong><br>
              Sistema de Monitoramento Educacional Seguro
            </p>
          </div>
        </div>
      `;

      const emailResponse = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${resendApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: 'EduConnect <<EMAIL>>',
          to: [student_email],
          subject: `✅ EduConnect - Sua conta foi criada com sucesso!`,
          html: emailHtml,
          reply_to: guardian_email || '<EMAIL>',
          tags: [
            { name: 'category', value: 'student_credentials' },
            { name: 'user_type', value: 'student' }
          ]
        }),
      });

      const emailResult = await emailResponse.json();

      if (!emailResponse.ok) {
        console.error('[CREATE_STUDENT_COMPLETE] ❌ Erro no envio do email:', emailResult);
        throw new Error(`Erro no envio do email: ${emailResult.message || 'API error'}`);
      }

      console.log('[CREATE_STUDENT_COMPLETE] ✅ Email enviado com sucesso:', emailResult.id);
    } catch (emailErr) {
      console.warn('[CREATE_STUDENT_COMPLETE] ⚠️ Email falhou (não crítico):', emailErr);
    }

    // PASSO 9: Log final e retorno
    await supabase
      .from('auth_logs')
      .insert({
        event_type: 'student_account_created_complete',
        user_id: newUser.user.id,
        metadata: {
          method: 'complete_rpc_flow',
          guardian_id: guardian_id,
          student_email: student_email
        },
        occurred_at: new Date().toISOString()
      });

    console.log('[CREATE_STUDENT_COMPLETE] 🎉 PROCESSO COMPLETO! Estudante criado:', newUser.user.id);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Conta de ${student_name} criada com sucesso`,
        student_id: newUser.user.id,
        activation_token: activation_token,
        email_sent: true,
        type: 'new_student_created'
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
        status: 200 
      }
    );

  } catch (error) {
    console.error('[CREATE_STUDENT_COMPLETE] 💥 ERRO FATAL:');
    console.error('[CREATE_STUDENT_COMPLETE] 💥 Error Object:', error);
    console.error('[CREATE_STUDENT_COMPLETE] 💥 Error Type:', typeof error);
    console.error('[CREATE_STUDENT_COMPLETE] 💥 Error Constructor:', error?.constructor?.name);
    
    if (error instanceof Error) {
      console.error('[CREATE_STUDENT_COMPLETE] 💥 Error Message:', error.message);
      console.error('[CREATE_STUDENT_COMPLETE] 💥 Error Stack:', error.stack);
    }
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    const errorDetails = error instanceof Error ? error.stack : String(error);
    
    console.error('[CREATE_STUDENT_COMPLETE] 💥 Final Error Message:', errorMessage);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date().toISOString(),
        function: 'create-student-complete'
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
        status: 500 
      }
    );
  }
});