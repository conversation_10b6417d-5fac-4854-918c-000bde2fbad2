import React, { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';

(mapboxgl as any).accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN as string;

const MAP_STYLE = 'mapbox://styles/mapbox/standard';

export const MapboxBackground: React.FC = () => {
  const mapContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mapContainer.current) return;
    const map = new mapboxgl.Map({
      container: mapContainer.current,
      style: MAP_STYLE,
      center: [-46.6388, -23.5489], // São Paulo, ajuste se necessário
      zoom: 10,
      interactive: false,
      attributionControl: false, // Desabilitar para evitar sobreposição de textos
    });

    // Desabilitar todas as interações
    map.scrollZoom.disable();
    map.boxZoom.disable();
    map.dragRotate.disable();
    map.dragPan.disable();
    map.keyboard.disable();
    map.doubleClickZoom.disable();
    map.touchZoomRotate.disable();

    return () => map.remove();
  }, []);

  return (
    <div
      ref={mapContainer}
      className="fixed inset-0 w-full h-full -z-10"
      style={{
        pointerEvents: 'none',
      }}
      aria-hidden="true"
    />
  );
}; 