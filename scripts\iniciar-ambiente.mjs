#!/usr/bin/env node
/**
 * Script de Inicialização de Ambiente para Locate-Family-Connect
 * 
 * Configura o ambiente de desenvolvimento após um encerramento diário,
 * seguindo o Anti-Break Protocol para garantir segurança e integridade.
 * 
 * Uso:
 *   node scripts/iniciar-ambiente.mjs
 *   node scripts/iniciar-ambiente.mjs --restore  # Restaura backups mais recentes
 *   node scripts/iniciar-ambiente.mjs --check    # Apenas verifica o ambiente
 * 
 * @version 1.0.0
 * @created 2025-06-03
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

// Configuração
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const PROJECT_ROOT = path.resolve(__dirname, '..');
const BACKUP_DIR = path.join(PROJECT_ROOT, '.backup');
const PLAN_FILE = path.join(PROJECT_ROOT, 'docs', 'plano-execucao.md');

// Conversão de funções callback para Promises
const execAsync = promisify(exec);
const readFileAsync = promisify(fs.readFile);
const readdirAsync = promisify(fs.readdir);
const copyFileAsync = promisify(fs.copyFile);
const existsAsync = promisify(fs.exists);

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  gray: '\x1b[90m',
  bold: '\x1b[1m'
};

// Argumentos da linha de comando
const args = process.argv.slice(2);
const shouldRestore = args.includes('--restore');
const checkOnly = args.includes('--check');

// Interface para input do usuário
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Prompt com Promise
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => resolve(answer));
  });
}

/**
 * Função principal
 */
async function main() {
  try {
    console.log(`
${colors.bold}${colors.cyan}=== INICIALIZAÇÃO DE AMBIENTE DE DESENVOLVIMENTO ===
${colors.reset}${colors.gray}Data: ${new Date().toLocaleString()}
${colors.reset}${colors.gray}Projeto: Locate-Family-Connect
${colors.reset}`);

    // 1. Verificar integridade do ambiente
    await checkEnvironmentIntegrity();
    
    // 2. Restaurar configurações, se necessário
    if (shouldRestore) {
      await restoreBackups();
    }
    
    // 3. Limpar recursos não utilizados
    if (!checkOnly) {
      await cleanupResources();
    }
    
    // 4. Iniciar serviços
    if (!checkOnly) {
      await startServices();
    }
    
    // 5. Mostrar plano do dia
    await showDailyPlan();

    if (checkOnly) {
      console.log(`\n${colors.green}${colors.bold}✅ VERIFICAÇÃO DE AMBIENTE CONCLUÍDA${colors.reset}`);
    } else {
      console.log(`\n${colors.green}${colors.bold}✅ AMBIENTE DE DESENVOLVIMENTO INICIALIZADO${colors.reset}`);
    }
    
    console.log(`${colors.cyan}Para encerrar o ambiente ao final do dia, execute: ${colors.bold}node scripts/encerramento-diario.mjs${colors.reset}`);
    
  } catch (error) {
    console.error(`\n${colors.red}${colors.bold}❌ ERRO NA INICIALIZAÇÃO: ${error.message}${colors.reset}`);
    process.exit(1);
  } finally {
    rl.close();
  }
}

/**
 * Verifica a integridade do ambiente de desenvolvimento
 */
async function checkEnvironmentIntegrity() {
  console.log(`\n${colors.bold}🔍 Verificando integridade do ambiente...${colors.reset}`);
  
  try {
    // 1. Verificar estado do Docker
    console.log(`${colors.gray}Verificando Docker...${colors.reset}`);
    
    try {
      const { stdout: dockerVersion } = await execAsync('docker --version');
      console.log(`${colors.green}✓ Docker instalado: ${dockerVersion.trim()}${colors.reset}`);
      
      const { stdout: dockerDf } = await execAsync('docker system df');
      console.log(`${colors.cyan}Recursos Docker:${colors.reset}`);
      console.log(`${colors.gray}${dockerDf}${colors.reset}`);
    } catch (error) {
      console.error(`${colors.red}✗ Docker não está disponível: ${error.message}${colors.reset}`);
      throw new Error('Docker não está disponível. Certifique-se de que está instalado e em execução.');
    }
    
    // 2. Verificar arquivos essenciais
    console.log(`\n${colors.gray}Verificando arquivos essenciais...${colors.reset}`);
    
    const essentialFiles = [
      '.env',
      'docker-compose.yml',
      'package.json'
    ];
    
    for (const file of essentialFiles) {
      const filePath = path.join(PROJECT_ROOT, file);
      if (await existsAsync(filePath)) {
        console.log(`${colors.green}✓ ${file} presente${colors.reset}`);
      } else {
        console.log(`${colors.red}✗ ${file} ausente${colors.reset}`);
        
        if (file === '.env') {
          // Verificar se existe backup do .env
          const envBackups = await findBackups('.env.backup');
          
          if (envBackups.length > 0) {
            console.log(`${colors.yellow}⚠️ .env ausente, mas backups encontrados:${colors.reset}`);
            envBackups.forEach((backup, index) => {
              console.log(`${colors.gray}  ${index + 1}. ${backup}${colors.reset}`);
            });
            
            const answer = await prompt(`${colors.yellow}Restaurar o backup mais recente? (s/N): ${colors.reset}`);
            
            if (answer.toLowerCase() === 's') {
              const mostRecent = envBackups[0];
              await copyFileAsync(
                path.join(BACKUP_DIR, mostRecent),
                path.join(PROJECT_ROOT, '.env')
              );
              console.log(`${colors.green}✓ .env restaurado a partir de ${mostRecent}${colors.reset}`);
            } else {
              console.log(`${colors.yellow}⚠️ Continuando sem arquivo .env${colors.reset}`);
            }
          } else {
            console.log(`${colors.yellow}⚠️ .env ausente e nenhum backup encontrado. Crie-o manualmente a partir de .env.example${colors.reset}`);
          }
        }
      }
    }
    
    // 3. Verificar dependências npm
    console.log(`\n${colors.gray}Verificando dependências npm...${colors.reset}`);
    
    try {
      await execAsync('npm --version');
      
      const packageLockExists = await existsAsync(path.join(PROJECT_ROOT, 'package-lock.json'));
      if (packageLockExists) {
        console.log(`${colors.green}✓ package-lock.json presente${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ package-lock.json ausente. npm install pode ser necessário${colors.reset}`);
      }
      
      // Verificar se node_modules existe
      const nodeModulesExists = await existsAsync(path.join(PROJECT_ROOT, 'node_modules'));
      if (nodeModulesExists) {
        console.log(`${colors.green}✓ node_modules presente${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ node_modules ausente. Executando npm install...${colors.reset}`);
        
        if (!checkOnly) {
          const answer = await prompt(`${colors.yellow}Executar npm install agora? (s/N): ${colors.reset}`);
          
          if (answer.toLowerCase() === 's') {
            console.log(`${colors.gray}Instalando dependências...${colors.reset}`);
            await execAsync('npm install', { cwd: PROJECT_ROOT });
            console.log(`${colors.green}✓ Dependências instaladas${colors.reset}`);
          } else {
            console.log(`${colors.yellow}⚠️ Continuando sem instalar dependências${colors.reset}`);
          }
        }
      }
    } catch (error) {
      console.error(`${colors.red}✗ npm não está disponível: ${error.message}${colors.reset}`);
    }
    
    // 4. Verificar estado do Git
    console.log(`\n${colors.gray}Verificando estado do Git...${colors.reset}`);
    
    try {
      const { stdout: gitStatus } = await execAsync('git status', { cwd: PROJECT_ROOT });
      console.log(`${colors.cyan}Estado do Git:${colors.reset}`);
      console.log(`${colors.gray}${gitStatus}${colors.reset}`);
      
      // Verificar se há commits não enviados
      if (gitStatus.includes('Your branch is ahead of')) {
        console.log(`${colors.yellow}⚠️ Existem commits locais não enviados para o repositório remoto${colors.reset}`);
        
        if (!checkOnly) {
          const answer = await prompt(`${colors.yellow}Enviar commits para o repositório remoto? (s/N): ${colors.reset}`);
          
          if (answer.toLowerCase() === 's') {
            console.log(`${colors.gray}Enviando commits...${colors.reset}`);
            await execAsync('git push', { cwd: PROJECT_ROOT });
            console.log(`${colors.green}✓ Commits enviados${colors.reset}`);
          } else {
            console.log(`${colors.yellow}⚠️ Continuando sem enviar commits${colors.reset}`);
          }
        }
      }
    } catch (error) {
      console.error(`${colors.red}✗ Erro ao verificar estado do Git: ${error.message}${colors.reset}`);
    }
  } catch (error) {
    if (error.message.includes('Docker não está disponível')) {
      throw error;
    }
    console.error(`${colors.red}✗ Erro na verificação de integridade: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️ Continuando com o protocolo...${colors.reset}`);
  }
}

/**
 * Encontra backups com um determinado prefixo, ordenados do mais recente para o mais antigo
 */
async function findBackups(prefix) {
  try {
    const backupFiles = await readdirAsync(BACKUP_DIR);
    return backupFiles
      .filter(file => file.startsWith(prefix))
      .sort()
      .reverse();
  } catch (error) {
    console.error(`${colors.red}Erro ao procurar backups: ${error.message}${colors.reset}`);
    return [];
  }
}

/**
 * Restaura backups mais recentes
 */
async function restoreBackups() {
  console.log(`\n${colors.bold}🔄 Restaurando backups...${colors.reset}`);
  
  try {
    // Lista de arquivos para restaurar
    const filesToRestore = [
      { prefix: '.env.backup', target: '.env' },
      { prefix: '.gitignore.backup', target: '.gitignore' },
      { prefix: 'docker-compose.backup', target: 'docker-compose.yml' }
    ];
    
    // Verificar se docker-compose-redis.yml existe e adicioná-lo à lista
    const redisComposeBackups = await findBackups('docker-compose-redis.backup');
    if (redisComposeBackups.length > 0) {
      filesToRestore.push({
        prefix: 'docker-compose-redis.backup',
        target: 'docker-compose-redis.yml'
      });
    }
    
    // Restaurar cada arquivo
    for (const file of filesToRestore) {
      const backups = await findBackups(file.prefix);
      
      if (backups.length > 0) {
        const mostRecent = backups[0];
        
        const targetPath = path.join(PROJECT_ROOT, file.target);
        const backupPath = path.join(BACKUP_DIR, mostRecent);
        
        // Verificar se o arquivo de destino já existe
        if (await existsAsync(targetPath)) {
          const answer = await prompt(`${colors.yellow}${file.target} já existe. Sobrescrever com ${mostRecent}? (s/N): ${colors.reset}`);
          
          if (answer.toLowerCase() !== 's') {
            console.log(`${colors.gray}Restauração de ${file.target} ignorada${colors.reset}`);
            continue;
          }
        }
        
        await copyFileAsync(backupPath, targetPath);
        console.log(`${colors.green}✓ ${file.target} restaurado a partir de ${mostRecent}${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ Nenhum backup encontrado para ${file.target}${colors.reset}`);
      }
    }
    
    console.log(`${colors.green}✓ Restauração de backups concluída${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}✗ Erro ao restaurar backups: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️ Continuando com o protocolo...${colors.reset}`);
  }
}

/**
 * Limpa recursos não utilizados
 */
async function cleanupResources() {
  console.log(`\n${colors.bold}🧹 Limpando recursos não utilizados...${colors.reset}`);
  
  try {
    // Verificar se há contêineres parados
    const { stdout: stoppedContainers } = await execAsync('docker ps -a -q -f status=exited -f name=locate-family');
    
    if (stoppedContainers.trim()) {
      console.log(`${colors.yellow}⚠️ Contêineres parados encontrados${colors.reset}`);
      
      const answer = await prompt(`${colors.yellow}Remover contêineres parados? (s/N): ${colors.reset}`);
      
      if (answer.toLowerCase() === 's') {
        await execAsync(`docker rm ${stoppedContainers}`);
        console.log(`${colors.green}✓ Contêineres parados removidos${colors.reset}`);
      } else {
        console.log(`${colors.gray}Remoção de contêineres ignorada${colors.reset}`);
      }
    } else {
      console.log(`${colors.green}✓ Nenhum contêiner parado encontrado${colors.reset}`);
    }
    
    // Verificar se há volumes Docker pendentes
    console.log(`${colors.gray}Verificando volumes Docker não utilizados...${colors.reset}`);
    
    const { stdout: volumesList } = await execAsync('docker volume ls -q -f dangling=true');
    
    if (volumesList.trim()) {
      console.log(`${colors.yellow}⚠️ Volumes não utilizados encontrados:${colors.reset}`);
      const { stdout: volumesDetails } = await execAsync('docker volume ls -f dangling=true');
      console.log(`${colors.gray}${volumesDetails}${colors.reset}`);
      
      const answer = await prompt(`${colors.yellow}Remover volumes não utilizados? (s/N): ${colors.reset}`);
      
      if (answer.toLowerCase() === 's') {
        await execAsync('docker volume prune -f');
        console.log(`${colors.green}✓ Volumes não utilizados removidos${colors.reset}`);
      } else {
        console.log(`${colors.gray}Remoção de volumes ignorada${colors.reset}`);
      }
    } else {
      console.log(`${colors.green}✓ Nenhum volume não utilizado encontrado${colors.reset}`);
    }
    
    // Limpar cache npm
    console.log(`${colors.gray}Verificando cache npm...${colors.reset}`);
    
    const answer = await prompt(`${colors.yellow}Limpar cache temporário do npm? (s/N): ${colors.reset}`);
    
    if (answer.toLowerCase() === 's') {
      await execAsync('npm cache clean --force');
      console.log(`${colors.green}✓ Cache npm limpo${colors.reset}`);
    } else {
      console.log(`${colors.gray}Limpeza de cache npm ignorada${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}✗ Erro na limpeza de recursos: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️ Continuando com o protocolo...${colors.reset}`);
  }
}

/**
 * Inicia serviços necessários
 */
async function startServices() {
  console.log(`\n${colors.bold}🚀 Iniciando serviços...${colors.reset}`);
  
  try {
    // Verificar se o arquivo docker-compose-redis.yml existe
    const redisComposeExists = await existsAsync(path.join(PROJECT_ROOT, 'docker-compose-redis.yml'));
    
    // Perguntar se deseja iniciar o Redis
    if (redisComposeExists) {
      const startRedis = await prompt(`${colors.yellow}Iniciar serviço Redis? (s/N): ${colors.reset}`);
      
      if (startRedis.toLowerCase() === 's') {
        console.log(`${colors.gray}Iniciando Redis...${colors.reset}`);
        await execAsync('docker compose -f docker-compose-redis.yml up -d', { cwd: PROJECT_ROOT });
        console.log(`${colors.green}✓ Redis iniciado${colors.reset}`);
      } else {
        console.log(`${colors.gray}Inicialização do Redis ignorada${colors.reset}`);
      }
    }
    
    // Perguntar se deseja iniciar os serviços principais
    const startMain = await prompt(`${colors.yellow}Iniciar serviços principais? (s/N): ${colors.reset}`);
    
    if (startMain.toLowerCase() === 's') {
      console.log(`${colors.gray}Iniciando serviços principais...${colors.reset}`);
      await execAsync('docker compose -f docker-compose.yml up -d', { cwd: PROJECT_ROOT });
      console.log(`${colors.green}✓ Serviços principais iniciados${colors.reset}`);
    } else {
      console.log(`${colors.gray}Inicialização dos serviços principais ignorada${colors.reset}`);
    }
    
    // Verificar contêineres em execução
    const { stdout: runningContainers } = await execAsync('docker ps');
    console.log(`${colors.cyan}Contêineres em execução:${colors.reset}`);
    console.log(`${colors.gray}${runningContainers}${colors.reset}`);
    
    // Verificar se o usuário deseja iniciar o servidor de desenvolvimento
    const startDev = await prompt(`${colors.yellow}Iniciar servidor de desenvolvimento npm? (s/N): ${colors.reset}`);
    
    if (startDev.toLowerCase() === 's') {
      console.log(`${colors.yellow}⚠️ O servidor de desenvolvimento será iniciado em um novo processo.${colors.reset}`);
      console.log(`${colors.yellow}⚠️ Você precisará abrir um novo terminal para continuar trabalhando.${colors.reset}`);
      
      const confirmDev = await prompt(`${colors.yellow}Confirmar inicialização do servidor? (s/N): ${colors.reset}`);
      
      if (confirmDev.toLowerCase() === 's') {
        // Iniciar em modo não-bloqueante
        console.log(`${colors.gray}Iniciando servidor de desenvolvimento...${colors.reset}`);
        await execAsync('start cmd.exe /k npm run dev', { cwd: PROJECT_ROOT, shell: true });
        console.log(`${colors.green}✓ Servidor de desenvolvimento iniciado em uma nova janela${colors.reset}`);
      } else {
        console.log(`${colors.gray}Inicialização do servidor de desenvolvimento ignorada${colors.reset}`);
      }
    } else {
      console.log(`${colors.gray}Inicialização do servidor de desenvolvimento ignorada${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}✗ Erro ao iniciar serviços: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️ Continuando com o protocolo...${colors.reset}`);
  }
}

/**
 * Mostra o plano do dia a partir do arquivo de plano
 */
async function showDailyPlan() {
  console.log(`\n${colors.bold}📋 Plano do dia...${colors.reset}`);
  
  try {
    if (await existsAsync(PLAN_FILE)) {
      const planContent = await readFileAsync(PLAN_FILE, 'utf8');
      
      // Encontrar a seção mais recente
      const today = new Date().toISOString().split('T')[0];
      const regex = new RegExp(`## Plano para ${today}[\\s\\S]*?(?=##|$)`, 'g');
      
      const todayPlan = planContent.match(regex);
      
      if (todayPlan && todayPlan.length > 0) {
        console.log(`${colors.cyan}Tarefas planejadas para hoje:${colors.reset}`);
        console.log(`${colors.gray}${todayPlan[0].trim()}${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️ Nenhum plano encontrado para hoje (${today})${colors.reset}`);
        
        // Encontrar a seção mais recente disponível
        const sectionRegex = /## Plano para (\d{4}-\d{2}-\d{2})[\s\S]*?(?=##|$)/g;
        const sections = [];
        let match;
        
        while ((match = sectionRegex.exec(planContent)) !== null) {
          sections.push({
            date: match[1],
            content: match[0].trim()
          });
        }
        
        if (sections.length > 0) {
          // Ordenar por data, mais recente primeiro
          sections.sort((a, b) => b.date.localeCompare(a.date));
          
          console.log(`${colors.cyan}Plano mais recente disponível (${sections[0].date}):${colors.reset}`);
          console.log(`${colors.gray}${sections[0].content}${colors.reset}`);
        } else {
          console.log(`${colors.yellow}⚠️ Nenhum plano encontrado no arquivo${colors.reset}`);
        }
      }
    } else {
      console.log(`${colors.yellow}⚠️ Arquivo de plano não encontrado (${PLAN_FILE})${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}✗ Erro ao mostrar plano: ${error.message}${colors.reset}`);
  }
}

// Executar função principal
main().catch(error => {
  console.error(`${colors.red}${colors.bold}❌ FALHA CRÍTICA: ${error.message}${colors.reset}`);
  process.exit(1);
});
