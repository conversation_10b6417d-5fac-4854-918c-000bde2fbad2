
/* iOS-specific map styles - VERSÃO DEFINITIVA OTIMIZADA */

/* Safe area support for iOS devices - MELHORADO */
.ios-map-container {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  
  /* NOVO: Forçar uso de toda área disponível */
  width: 100vw !important;
  height: 100vh !important;
}

/* Full screen map for iOS - OTIMIZADO */
.ios-map-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 1000;
  
  /* Forçar aceleração de hardware */
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

/* iOS touch optimizations - SIGNIFICATIVAMENTE MELHORADO */
.ios-map-touch-optimized {
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
  touch-action: pan-x pan-y !important;
  -webkit-overflow-scrolling: touch !important;
  -webkit-transform: translate3d(0, 0, 0) !important;
  transform: translate3d(0, 0, 0) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
  -webkit-perspective: 1000px !important;
  perspective: 1000px !important;
}

/* iOS-style buttons - MELHORADO */
.ios-map-button, .ios-button {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 12px !important;
  margin: 4px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease !important;
  min-width: 44px !important;
  min-height: 44px !important;
  
  /* Forçar aceleração de hardware */
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.ios-map-button:active, .ios-button:active {
  transform: scale(0.95) translate3d(0, 0, 0) !important;
  background: rgba(255, 255, 255, 0.85) !important;
}

/* CORREÇÃO CRÍTICA: Responsive map container para iOS - ALTURAS AUMENTADAS */
.ios-responsive-map {
  width: 100% !important;
  height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 60px) !important;
  min-height: 500px !important; /* AUMENTADO significativamente */
  max-height: none !important;
}

/* CORREÇÃO PRINCIPAL: iOS viewport fixes - ALTURAS MUITO AUMENTADAS */
@media screen and (max-width: 768px) {
  .map-container-ios {
    width: 100vw !important;
    height: 75vh !important; /* AUMENTADO de 65vh para 75vh */
    min-height: 550px !important; /* AUMENTADO de 450px para 550px */
    margin: 0 !important;
    border-radius: 0 !important;
    position: relative !important;
    overflow: hidden !important;
    
    /* Forçar aceleração de hardware */
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  
  .map-container-ios .mapboxgl-canvas {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    
    /* Otimizações iOS específicas */
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  
  .map-container-ios .mapboxgl-map {
    width: 100% !important;
    height: 100% !important;
  }
}

/* iPhone specific adjustments - ALTURAS MUITO AUMENTADAS */
@media screen and (max-width: 428px) {
  .map-container-ios {
    height: 70vh !important; /* AUMENTADO de 60vh para 70vh */
    min-height: 500px !important; /* AUMENTADO de 400px para 500px */
  }
  
  .ios-responsive-map {
    height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 50px) !important;
    min-height: 450px !important; /* AUMENTADO */
  }
}

/* Landscape mode adjustments for iOS - ALTURAS MUITO AUMENTADAS */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .map-container-ios {
    height: 85vh !important; /* AUMENTADO de 80vh para 85vh */
    min-height: 400px !important; /* AUMENTADO de 300px para 400px */
  }
}

/* NOVA SEÇÃO: Full-screen map support for iOS */
@supports (-webkit-touch-callout: none) {
  /* Full-screen map container for iOS mobile */
  .ios-fullscreen-map-container {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 1000 !important;
    background: white;
    overflow: hidden;
  }

  /* Full-screen map section */
  .ios-fullscreen-map-section {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Full-screen map wrapper */
  .ios-fullscreen-map {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
  }

  /* Handle safe areas for devices with notch */
  @media screen and (min-width: 375px) and (min-height: 812px) {
    .ios-fullscreen-map-container {
      padding-top: env(safe-area-inset-top) !important;
      padding-bottom: env(safe-area-inset-bottom) !important;
      padding-left: env(safe-area-inset-left) !important;
      padding-right: env(safe-area-inset-right) !important;
    }
  }

  /* Optimize touch interactions for full-screen */
  .ios-fullscreen-map * {
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    touch-action: pan-x pan-y !important;
  }

  /* Ensure map canvas takes full space */
  .ios-fullscreen-map .mapboxgl-canvas-container,
  .ios-fullscreen-map .mapboxgl-canvas {
    width: 100% !important;
    height: 100% !important;
  }

  /* Hide unnecessary UI elements in full-screen */
  .ios-fullscreen-map-container .mapboxgl-ctrl-top-left,
  .ios-fullscreen-map-container .mapboxgl-ctrl-top-right,
  .ios-fullscreen-map-container .mapboxgl-ctrl-bottom-left,
  .ios-fullscreen-map-container .mapboxgl-ctrl-bottom-right {
    display: none !important;
  }

  /* Legacy support for older iOS versions */
  .map-container-ios {
    height: calc(100vh - 120px) !important;
    min-height: 500px !important;
  }

  @media screen and (orientation: landscape) {
    .map-container-ios {
      height: calc(100vh - 80px) !important;
      min-height: 350px !important;
    }
  }
}

/* Performance optimizations for iOS - MELHORADO */
.ios-optimized {
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
  -webkit-perspective: 1000px !important;
  perspective: 1000px !important;
  will-change: transform !important;
}

/* NOVA: Garantir que o mapa ocupe toda a área disponível */
.map-responsive {
  width: 100% !important;
  height: 100% !important;
  min-height: 500px !important; /* AUMENTADO */
}

.map-highlight {
  width: 100% !important;
  min-height: 550px !important; /* AUMENTADO significativamente */
}

/* NOVA: Smooth scrolling for iOS */
.ios-smooth-scroll {
  -webkit-overflow-scrolling: touch !important;
  scroll-behavior: smooth !important;
}

/* NOVA: Prevent zoom on double tap */
.ios-map-no-zoom {
  touch-action: manipulation !important;
  -ms-touch-action: manipulation !important;
}

/* NOVA: Mapbox controls positioning for iOS */
.mapboxgl-ctrl-top-right {
  top: calc(env(safe-area-inset-top, 0px) + 15px) !important;
  right: 15px !important;
}

.mapboxgl-ctrl-bottom-left {
  bottom: calc(env(safe-area-inset-bottom, 0px) + 15px) !important;
  left: 15px !important;
}

/* NOVA: Garantir que controles não sejam bloqueados por safe areas */
.ios-map-controls {
  position: absolute;
  top: calc(env(safe-area-inset-top, 10px) + 15px);
  right: 15px;
  z-index: 10;
}

/* DEPURAÇÃO: Log visual para iOS (remover em produção) */
@media screen and (max-width: 768px) {
  .map-container-ios::before {
    content: "iOS Map: " attr(data-height);
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
  }
}
