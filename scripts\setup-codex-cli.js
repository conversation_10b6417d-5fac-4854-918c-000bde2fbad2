/**
 * Script de Instalação e Configuração do Codex CLI
 * Para o projeto Locate-Family-Connect
 */

const { spawnSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Diretório do projeto
const PROJECT_ROOT = path.resolve(__dirname, '..');
// Diretório de configuração do Codex
const CODEX_CONFIG_DIR = path.join(os.homedir(), '.codex');

console.log('🚀 Iniciando configuração do Codex CLI para Locate-Family-Connect...');

// 1. Verificar se o Codex CLI já está instalado
console.log('📦 Verificando instalação do Codex CLI...');
const checkCodex = spawnSync('npx', ['-c', 'codex --version'], { shell: true, encoding: 'utf8' });

if (checkCodex.status !== 0) {
  console.log('🔧 Instalando Codex CLI globalmente...');
  const installResult = spawnSync('npm', ['install', '-g', '@openai/codex'], { 
    shell: true, 
    stdio: 'inherit' 
  });
  
  if (installResult.status !== 0) {
    console.error('❌ Falha ao instalar Codex CLI. Por favor, instale manualmente:');
    console.error('npm install -g @openai/codex');
    process.exit(1);
  }
  console.log('✅ Codex CLI instalado com sucesso!');
} else {
  console.log(`✅ Codex CLI já instalado: ${checkCodex.stdout.trim()}`);
}

// 2. Configurar diretório do Codex
console.log('📁 Configurando diretório do Codex...');
if (!fs.existsSync(CODEX_CONFIG_DIR)) {
  fs.mkdirSync(CODEX_CONFIG_DIR, { recursive: true });
  console.log(`✅ Diretório criado: ${CODEX_CONFIG_DIR}`);
} else {
  console.log(`📂 Diretório já existe: ${CODEX_CONFIG_DIR}`);
}

// 3. Criar configuração do Codex
const configFile = path.join(CODEX_CONFIG_DIR, 'config.json');
const configContent = {
  "model": "o4-mini",
  "approvalMode": "suggest",
  "provider": "openai",
  "notify": true,
  "history": {
    "maxSize": 1000,
    "saveHistory": true
  }
};

console.log('⚙️ Criando arquivo de configuração do Codex...');
fs.writeFileSync(configFile, JSON.stringify(configContent, null, 2));
console.log(`✅ Configuração salva em: ${configFile}`);

// 4. Copiar AGENTS.md para configuração global do Codex
const projectAgentsFile = path.join(PROJECT_ROOT, 'AGENTS.md');
const codexAgentsFile = path.join(CODEX_CONFIG_DIR, 'AGENTS.md');

console.log('📋 Configurando instruções do agente...');
if (fs.existsSync(projectAgentsFile)) {
  fs.copyFileSync(projectAgentsFile, codexAgentsFile);
  console.log(`✅ Instruções copiadas para: ${codexAgentsFile}`);
} else {
  console.log('⚠️ Arquivo AGENTS.md não encontrado no projeto. Pulando esta etapa.');
}

// 5. Verificar variável de ambiente OPENAI_API_KEY
console.log('🔑 Verificando variável de ambiente OPENAI_API_KEY...');
if (!process.env.OPENAI_API_KEY) {
  console.log(`
⚠️ API Key não encontrada no ambiente.
Por favor, configure sua chave da API OpenAI:

export OPENAI_API_KEY="sua-chave-api"

Ou adicione-a ao arquivo .env na raiz do projeto.
`);
}

// 6. Criar script auxiliar para execução do Codex
const codexRunnerFile = path.join(PROJECT_ROOT, 'codex-run.js');
const codexRunnerContent = `#!/usr/bin/env node
/**
 * Script auxiliar para execução do Codex CLI
 * Locate-Family-Connect
 */
require('dotenv').config();
const { spawn } = require('child_process');
const path = require('path');

// Argumentos da linha de comando (remove node e o script)
const args = process.argv.slice(2);

// Executa o Codex CLI
const codex = spawn('codex', args, {
  stdio: 'inherit',
  env: process.env,
  shell: true
});

codex.on('close', (code) => {
  process.exit(code);
});
`;

console.log('📝 Criando script auxiliar para Codex...');
fs.writeFileSync(codexRunnerFile, codexRunnerContent);
fs.chmodSync(codexRunnerFile, '755');
console.log(`✅ Script criado em: ${codexRunnerFile}`);

// 7. Adicionar script ao package.json
console.log('📦 Atualizando package.json...');
const packageJsonFile = path.join(PROJECT_ROOT, 'package.json');
if (fs.existsSync(packageJsonFile)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonFile, 'utf8'));
  
  if (!packageJson.scripts) {
    packageJson.scripts = {};
  }
  
  packageJson.scripts.codex = 'node codex-run.js';
  
  fs.writeFileSync(packageJsonFile, JSON.stringify(packageJson, null, 2));
  console.log('✅ Script adicionado ao package.json: npm run codex');
} else {
  console.log('⚠️ package.json não encontrado. Pulando atualização.');
}

console.log(`
🎉 Configuração do Codex CLI concluída!

Para usar o Codex no seu projeto:
- Comando simples: npm run codex
- Com prompt: npm run codex -- "explicar o sistema de autenticação PKCE"
- Modo interativo: npm run codex

Para mais informações, consulte: 
https://github.com/openai/codex
`);
