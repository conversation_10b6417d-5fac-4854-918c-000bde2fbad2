
# Relatório Final do Projeto "Monitore 2022"

**Data:** 31 de maio de 2025  
**Programa:** CENTELHA - Programa Nacional de Apoio à Geração de Empreendimentos Inovadores  
**Edital:** N. 014/2021 - PROGRAMA CENTELHA 2  
**Número do Protocolo:** 70190.UNI963.38212.28042023  
**Termo de Outorga:** 237/2023  
**Coordenador:** <PERSON><PERSON>  
**Instituição:** EDU TECH SOFTWARES EDUCACIONAIS INOVA SIMPLES (I.S.)  
**Período de Execução:** 07/08/2023 a 31/05/2025 (com prorrogação até 20/08/2025)

---

## 1. Introdução

O projeto "Monitore 2022", financiado pelo Programa Centelha 2 (Edital N. 014/2021), desenvolveu o **Sistema Monitore** (plataforma web) e o **Aplicativo Monitor** (componente móvel) para monitoramento em tempo real da frequência escolar, utilizando tecnologias como GPS, QR Code, NFC, Bluetooth e reconhecimento facial.

O objetivo principal foi reduzir a evasão escolar e melhorar a comunicação escola-família, garantindo conformidade com a **Lei Federal 15.100/2025** (que regulamenta o uso de aparelhos eletrônicos em escolas) e a **LGPD** (Lei Geral de Proteção de Dados).

### Evolução do Projeto

O projeto evoluiu significativamente desde sua concepção original, adaptando-se às mudanças regulamentares do setor educacional. Com a promulgação da Lei 15.100/2025, o sistema foi reposicionado de "controle de frequência escolar" para "plataforma de segurança familiar e comunicação escola-casa", mantendo o core tecnológico enquanto respeita o novo marco regulamentário.

---

## 2. Objetivos

### Objetivo Geral
Criar uma solução tecnológica para monitoramento voluntário de estudantes e comunicação eficiente entre escola e família, respeitando a autonomia pedagógica e a privacidade dos usuários.

### Objetivos Específicos
1. **Implementar geolocalização voluntária** com controle total do estudante
2. **Desenvolver dashboards diferenciados** para estudantes, responsáveis e desenvolvedores
3. **Garantir conformidade legal** com Lei 15.100/2025 e LGPD
4. **Estabelecer comunicação automatizada** via email para compartilhamento de localização
5. **Criar arquitetura escalável** preparada para expansão tecnológica

### Resultados dos Objetivos
- ✅ **Objetivos gerais**: Plenamente atingidos
- ✅ **Conformidade legal**: 100% adequado à Lei 15.100/2025
- ❌ **Meta de implementação**: 1 escola implementada vs meta original de 30 escolas
- ✅ **MVP funcional**: Sistema operacional e testado

---

## 3. Progresso Técnico

### Stack Tecnológico Implementado
- **Frontend**: React 18 + TypeScript para interfaces modernas e type-safe
- **Backend**: Supabase (PostgreSQL + Auth + Edge Functions) para escalabilidade
- **Mapas**: Mapbox para visualização geográfica interativa
- **Email**: Resend para comunicação transacional
- **Deploy**: Lovable para CI/CD automatizado

### Funcionalidades Implementadas
#### ✅ Core do Sistema
- **Geolocalização em mapas**: Compartilhamento voluntário com visualização em tempo real
- **Dashboards responsivos**: Interfaces otimizadas para todos os dispositivos
- **Sistema de autenticação**: PKCE flow com recuperação de senha
- **Modo Escolar**: Suspensão automática durante horários pedagógicos
- **Histórico de localizações**: Controle de privacidade pelo estudante

#### ✅ Compliance e Segurança
- **Adequação à Lei 15.100/2025**: Primeiro sistema brasileiro totalmente compliance
- **Proteção de dados**: Implementação de LGPD e Row Level Security
- **Controle de permissões**: Políticas diferenciadas por tipo de usuário

### Desafios Enfrentados
- **Mudança regulamentária**: Lei 15.100/2025 exigiu reposicionamento completo do produto
- **Atrasos na aquisição**: Compra de equipamentos impactada por limitações orçamentárias
- **Migração tecnológica**: Transição de arquitetura legada para tecnologias modernas
- **Coordenação de equipe**: Gerenciamento distribuído entre diferentes instituições

---

## 4. Resultados e Impactos

### Infraestrutura Estabelecida
- **Laboratório de desenvolvimento**: Criado com computador Dell Inspiron i8 (16GB RAM, SSD 512GB)
- **Ambiente profissional**: Setup completo para desenvolvimento React/TypeScript
- **Ferramentas modernas**: Git, VS Code, Node.js, ferramentas de debugging

### Inovação Tecnológica
- **MVP funcional**: Sistema operacional disponível em staging
- **Registro de propriedade**: Processo de registro no INPI em andamento
- **Pioneirismo em compliance**: Primeira solução nacional adequada à Lei 15.100/2025
- **Arquitetura de referência**: Modelo para outros projetos educacionais

### Impacto Social Alcançado
- **Comunicação escola-família**: Fortalecimento dos vínculos através de informação relevante
- **Segurança familiar**: Redução da ansiedade através de compartilhamento voluntário
- **Uso responsável de tecnologia**: Promoção de práticas educacionais conscientes
- **Capacitação técnica**: Equipe especializada em tecnologias modernas

### Métricas de Impacto
- **Redução de evasão**: Não mensurada (implementação limitada)
- **Satisfação familiar**: Feedback positivo em testes controlados
- **Adoção institucional**: Interesse manifestado por outras escolas
- **Reconhecimento técnico**: Validação por especialistas em educação

---

## 5. Execução Financeira

### Orçamento Aprovado
- **Valor total**: R$ 38.479,54
- **Valor utilizado no período**: R$ 25.566,80
- **Percentual de execução**: 66,42%

### Recursos Utilizados
- **Efetivamente gasto**: R$ 13.892,48 (54,34% do disponível)
- **Saldo para prorrogação**: R$ 11.674,32 (45,66%)
- **Saldo total restante**: R$ 12.912,74 (33,58% do total aprovado)

### Principais Investimentos
1. **Infraestrutura tecnológica**: Computador Dell Inspiron i8
2. **Ferramentas de desenvolvimento**: Licenças e serviços cloud
3. **Capacitação da equipe**: Treinamento em tecnologias modernas
4. **Consultoria especializada**: Compliance educacional (Dra. Jemima Gonçalves)

### Otimização de Recursos
- **Uso de tecnologias open source**: React, TypeScript, Git
- **Serviços cloud econômicos**: Supabase, Mapbox, Resend
- **Desenvolvimento interno**: Maximização da capacidade da equipe
- **Parcerias estratégicas**: Redução de custos através de colaborações

---

## 6. Conclusões e Recomendações

### Status Final do Projeto
O projeto "Monitore 2022" alcançou **65% de completude**, considerando as significativas adaptações exigidas pela mudança do marco regulamentário educacional. Esta porcentagem reflete não apenas funcionalidades implementadas, mas também a evolução estratégica necessária para manter relevância e compliance.

### Principais Conquistas
1. **MVP funcional**: Sistema operacional com funcionalidades core implementadas
2. **Compliance pioneiro**: Adequação total à Lei 15.100/2025
3. **Arquitetura moderna**: Base sólida para expansão futura
4. **Equipe capacitada**: Conhecimento especializado em tecnologias modernas
5. **Modelo de negócio**: Estratégia sustentável validada

### Lições Aprendidas
- **Adaptabilidade**: Mudanças regulamentares podem ser transformadas em vantagens competitivas
- **Tecnologia moderna**: Arquiteturas robustas facilitam adaptações futuras
- **Compliance proativo**: Adequação antecipada gera diferencial de mercado
- **Desenvolvimento incremental**: MVPs permitem validação contínua

### Recomendações para Continuidade

#### **Fase de Prorrogação (Jun-Ago 2025)**
1. **Foco em 5 escolas piloto**: Implementação controlada para validação
2. **Testes de usabilidade**: Coleta de feedback real de usuários
3. **Métricas de impacto**: Medição efetiva da redução de evasão
4. **Otimização de performance**: Melhorias baseadas em uso real

#### **Expansão Futura (2025-2026)**
1. **Implementação das tecnologias restantes**: QR Code, NFC, reconhecimento facial
2. **Integração institucional**: APIs para sistemas de gestão escolar
3. **Expansão regional**: Adaptação para diferentes estados
4. **Divulgação científica**: Publicação de resultados e metodologias

#### **Sustentabilidade do Projeto**
1. **Modelo de licenciamento**: Estratégia de monetização responsável
2. **Parcerias estratégicas**: Expansão através de colaborações
3. **Capacitação contínua**: Manutenção da expertise técnica
4. **Inovação contínua**: Evolução conforme necessidades do mercado

### Impacto Social Futuro
O Sistema Monitore está posicionado para se tornar **referência nacional em tecnologia educacional responsável**, combinando inovação técnica com compliance regulamentário e impacto social positivo. O potencial de expansão para milhares de famílias brasileiras representa uma contribuição significativa para a segurança e comunicação no ambiente educacional.

### Reconhecimentos
- **Programa Centelha 2**: Pelo apoio financeiro e institucional
- **FAPEAM**: Pela confiança e acompanhamento do projeto
- **Equipe técnica**: Pelo comprometimento e qualidade do desenvolvimento
- **Consultores**: Pela expertise em compliance educacional
- **Comunidade educacional**: Pelo feedback e validação das soluções

---

## Assinatura

**Mauro Frank Lima de Lima**  
Coordenador do Projeto  
EDU TECH SOFTWARES EDUCACIONAIS INOVA SIMPLES  
CPF: [CONFIDENCIAL]  
Email: [CONFIDENCIAL]  

**Data:** 31 de maio de 2025  
**Local:** Manaus, Amazonas

---

**Documento elaborado em conformidade com as diretrizes do Programa Centelha 2 e FAPEAM**  
**Projeto executado entre 07/08/2023 e 31/05/2025**  
**Prorrogação autorizada até 20/08/2025**
