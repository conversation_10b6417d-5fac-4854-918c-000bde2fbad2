# Frontend Guide - Locate-Family-Connect

## 🌟 Visão Geral da Arquitetura Frontend

O frontend do Locate-Family-Connect é construído utilizando React + TypeScript + Vite, com uma arquitetura de componentes orientada a perfis de usuário (guardian/student/developer).

### 🧩 Stack Tecnológica

- **Base**: React 18 + TypeScript + Vite
- **Estilização**: TailwindCSS + Radix UI
- **Roteamento**: React Router v6
- **Estado Global**: Context API + React Query
- **Mapas**: MapBox API
- **Autenticação**: PKCE (Supabase Auth)

## 📁 Estrutura de Diretórios

```
/src
  /components
    /common         # Componentes compartilhados
    /guardian       # Componentes específicos para guardiões
    /student        # Componentes específicos para estudantes
    /developer      # Componentes específicos para desenvolvedores
    /map            # Componentes relacionados ao mapa
  /contexts         # Context APIs (AuthContext, etc.)
  /hooks            # Custom hooks
  /lib              # Utilitários e configurações
    /supabase.ts    # Cliente Supabase
  /pages            # Páginas principais
  /types            # Definições de tipos
  /utils            # Funções utilitárias
```

## 🔒 Fluxo de Autenticação

O sistema utiliza autenticação PKCE através do Supabase, gerenciada pelo `UnifiedAuthContext.tsx`. O fluxo é baseado em perfis:

1. Login com email/senha ou OTP
2. Verificação do tipo de usuário (guardians, students, developer)
3. Redirecionamento para dashboard correspondente
4. Verificação contínua de token via interceptors

### 📊 Diagrama de Fluxo de Autenticação

```
Login → AuthCheck → UserProfileCheck → RoleBasedDashboard
```

## 📱 Componentes por Perfil

### Guardian
- GuardianDashboard
- StudentLocationMap
- NotificationSettings
- GuardianProfile

### Student
- StudentDashboard
- LocationSharing
- StudentProfile
- EmergencyContacts

## 🚀 Boas Práticas

### Performance
- Memoize componentes com React.memo para renders desnecessários
- Utilizar lazy loading para componentes grandes
- Implementar virtualização para listas longas
- Otimizar re-renders com useCallback e useMemo

### Código
- Limitar arquivos a 300 linhas máximo
- Separar lógica de UI em custom hooks
- Usar tipos estritos (evitar `any`)
- Implementar error boundaries em componentes críticos
- Centralizar strings em arquivos de constantes

### Estado
- Preferir estados locais (useState) quando possível
- Utilizar Context API para estados compartilhados
- Implementar React Query para cache e sincronização com backend

## 📐 Layout e Responsividade

- Mobile-first approach
- Breakpoints: sm:640px, md:768px, lg:1024px, xl:1280px
- Usar Grid e Flexbox para layouts complexos
- Implementar Media Queries para ajustes específicos

## 🧪 Testes

- Componentes críticos testados com Jest e React Testing Library
- E2E com Cypress
- Verificação de acessibilidade (A11Y) integrada aos testes

## 🛑 Anti-Padrões (Evitar)

- Mutação direta do estado
- Props drilling excessivo
- Componentes muito grandes (>300 linhas)
- Lógica de negócio em componentes de UI
- Uso de `any` em tipagens
- Ausência de error handling em operações assíncronas

## 📚 Referências

- [Documentação do React](https://reactjs.org/docs/getting-started.html)
- [Documentação do Supabase para Auth](https://supabase.com/docs/guides/auth)
- [Documentação do MapBox](https://docs.mapbox.com/mapbox-gl-js/api/)
- [Repositório de Componentes](https://github.com/seu-usuario/locate-family-connect/src/components)
