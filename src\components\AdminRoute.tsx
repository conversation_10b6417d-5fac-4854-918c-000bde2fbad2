
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useIsAdmin, useHasAdminPermissions } from '@/hooks/use-developer';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useTranslation } from 'react-i18next';

interface AdminRouteProps {
  children: React.ReactNode;
  requireStrictAdmin?: boolean; // Se true, requer admin; se false, aceita admin ou developer
}

export const AdminRoute: React.FC<AdminRouteProps> = ({ 
  children, 
  requireStrictAdmin = false 
}) => {
  const { user, isLoading } = useUser();
  const isAdmin = useIsAdmin();
  const hasAdminPerms = useHasAdminPermissions();
  const { t } = useTranslation();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin h-8 w-8 border-4 border-blue-600 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Se requer admin estrito, verificar apenas admin
  // Senão, aceitar qualquer usuário com permissões administrativas
  const hasPermission = requireStrictAdmin ? isAdmin : hasAdminPerms;

  if (!hasPermission) {
    return <Navigate to={`/login?message=${encodeURIComponent(t('admin.accessDenied'))}`} replace />;
  }

  return <>{children}</>;
};

export default AdminRoute;
