
import { useRef, useEffect, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import { LocationData } from '@/types/database';
import { useToast } from '@/components/ui/use-toast';
import { mapboxDebugger } from '@/utils/mapboxProductionDebug';

interface UseMapMarkersProps {
  map: mapboxgl.Map | null;
  mapLoaded: boolean;
  locations: LocationData[];
  selectedUserId?: string;
  forceUpdateKey?: number;
}

export function useMapMarkers({
  map,
  mapLoaded,
  locations,
  selectedUserId,
  forceUpdateKey
}: UseMapMarkersProps) {
  const markers = useRef<mapboxgl.Marker[]>([]);
  const { toast } = useToast();
  const retryTimeoutRef = useRef<number | null>(null);

  // Detecta se estamos no dashboard do responsável
  const isParentDashboard = () => {
    return (
      window.location.pathname.includes('parent-dashboard') || 
      window.location.pathname.includes('parent/dashboard') ||
      window.location.pathname.includes('guardian') ||
      document.title.toLowerCase().includes('responsável')
    );
  };

  // Create a safe marker element that works in production builds
  const createSafeMarkerElement = useCallback((location: LocationData, isRecentLocation: boolean) => {
    try {
      // Use a simpler approach for production builds to avoid appendChild issues
      const markerElement = document.createElement('div');
      markerElement.className = 'custom-marker';

      // Apply styles directly without complex nested elements
      const size = isRecentLocation ? '30px' : '20px';
      const backgroundColor = isRecentLocation ? '#ff0000' : '#888';
      const border = isRecentLocation ? '3px solid #ffffff' : '1px solid #ffffff';
      const boxShadow = isRecentLocation ? '0 0 10px rgba(255, 0, 0, 0.7)' : 'none';

      markerElement.style.cssText = `
        width: ${size};
        height: ${size};
        border-radius: 50%;
        background-color: ${backgroundColor};
        border: ${border};
        box-shadow: ${boxShadow};
        position: relative;
        cursor: pointer;
      `;

      // Add accessibility attributes
      markerElement.setAttribute('tabindex', '0');
      markerElement.setAttribute('role', 'img');
      markerElement.setAttribute('aria-label', `${location.user?.full_name || 'Localização atual'}: ${new Date(location.timestamp).toLocaleString()}`);

      // For recent locations, add a simple text indicator instead of complex nested elements
      if (isRecentLocation) {
        markerElement.setAttribute('data-recent', 'true');
        markerElement.style.cssText += `
          &::after {
            content: 'ATUAL';
            position: absolute;
            top: -15px;
            right: -25px;
            background-color: #ff3c00;
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
            white-space: nowrap;
          }
        `;
      }

      return markerElement;
    } catch (error) {
      console.error('[useMapMarkers] Failed to create marker element:', error);
      // Fallback to the simplest possible marker
      const fallbackElement = document.createElement('div');
      fallbackElement.style.cssText = `
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #ff0000;
        border: 2px solid #ffffff;
      `;
      return fallbackElement;
    }
  }, []);

  // Enhanced helper function with fallback marker creation strategy
  const addSingleMarkerSafely = useCallback((location: LocationData, isRecentLocation: boolean, retryCount = 0): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!map) {
        mapboxDebugger.logMarkerAddition(map, null, `Marker addition failed - missing map (attempt ${retryCount + 1})`);
        resolve(false);
        return;
      }

      // Enhanced container readiness check with production debugging
      const mapContainer = map.getContainer();
      const isContainerReady = mapboxDebugger.logContainerReadiness(mapContainer, `Marker addition attempt ${retryCount + 1}`);

      if (!isContainerReady) {
        if (retryCount < 5) { // Increased retry count for production builds
          const timing = mapboxDebugger.getProductionTimingRecommendations();
          const delay = Math.min(timing.markerDelay * Math.pow(1.5, retryCount), 1000);
          console.warn(`[useMapMarkers] Container not ready for marker addition, retrying in ${delay}ms... (attempt ${retryCount + 1})`);
          setTimeout(() => {
            addSingleMarkerSafely(location, isRecentLocation, retryCount + 1).then(resolve);
          }, delay);
          return;
        } else {
          console.error('[useMapMarkers] Container not ready after 5 retries, skipping marker');
          mapboxDebugger.logMarkerAddition(map, null, 'Container not ready after all retries');
          resolve(false);
          return;
        }
      }

      // Create marker with fallback strategy
      let marker: mapboxgl.Marker;
      try {
        // Try custom marker first
        const markerElement = createSafeMarkerElement(location, isRecentLocation);
        marker = new mapboxgl.Marker({
          element: markerElement,
          anchor: 'bottom',
        }).setLngLat([location.longitude, location.latitude]);

        mapboxDebugger.logMarkerAddition(map, marker, `Created custom marker (attempt ${retryCount + 1})`);
      } catch (error) {
        console.warn(`[useMapMarkers] Failed to create custom marker, using default (attempt ${retryCount + 1}):`, error);
        // Fallback to default Mapbox marker
        marker = new mapboxgl.Marker({
          color: isRecentLocation ? '#ff0000' : '#888888',
          scale: isRecentLocation ? 1.2 : 1.0
        }).setLngLat([location.longitude, location.latitude]);

        mapboxDebugger.logMarkerAddition(map, marker, `Created fallback marker (attempt ${retryCount + 1})`);
      }

      // Create popup
      const popupContent = `
        <div style="padding: 5px;">
          <h3 style="font-weight: bold; margin-bottom: 5px; font-size: 16px;">
            ${location.user?.full_name || 'Localização'}
            ${isRecentLocation ? '<span style="background-color: #ff3c00; color: white; padding: 2px 5px; border-radius: 3px; font-size: 10px; margin-left: 5px;">ATUAL</span>' : ''}
          </h3>
          <p style="margin-bottom: 3px; font-size: 14px;">${new Date(location.timestamp).toLocaleString()}</p>
          ${location.address ? `<p style="color: #666; font-size: 12px;">${location.address}</p>` : ''}
          ${isRecentLocation ? '<p style="font-weight: bold; color: #ff3c00; margin-top: 5px; border-top: 1px solid #eee; padding-top: 5px;">LOCALIZAÇÃO MAIS RECENTE</p>' : ''}
        </div>
      `;

      const popup = new mapboxgl.Popup({ offset: 25 }).setHTML(popupContent);
      marker.setPopup(popup);

      // Attempt to add marker with retry logic and debugging
      try {
        mapboxDebugger.logMarkerAddition(map, marker, `Attempting marker addition (attempt ${retryCount + 1})`);
        marker.addTo(map);
        markers.current.push(marker); // Add to tracking array on successful addition
        mapboxDebugger.logMarkerAddition(map, marker, `Marker added successfully (attempt ${retryCount + 1})`);
        resolve(true);
      } catch (error) {
        console.error(`[useMapMarkers] Failed to add marker (attempt ${retryCount + 1}):`, error);
        mapboxDebugger.logMarkerAddition(map, marker, `Marker addition failed with error: ${error}`);

        // Retry marker addition with exponential backoff
        if (retryCount < 3) {
          const timing = mapboxDebugger.getProductionTimingRecommendations();
          const delay = Math.min(timing.retryDelay * Math.pow(2, retryCount), 2000);
          setTimeout(() => {
            addSingleMarkerSafely(location, isRecentLocation, retryCount + 1).then(resolve);
          }, delay);
        } else {
          console.error('[useMapMarkers] Failed to add marker after 3 retries, giving up');
          resolve(false);
        }
      }
    });
  }, [map, createSafeMarkerElement]);

  // Helper function to add markers with enhanced retry logic and debugging
  const addMarkersToMap = useCallback((retryCount = 0) => {
    if (!map || !mapLoaded || !locations || locations.length === 0) {
      console.log('[useMapMarkers] Skipping marker addition:', {
        hasMap: !!map,
        mapLoaded,
        locationCount: locations?.length || 0
      });
      return;
    }

    // Enhanced safety check with production debugging
    const mapContainer = map.getContainer();
    const isContainerFullyReady = mapboxDebugger.logContainerReadiness(mapContainer, `Main marker addition attempt ${retryCount + 1}`);

    if (!isContainerFullyReady) {
      if (retryCount < 5) { // Increased retry count for production timing
        const timing = mapboxDebugger.getProductionTimingRecommendations();
        const delay = Math.min(timing.containerDelay * Math.pow(1.5, retryCount), 1500);
        console.warn(`[useMapMarkers] Map container not fully ready, retrying in ${delay}ms... (attempt ${retryCount + 1})`);

        // Check if we're in a problematic environment and adjust strategy
        if (mapboxDebugger.isProblematicEnvironment()) {
          console.warn('[useMapMarkers] Detected problematic production environment, using extended delays');
        }

        retryTimeoutRef.current = setTimeout(() => addMarkersToMap(retryCount + 1), delay);
        return;
      } else {
        console.error('[useMapMarkers] Map container not ready after 5 retries, skipping marker addition');
        // Export debug logs for analysis
        console.error('[useMapMarkers] Debug logs:', mapboxDebugger.exportDebugLogs().slice(-5));
        return;
      }
    }

    // Clear any existing retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    console.log(`[useMapMarkers] Starting marker addition for ${locations.length} locations (${import.meta.env.PROD ? 'production' : 'development'} mode)`);
    mapboxDebugger.captureDebugInfo(map, mapContainer);
    
    // Clean up previous markers efficiently
    markers.current.forEach(marker => marker.remove());
    markers.current = [];
    
    const shouldShowAllStudents = isParentDashboard() && !selectedUserId;
    const filteredLocations = shouldShowAllStudents 
      ? locations 
      : (selectedUserId 
          ? locations.filter(loc => loc.user_id === selectedUserId)
          : locations);
    
    console.log('[MapView] Filtered locations:', filteredLocations.length);
    
    // Organize locations by user
    const locationsByUser = new Map<string, LocationData[]>();
    
    filteredLocations.forEach(loc => {
      const userId = loc.user_id;
      if (!locationsByUser.has(userId)) {
        locationsByUser.set(userId, []);
      }
      locationsByUser.get(userId)?.push(loc);
    });
    
    // Sort locations
    locationsByUser.forEach((userLocs, userId) => {
      locationsByUser.set(userId, 
        userLocs.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        )
      );
    });
    
    const mostRecentLocations: LocationData[] = [];
    
    locationsByUser.forEach((userLocs) => {
      if (userLocs.length > 0) {
        mostRecentLocations.push(userLocs[0]);
      }
    });
    
    // Add markers using the safe creation strategy with enhanced error recovery
    let successCount = 0;
    let failureCount = 0;
    const totalMarkers = Array.from(locationsByUser.values()).reduce((sum, locs) => sum + locs.length, 0);

    locationsByUser.forEach((userLocs, _userId) => {
      userLocs.forEach((location, index) => {
        const isRecentLocation = index === 0;

        // Use the enhanced safe marker addition with fallback strategy
        addSingleMarkerSafely(location, isRecentLocation).then((success) => {
          if (success) {
            successCount++;
            console.log(`[useMapMarkers] Successfully added marker for ${location.user?.full_name || 'unknown user'} (${successCount}/${totalMarkers})`);
          } else {
            failureCount++;
            console.warn(`[useMapMarkers] Failed to add marker for ${location.user?.full_name || 'unknown user'} after all retries (${failureCount} failures)`);
          }

          // Show user feedback when all markers have been processed
          if (successCount + failureCount === totalMarkers) {
            if (failureCount > 0 && successCount === 0) {
              // All markers failed
              toast({
                title: "Erro ao carregar marcadores",
                description: "Não foi possível carregar os marcadores no mapa. Tente recarregar a página.",
                variant: "destructive",
                duration: 5000
              });
            } else if (failureCount > 0) {
              // Some markers failed
              toast({
                title: "Alguns marcadores não carregaram",
                description: `${successCount} de ${totalMarkers} marcadores foram carregados com sucesso.`,
                variant: "default",
                duration: 3000
              });
            } else if (successCount > 0) {
              // All markers succeeded
              console.log(`[useMapMarkers] All ${successCount} markers loaded successfully`);
            }
          }
        }).catch((error) => {
          failureCount++;
          console.error('[useMapMarkers] Unexpected error in marker addition:', error);

          // Show error feedback if this was the last marker to process
          if (successCount + failureCount === totalMarkers && failureCount === totalMarkers) {
            toast({
              title: "Erro crítico no mapa",
              description: "Falha ao carregar marcadores. Verifique sua conexão e tente novamente.",
              variant: "destructive",
              duration: 5000
            });
          }
        });
      });
    });

    // Apply intelligent zoom
    if (mostRecentLocations.length > 0) {
      const parentDashboard = isParentDashboard();
      const parentZoom = 16;
      const regularZoom = 15;
      const parentPadding = 100;
      const regularPadding = 50;

      if (parentDashboard) {
        if (mostRecentLocations.length === 1) {
          const singleLocation = mostRecentLocations[0];
          map.flyTo({
            center: [singleLocation.longitude, singleLocation.latitude],
            zoom: parentZoom + 2,
            essential: true,
            speed: 0.5
          });
        } else {
          // Check if all locations are in the same region
          let allInSameRegion = true;
          const firstLoc = mostRecentLocations[0];
          
          for (let i = 1; i < mostRecentLocations.length; i++) {
            const loc = mostRecentLocations[i];
            const latDiff = Math.abs(loc.latitude - firstLoc.latitude);
            const lngDiff = Math.abs(loc.longitude - firstLoc.longitude);
            
            if (latDiff > 5 || lngDiff > 5) {
              allInSameRegion = false;
              break;
            }
          }
          
          if (allInSameRegion) {
            const bounds = new mapboxgl.LngLatBounds();
            mostRecentLocations.forEach(loc => {
              bounds.extend([loc.longitude, loc.latitude]);
            });
            
            map.fitBounds(bounds, {
              padding: parentPadding,
              maxZoom: parentZoom
            });
          } else {
            const sortedLocations = [...mostRecentLocations].sort((a, b) => 
              new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
            );
            
            const absoluteLatestLocation = sortedLocations[0];
            map.flyTo({
              center: [absoluteLatestLocation.longitude, absoluteLatestLocation.latitude],
              zoom: parentZoom,
              essential: true,
              speed: 0.5
            });
            
            toast({
              title: "Localização mais recente em foco",
              description: `Mostrando a localização mais recente de ${absoluteLatestLocation.user?.full_name}. Use os botões para ver outras localizações.`,
              duration: 5000
            });
          }
        }
      } else {
        const bounds = new mapboxgl.LngLatBounds();
        locations.forEach(location => {
          bounds.extend([location.longitude, location.latitude]);
        });
        
        map.fitBounds(bounds, {
          padding: regularPadding,
          maxZoom: regularZoom
        });
      }
    }
  }, [map, mapLoaded, locations, selectedUserId, toast]);

  // Update markers when locations change
  useEffect(() => {
    addMarkersToMap();

    // Cleanup function
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    };
  }, [addMarkersToMap, forceUpdateKey]);

  return { markers: markers.current };
}
