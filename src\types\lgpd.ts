
export interface UserData {
  personal_info: {
    id: string;
    email: string;
    full_name: string;
    phone: string;
    user_type: string;
    created_at: string;
  };
  data_collection_consent: {
    granted_at: string;
    purpose: string;
    legal_basis: string;
  };
  location_data?: {
    recent_locations: any[];
    total_shared: number;
  };
  relationships?: {
    guardians: any[];
  };
}

export interface DataSummary {
  personal: {
    name: string;
    email: string;
    phone: string;
    type: string;
  };
}
