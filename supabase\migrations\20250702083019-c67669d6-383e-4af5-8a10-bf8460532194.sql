-- Fix create_student_account_direct RPC to generate invitation_token
-- Migration: Fix missing invitation_token in family_invitations insert

CREATE OR REPLACE FUNCTION public.create_student_account_direct(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_guardian_id UUID,
  p_student_phone TEXT DEFAULT NULL
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT,
  student_id UUID,
  temp_password TEXT,
  activation_token TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_clean_cpf TEXT;
  v_temp_password TEXT;
  v_activation_token TEXT;
  v_invitation_token TEXT;  -- Add invitation token variable
  v_student_user_id UUID;
  v_existing_student_id UUID;
  v_guardian_email TEXT;
  v_guardian_name TEXT;
BEGIN
  -- Validar guardian
  SELECT u.email, COALESCE(p.full_name, u.email)
  INTO v_guardian_email, v_guardian_name
  FROM auth.users u
  LEFT JOIN public.profiles p ON p.user_id = u.id
  WHERE u.id = p_guardian_id;

  IF v_guardian_email IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Guardian não encontrado', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Validar dados básicos
  IF p_student_name IS NULL OR TRIM(p_student_name) = '' THEN
    RETURN QUERY SELECT FALSE, 'Nome do estudante é obrigatório', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  IF p_student_email IS NULL OR p_student_email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
    RETURN QUERY SELECT FALSE, 'Email inválido', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Limpar CPF
  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  IF char_length(v_clean_cpf) <> 11 THEN
    RETURN QUERY SELECT FALSE, 'CPF deve ter 11 dígitos válidos', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Verificar se estudante já existe por CPF
  SELECT user_id INTO v_existing_student_id
  FROM public.profiles
  WHERE regexp_replace(cpf, '[^0-9]', '', 'g') = v_clean_cpf
  AND user_type = 'student';

  IF v_existing_student_id IS NOT NULL THEN
    -- Verificar se já está vinculado
    IF EXISTS (
      SELECT 1 FROM public.student_guardian_relationships 
      WHERE student_id = v_existing_student_id AND guardian_id = p_guardian_id
    ) THEN
      RETURN QUERY SELECT FALSE, 'Estudante já está vinculado à sua conta', NULL::UUID, NULL::TEXT, NULL::TEXT;
      RETURN;
    ELSE
      -- Criar relacionamento com estudante existente
      INSERT INTO public.student_guardian_relationships (
        student_id, guardian_id, relationship_type, is_primary
      ) VALUES (
        v_existing_student_id, p_guardian_id, 'parent', TRUE
      );
      
      RETURN QUERY SELECT TRUE, 'Estudante vinculado com sucesso', v_existing_student_id, NULL::TEXT, NULL::TEXT;
      RETURN;
    END IF;
  END IF;

  -- Verificar se email já existe
  IF EXISTS (SELECT 1 FROM auth.users WHERE LOWER(email) = LOWER(p_student_email)) THEN
    RETURN QUERY SELECT FALSE, 'Este email já está registrado no sistema', NULL::UUID, NULL::TEXT, NULL::TEXT;
    RETURN;
  END IF;

  -- Gerar credenciais temporárias E token de convite
  v_temp_password := substring(upper(p_student_name) from 1 for 3) || 
                     substr(v_clean_cpf, 9, 2) || 
                     to_char(EXTRACT(YEAR FROM NOW()), '99') || '!';
  v_activation_token := 'act_' || encode(gen_random_bytes(32), 'hex') || '_' || extract(epoch from now())::bigint;
  v_invitation_token := 'inv_' || encode(gen_random_bytes(24), 'hex') || '_' || extract(epoch from now())::bigint;

  -- Gerar UUID para o novo estudante
  v_student_user_id := gen_random_uuid();

  -- Criar entrada no family_invitations para rastreamento (COM invitation_token)
  INSERT INTO public.family_invitations(
    id, guardian_id, guardian_email, student_name, student_email, student_cpf, student_phone,
    invitation_type, status, temp_password, activation_token, invitation_token, expires_at
  ) VALUES (
    gen_random_uuid(), p_guardian_id, v_guardian_email, p_student_name, p_student_email,
    v_clean_cpf, p_student_phone, 'direct_rpc_creation', 'pending',
    v_temp_password, v_activation_token, v_invitation_token, NOW() + interval '7 days'
  );

  -- Log da operação
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_account_direct_creation_initiated', p_guardian_id,
          jsonb_build_object(
            'method', 'direct_rpc',
            'student_email', p_student_email,
            'temp_user_id', v_student_user_id
          ),
          NOW());

  -- Retornar dados para que Edge Function possa criar usuário auth
  RETURN QUERY SELECT TRUE, 'Dados preparados para criação de conta', v_student_user_id, v_temp_password, v_activation_token;
END;
$$;