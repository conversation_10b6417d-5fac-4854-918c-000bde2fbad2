import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import { CheckCircle, XCircle, Clock, ArrowLeft, UserPlus } from 'lucide-react';
import { familyInvitationService } from '@/lib/services/family/FamilyInvitationService';
import { useProfile } from '@/hooks/useProfile';

const AcceptInvitationPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { profile } = useProfile();
  const { t } = useTranslation();
  
  const [isLoading, setIsLoading] = useState(false);
  const [invitationStatus, setInvitationStatus] = useState<'loading' | 'valid' | 'invalid' | 'accepted' | 'error'>('loading');
  const [invitationData, setInvitationData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setInvitationStatus('invalid');
      setErrorMessage(t('acceptInvitation.tokenNotFound'));
      return;
    }

    // Validate token format (basic check)
    if (token.length < 10) {
      setInvitationStatus('invalid');
      setErrorMessage(t('acceptInvitation.invalidToken'));
      return;
    }

    setInvitationStatus('valid');
  }, [token]);

  const handleAcceptInvitation = async () => {
    if (!token) return;

    setIsLoading(true);
    try {
      console.log('[AcceptInvitationPage] Accepting invitation with token:', token);
      
      const result = await familyInvitationService.acceptInvitation(token);

      if (result.success) {
        setInvitationStatus('accepted');
        toast({
          title: t('common.success'),
          description: result.message
        });

        // Redirect to profile after 3 seconds
        setTimeout(() => {
          navigate('/profile');
        }, 3000);
      } else {
        setInvitationStatus('error');
        setErrorMessage(result.message);
        toast({
          variant: 'destructive',
          title: t('common.error'),
          description: result.message
        });
      }
    } catch (error: any) {
      console.error('[AcceptInvitationPage] Error accepting invitation:', error);
      setInvitationStatus('error');
      setErrorMessage(t('acceptInvitation.processingError'));
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: t('acceptInvitation.processingError')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    const userType = profile?.user_type;
    
    if (userType === 'parent') {
      navigate('/parent-dashboard');
    } else if (userType === 'student') {
      navigate('/student-dashboard');
    } else {
      navigate('/dashboard');
    }
  };

  const handleGoToProfile = () => {
    navigate('/profile');
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-md">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          {t('common.back')}
        </Button>
        <h1 className="text-xl font-bold">{t('acceptInvitation.pageTitle')}</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            {t('acceptInvitation.cardTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {invitationStatus === 'loading' && (
            <div className="text-center py-8">
              <Clock className="mx-auto h-10 w-10 text-blue-500 animate-spin" />
              <p className="mt-4 text-gray-600">{t('acceptInvitation.validating')}</p>
            </div>
          )}

          {invitationStatus === 'invalid' && (
            <Alert className="border-red-200 bg-red-50">
              <XCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-700">
                <strong>{t('acceptInvitation.invalid')}</strong>
                <br />
                {errorMessage}
              </AlertDescription>
            </Alert>
          )}

          {invitationStatus === 'valid' && (
            <div className="space-y-6">
              <Alert className="border-blue-200 bg-blue-50">
                <UserPlus className="h-4 w-4 text-blue-500" />
                <AlertDescription className="text-blue-700">
                  <strong>{t('acceptInvitation.valid')}</strong>
                  <br />
                  {t('acceptInvitation.inviteMessage')}
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">{t('acceptInvitation.whatHappens')}</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• {t('acceptInvitation.benefit1')}</li>
                    <li>• {t('acceptInvitation.benefit2')}</li>
                    <li>• {t('acceptInvitation.benefit3')}</li>
                    <li>• {t('acceptInvitation.benefit4')}</li>
                  </ul>
                </div>

                <Button
                  onClick={handleAcceptInvitation}
                  disabled={isLoading}
                  className="w-full"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      {t('common.processing')}
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      {t('acceptInvitation.acceptButton')}
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          {invitationStatus === 'accepted' && (
            <div className="space-y-6">
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-green-700">
                  <strong>{t('acceptInvitation.acceptedTitle')}</strong>
                  <br />
                  {t('acceptInvitation.acceptedMessage')}
                </AlertDescription>
              </Alert>

              <div className="flex flex-col gap-3">
                <Button
                  onClick={handleGoToProfile}
                  className="w-full"
                  size="lg"
                >
                  {t('acceptInvitation.goToProfile')}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={handleGoBack}
                  className="w-full"
                >
                  {t('acceptInvitation.backToDashboard')}
                </Button>
              </div>
            </div>
          )}

          {invitationStatus === 'error' && (
            <div className="space-y-6">
              <Alert className="border-red-200 bg-red-50">
                <XCircle className="h-4 w-4 text-red-500" />
                <AlertDescription className="text-red-700">
                  <strong>{t('acceptInvitation.errorTitle')}</strong>
                  <br />
                  {errorMessage}
                </AlertDescription>
              </Alert>

              <div className="flex flex-col gap-3">
                <Button
                  onClick={() => setInvitationStatus('valid')}
                  variant="outline"
                  className="w-full"
                >
                  {t('acceptInvitation.tryAgain')}
                </Button>
                
                <Button
                  onClick={handleGoBack}
                  className="w-full"
                >
                  {t('acceptInvitation.backToDashboard')}
                </Button>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="border-t pt-6">
            <h3 className="text-sm font-medium text-gray-900 mb-2">{t('acceptInvitation.needHelp')}</h3>
            <p className="text-sm text-gray-600">{t('acceptInvitation.helpText')}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AcceptInvitationPage; 