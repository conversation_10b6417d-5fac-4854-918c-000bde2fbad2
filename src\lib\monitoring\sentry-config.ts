import * as Sentry from '@sentry/react';
import { env } from '@/env';

export const initSentry = () => {
  if (!env.VITE_SENTRY_DSN) {
    console.warn('[Sentry] DSN não configurado - error tracking desabilitado');
    return;
  }

  Sentry.init({
    dsn: env.VITE_SENTRY_DSN,
    environment: env.MODE || 'development',
    
    // Configurações de performance
    tracesSampleRate: env.MODE === 'production' ? 0.1 : 1.0,
    
    // Configurações de release
    release: `educonnect@${import.meta.env.VITE_APP_VERSION || '1.0.0'}`,
    
    // Configurações de privacy
    beforeSend(event, hint) {
      // Filtrar dados sensíveis
      if (event.exception) {
        const error = hint.originalException;
        if (error instanceof Error) {
          // Remover informações sensíveis do stack trace
          event.exception.values?.forEach(exception => {
            if (exception.stacktrace?.frames) {
              exception.stacktrace.frames = exception.stacktrace.frames.filter(
                frame => !frame.filename?.includes('password') && !frame.filename?.includes('token')
              );
            }
          });
        }
      }
      
      // Não enviar erros de rede local em desenvolvimento
      if (env.MODE === 'development' && event.exception?.values?.[0]?.value?.includes('localhost')) {
        return null;
      }
      
      return event;
    }
  });

  console.log('[Sentry] Error tracking inicializado');
};

// Funções utilitárias para captura de erros
export const captureError = (error: Error, context?: Record<string, any>) => {
  Sentry.captureException(error, {
    tags: context?.tags,
    extra: context?.extra,
    contexts: context?.contexts
  });
};

export const captureMessage = (message: string, level: 'info' | 'warning' | 'error' = 'info') => {
  Sentry.captureMessage(message, level);
};

// Configuração de usuário para Sentry (sem dados sensíveis)
export const setSentryUser = (userId: string, userType?: string) => {
  Sentry.setUser({
    id: userId,
    userType: userType,
    // Não incluir email ou outros dados sensíveis
  });
};

// Breadcrumb personalizado para ações importantes
export const addBreadcrumb = (message: string, category: string, data?: Record<string, any>) => {
  Sentry.addBreadcrumb({
    message,
    category,
    data,
    level: 'info',
    timestamp: Date.now() / 1000
  });
};

// Performance monitoring básico
export const setContext = (key: string, context: Record<string, any>) => {
  Sentry.setContext(key, context);
};