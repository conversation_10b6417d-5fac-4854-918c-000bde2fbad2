
-- Atualizar função para criar estudante com envio de email integrado
CREATE OR REPLACE FUNCTION public.create_student_account_direct(
  p_student_name TEXT,
  p_student_email TEXT,
  p_student_cpf TEXT,
  p_student_phone TEXT DEFAULT NULL
)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT,
  student_id UUID,
  temp_password TEXT,
  activation_token TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_guardian_id UUID := auth.uid();
  v_clean_cpf TEXT;
  v_temp_password TEXT;
  v_activation_token TEXT;
  v_new_user UUID;
  v_guardian_name TEXT;
  v_guardian_email TEXT;
  v_email_result JSONB;
BEGIN
  -- Verificar autenticação
  IF v_guardian_id IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Usuário não autenticado', NULL, NULL, NULL;
    RETURN;
  END IF;

  -- Obter dados do responsável
  SELECT u.email, COALESCE(p.full_name, u.email)
  INTO v_guardian_email, v_guardian_name
  FROM auth.users u
  LEFT JOIN public.profiles p ON p.user_id = u.id
  WHERE u.id = v_guardian_id;

  -- Validar CPF
  v_clean_cpf := regexp_replace(p_student_cpf, '[^0-9]', '', 'g');
  IF char_length(v_clean_cpf) <> 11 THEN
    RETURN QUERY SELECT FALSE, 'CPF inválido', NULL, NULL, NULL;
    RETURN;
  END IF;

  -- Gerar credenciais temporárias
  v_temp_password := substr(md5(random()::text), 1, 8) || '!';
  v_activation_token := 'act_' || encode(gen_random_bytes(16), 'base64') || '_' || extract(epoch from now())::text;

  -- Criar usuário no auth.users via admin
  SELECT auth.admin_create_user(
    email := p_student_email,
    password := v_temp_password,
    email_confirm := true,
    user_metadata := jsonb_build_object(
      'full_name', p_student_name,
      'user_type', 'student'
    )
  ) INTO v_new_user;

  IF v_new_user IS NULL THEN
    RETURN QUERY SELECT FALSE, 'Falha ao criar usuário', NULL, NULL, NULL;
    RETURN;
  END IF;

  -- Criar perfil do estudante
  INSERT INTO public.profiles(
    user_id, full_name, email, cpf, phone, user_type,
    account_creation_method, created_by_user_id,
    requires_password_change, activation_token,
    activation_expires_at, status
  ) VALUES (
    v_new_user, p_student_name, p_student_email, v_clean_cpf,
    p_student_phone, 'student', 'direct_db', v_guardian_id,
    TRUE, v_activation_token, NOW() + interval '7 days', 'pending_activation'
  );

  -- Criar relacionamento responsável-estudante
  INSERT INTO public.student_guardian_relationships(
    student_id, guardian_id, relationship_type, is_primary
  ) VALUES (
    v_new_user, v_guardian_id, 'parent', TRUE
  );

  -- Enviar email via função send-student-credentials
  SELECT public.send_student_credentials_direct(
    p_student_name,
    p_student_email,
    v_guardian_name,
    v_guardian_email,
    v_temp_password,
    v_activation_token
  ) INTO v_email_result;

  -- Log da operação
  INSERT INTO public.auth_logs(event_type, user_id, metadata, occurred_at)
  VALUES ('student_created_direct', v_new_user,
          jsonb_build_object(
            'guardian_id', v_guardian_id, 
            'email', p_student_email,
            'email_sent', v_email_result->>'success'
          ),
          NOW());

  RETURN QUERY SELECT TRUE, 'Estudante criado com sucesso', v_new_user, v_temp_password, v_activation_token;
END;
$$;

-- Função auxiliar para envio de email via Resend
CREATE OR REPLACE FUNCTION public.send_student_credentials_direct(
  p_student_name TEXT,
  p_student_email TEXT,
  p_guardian_name TEXT,
  p_guardian_email TEXT,
  p_temp_password TEXT,
  p_activation_token TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result JSONB;
BEGIN
  -- Chamar a Edge Function de envio de email
  SELECT http((
    'POST',
    current_setting('app.settings.functions_url') || '/send-student-credentials',
    ARRAY[
      ('Content-Type', 'application/json'),
      ('Authorization', 'Bearer ' || current_setting('app.settings.service_role_key'))
    ],
    'application/json',
    jsonb_build_object(
      'student_name', p_student_name,
      'student_email', p_student_email,
      'guardian_name', p_guardian_name,
      'guardian_email', p_guardian_email,
      'temp_password', p_temp_password,
      'activation_token', p_activation_token,
      'expires_at', (NOW() + interval '7 days')::text
    )::text
  )) INTO v_result;
  
  RETURN COALESCE(v_result, '{"success": false, "error": "Failed to send email"}'::jsonb);
EXCEPTION WHEN OTHERS THEN
  RETURN '{"success": false, "error": "Email service unavailable"}'::jsonb;
END;
$$;

-- Garantir permissões
GRANT EXECUTE ON FUNCTION public.create_student_account_direct(TEXT, TEXT, TEXT, TEXT) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.send_student_credentials_direct(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated, service_role;
