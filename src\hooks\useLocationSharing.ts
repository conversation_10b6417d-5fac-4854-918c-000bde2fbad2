import { useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { supabase } from '@/integrations/supabase/client';

export interface UseLocationSharingReturn {
  shareLocation: (
    latitude: number,
    longitude: number,
    guardianEmail: string,
    senderName?: string
  ) => Promise<boolean>;
  isSharing: boolean;
}

export const useLocationSharing = (): UseLocationSharingReturn => {
  const [isSharing, setIsSharing] = useState(false);
  const { toast } = useToast();

  const shareLocation = async (
    latitude: number,
    longitude: number,
    guardianEmail: string,
    senderName?: string
  ): Promise<boolean> => {
    setIsSharing(true);
    
    try {
      console.log('[useLocationSharing] Compartilhando localização:', { 
        email: guardianEmail, 
        latitude, 
        longitude,
        senderName: senderName || 'Estudante'
      });

      // Use supabase edge function instead of direct fetch
      const { data, error } = await supabase.functions.invoke('share-location', {
        body: {
          email: guardianEmail,
          latitude,
          longitude,
          senderName: senderName || 'Estudante',
          isRequest: false
        }
      });

      if (error) {
        console.error('[useLocationSharing] Erro da Edge Function:', error);
        throw error;
      }

      console.log('[useLocationSharing] Resposta da Edge Function:', data);
      
      toast({
        title: "Sucesso!",
        description: "Localização compartilhada com sucesso.",
        variant: "default"
      });
      
      return true;
    } catch (error: any) {
      console.error('[useLocationSharing] Erro ao compartilhar localização:', error);
      
      const errorMessage = error?.message || error?.details || 'Erro desconhecido';
      
      toast({
        title: "Erro",
        description: `Não foi possível compartilhar a localização: ${errorMessage}`,
        variant: "destructive"
      });
      
      return false;
    } finally {
      setIsSharing(false);
    }
  };

  return {
    shareLocation,
    isSharing
  };
};
