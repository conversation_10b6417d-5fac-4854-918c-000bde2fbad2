# Backend Overview - Locate-Family-Connect

## 🏗️ Arquitetura Supabase

O Locate-Family-Connect utiliza o Supabase como plataforma principal de backend, oferecendo um conjunto integrado de serviços:

### 🧩 Serviços Supabase Utilizados

- **Auth**: Autenticação PKCE com múltiplos perfis
- **Database**: PostgreSQL com Row Level Security
- **Edge Functions**: Funções serverless para processamento assíncrono
- **Storage**: Armazenamento de arquivos e imagens
- **Realtime**: Atualizações em tempo real (localização)

### ⚙️ Configuração Principal

A configuração do cliente Supabase está centralizada em:

```
/src/lib/supabase.ts
```

## 🔒 Autenticação e Segurança

### PKCE Authentication Flow

A implementação utiliza o fluxo PKCE (Proof Key for Code Exchange) para maior segurança:

1. Geração de code verifier (cliente)
2. Transformação em code challenge
3. Autenticação com code challenge
4. Troca do código por tokens usando code verifier
5. Refresh tokens automático

O processo é gerenciado pelo `UnifiedAuthContext.tsx` que:
- Preserva a sessão de forma segura
- Gerencia refresh tokens
- Redireciona com base no perfil do usuário

### Row Level Security (RLS)

A segurança em nível de linha é fundamental para o controle de acesso:

```sql
-- Exemplo: Política para tabela locations
CREATE POLICY "Students access own locations" 
ON public.locations
FOR ALL 
TO authenticated
USING (auth.uid() = student_id);

CREATE POLICY "Guardians access student locations" 
ON public.locations
FOR SELECT 
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM guardians
    WHERE guardians.guardian_id = auth.uid()
    AND guardians.student_id = locations.student_id
  )
);
```

## ⚡ Edge Functions

### Funções Implementadas

- **share-location**: Envia localização via email para responsáveis
- **notification-handler**: Processa e envia notificações 
- **geocode-address**: Converte endereços em coordenadas

### Implementação e Deploy

Edge Functions estão localizadas em:
```
/supabase/functions/
```

Deploy via Supabase CLI:
```bash
supabase functions deploy share-location --project-ref=your-project-ref
```

Todas as Edge Functions devem incluir:
- Tratamento detalhado de erros
- Logs estruturados
- Validação de entrada
- Segurança CORS configurada

## 📧 Email e Notificações

### Integração Resend

Configuração do serviço de email Resend:
- Domínio verificado: sistema-monitore.com.br
- Templates de email em `/src/lib/email-templates/`
- Chaves de API armazenadas em variáveis de ambiente

Scripts de diagnóstico disponíveis:
```
/scripts/test-resend.mjs
/scripts/test-email.mjs
```

### Fluxo de Notificações

1. Evento gerado (nova localização, geocerca)
2. Processamento por Edge Function ou Trigger SQL
3. Envio via Resend API
4. Rastreamento de entrega/abertura

## 🗄️ Banco de Dados

### Esquema Principal

```
auth.users         # Usuários da aplicação
public.profiles    # Perfis com dados adicionais
public.guardians   # Relações responsável-estudante
public.students    # Dados de estudantes
public.locations   # Registros de localização
public.geofences   # Definições de geocercas
```

### Índices Críticos

- `guardians_student_id_idx`: Otimiza consultas por estudante
- `guardians_email_idx`: Otimiza busca por email
- `idx_users_user_type`: Otimiza filtragem por tipo de usuário
- `location_created_at_idx`: Otimiza consultas temporais

### Triggers e Funções

```sql
-- Exemplo: Trigger para notificação automática
CREATE FUNCTION notify_location_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Lógica de notificação
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER location_update_trigger
AFTER INSERT ON locations
FOR EACH ROW
EXECUTE PROCEDURE notify_location_update();
```

## 🔄 Integração com Serviços Externos

### MapBox

- API para renderização de mapas
- Geocodificação (conversão endereço ↔ coordenadas)
- Serviço de rotas e direções

### (Planejado) Redis

- Caching de dados frequentes
- Sistema de filas para notificações
- Circuit breaker para resiliência

## 🔍 Monitoramento e Diagnóstico

Scripts disponíveis em `/scripts/`:
- `test-db-connection.js`: Verifica conectividade com banco
- `test-resend.mjs`: Verifica serviço de email
- `conexao-supabase.mjs`: Diagnóstico completo da conexão

### Logs Estruturados

Os logs seguem o padrão:
```
{
  "level": "info|warn|error",
  "message": "Descrição do evento",
  "timestamp": "ISO8601",
  "context": { dados adicionais },
  "user_id": "opcional, quando autenticado"
}
```

## 🚀 Supabase CLI - Comandos Essenciais

```bash
# Iniciar supabase localmente
supabase start

# Aplicar migrações
supabase db push

# Gerar tipos TypeScript
supabase gen types typescript > src/types/database.types.ts

# Deploy de Edge Function
supabase functions deploy function-name
```

## 📚 Referências

- [Documentação Supabase](https://supabase.com/docs)
- [Guia de Segurança RLS](https://supabase.com/docs/guides/auth/row-level-security)
- [Edge Functions](https://supabase.com/docs/guides/functions)
- [Configuração Resend](docs/configuracao-resend.md)
- [Supabase CLI](https://supabase.com/docs/reference/cli/introduction)
