
describe('Performance Tests', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
  });

  it('should load login page within acceptable time', () => {
    const startTime = Date.now();
    
    cy.visit('/login').then(() => {
      const loadTime = Date.now() - startTime;
      cy.log(`Login page load time: ${loadTime}ms`);
      
      // Assert load time is reasonable (less than 3 seconds)
      expect(loadTime).to.be.lessThan(3000);
    });
    
    // Verify critical elements are present
    cy.get('[data-cy="login-form"]').should('be.visible');
    cy.get('[data-cy="email-input"]').should('be.visible');
    cy.get('[data-cy="password-input"]').should('be.visible');
  });

  it('should handle rapid navigation without issues', () => {
    // Rapidly navigate between pages
    cy.visit('/login');
    cy.visit('/register');
    cy.visit('/login');
    cy.visit('/register');
    
    // Should still work correctly
    cy.get('[data-cy="register-form"]').should('be.visible');
    cy.get('[data-cy="auth-tabs"]').should('be.visible');
  });

  it('should handle multiple form submissions gracefully', () => {
    cy.visit('/login');
    
    // Submit form multiple times rapidly
    cy.get('[data-cy="email-input"]').type('<EMAIL>');
    cy.get('[data-cy="password-input"]').type('password');
    
    // Click submit button multiple times
    cy.get('[data-cy="submit-button"]').click();
    cy.get('[data-cy="submit-button"]').click();
    cy.get('[data-cy="submit-button"]').click();
    
    // Should not cause application to crash
    cy.get('[data-cy="login-form"]').should('be.visible');
  });

  it('should measure and log component render times', () => {
    cy.visit('/login');
    
    // Use performance API to measure render time
    cy.window().then((win) => {
      const performanceEntries = win.performance.getEntriesByType('navigation');
      const navigationTiming = performanceEntries[0] as PerformanceNavigationTiming;
      
      const loadTime = navigationTiming.loadEventEnd - navigationTiming.loadEventStart;
      cy.log(`Page load complete time: ${loadTime}ms`);
      
      // Log DOM content loaded time
      const domContentLoaded = navigationTiming.domContentLoadedEventEnd - navigationTiming.domContentLoadedEventStart;
      cy.log(`DOM content loaded time: ${domContentLoaded}ms`);
    });
  });
});
