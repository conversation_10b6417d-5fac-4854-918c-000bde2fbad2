{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# 🎓 Guia do Desenvolvedor - EduConnect\n", "\n", "## 📑 Sumário Executivo\n", "\n", "### Informaç<PERSON><PERSON>\n", "- **Porta Padrão**: 8080\n", "- **Node.js**: v18+ requerido\n", "- **Gerenciador <PERSON>**: pnpm\n", "- **Banco de Dados**: PostgreSQL via Docker\n", "- **Framework Principal**: React + TypeScript\n", "- **Ambiente de Execução**: Vite\n", "\n", "### <PERSON>s Importantes\n", "- **Frontend**: http://localhost:8080\n", "- **pgAdmin**: http://localhost:5050\n", "- **API Docs**: http://localhost:3000/dev/api-docs\n", "\n", "## 🚀 <PERSON><PERSON><PERSON>\n", "\n", "```bash\n", "# 1. <PERSON><PERSON> o repositório\n", "git clone [URL_DO_REPOSITÓRIO]\n", "\n", "# 2. Configure o ambiente\n", "cp .env.example .env\n", "pnpm install\n", "\n", "# 3. <PERSON><PERSON><PERSON> os serviços\n", "docker-compose up -d\n", "pnpm dev\n", "```\n", "\n", "## 🛠️ Pré-requisitos\n", "\n", "### Software Necessário\n", "1. **Node.js** (v18+)\n", "   ```bash\n", "   # Verificação\n", "   node --version  # Deve mostrar v18.x.x ou superior\n", "   ```\n", "\n", "2. **<PERSON><PERSON> + <PERSON><PERSON><PERSON>**\n", "   ```bash\n", "   # Verificações\n", "   docker --version\n", "   docker-compose --version\n", "   ```\n", "\n", "3. **pnpm**\n", "   ```bash\n", "   # Instalação\n", "   npm install -g pnpm\n", "\n", "   # Verificação\n", "   pnpm --version\n", "   ```\n", "\n", "4. **Git**\n", "   ```bash\n", "   # Verificação\n", "   git --version\n", "   ```\n", "\n", "### VS Code (Recomendado)\n", "#### <PERSON><PERSON><PERSON><PERSON>\n", "- ESLint\n", "- <PERSON><PERSON><PERSON>\n", "- TypeScript + JavaScript\n", "- <PERSON><PERSON>\n", "- GitLens\n", "\n", "#### Configurações Recomendadas\n", "```json\n", "{\n", "  \"editor.formatOnSave\": true,\n", "  \"editor.codeActionsOnSave\": {\n", "    \"source.fixAll.eslint\": true\n", "  },\n", "  \"typescript.updateImportsOnFileMove.enabled\": \"always\"\n", "}\n", "```\n", "\n", "## ⚙️ Configuração do Ambiente\n", "\n", "### <PERSON>st<PERSON><PERSON>"], "id": "1c758dd13eaf9337"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["\n", "## ⚙️ Configuração do Ambiente\n", "\n", "### Estrutura do .env"], "id": "328ba15e19509a6"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["\n", "### Inicialização do Banco de Dados\n", "```bash\n", "# Aplicar migrações\n", "pnpm db:migrate\n", "\n", "# Popular com dados iniciais (se necessário)\n", "pnpm db:seed\n", "\n", "# Verificar estrutura\n", "pnpm db:studio\n", "```\n", "\n", "## 📝 Fluxo de Desenvolvimento\n", "\n", "### 1. Prepar"], "id": "166b14dcafb4caf8"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["\n", "## 📝 Fluxo de Desenvolvimento\n", "\n", "### 1. Preparação do Ambiente"], "id": "25cc58fb7def1434"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}