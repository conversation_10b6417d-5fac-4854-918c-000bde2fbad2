{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "# EduConnect Developer Quick Start Guide", "id": "bc41db085174a3c5"}, {"metadata": {"jupyter": {"is_executing": true}}, "cell_type": "code", "source": ["# Prerequisites Check\n", "node --version  # Should be 18+\n", "docker --version\n", "pnpm --version\n", "\n", "# Initial Setup\n", "git clone [REPOSITORY_URL]\n", "cd educonnect\n", "cp .env.example .env\n", "pnpm install"], "id": "2cea4b537ec3904b", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["# Start Services\n", "docker-compose up -d\n", "pnpm dev\n", "\n", "# Health Checks\n", "curl http://localhost:8080\n", "curl http://localhost:5050"], "id": "f4d94954833b78d", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["# Common Development Commands\n", "\n", "# Start development server\n", "pnpm dev\n", "\n", "# Run test suite\n", "pnpm test\n", "\n", "# Create production build\n", "pnpm build\n", "\n", "# Run database migrations\n", "pnpm db:migrate"], "id": "8e6760bb0b9b9d47", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": "", "id": "b26f3f4446a9ef80"}, {"metadata": {}, "cell_type": "code", "source": "", "id": "c7f7944b51a92dce", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "", "id": "fc37ccc0b9edbb6d", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "", "id": "9f65e039f8c4c829", "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}