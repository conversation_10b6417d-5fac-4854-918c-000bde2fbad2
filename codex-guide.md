# Guia de Utilização do Codex CLI no Projeto Locate-Family-Connect

## O que é o Codex CLI?

O Codex CLI é um agente de codificação leve que roda diretamente no terminal, desenvolvido pela OpenAI. Ele permite automatizar tarefas de desenvolvimento, fazer refatorações, resolver bugs e implementar novas funcionalidades seguindo os padrões do projeto.

## Configuração Inicial

1. **Instalar o Codex CLI**:
   ```bash
   npm install -g @openai/codex
   ```

2. **Configurar a API Key**:
   ```bash
   # Temporário para sessão atual
   export OPENAI_API_KEY="sua-chave-api"
   
   # Ou adicione ao .env (já configurado no script)
   OPENAI_API_KEY=sua-chave-api
   ```

3. **Verificar Instalação**:
   ```bash
   codex --version
   ```

## Protocolo de Uso Seguro (Break-Safe Philosophy)

Seguindo nossos princípios de desenvolvimento seguro, aplique estes passos antes de aplicar qualquer alteração sugerida pelo Codex:

### ✅ Pré-execução

```text
[ ] Sistema funciona atualmente? (verificar com npm run dev)
[ ] Backup criado? (git branch ou commit atual)
[ ] Métricas de baseline registradas?
```

### 🧪 Durante a Execução

- Utilize o modo `suggest` para revisão manual (padrão)
- Revise cuidadosamente todas as alterações propostas
- Verifique se as alterações seguem os padrões de código
- Confirme que nenhuma regra RLS ou autenticação foi comprometida

### 🔄 Pós-execução

```text
[ ] Testes executados e passando?
[ ] Funcionalidade validada manualmente?
[ ] Nenhuma regressão introduzida?
[ ] Commit com mensagem descritiva?
```

## Casos de Uso Recomendados

### 1. Auditoria de Segurança

```bash
npm run codex -- "auditar segurança da Edge Function de compartilhamento de localização"
```

### 2. Refatoração de Componentes

```bash
npm run codex -- "refatorar UnifiedAuthContext.tsx para melhorar reutilização de código"
```

### 3. Implementação de Testes

```bash
npm run codex -- "criar testes para o sistema PKCE de autenticação"
```

### 4. Revisão de Código

```bash
npm run codex -- "revisar implementação RLS das tabelas guardians e locations"
```

## Modos de Aprovação

O Codex CLI suporta diferentes modos de aprovação:

- **suggest** (padrão): Requer aprovação para todas as alterações
- **auto-edit**: Permite edições automáticas, mas requer aprovação para comandos
- **full-auto**: Execução completamente automática (use com cautela!)

Para alterar o modo:

```bash
npm run codex -- --approval-mode suggest "sua tarefa aqui"
```

## Recursos Adicionais

- [Repositório oficial do Codex](https://github.com/openai/codex)
- [Documentação do Codex CLI](https://github.com/openai/codex#readme)

## Troubleshooting

### Porta 8080 em uso

Se você encontrar problemas com o serviço Apache ou outro serviço na porta 8080:

1. Verifique processos em execução:
   ```bash
   netstat -ano | findstr :8080
   ```

2. Encerre o processo:
   ```bash
   taskkill /PID [número-do-processo] /F
   ```

3. Configure o Codex para usar outra porta (se necessário)
   ```bash
   # No arquivo ~/.codex/config.json
   # Adicione: "serverPort": 8081
   ```
