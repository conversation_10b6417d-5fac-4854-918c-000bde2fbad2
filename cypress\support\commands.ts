
// Custom Cypress commands for the project
declare global {
  namespace Cypress {
    interface Chainable {
      loginAs(userType: 'student' | 'parent'): Chainable<void>;
      mockAuthState(user: any): Chainable<void>;
      clearAuthState(): Chainable<void>;
    }
  }
}

Cypress.Commands.add('loginAs', (userType: 'student' | 'parent') => {
  const users = {
    student: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      id: 'student-mock-id'
    },
    parent: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      id: 'parent-mock-id'
    }
  };

  const user = users[userType];
  
  cy.visit('/login');
  cy.get('[data-cy="email-input"]').type(user.email);
  cy.get('[data-cy="password-input"]').type(user.password);
  cy.get('[data-cy="submit-button"]').click();
});

Cypress.Commands.add('mockAuthState', (user: any) => {
  cy.window().then((win) => {
    win.localStorage.setItem('supabase.auth.token', JSON.stringify({
      access_token: 'mock-token',
      refresh_token: 'mock-refresh-token',
      user: user
    }));
  });
});

Cypress.Commands.add('clearAuthState', () => {
  cy.clearCookies();
  cy.clearLocalStorage();
});

export {};
