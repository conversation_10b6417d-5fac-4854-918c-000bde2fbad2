import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Clock, CheckCircle, XCircle, AlertCircle, Mail, User, Calendar, RefreshCcw } from "lucide-react";
import { useStudentInvitations, StudentInvitation } from '@/hooks/useStudentInvitations';
import { formatDistanceToNow } from 'date-fns';
import { getDateLocale } from '@/utils/dateLocale';
import { useTranslation } from 'react-i18next';

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending':
      return <Clock className="h-4 w-4" />;
    case 'accepted':
      return <CheckCircle className="h-4 w-4" />;
    case 'rejected':
      return <XCircle className="h-4 w-4" />;
    case 'expired':
      return <AlertCircle className="h-4 w-4" />;
    default:
      return <Clock className="h-4 w-4" />;
  }
};

const getStatusBadge = (status: string, expiresAt: string, t: any) => {
  const isExpired = new Date(expiresAt) < new Date();
  const effectiveStatus = isExpired && status === 'pending' ? 'expired' : status;

  switch (effectiveStatus) {
    case 'pending':
      return <Badge variant="outline" className="text-amber-600 border-amber-300 bg-amber-50">{t('studentInvitationsStatus.pending', 'Pending')}</Badge>;
    case 'accepted':
      return <Badge variant="outline" className="text-green-600 border-green-300 bg-green-50">{t('studentInvitationsStatus.accepted', 'Accepted')}</Badge>;
    case 'rejected':
      return <Badge variant="outline" className="text-red-600 border-red-300 bg-red-50">{t('studentInvitationsStatus.rejected', 'Rejected')}</Badge>;
    case 'expired':
      return <Badge variant="outline" className="text-gray-600 border-gray-300 bg-gray-50">{t('studentInvitationsStatus.expired', 'Expired')}</Badge>;
    default:
      return <Badge variant="outline">{t('studentInvitationsStatus.unknown', 'Unknown')}</Badge>;
  }
};

const formatDate = (dateString: string, language?: string) => {
  return formatDistanceToNow(new Date(dateString), {
    addSuffix: true,
    locale: language ? getDateLocale(language) : undefined
  });
};

const InvitationItem: React.FC<{ invitation: StudentInvitation, t: any, language: string }> = ({ invitation, t, language }) => {
  const isExpired = new Date(invitation.expires_at) < new Date();
  const effectiveStatus = isExpired && invitation.status === 'pending' ? 'expired' : invitation.status;

  return (
    <div className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg">
      <div className="flex-shrink-0 mt-1">
        {getStatusIcon(effectiveStatus)}
      </div>
      
      <div className="flex-1 space-y-2">
        <div className="flex items-start justify-between">
          <div>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="font-medium text-gray-900">{invitation.student_name}</span>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <Mail className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-500">{invitation.student_email}</span>
            </div>
          </div>
          <div className="text-right">
            {getStatusBadge(invitation.status, invitation.expires_at, t)}
          </div>
        </div>
        
        <div className="flex items-center gap-4 text-sm text-gray-500">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{t('studentInvitationsStatus.sent', 'Sent')} {formatDate(invitation.created_at, language)}</span>
          </div>
          
          {invitation.status === 'accepted' && invitation.accepted_at && (
            <div className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>{t('studentInvitationsStatus.acceptedAt', 'Accepted')} {formatDate(invitation.accepted_at, language)}</span>
            </div>
          )}
          
          {effectiveStatus === 'pending' && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-amber-500" />
              <span>{t('studentInvitationsStatus.expires', 'Expires')} {formatDate(invitation.expires_at, language)}</span>
            </div>
          )}
          
          {effectiveStatus === 'expired' && (
            <div className="flex items-center gap-1">
              <AlertCircle className="h-3 w-3 text-gray-500" />
              <span>{t('studentInvitationsStatus.expiredAt', 'Expired')} {formatDate(invitation.expires_at, language)}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const StudentInvitationsStatus: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { invitations, isLoading, error, refetch } = useStudentInvitations();

  // Log detailed analysis of invitations
  React.useEffect(() => {
    if (!isLoading && invitations.length > 0) {
      console.log('[StudentInvitationsStatus] Current invitations:', invitations);
      
      const pendingCount = invitations.filter(inv => inv.status === 'pending').length;
      const acceptedCount = invitations.filter(inv => inv.status === 'accepted').length;
      const rejectedCount = invitations.filter(inv => inv.status === 'rejected').length;
      const expiredCount = invitations.filter(inv => {
        const isExpired = new Date(inv.expires_at) < new Date();
        return isExpired && inv.status === 'pending';
      }).length;
      
      console.log('[StudentInvitationsStatus] Status counts:', {
        pending: pendingCount,
        accepted: acceptedCount,
        rejected: rejectedCount,
        expired: expiredCount,
        total: invitations.length
      });
      
      // Log each invitation with effective status
      invitations.forEach((inv, index) => {
        const isExpired = new Date(inv.expires_at) < new Date();
        const effectiveStatus = isExpired && inv.status === 'pending' ? 'expired' : inv.status;
        
        console.log(`[StudentInvitationsStatus] Invitation ${index + 1}:`, {
          id: inv.id,
          student_name: inv.student_name,
          original_status: inv.status,
          expires_at: inv.expires_at,
          is_expired: isExpired,
          effective_status: effectiveStatus,
          accepted_at: inv.accepted_at
        });
      });
    }
  }, [invitations, isLoading]);

  // Debug function to force refresh and show raw data
  const handleDebugRefresh = async () => {
    console.log('[StudentInvitationsStatus] 🔄 Manual refresh triggered');
    await refetch();
  };

  // Don't render anything if there are no invitations and no error
  if (!isLoading && !error && invitations.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          {t('studentInvitationsStatus.title', 'Invitation Status')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ) : error ? (
          <div className="text-red-600 text-sm">
            {t('studentInvitationsStatus.error', 'Error loading invitations')}: {error}
          </div>
        ) : (
          <div className="space-y-4">
            {/* Summary badges */}
            <div className="flex gap-2 flex-wrap">
              <Badge variant="outline" className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {invitations.filter(inv => inv.status === 'pending').length} {t('studentInvitationsStatus.pending', 'Pending')}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                {invitations.filter(inv => inv.status === 'accepted').length} {t('studentInvitationsStatus.accepted', 'Accepted')}
              </Badge>
            </div>
            {invitations.map((invitation) => (
              <InvitationItem key={invitation.id} invitation={invitation} t={t} language={i18n.language} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StudentInvitationsStatus; 