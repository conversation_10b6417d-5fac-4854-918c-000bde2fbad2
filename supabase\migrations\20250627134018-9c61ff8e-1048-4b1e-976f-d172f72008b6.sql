
-- CORREÇÃO: Resolver problema de ON CONFLICT na tabela profiles
-- Objetivo: Corrigir RLS e permitir login do usu<PERSON><PERSON> <EMAIL>

-- 1. TEMPORARIAMENTE DESABILITAR RLS PARA DIAGNÓSTICO
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 2. REMOVER POLÍTICAS PROBLEMÁTICAS QUE PODEM ESTAR CAUSANDO LOOPS
DROP POLICY IF EXISTS "profiles_cpf_verification" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role_all" ON public.profiles;

-- 3. REABILITAR RLS E CRIAR POLÍTICAS SIMPLES
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Política para SELECT: Usu<PERSON>rios podem ver seus próprios perfis
CREATE POLICY "profiles_select_policy" 
ON public.profiles 
FOR SELECT 
USING (auth.uid() = user_id);

-- Política para INSERT: Usuários podem criar seus próprios perfis
CREATE POLICY "profiles_insert_policy" 
ON public.profiles 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Política para UPDATE: Usuários podem atualizar seus próprios perfis
CREATE POLICY "profiles_update_policy" 
ON public.profiles 
FOR UPDATE 
USING (auth.uid() = user_id);

-- 4. VERIFICAR E CORRIGIR PERFIL DO USUÁRIO (SEM ON CONFLICT)
-- Primeiro verificar se já existe
DO $$
DECLARE
    v_user_id UUID;
    v_profile_exists BOOLEAN;
BEGIN
    -- Buscar ID do usuário
    SELECT id INTO v_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Verificar se perfil existe
        SELECT EXISTS(
            SELECT 1 FROM public.profiles WHERE user_id = v_user_id
        ) INTO v_profile_exists;
        
        -- Se não existe, criar
        IF NOT v_profile_exists THEN
            INSERT INTO public.profiles (user_id, email, full_name, user_type, cpf)
            VALUES (
                v_user_id,
                '<EMAIL>',
                'Usuário Educatechnov',
                'student',
                '000.000.000-00'
            );
        ELSE
            -- Se existe, atualizar
            UPDATE public.profiles 
            SET 
                email = '<EMAIL>',
                full_name = COALESCE(full_name, 'Usuário Educatechnov'),
                user_type = COALESCE(user_type, 'student'),
                cpf = COALESCE(cpf, '000.000.000-00'),
                updated_at = NOW()
            WHERE user_id = v_user_id;
        END IF;
    END IF;
END $$;

-- 5. LOGS PARA DIAGNÓSTICO
INSERT INTO public.auth_logs (
    event_type,
    user_id,
    metadata,
    occurred_at
) VALUES (
    'database_schema_fix_applied_v2',
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    jsonb_build_object(
        'action', 'rls_policies_recreated_fixed',
        'email', '<EMAIL>',
        'timestamp', NOW(),
        'fix_phase', 'phase_1_corrected'
    ),
    NOW()
);
