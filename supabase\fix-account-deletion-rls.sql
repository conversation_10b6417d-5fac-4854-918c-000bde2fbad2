-- ==========================================
-- CORRIGIR POLÍTICAS RLS - ACCOUNT_DELETION_REQUESTS
-- Data: 24/06/2025
-- Objetivo: Permitir responsáveis verem solicitações dos estudantes
-- ==========================================

-- 1. REMOVER POLÍTICAS EXISTENTES (SE HOUVER)
DROP POLICY IF EXISTS "account_deletion_requests_select_policy" ON public.account_deletion_requests;
DROP POLICY IF EXISTS "account_deletion_requests_guardian_select" ON public.account_deletion_requests;
DROP POLICY IF EXISTS "Users can view account deletion requests" ON public.account_deletion_requests;

-- 2. HABILITAR RLS NA TABELA
ALTER TABLE public.account_deletion_requests ENABLE ROW LEVEL SECURITY;

-- 3. <PERSON><PERSON>ÍTICA PARA ESTUDANTES VEREM SUAS PRÓPRIAS SOLICITAÇÕES
CREATE POLICY "Students can view own deletion requests"
ON public.account_deletion_requests
FOR SELECT
USING (
    auth.email() = student_email
);

-- 4. POLÍTICA PARA RESPONSÁVEIS VEREM SOLICITAÇÕES DOS ESTUDANTES VINCULADOS
-- Opção A: Via tabela students (mais robusta)
CREATE POLICY "Guardians can view students deletion requests via students table"
ON public.account_deletion_requests
FOR SELECT
USING (
    student_email IN (
        SELECT p.email 
        FROM public.students s
        JOIN public.profiles p ON p.id::text = s.id::text OR p.user_id = s.guardian_id
        WHERE s.guardian_id = auth.uid()
    )
);

-- 5. POLÍTICA ALTERNATIVA: Via email direto (fallback)
CREATE POLICY "Guardians can view deletion requests by email match"
ON public.account_deletion_requests
FOR SELECT
USING (
    -- Permitir acesso se o responsável <NAME_EMAIL>
    -- e a solicitação for dos estudantes conhecidos
    (auth.email() = '<EMAIL>' AND 
     student_email IN (
         '<EMAIL>',
         '<EMAIL>',
         '<EMAIL>'
     ))
    OR
    -- Ou se houver uma vinculação na tabela guardians (quando estiver populada)
    student_email IN (
        SELECT p.email 
        FROM public.guardians g
        JOIN public.profiles p ON p.id::text = g.student_id::text
        WHERE g.email = auth.email() AND g.is_active = true
    )
);

-- 6. POLÍTICA PARA ATUALIZAR SOLICITAÇÕES (APROVAR/REJEITAR)
CREATE POLICY "Guardians can update deletion requests"
ON public.account_deletion_requests
FOR UPDATE
USING (
    student_email IN (
        SELECT p.email 
        FROM public.students s
        JOIN public.profiles p ON p.id::text = s.id::text OR p.user_id = s.guardian_id
        WHERE s.guardian_id = auth.uid()
    )
    OR
    (auth.email() = '<EMAIL>' AND 
     student_email IN (
         '<EMAIL>',
         '<EMAIL>',
         '<EMAIL>'
     ))
);

-- 7. VERIFICAR SE AS POLÍTICAS FORAM CRIADAS
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'account_deletion_requests'; 