import React from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useUser } from "@/contexts/UnifiedAuthContext";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UserCircle, Menu, LogOut, Home, Map, Users, RefreshCw } from "lucide-react";
import CacheClearButton from "./CacheClearButton";
import LogoutButton from "./LogoutButton";

const Navbar = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  
  // Get user_type from profile or user metadata
  const userType = user?.user_metadata?.user_type || 'student';

  return (
    <header className="border-b bg-background">
      <div className="container flex h-16 items-center px-4">
        <Link to="/dashboard" className="flex items-center gap-2 font-semibold">
          <span className="text-xl font-bold text-primary">EduConnect</span>
        </Link>
        
        <nav className="mx-6 flex items-center space-x-4 lg:space-x-6 hidden md:block">
          <Link
            to="/dashboard"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            <span className="flex items-center gap-2">
              <Home size={18} />
              Início
            </span>
          </Link>
          
          {userType === 'student' ? (
            <>
              <Link
                to="/student-map"
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                <span className="flex items-center gap-2">
                  <Map size={18} />
                  Mapa
                </span>
              </Link>
              <Link
                to="/guardians"
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                <span className="flex items-center gap-2">
                  <Users size={18} />
                  Responsáveis
                </span>
              </Link>
            </>
          ) : (
            <Link
              to="/parent-children"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              <span className="flex items-center gap-2">
                <Users size={18} />
                Estudantes
              </span>
            </Link>
          )}
        </nav>
        
        <div className="ml-auto flex items-center gap-2">
          <CacheClearButton />
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full" data-cy="user-menu-button">
                <UserCircle className="h-6 w-6" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>
                {user?.user_metadata?.full_name || 'Minha Conta'}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => navigate('/profile')}
                className="cursor-pointer"
                data-cy="profile-link"
              >
                Perfil
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => navigate('/dashboard')}
              >
                Dashboard
              </DropdownMenuItem>
              {userType === 'student' && (
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => navigate('/guardians')}
                >
                  Meus Responsáveis
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="cursor-pointer text-destructive"
              >
                <LogoutButton variant="ghost" size="sm" className="w-full justify-start p-0 h-auto font-normal">
                  <LogOut className="mr-2 h-4 w-4" />
                  Sair
                </LogoutButton>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
