#!/usr/bin/env node

/**
 * Script de Simulação - Hook useAccountDeletionRequests
 * 
 * Objetivo: Simular exatamente o que o hook faz para identificar onde falha
 * Data: 24/06/2025
 */

import { createClient } from '@supabase/supabase-js';
import 'dotenv/config';

const supabaseUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Simular usuário logado
const mockUser = {
  id: '40b1ac70-e024-4a9a-ba9c-b7b42f8b8f32',
  email: '<EMAIL>',
  user_metadata: {
    user_type: 'parent'
  }
};

console.log('🎭 SIMULAÇÃO - Hook useAccountDeletionRequests');
console.log('='.repeat(60));
console.log(`👤 Usuário simulado: ${mockUser.email}`);
console.log('='.repeat(60));

async function simulateHookBehavior() {
  console.log('\n📋 SIMULANDO fetchRequestsDirectQuery (fallback)');
  console.log('-'.repeat(50));
  
  try {
    console.log('[FALLBACK] Usando query direta devido a limitações de quota');
    console.log('[FALLBACK] Email do responsável:', mockUser.email);
    
    // Buscar IDs dos estudantes vinculados a este responsável via EMAIL
    console.log('\n🔍 PASSO 1: Buscar estudantes vinculados na tabela guardians');
    const { data: studentIds, error: guardianError } = await supabase
      .from('guardians')
      .select('student_id')
      .eq('email', mockUser.email)
      .eq('is_active', true);

    if (guardianError) {
      console.error('[FALLBACK] Erro ao buscar estudantes vinculados:', guardianError);
      throw guardianError;
    }

    console.log(`✅ Registros na tabela guardians: ${studentIds?.length || 0}`);
    if (studentIds && studentIds.length > 0) {
      console.log('📋 Dados encontrados:');
      studentIds.forEach((item, i) => {
        console.log(`   Vinculação ${i + 1}:`, JSON.stringify(item, null, 2));
      });
    } else {
      console.log('⚠️ PROBLEMA: Nenhuma vinculação guardian-student encontrada!');
      console.log('💡 Isso explica por que as solicitações não aparecem.');
      return [];
    }

    const studentIdsList = studentIds.map(s => s.student_id);
    console.log('\n📝 IDs dos estudantes extraídos:', studentIdsList);

    console.log('\n🔍 PASSO 2: Buscar solicitações de exclusão para esses estudantes');
    const { data: deletionRequests, error: requestsError } = await supabase
      .from('account_deletion_requests')
      .select('*')
      .in('student_id', studentIdsList)
      .order('requested_at', { ascending: false });

    if (requestsError) {
      console.error('[FALLBACK] Erro ao buscar solicitações:', requestsError);
      throw requestsError;
    }

    console.log(`✅ Solicitações encontradas: ${deletionRequests?.length || 0}`);
    if (deletionRequests && deletionRequests.length > 0) {
      console.log('📋 Solicitações:');
      deletionRequests.forEach((req, i) => {
        console.log(`   Solicitação ${i + 1}:`, {
          id: req.id,
          student_name: req.student_name,
          student_email: req.student_email,
          status: req.status,
          requested_at: req.requested_at
        });
      });
    }

    return deletionRequests || [];

  } catch (error) {
    console.error('❌ Erro na simulação:', error);
    return [];
  }
}

async function compareWithStudentService() {
  console.log('\n📊 COMPARAÇÃO: Como o StudentProfileService consegue funcionar?');
  console.log('-'.repeat(50));
  
  try {
    console.log('🔍 Testando o mesmo fallback usado pelo StudentProfileService...');
    
    // Exatamente o mesmo código que StudentProfileService usa
    const { data: guardiansData, error: guardiansError } = await supabase
      .from('guardians')
      .select('student_id')
      .eq('email', mockUser.email)
      .eq('is_active', true);
    
    if (guardiansError) {
      console.log('❌ Erro:', guardiansError);
      return;
    }
    
    console.log(`✅ StudentProfileService fallback: ${guardiansData?.length || 0} registros`);
    
    if (guardiansData && guardiansData.length > 0) {
      console.log('📋 Dados do StudentProfileService:');
      guardiansData.forEach((item, i) => {
        console.log(`   Item ${i + 1}:`, JSON.stringify(item, null, 2));
      });
      
      // Continuar com busca de perfis
      const studentIds = guardiansData.map(g => g.student_id).filter(id => id !== null);
      
      if (studentIds.length > 0) {
        console.log('\n🔍 Buscando perfis dos estudantes...');
        const { data: profilesData, error: profilesError } = await supabase
          .from('profiles')
          .select('*')
          .in('user_id', studentIds);
        
        if (profilesError) {
          console.log('❌ Erro nos perfis:', profilesError);
        } else {
          console.log(`✅ Perfis encontrados: ${profilesData?.length || 0}`);
          if (profilesData && profilesData.length > 0) {
            profilesData.forEach((profile, i) => {
              console.log(`   Perfil ${i + 1}:`, {
                user_id: profile.user_id,
                full_name: profile.full_name,
                email: profile.email
              });
            });
          }
        }
      }
    } else {
      console.log('⚠️ StudentProfileService também não encontra dados!');
      console.log('🤔 Como o dashboard consegue mostrar estudantes então?');
    }
    
  } catch (error) {
    console.error('❌ Erro na comparação:', error);
  }
}

// Executar simulação
simulateHookBehavior()
  .then(compareWithStudentService)
  .then(() => {
    console.log('\n🎯 CONCLUSÃO');
    console.log('-'.repeat(50));
    console.log('✅ Simulação concluída! ');
    console.log('📊 Se não há vinculações na tabela guardians,');
    console.log('   o problema é que o Frank não está vinculado aos estudantes.');
    console.log('💡 Mas então como o dashboard funciona? Deve usar outro mecanismo!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  }); 