describe('Comprehensive Authentication Tests', () => {
  
  describe('Password Recovery Flow Test', () => {
    const studentForPasswordReset = {
      email: '<EMAIL>'
    };

    beforeEach(() => {
      cy.visit('/login');
      cy.wait(2000);
    });

    it('should successfully initiate password recovery for student', () => {
      cy.log('🔐 Step 1: Accessing password recovery');
      
      // Procurar por link "Esqueci minha senha" ou similar
      cy.get('body').then(($body) => {
        if ($body.find('a:contains("Esqueci minha senha")').length > 0) {
          cy.get('a:contains("Esqueci minha senha")').click();
        } else if ($body.find('a:contains("Recuperar senha")').length > 0) {
          cy.get('a:contains("Recuperar senha")').click();
        } else if ($body.find('button:contains("Esqueci minha senha")').length > 0) {
          cy.get('button:contains("Esqueci minha senha")').click();
        } else {
          cy.get('a, button').contains(/esqueci|recuperar/i).first().click();
        }
      });

      cy.log('📧 Step 2: Verifying password recovery page');
      cy.url({ timeout: 10000 }).should('include', 'reset');
      
      cy.log('📝 Step 3: Filling recovery email');
      cy.get('input[type="email"], input[name="email"], input[placeholder*="email"]', { timeout: 5000 })
        .should('be.visible')
        .clear()
        .type(studentForPasswordReset.email);

      cy.log('🚀 Step 4: Submitting recovery request');
      cy.intercept('POST', '**/auth/v1/recover', { statusCode: 200 }).as('passwordRecoveryRequest');
      cy.intercept('POST', '**/functions/v1/send-password-reset', { statusCode: 200 }).as('sendPasswordResetRequest');

      cy.get('button[type="submit"], button:contains("Enviar"), button:contains("Recuperar")')
        .should('be.visible')
        .should('not.be.disabled')
        .click();

      cy.log('✅ Step 5: Verifying recovery success message');
      cy.get('body', { timeout: 10000 }).should(($body) => {
        const text = $body.text();
        expect(text).to.satisfy((content) => {
          return content.includes('enviado') || 
                 content.includes('email') || 
                 content.includes('sucesso') ||
                 content.includes('verifique') ||
                 content.includes('link');
        });
      });

      cy.window().then((win) => {
        cy.task('log', '✅ Password recovery flow initiated successfully!');
        cy.task('log', `📧 Recovery email: ${studentForPasswordReset.email}`);
      });
    });
  });

  describe('Student Login Tests', () => {
    const studentCredentials = [
      {
        email: '<EMAIL>',
        password: '4EG8GsjBT5KjD3k',
        name: 'Ceti Sergio Pessoa'
      },
      {
        email: '<EMAIL>',
        password: '4EG8GsjBT5KjD3k',
        name: 'Frank Lima'
      }
    ];

    studentCredentials.forEach((student) => {
      it(`should successfully login as student: ${student.email}`, () => {
        cy.visit('/login');
        cy.wait(2000);

        cy.get('input[type="email"]', { timeout: 10000 })
          .clear()
          .type(student.email);
        
        cy.get('input[type="password"]')
          .clear()
          .type(student.password);
        
        cy.get('button[type="submit"]').click();

        cy.url({ timeout: 15000 }).should('include', '/student-dashboard');
        cy.contains('Dashboard do Estudante', { timeout: 10000 }).should('be.visible');
        
        cy.task('log', `✅ Student login successful: ${student.email}`);
        
        // Logout
        cy.get('body').then(($body) => {
          if ($body.find('button:contains("Sair")').length > 0) {
            cy.get('button:contains("Sair")').click();
          }
        });
      });
    });
  });

  describe('Guardian Login Tests', () => {
    const guardianCredentials = [
      {
        email: '<EMAIL>',
        password: 'Senh@97481716',
        name: 'Frank Webber Dev'
      },
      {
        email: '<EMAIL>',
        password: 'Escola2025!',
        name: 'Frank Webber'
      },
      {
        email: '<EMAIL>',
        password: '<EMAIL>',
        name: 'Mauro Lima'
      }
    ];

    guardianCredentials.forEach((guardian) => {
      it(`should successfully login as guardian: ${guardian.email}`, () => {
        cy.visit('/login');
        cy.wait(2000);

        cy.get('input[type="email"]', { timeout: 10000 })
          .clear()
          .type(guardian.email);
        
        cy.get('input[type="password"]')
          .clear()
          .type(guardian.password);
        
        cy.get('button[type="submit"]').click();

        cy.url({ timeout: 15000 }).should('include', '/parent-dashboard');
        cy.contains('Dashboard do Responsável', { timeout: 10000 }).should('be.visible');
        
        cy.task('log', `✅ Guardian login successful: ${guardian.email}`);
        
        // Logout
        cy.get('body').then(($body) => {
          if ($body.find('button:contains("Sair")').length > 0) {
            cy.get('button:contains("Sair")').click();
          }
        });
      });
    });
  });

  describe('Cross-User Flow Tests', () => {
    it('should test complete guardian-to-student creation flow', () => {
      // Este teste combina login do guardian com criação de estudante
      const guardianEmail = '<EMAIL>';
      const guardianPassword = 'Senh@97481716';
      
      const newStudent = {
        name: 'Fabio Leda Cunha',
        email: '<EMAIL>',
        cpf: '717.102.482-20',
        phone: '+5592994897102'
      };

      cy.visit('/login');
      cy.wait(2000);

      // Login como guardian
      cy.get('input[type="email"]', { timeout: 10000 })
        .clear()
        .type(guardianEmail);
      
      cy.get('input[type="password"]')
        .clear()
        .type(guardianPassword);
      
      cy.get('button[type="submit"]').click();

      cy.url({ timeout: 15000 }).should('include', '/parent-dashboard');

      // Tentar criar estudante
      cy.get('body').then(($body) => {
        if ($body.find('[data-testid="add-student-btn"]').length > 0) {
          cy.get('[data-testid="add-student-btn"]').click();
        } else if ($body.find('button:contains("Adicionar Estudante")').length > 0) {
          cy.get('button:contains("Adicionar Estudante")').first().click();
        } else if ($body.find('button:contains("Convidar Estudante")').length > 0) {
          cy.get('button:contains("Convidar Estudante")').first().click();
        } else {
          cy.get('button').contains('+').first().click();
        }
      });

      cy.wait(1000);

      // Preencher formulário
      cy.get('input[name="name"], input[placeholder*="nome"], input[placeholder*="Nome"]', { timeout: 5000 })
        .should('be.visible')
        .clear()
        .type(newStudent.name);

      cy.get('input[name="email"], input[type="email"], input[placeholder*="email"]')
        .should('be.visible')
        .clear()
        .type(newStudent.email);

      cy.get('input[name="cpf"], input[placeholder*="CPF"], input[placeholder*="cpf"]')
        .should('be.visible')
        .clear()
        .type(newStudent.cpf);

      cy.get('input[name="phone"], input[name="telefone"], input[placeholder*="telefone"]')
        .should('be.visible')
        .clear()
        .type(newStudent.phone);

      // Interceptar Edge Function
      cy.intercept('POST', '**/functions/v1/create-student-account').as('createStudentRequest');

      cy.get('button[type="submit"], button:contains("Criar"), button:contains("Enviar"), button:contains("Convidar")')
        .should('be.visible')
        .should('not.be.disabled')
        .click();

      // Verificar que não houve erro 500
      cy.wait('@createStudentRequest', { timeout: 30000 }).then((interception) => {
        expect(interception.response.statusCode).to.not.equal(500);
        expect([200, 201, 400]).to.include(interception.response.statusCode);
      });

      cy.task('log', '✅ Complete guardian-to-student flow tested successfully!');
    });
  });
}); 