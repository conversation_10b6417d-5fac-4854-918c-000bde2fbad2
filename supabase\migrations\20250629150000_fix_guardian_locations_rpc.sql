
-- Corrige a função RPC para garantir que retorna dados corretamente

-- <PERSON><PERSON>, vamos recriar a função com logs mais detalhados
CREATE OR REPLACE FUNCTION public.get_guardian_locations_bypass_v2(p_student_id uuid)
RETURNS TABLE(
  id uuid, 
  user_id uuid, 
  latitude double precision, 
  longitude double precision, 
  location_timestamp timestamp with time zone, 
  address text, 
  shared_with_guardians boolean, 
  student_name text
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public', 'auth'
AS $$
DECLARE
  v_guardian_email TEXT;
  v_guardian_exists BOOLEAN;
  v_location_count INTEGER;
BEGIN
  -- Obter o email do responsável autenticado
  SELECT email INTO v_guardian_email 
  FROM auth.users 
  WHERE id = auth.uid();
  
  -- Log inicial
  INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
  VALUES (
    'get_guardian_locations_bypass_v2_start',
    auth.uid(),
    jsonb_build_object(
      'guardian_email', v_guardian_email,
      'student_id', p_student_id,
      'timestamp', now()
    ),
    now()
  );

  -- Verificar se o usuário atual é um responsável do estudante
  SELECT EXISTS (
    SELECT 1 FROM public.guardians
    WHERE student_id = p_student_id
    AND email = v_guardian_email
    AND is_active = true
  ) INTO v_guardian_exists;
  
  -- Log da verificação
  INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
  VALUES (
    'guardian_verification',
    auth.uid(),
    jsonb_build_object(
      'guardian_exists', v_guardian_exists,
      'guardian_email', v_guardian_email,
      'student_id', p_student_id
    ),
    now()
  );

  IF NOT v_guardian_exists THEN
    -- Log do erro de permissão
    INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
    VALUES (
      'permission_denied',
      auth.uid(),
      jsonb_build_object(
        'reason', 'Guardian not found or inactive',
        'guardian_email', v_guardian_email,
        'student_id', p_student_id
      ),
      now()
    );
    
    RAISE EXCEPTION 'Você não tem permissão para ver as localizações deste estudante';
  END IF;

  -- Contar localizações disponíveis
  SELECT COUNT(*) INTO v_location_count
  FROM public.locations l
  WHERE l.user_id = p_student_id
  AND l.shared_with_guardians = true;
  
  -- Log da contagem
  INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
  VALUES (
    'location_count_check',
    auth.uid(),
    jsonb_build_object(
      'location_count', v_location_count,
      'student_id', p_student_id
    ),
    now()
  );
  
  -- Retornar as localizações do estudante
  RETURN QUERY
  SELECT
    l.id AS id,
    l.user_id AS user_id,
    l.latitude AS latitude,
    l.longitude AS longitude,
    l."timestamp" AS location_timestamp,
    l.address AS address,
    l.shared_with_guardians AS shared_with_guardians,
    COALESCE(p.full_name, 'Estudante') AS student_name
  FROM
    public.locations l
    LEFT JOIN public.profiles p ON l.user_id = p.user_id
  WHERE
    l.user_id = p_student_id
    AND l.shared_with_guardians = true
  ORDER BY
    l."timestamp" DESC;
    
  -- Log final
  INSERT INTO public.auth_logs (event_type, user_id, metadata, occurred_at)
  VALUES (
    'get_guardian_locations_bypass_v2_complete',
    auth.uid(),
    jsonb_build_object(
      'returned_count', v_location_count,
      'student_id', p_student_id
    ),
    now()
  );
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION public.get_guardian_locations_bypass_v2(UUID) TO authenticated;

-- Registrar a migração
INSERT INTO public.auth_logs (event_type, metadata, occurred_at)
VALUES (
  'migration_applied',
  jsonb_build_object(
    'migration', '20250629150000_fix_guardian_locations_rpc',
    'description', 'Corrigida função RPC com logs detalhados'
  ),
  now()
);
