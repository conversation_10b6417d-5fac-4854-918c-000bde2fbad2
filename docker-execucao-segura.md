# Guia de Execução Segura com Docker

## Requisitos Iniciais

- Docker Desktop instalado e funcionando
- Supabase CLI configurado localmente
- Node.js 20.x instalado para desenvolvimento local (opcional)

## Passos para Execução Segura

### 1. Preparação do Ambiente

```bash
# Verificar se o Supabase local está rodando (necessário para a aplicação)
supabase status

# Se não estiver, iniciar o Supabase local
supabase start
```

### 2. Variáveis de Ambiente

Antes de executar o Docker, garanta que o arquivo `.env` esteja corretamente configurado:

1. Crie um backup do arquivo `.env` atual:
   ```bash
   cp .env .env.backup
   ```

2. Verifique se todas as variáveis necessárias estão presentes e corretamente escapadas:
   - Tokens Supabase (`VITE_SUPABASE_URL`, `VITE_SUPABASE_ANON_KEY`)
   - Configurações Mapbox (`VITE_MAPBOX_TOKEN`)
   - Configurações Resend (`VITE_RESEND_API_KEY`)
   - Senhas de teste (com `$` escapados como `\$`)

3. Remova quaisquer tokens MCP sensíveis do arquivo `.env` que será usado no Docker:
   - `GITHUB_PAT`
   - `ANTHROPIC_API_KEY`
   - `OPENAI_API_KEY`
   - `GEMINI_API_KEY`
   - `OPENROUTER_API_KEY`

### 3. Construção e Execução

```bash
# Parar qualquer serviço anterior
docker compose -f docker-compose.yml down

# Construir a imagem Docker (sem usar cache)
docker compose -f docker-compose.yml build --no-cache app

# Iniciar os serviços
docker compose -f docker-compose.yml up -d
```

### 4. Verificação

Verifique se o serviço está rodando corretamente:

```bash
# Verificar logs para garantir que a aplicação iniciou sem erros
docker compose -f docker-compose.yml logs app

# Verificar se a porta 8080 está disponível
curl http://localhost:8080
```

Acesse a aplicação em: http://localhost:8080

### 5. Diagnóstico de Problemas

Se a aplicação não iniciar corretamente:

1. Verifique conexão com o Supabase:
   ```bash
   node test-db-connection.js
   ```

2. Verifique conflitos de porta:
   ```bash
   # Verificar se a porta 8080 está em uso
   netstat -ano | findstr :8080
   
   # Se necessário, matar o processo
   kill-port 8080
   ```

3. Verifique os logs do Docker:
   ```bash
   docker compose -f docker-compose.yml logs --tail=100 app
   ```

### 6. Considerações de Segurança

1. **Tokens e Senhas**:
   - Nunca armazene tokens sensíveis diretamente no Dockerfile
   - Use o arquivo `.env` com caracteres especiais devidamente escapados
   - Reveze tokens regularmente, especialmente após builds

2. **Exposição de Portas**:
   - A aplicação está configurada para expor apenas a porta 8080
   - Use `network_mode: host` apenas em desenvolvimento
   - Em produção, use redes Docker específicas e não exponha portas desnecessárias

3. **Integridade do Build**:
   - Verifique a integridade do build antes de iniciar a aplicação
   - Certifique-se de que todas as dependências estejam corretamente instaladas
   - Observe os logs para detectar vulnerabilidades reportadas pelo npm

## Protocolo para Commits Após Testes Docker

Após validar que o Docker está funcionando corretamente, siga este protocolo para salvar suas alterações:

```bash
# Ativar protocolo de segurança
mkdir -p .backup
cp .gitignore .backup/.gitignore.dev
cp .env .backup/.env.backup

# Atualizar .gitignore com regras estritas
echo ".env" >> .gitignore
echo ".env.*" >> .gitignore
echo "mcp_config.json" >> .gitignore
echo "~/.codeium/windsurf/mcp_config.json" >> .gitignore
echo "*.local" >> .gitignore
echo ".secret*" >> .gitignore

# Verificar e commitar alterações
git status
git add -A

# Adicionar mensagem de commit descritiva
git commit -m "fix: Resolve Docker build issues with TypeScript config and path aliases [fix]"

# Enviar alterações
git push

# Restaurar estado de desenvolvimento
cp .backup/.gitignore.dev .gitignore
cp .backup/.env.backup .env
```

## Execução em Desenvolvimento Local (Sem Docker)

Para desenvolvimento local sem Docker:

```bash
# Garantir que a porta 8080 esteja livre
kill-port 8080

# Iniciar o servidor de desenvolvimento
npm run dev
```

Lembre-se de verificar sempre a conexão com o Supabase local antes de iniciar o desenvolvimento.
