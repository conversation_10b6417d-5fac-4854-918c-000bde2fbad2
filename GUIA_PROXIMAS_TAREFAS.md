# 📋 Guia de Próximas Tarefas

Este guia resume as etapas recomendadas para evoluir o Locate-Family-Connect até se tornar um localizador completo em qualquer dispositivo. As tarefas foram compiladas a partir da documentação existente (*OFFLINE_FUNCTIONALITY_BASELINE.md*, *OFFLINE_ARCHITECTURE.md* e *ROADMAP_RESUMO.md*).

## 1. Fundamentos PWA e Funcionamento Offline
- Criar `public/manifest.json` com nome, ícones e configurações de tela.
- Adicionar meta tags de PWA em `index.html`.
- Implementar `public/sw.js` e registrar no app via `src/lib/sw-registration.ts` e hook `useServiceWorker.ts`.
- Desenvolver componentes `NetworkStatus` e `OfflineBanner` para indicar conectividade.

## 2. Cache de Dados e Fila de Sincronização
- Implementar utilitário `offline-storage.ts` usando IndexedDB para perfis, localizações e configurações.
- Criar hook `useOfflineData.ts` para leitura e gravação no cache.
- Desenvolver `sync-queue.ts` e `useSyncQueue.ts` para registrar ações feitas offline.
- Integrar sincronização automática quando a rede retornar, com tratamento de conflitos simples.

## 3. Rastreamento em Segundo Plano e Geofencing
- Estender `useStudentLocation.ts` para registrar localizações em intervalos regulares.
- Permitir background tracking em navegadores compatíveis (via Service Worker ou APIs nativas).
- Implementar tabela e lógica de geofences para alertas de entrada/saída de áreas definidas.
- Adicionar notificações ou emails quando limites forem cruzados.

## 4. Notificações Push e Botão de SOS
- Configurar serviço de push (ex.: Web Push ou Firebase) e solicitar permissão ao usuário.
- Criar componente de gerenciamento de notificações e preferências.
- Implementar botão de SOS no dashboard do estudante para enviar alerta imediato aos responsáveis.

## 5. Otimizações Finais e Documentação
- Cache de mapas do Mapbox para regiões visitadas com frequência.
- Métricas de uso offline e relatórios de sincronização.
- Revisar performance geral (bundle size, carregamento do mapa e uso de memória).
- Atualizar README e demais guias com instruções de instalação PWA e uso offline.

### Sequência Recomendada
1. **PWA e Service Worker** (1 semana)
2. **Cache de Dados e Fila de Sync** (2 semanas)
3. **Rastreamento em Segundo Plano e Geofencing** (2 semanas)
4. **Push Notifications e SOS** (1 semana)
5. **Polimento e Documentação** (contínuo)

Seguindo essas etapas, o sistema ganhará suporte robusto a funcionamento offline, notificações e rastreamento em múltiplos dispositivos.
