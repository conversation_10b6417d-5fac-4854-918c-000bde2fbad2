/**
 * Smart Cache Indicator - Shows cache status and controls
 * Extended with sync system integration
 */

import React from 'react';
import { Database, RefreshCw, Trash2, HardDrive, Clock, RefreshCw as SyncIcon } from 'lucide-react';
import { useSmartCache } from '@/hooks/useSmartCache';
import { useSyncQueue } from '@/hooks/useSyncQueue';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface SmartCacheIndicatorProps {
  className?: string;
  variant?: 'minimal' | 'detailed' | 'card';
  showControls?: boolean;
  showSyncStatus?: boolean;
}

export function SmartCacheIndicator({ 
  className = '', 
  variant = 'minimal',
  showControls = false,
  showSyncStatus = true
}: SmartCacheIndicatorProps) {
  const { 
    isInitialized, 
    stats, 
    lastSync, 
    lastKnownLocation,
    refreshCache, 
    syncWithServer, 
    clearCache,
    isSyncing,
    error 
  } = useSmartCache();
  
  const { 
    queueStats, 
    syncStats, 
    forceSync,
    isSyncing: isSyncQueueActive 
  } = useSyncQueue();
  
  const { isOnline } = useNetworkStatus();

  if (!isInitialized) {
    return (
      <div className={cn("flex items-center gap-1 text-xs text-gray-500", className)}>
        <Database className="w-3 h-3 animate-pulse" />
        <span>Inicializando cache...</span>
      </div>
    );
  }

  const hasPendingSync = (queueStats?.totalPending || 0) > 0;
  const syncInProgress = isSyncing || isSyncQueueActive;

  if (variant === 'minimal') {
    return (
      <div className={cn(
        "flex items-center gap-1 text-xs",
        error ? "text-red-600" : 
        syncInProgress ? "text-blue-600" :
        hasPendingSync ? "text-yellow-600" :
        "text-green-600",
        className
      )}>
        <div className="relative">
          <Database className={cn(
            "w-3 h-3",
            syncInProgress && "animate-pulse"
          )} />
          {showSyncStatus && hasPendingSync && (
            <SyncIcon className="w-2 h-2 absolute -top-1 -right-1 text-yellow-600" />
          )}
        </div>
        
        <span className="hidden sm:inline">
          Cache: {stats?.totalItems || 0} itens
        </span>
        
        {lastKnownLocation && (
          <Badge variant="secondary" className="text-xs">
            Loc. offline
          </Badge>
        )}
        
        {showSyncStatus && hasPendingSync && (
          <Badge variant="outline" className="text-xs text-yellow-600">
            {queueStats?.totalPending} pendente{(queueStats?.totalPending || 0) > 1 ? 's' : ''}
          </Badge>
        )}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={cn(
        "p-3 rounded-lg border bg-card text-card-foreground",
        className
      )}>
        <div className="flex items-center gap-2 mb-3">
          <div className="relative">
            <Database className={cn(
              "w-4 h-4",
              syncInProgress && "animate-pulse",
              error ? "text-red-600" : "text-green-600"
            )} />
            {showSyncStatus && hasPendingSync && (
              <SyncIcon className="w-2 h-2 absolute -top-1 -right-1 text-yellow-600" />
            )}
          </div>
          <span className="font-medium text-sm">Cache Inteligente</span>
          
          {syncInProgress && (
            <Badge variant="secondary" className="text-xs">
              Sincronizando
            </Badge>
          )}
        </div>

        {stats && (
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span>Total de itens:</span>
              <Badge variant="outline">{stats.totalItems}</Badge>
            </div>
            
            {Object.entries(stats.stores).map(([store, count]) => (
              <div key={store} className="flex justify-between text-gray-600">
                <span className="capitalize">{store}:</span>
                <span>{count}</span>
              </div>
            ))}
            
            {/* Sync status section */}
            {showSyncStatus && queueStats && (
              <>
                <div className="border-t pt-2 mt-2">
                  <div className="flex justify-between text-gray-600 mb-1">
                    <span>Sincronização:</span>
                  </div>
                  <div className="grid grid-cols-3 gap-1 text-center">
                    <div className="flex flex-col">
                      <span className="font-medium text-green-600 text-xs">{queueStats.totalCompleted}</span>
                      <span className="text-gray-500 text-xs">OK</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="font-medium text-yellow-600 text-xs">{queueStats.totalPending}</span>
                      <span className="text-gray-500 text-xs">Pendente</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="font-medium text-red-600 text-xs">{queueStats.totalFailed}</span>
                      <span className="text-gray-500 text-xs">Falhou</span>
                    </div>
                  </div>
                </div>
              </>
            )}
            
            {lastSync && (
              <div className="flex items-center gap-1 text-gray-600 mt-2">
                <Clock className="w-3 h-3" />
                <span>Sincronizado: {lastSync.toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="text-red-600 text-xs mt-2">
            Erro: {error}
          </div>
        )}

        {showControls && (
          <div className="flex gap-2 mt-3">
            <Button
              size="sm"
              variant="outline"
              onClick={refreshCache}
              className="text-xs h-7"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Atualizar
            </Button>
            
            {isOnline && (
              <>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={syncWithServer}
                  disabled={syncInProgress}
                  className="text-xs h-7"
                >
                  <Database className={cn(
                    "w-3 h-3 mr-1",
                    syncInProgress && "animate-spin"
                  )} />
                  Cache Sync
                </Button>
                
                {showSyncStatus && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={forceSync}
                    disabled={syncInProgress}
                    className="text-xs h-7"
                  >
                    <SyncIcon className={cn(
                      "w-3 h-3 mr-1",
                      syncInProgress && "animate-spin"
                    )} />
                    Sync Queue
                  </Button>
                )}
              </>
            )}
            
            <Button
              size="sm"
              variant="outline"
              onClick={clearCache}
              className="text-xs h-7 text-red-600 hover:text-red-700"
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Limpar
            </Button>
          </div>
        )}
      </div>
    );
  }

  // Variant 'card'
  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          <div className="relative">
            <Database className={cn(
              "w-4 h-4",
              syncInProgress && "animate-pulse",
              error ? "text-red-600" : "text-green-600"
            )} />
            {showSyncStatus && hasPendingSync && (
              <SyncIcon className="w-2 h-2 absolute -top-1 -right-1 text-yellow-600" />
            )}
          </div>
          Cache Inteligente
          
          {error && <Badge variant="destructive">Erro</Badge>}
          {syncInProgress && <Badge variant="secondary">Sincronizando</Badge>}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {stats && (
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex items-center gap-2">
              <HardDrive className="w-3 h-3 text-gray-500" />
              <span>{stats.totalItems} itens</span>
            </div>
            
            {lastKnownLocation && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  Localização offline
                </Badge>
              </div>
            )}
            
            {showSyncStatus && queueStats && (
              <>
                <div className="col-span-2 text-xs text-gray-600">
                  <div className="flex justify-between mb-1">
                    <span>Status de Sincronização:</span>
                  </div>
                  <div className="flex gap-2">
                    <span className="text-green-600">{queueStats.totalCompleted} OK</span>
                    <span className="text-yellow-600">{queueStats.totalPending} pendente</span>
                    {queueStats.totalFailed > 0 && (
                      <span className="text-red-600">{queueStats.totalFailed} falhou</span>
                    )}
                  </div>
                </div>
              </>
            )}
            
            {lastSync && (
              <div className="col-span-2 flex items-center gap-2 text-xs text-gray-600">
                <Clock className="w-3 h-3" />
                <span>Última sincronização: {lastSync.toLocaleString()}</span>
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="text-red-600 text-sm">
            {error}
          </div>
        )}

        {showControls && (
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={refreshCache}
              className="flex-1"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Atualizar
            </Button>
            
            {isOnline && (
              <Button
                size="sm"
                variant="outline"
                onClick={syncWithServer}
                disabled={syncInProgress}
                className="flex-1"
              >
                <Database className={cn(
                  "w-3 h-3 mr-1",
                  syncInProgress && "animate-spin"
                )} />
                Sincronizar
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
