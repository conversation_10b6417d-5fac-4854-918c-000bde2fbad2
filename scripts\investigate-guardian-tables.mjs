#!/usr/bin/env node

/**
 * Script de Investigação - Tabelas de Guardians
 * 
 * Objetivo: Investigar todas as tabelas relacionadas a guardians e identificar 
 *           qual tabela contém as vincula<PERSON><PERSON><PERSON> Frank-Estudantes
 * Data: 24/06/2025
 */

import { createClient } from '@supabase/supabase-js';
import 'dotenv/config';

const supabaseUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MDk3NzksImV4cCI6MjA1ODk4NTc3OX0.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

const FRANK_EMAIL = '<EMAIL>';
const MAURICIO_EMAIL = '<EMAIL>';
const FRANK_ID = '40b1ac70-e024-4a9a-ba9c-b7b42f8b8f32'; // ID do Frank no sistema

console.log('🔍 INVESTIGAÇÃO - TABELAS DE GUARDIANS');
console.log('='.repeat(60));
console.log(`👤 Usuário: ${FRANK_EMAIL}`);
console.log(`📧 Estudante: ${MAURICIO_EMAIL}`);
console.log('='.repeat(60));

async function investigateGuardianTables() {
  try {
    
    console.log('\n1️⃣ TABELA: guardians');
    console.log('-'.repeat(40));
    
    try {
      const { data: guardians, error: guardiansError } = await supabase
        .from('guardians')
        .select('*')
        .eq('email', FRANK_EMAIL);
      
      if (guardiansError) {
        console.log('❌ Erro:', guardiansError.message);
      } else {
        console.log(`✅ Registros encontrados: ${guardians?.length || 0}`);
        if (guardians && guardians.length > 0) {
          guardians.forEach((g, i) => {
            console.log(`   Guardian ${i + 1}:`, JSON.stringify(g, null, 2));
          });
        }
      }
    } catch (error) {
      console.log('❌ Exceção:', error.message);
    }

    console.log('\n2️⃣ TABELA: guardian_profiles');
    console.log('-'.repeat(40));
    
    try {
      const { data: guardianProfiles, error: profilesError } = await supabase
        .from('guardian_profiles')
        .select('*')
        .eq('email', FRANK_EMAIL);
      
      if (profilesError) {
        console.log('❌ Erro:', profilesError.message);
      } else {
        console.log(`✅ Registros encontrados: ${guardianProfiles?.length || 0}`);
        if (guardianProfiles && guardianProfiles.length > 0) {
          guardianProfiles.forEach((g, i) => {
            console.log(`   Profile ${i + 1}:`, JSON.stringify(g, null, 2));
          });
        }
      }
    } catch (error) {
      console.log('❌ Exceção:', error.message);
    }

    console.log('\n3️⃣ TABELA: student_guardian_relationships');
    console.log('-'.repeat(40));
    
    try {
      // Primeiro, buscar pelo guardian_id (se existir campo)
      const { data: relationships1, error: rel1Error } = await supabase
        .from('student_guardian_relationships')
        .select('*')
        .eq('guardian_id', FRANK_ID);
      
      if (rel1Error) {
        console.log('❌ Erro (guardian_id):', rel1Error.message);
      } else {
        console.log(`✅ Registros por guardian_id: ${relationships1?.length || 0}`);
        if (relationships1 && relationships1.length > 0) {
          relationships1.forEach((r, i) => {
            console.log(`   Relacionamento ${i + 1}:`, JSON.stringify(r, null, 2));
          });
        }
      }

      // Tentar por email
      const { data: relationships2, error: rel2Error } = await supabase
        .from('student_guardian_relationships')
        .select('*')
        .eq('guardian_email', FRANK_EMAIL);
      
      if (rel2Error) {
        console.log('❌ Erro (guardian_email):', rel2Error.message);
      } else {
        console.log(`✅ Registros por guardian_email: ${relationships2?.length || 0}`);
        if (relationships2 && relationships2.length > 0) {
          relationships2.forEach((r, i) => {
            console.log(`   Relacionamento ${i + 1}:`, JSON.stringify(r, null, 2));
          });
        }
      }
    } catch (error) {
      console.log('❌ Exceção:', error.message);
    }

    console.log('\n4️⃣ VERIFICAR PERFIL DO FRANK');
    console.log('-'.repeat(40));
    
    try {
      const { data: frankProfile, error: frankError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', FRANK_EMAIL)
        .single();
      
      if (frankError) {
        console.log('❌ Erro:', frankError.message);
      } else {
        console.log('✅ Perfil do Frank encontrado:');
        console.log(JSON.stringify(frankProfile, null, 2));
      }
    } catch (error) {
      console.log('❌ Exceção:', error.message);
    }

    console.log('\n5️⃣ VERIFICAR PERFIL DO MAURÍCIO');
    console.log('-'.repeat(40));
    
    try {
      const { data: mauricioProfile, error: mauricioError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', MAURICIO_EMAIL)
        .single();
      
      if (mauricioError) {
        console.log('❌ Erro:', mauricioError.message);
      } else {
        console.log('✅ Perfil do Maurício encontrado:');
        console.log(JSON.stringify(mauricioProfile, null, 2));
      }
    } catch (error) {
      console.log('❌ Exceção:', error.message);
    }

    console.log('\n6️⃣ VERIFICAR DADOS NA TABELA account_deletion_requests');
    console.log('-'.repeat(40));
    
    try {
      const { data: deletionRequest, error: delError } = await supabase
        .from('account_deletion_requests')
        .select('*')
        .eq('student_email', MAURICIO_EMAIL);
      
      if (delError) {
        console.log('❌ Erro:', delError.message);
      } else {
        console.log(`✅ Solicitações encontradas: ${deletionRequest?.length || 0}`);
        if (deletionRequest && deletionRequest.length > 0) {
          deletionRequest.forEach((req, i) => {
            console.log(`   Solicitação ${i + 1}:`, {
              id: req.id,
              student_name: req.student_name,
              status: req.status,
              requested_at: req.requested_at
            });
          });
        }
      }
    } catch (error) {
      console.log('❌ Exceção:', error.message);
    }

    console.log('\n7️⃣ COMO O DASHBOARD CONSEGUE MOSTRAR OS ESTUDANTES?');
    console.log('-'.repeat(40));
    
    // Investigar todas as tabelas que podem ter a vinculação
    const tablesToCheck = [
      'students',
      'guardian_profiles', 
      'student_guardian_relationships'
    ];

    for (const table of tablesToCheck) {
      try {
        console.log(`\n   🔍 Verificando tabela: ${table}`);
        
        // Tentar diferentes campos de busca
        const searches = [
          { field: 'guardian_email', value: FRANK_EMAIL },
          { field: 'email', value: FRANK_EMAIL },
          { field: 'guardian_id', value: FRANK_ID },
          { field: 'created_by', value: FRANK_ID }
        ];

        for (const search of searches) {
          try {
            const { data, error } = await supabase
              .from(table)
              .select('*')
              .eq(search.field, search.value)
              .limit(5);
            
            if (!error && data && data.length > 0) {
              console.log(`   ✅ ${table}.${search.field}: ${data.length} registros`);
              data.forEach((item, i) => {
                console.log(`      Item ${i + 1}:`, JSON.stringify(item, null, 2));
              });
            }
          } catch (e) {
            // Campo não existe, ignorar
          }
        }
      } catch (error) {
        console.log(`   ❌ Erro na tabela ${table}:`, error.message);
      }
    }

    console.log('\n8️⃣ DIAGNÓSTICO FINAL');
    console.log('-'.repeat(40));
    
    console.log('📊 RESUMO:');
    console.log('- Frank aparece nos estudantes da página de perfil ✅');
    console.log('- Solicitação do Maurício existe no banco ✅');
    console.log('- Solicitações NÃO aparecem na interface ❌');
    console.log('- Necessário identificar tabela correta de vinculação');

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar investigação
investigateGuardianTables().then(() => {
  console.log('\n✅ Investigação concluída!');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
}); 