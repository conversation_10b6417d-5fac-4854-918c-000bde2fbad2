import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, confirmationUrl, fullName, userType } = await req.json()

    // Validate required fields
    if (!email || !confirmationUrl) {
      return new Response(
        JSON.stringify({ error: 'Email and confirmationUrl are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const resendApiKey = Deno.env.get('RESEND_API_KEY')
    if (!resendApiKey) {
      throw new Error('RESEND_API_KEY not configured')
    }

    // Generate email template
    const emailSubject = '✅ Confirme sua conta - Sistema Monitore'
    const emailHtml = generateConfirmationEmailHtml(confirmationUrl, fullName, userType, email)
    const emailText = generateConfirmationEmailText(confirmationUrl, fullName, userType)

    // Send email via Resend
    const resendResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'Sistema Monitore <<EMAIL>>',
        to: [email],
        subject: emailSubject,
        html: emailHtml,
        text: emailText,
        reply_to: '<EMAIL>',
        tags: [
          {
            name: 'type',
            value: 'email_confirmation'
          },
          {
            name: 'user_type', 
            value: userType || 'unknown'
          }
        ]
      }),
    })

    if (!resendResponse.ok) {
      const resendError = await resendResponse.text()
      console.error('Resend API error:', resendError)
      throw new Error(`Failed to send email via Resend: ${resendError}`)
    }

    const resendData = await resendResponse.json()
    console.log('Email sent successfully via Resend:', resendData)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Confirmation email sent successfully',
        emailId: resendData.id 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in send-confirmation-email function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to send confirmation email', 
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

function generateConfirmationEmailHtml(
  confirmationUrl: string, 
  fullName: string, 
  userType: string,
  email: string
): string {
  const userTypeDisplay = userType === 'student' ? 'Estudante' : 'Responsável'
  const greeting = fullName ? `Olá, ${fullName}!` : 'Olá!'
  
  return `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirme sua conta - Sistema Monitore</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header { 
            text-align: center; 
            padding: 20px 0; 
            border-bottom: 2px solid #e5e7eb; 
            margin-bottom: 30px;
        }
        .logo { 
            font-size: 28px; 
            font-weight: bold; 
            color: #1f2937;
            margin-bottom: 10px;
        }
        .subtitle { 
            color: #6b7280; 
            font-size: 16px;
        }
        .content { 
            padding: 20px 0; 
        }
        .highlight-box { 
            background: #f3f4f6; 
            border-left: 4px solid #3b82f6; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px;
        }
        .btn { 
            display: inline-block; 
            background: #3b82f6; 
            color: white; 
            padding: 16px 32px; 
            text-decoration: none; 
            border-radius: 8px; 
            font-weight: bold; 
            text-align: center; 
            margin: 20px 0;
        }
        .btn:hover { 
            background: #2563eb; 
        }
        .user-info {
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .footer { 
            text-align: center; 
            padding: 30px 0; 
            border-top: 1px solid #e5e7eb; 
            margin-top: 40px; 
            color: #6b7280; 
            font-size: 14px;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            body { padding: 10px; }
            .btn { display: block; margin: 20px 0; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">📍 Sistema Monitore</div>
        <div class="subtitle">Localização Familiar Segura</div>
    </div>

    <div class="content">
        <h2>✅ Confirme sua conta</h2>
        
        <p>${greeting}</p>
        
        <p>Sua conta foi criada com sucesso! Para começar a usar o Sistema Monitore, você precisa confirmar seu email.</p>
        
        <div class="user-info">
            <strong>📧 Email:</strong> ${email}<br>
            <strong>👤 Tipo de conta:</strong> ${userTypeDisplay}
        </div>

        <div class="highlight-box">
            <p><strong>🎯 Próximo passo:</strong></p>
            <p>Clique no botão abaixo para confirmar sua conta e começar a usar todas as funcionalidades do sistema:</p>
            
            <a href="${confirmationUrl}" class="btn">
                ✅ Confirmar minha conta
            </a>
        </div>

        <div class="warning">
            <p><strong>⏰ Importante:</strong> Este link de confirmação expira em 24 horas. Se você não confirmar até lá, será necessário solicitar um novo email de confirmação.</p>
        </div>

        <p><strong>O que você pode fazer após confirmar:</strong></p>
        ${userType === 'student' ? `
        <ul>
            <li>📱 Compartilhar sua localização com responsáveis</li>
            <li>🔒 Controlar quando e com quem compartilhar</li>
            <li>🔗 Aceitar convites de vínculos familiares</li>
            <li>📊 Ver histórico de suas localizações</li>
        </ul>
        ` : `
        <ul>
            <li>👥 Visualizar localização dos estudantes vinculados</li>
            <li>🔗 Solicitar vínculos com estudantes</li>
            <li>📍 Ver estudantes no mapa em tempo real</li>
            <li>⚡ Enviar notificações e alertas</li>
        </ul>
        `}

        <p>Se você não criou esta conta, pode ignorar este email com segurança.</p>

        <p><strong>Precisa de ajuda?</strong><br>
        Entre em contato conosco: <a href="mailto:<EMAIL>"><EMAIL></a></p>
    </div>

    <div class="footer">
        <p>© 2025 Sistema Monitore - Localização Familiar Segura</p>
        <p>Este email foi enviado para ${email}</p>
        <p>Se você não deseja mais receber estes emails, <a href="mailto:<EMAIL>">clique aqui</a></p>
    </div>
</body>
</html>`
}

function generateConfirmationEmailText(
  confirmationUrl: string, 
  fullName: string, 
  userType: string
): string {
  const userTypeDisplay = userType === 'student' ? 'Estudante' : 'Responsável'
  const greeting = fullName ? `Olá, ${fullName}!` : 'Olá!'
  
  return `
${greeting}

✅ CONFIRME SUA CONTA - SISTEMA MONITORE

Sua conta foi criada como ${userTypeDisplay}!

Para começar a usar o Sistema Monitore, confirme sua conta clicando no link abaixo:

${confirmationUrl}

⏰ IMPORTANTE: Este link expira em 24 horas.

${userType === 'student' ? `
Após confirmar, você poderá:
• Compartilhar sua localização com responsáveis
• Controlar quando e com quem compartilhar
• Aceitar convites de vínculos familiares
• Ver histórico de suas localizações
` : `
Após confirmar, você poderá:
• Visualizar localização dos estudantes vinculados  
• Solicitar vínculos com estudantes
• Ver estudantes no mapa em tempo real
• Enviar notificações e alertas
`}

Se você não criou esta conta, ignore este email.

Precisa de ajuda? Entre em contato: <EMAIL>

---
© 2025 Sistema Monitore - Localização Familiar Segura
`
} 