# Service Worker Cache Troubleshooting

Quando ocorre um novo deployment ou um erro de rede impede o carregamento de chunks JS, o service worker pode retornar uma resposta 503 em cache. Isso se manifesta como erros `Failed to fetch dynamically imported module` no console do navegador.

---

## 🚨 Sintomas

* Console exibe:

  ```
  [SW] Cache-first failed: TypeError: Failed to fetch
  ```
* Painel de rede mostra:

  * Arquivos `.js` com status `503 Service Unavailable`
* A aplicação:

  * Não carrega ou trava com erro
  * React lança erro: `Failed to fetch dynamically imported module`

---

## ✅ Etapas de Correção

### 1. **Limpar cache via utilitário interno**

Se o app ainda carrega parcialmente:

```ts
import { clearAppCache } from '@/lib/utils/cache-manager';

clearAppCache(); // Limpa tokens e dados do Service Worker
```

* Após executar, a página será recarregada e os assets mais recentes serão baixados.

---

### 2. **Limpeza manual pelo navegador**

Se o app não carrega:

1. Abra **DevTools** (F12) → aba **Application**
2. Vá para **Storage** e clique em **Clear site data**
3. Em **Service Workers**, clique em **Unregister**
4. Recarregue a página (`Ctrl + Shift + R` ou `Cmd + Shift + R`)

---

## ℹ️ Dica Extra: Force update do Service Worker

Para garantir atualização no próximo deploy, use este padrão no seu `registerServiceWorker.ts`:

```ts
navigator.serviceWorker.getRegistrations().then(registrations => {
  registrations.forEach(reg => reg.update());
});
```

---

## 📌 Observação

* Esse problema ocorre com **estratégia cache-first** quando a rede falha antes de um SW atualizado ser instalado.
* Pode ser minimizado usando estratégias **network-first** para arquivos críticos ou aplicando versionamento de cache.

---

