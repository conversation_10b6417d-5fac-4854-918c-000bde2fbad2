-- =====================================================
-- CORRIGIR RLS DA TABELA PROFILES 
-- Com política explícita para verificação de CPF
-- =====================================================

-- Reabilitar RLS na tabela profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Remover todas as políticas existentes para evitar conflitos
DROP POLICY IF EXISTS "Allow CPF verification" ON public.profiles;
DROP POLICY IF EXISTS "Any authenticated user can select profiles" ON public.profiles;
DROP POLICY IF EXISTS "Authenticated users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Authenticated users can view their own profile." ON public.profiles;
DROP POLICY IF EXISTS "Profiles can view their own record" ON public.profiles;
DROP POLICY IF EXISTS "Service role full access" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;

-- Criar políticas claras e específicas
CREATE POLICY "profiles_select_own" ON public.profiles
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "profiles_insert_own" ON public.profiles
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "profiles_update_own" ON public.profiles
  FOR UPDATE TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Política específica para verificação de CPF (MUITO PERMISSIVA)
CREATE POLICY "profiles_cpf_verification" ON public.profiles
  FOR SELECT TO authenticated
  USING (true);

-- Política de service role (para admin/sistema)
CREATE POLICY "profiles_service_role_all" ON public.profiles
  FOR ALL TO service_role
  USING (true)
  WITH CHECK (true);
