-- ==========================================
-- SISTEMA DE SOLICITAÇÕES DE EXCLUSÃO DE CONTA - VERSÃO FINAL
-- Data: 24/06/2025
-- Autor: Sistema de Harmonização
-- Objetivo: Sincronizar banco com interface
-- Fix: Corrigir estrutura real da tabela guardians (usa email, não guardian_id)
-- ==========================================

-- 1. CRIAR TABELA SE NÃO EXISTIR
CREATE TABLE IF NOT EXISTS "public"."account_deletion_requests" (
    "id" UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    "student_id" UUID NOT NULL REFERENCES "public"."profiles"("id") ON DELETE CASCADE,
    "student_email" TEXT NOT NULL,
    "student_name" TEXT NOT NULL,
    "reason" TEXT,
    "status" TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    "requested_at" TIMESTAMPTZ DEFAULT now(),
    "processed_at" TIMESTAMPTZ,
    "processed_by_guardian_email" TEXT,
    "guardian_notes" TEXT,
    "created_at" TIMESTAMPTZ DEFAULT now(),
    "updated_at" TIMESTAMPTZ DEFAULT now()
);

-- 2. INDICES PARA PERFORMANCE
CREATE INDEX IF NOT EXISTS "idx_account_deletion_requests_student_id" 
ON "public"."account_deletion_requests"("student_id");

CREATE INDEX IF NOT EXISTS "idx_account_deletion_requests_status" 
ON "public"."account_deletion_requests"("status");

-- 3. TRIGGER PARA UPDATED_AT
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_account_deletion_requests_updated_at 
ON "public"."account_deletion_requests";

CREATE TRIGGER update_account_deletion_requests_updated_at
    BEFORE UPDATE ON "public"."account_deletion_requests"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 4. POLÍTICAS RLS
ALTER TABLE "public"."account_deletion_requests" ENABLE ROW LEVEL SECURITY;

-- Política para estudantes verem apenas suas próprias solicitações
DROP POLICY IF EXISTS "Students can view own deletion requests" 
ON "public"."account_deletion_requests";

CREATE POLICY "Students can view own deletion requests"
ON "public"."account_deletion_requests"
FOR SELECT
TO authenticated
USING (
    student_id = auth.uid()
);

-- Política para estudantes criarem suas próprias solicitações
DROP POLICY IF EXISTS "Students can create own deletion requests" 
ON "public"."account_deletion_requests";

CREATE POLICY "Students can create own deletion requests"
ON "public"."account_deletion_requests"
FOR INSERT
TO authenticated
WITH CHECK (
    student_id = auth.uid()
);

-- Política para responsáveis verem solicitações de seus estudantes
DROP POLICY IF EXISTS "Guardians can view student deletion requests" 
ON "public"."account_deletion_requests";

CREATE POLICY "Guardians can view student deletion requests"
ON "public"."account_deletion_requests"
FOR SELECT
TO authenticated
USING (
    student_id IN (
        SELECT g.student_id 
        FROM "public"."guardians" g
        JOIN "public"."profiles" p ON p.id = auth.uid()
        WHERE g.email = p.email
        AND g.is_active = TRUE
    )
);

-- Política para responsáveis atualizarem (processarem) solicitações
DROP POLICY IF EXISTS "Guardians can process deletion requests" 
ON "public"."account_deletion_requests";

CREATE POLICY "Guardians can process deletion requests"
ON "public"."account_deletion_requests"
FOR UPDATE
TO authenticated
USING (
    student_id IN (
        SELECT g.student_id 
        FROM "public"."guardians" g
        JOIN "public"."profiles" p ON p.id = auth.uid()
        WHERE g.email = p.email
        AND g.is_active = TRUE
    )
);

-- 5. FUNÇÃO RPC PARA BUSCAR SOLICITAÇÕES DO RESPONSÁVEL
CREATE OR REPLACE FUNCTION "public"."get_guardian_deletion_requests"()
RETURNS TABLE (
    id UUID,
    student_id UUID,
    student_email TEXT,
    student_name TEXT,
    reason TEXT,
    status TEXT,
    requested_at TIMESTAMPTZ,
    processed_at TIMESTAMPTZ,
    guardian_notes TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    guardian_email TEXT;
BEGIN
    -- Obter email do responsável atual
    SELECT email INTO guardian_email
    FROM "public"."profiles"
    WHERE id = auth.uid();

    IF guardian_email IS NULL THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT 
        adr.id,
        adr.student_id,
        adr.student_email,
        adr.student_name,
        adr.reason,
        adr.status,
        adr.requested_at,
        adr.processed_at,
        adr.guardian_notes,
        adr.created_at,
        adr.updated_at
    FROM "public"."account_deletion_requests" adr
    WHERE adr.student_id IN (
        SELECT g.student_id 
        FROM "public"."guardians" g
        WHERE g.email = guardian_email
        AND g.is_active = TRUE
    )
    ORDER BY adr.requested_at DESC;
END;
$$;

-- 6. FUNÇÃO RPC PARA PROCESSAR SOLICITAÇÕES
CREATE OR REPLACE FUNCTION "public"."process_account_deletion_request"(
    p_request_id UUID,
    p_action TEXT,
    p_guardian_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    guardian_email TEXT;
    request_student_id UUID;
BEGIN
    -- Obter email do responsável atual
    SELECT email INTO guardian_email
    FROM "public"."profiles"
    WHERE id = auth.uid();

    IF guardian_email IS NULL THEN
        RAISE EXCEPTION 'Guardian profile not found';
    END IF;

    -- Verificar se a solicitação existe e obter student_id
    SELECT student_id INTO request_student_id
    FROM "public"."account_deletion_requests"
    WHERE id = p_request_id;

    IF request_student_id IS NULL THEN
        RAISE EXCEPTION 'Request not found';
    END IF;

    -- Verificar se o responsável tem permissão
    IF NOT EXISTS (
        SELECT 1 FROM "public"."guardians" g
        WHERE g.email = guardian_email
        AND g.student_id = request_student_id
        AND g.is_active = TRUE
    ) THEN
        RAISE EXCEPTION 'Unauthorized: You do not have permission to process this request';
    END IF;

    -- Atualizar a solicitação
    UPDATE "public"."account_deletion_requests"
    SET 
        status = CASE 
            WHEN p_action = 'approve' THEN 'approved'
            WHEN p_action = 'reject' THEN 'rejected'
            ELSE status
        END,
        processed_at = now(),
        processed_by_guardian_email = guardian_email,
        guardian_notes = p_guardian_notes
    WHERE id = p_request_id;

    -- Se aprovado, marcar o perfil do estudante para exclusão
    IF p_action = 'approve' THEN
        UPDATE "public"."profiles"
        SET 
            updated_at = now(),
            user_metadata = COALESCE(user_metadata, '{}')::jsonb || '{"deletion_approved": true}'::jsonb
        WHERE id = request_student_id;
    END IF;

    RETURN TRUE;
END;
$$;

-- 7. GRANTS PARA EXECUÇÃO DAS FUNÇÕES
GRANT EXECUTE ON FUNCTION "public"."get_guardian_deletion_requests"() TO authenticated;
GRANT EXECUTE ON FUNCTION "public"."process_account_deletion_request"(UUID, TEXT, TEXT) TO authenticated; 