# 📱 Funcionalidades Offline do Locate-Family-Connect

## 🎯 Visão Geral

O sistema Locate-Family-Connect possui um robusto sistema de funcionalidades offline que permite aos usuários continuar usando grande parte da aplicação mesmo sem conexão com a internet.

---

## 🔧 Tecnologias Utilizadas

### 1. **Service Worker**
- **Arquivo**: `src/lib/offline/service-worker-registration.ts`
- **Função**: Cache de recursos estáticos (CSS, JS, imagens)
- **Estratégia**: Cache-first com fallback para rede
- **Auto-update**: Verifica atualizações automaticamente

### 2. **IndexedDB Manager**
- **Arquivo**: `src/lib/offline/indexed-db-manager.ts`
- **Função**: Armazenamento local estruturado
- **Recursos**: TTL (Time To Live), versionamento, limpeza automática
- **Stores**: locations, profiles, guardians, settings, api_cache

### 3. **Smart Cache System**
- **Arquivo**: `src/lib/offline/smart-cache.ts`
- **Hook**: `src/hooks/useSmartCache.ts`
- **Função**: Cache inteligente de dados do usuário
- **Sync**: Sincronização automática quando volta online

### 4. **Sync Queue**
- **Arquivo**: `src/lib/offline/sync-queue.ts`
- **Hook**: `src/hooks/useSyncQueue.ts`
- **Função**: Fila de ações para executar quando voltar online
- **Priorização**: Sistema de prioridades (high, medium, low)

---

## ✅ Funcionalidades Disponíveis Offline

### 📍 **Localização**
- ✅ Visualizar último local conhecido
- ✅ Histórico de localizações (últimas 50)
- ✅ Obter localização atual via GPS
- ✅ Enviar localização (enfileira quando offline)

### 👤 **Perfil do Usuário**
- ✅ Visualizar dados do perfil
- ✅ Editar informações básicas (salvo localmente)
- ✅ Alterar foto do perfil (fica na fila)
- ❌ Salvar alterações no servidor

### 👨‍👩‍👧‍👦 **Gestão de Responsáveis**
- ✅ Visualizar lista de responsáveis
- ✅ Ver informações de contato
- ❌ Adicionar novos responsáveis
- ❌ Remover responsáveis

### 📧 **Comunicação**
- ✅ Rascunhar mensagens
- ✅ Ver histórico de mensagens enviadas
- ❌ Enviar emails/SMS (fica na fila)

### ⚙️ **Configurações**
- ✅ Alterar idioma (pt-BR/en-GB)
- ✅ Configurações de tema
- ✅ Preferências de notificação
- ✅ Configurações de privacidade

### 🗺️ **Mapas**
- ✅ Visualizar mapa offline (tiles em cache)
- ✅ Mostrar última localização conhecida
- ❌ Buscar endereços
- ❌ Atualizações em tempo real

---

## 🔄 Sistema de Sincronização

### **Quando Volta Online**
1. **Auto-detecção**: Hook `useNetworkStatus` detecta conexão
2. **Sync automático**: Processa fila de ações pendentes
3. **Merge inteligente**: Resolve conflitos de dados
4. **Notificação**: Informa usuário sobre sincronização

### **Tipos de Ações na Fila**
```typescript
- LOCATION_UPDATE    // Atualizar localização
- LOCATION_SHARE     // Enviar localização
- PROFILE_UPDATE     // Atualizar perfil
- SEND_EMAIL         // Enviar email
- SEND_SMS           // Enviar SMS
- ADD_GUARDIAN       // Adicionar responsável
- REMOVE_GUARDIAN    // Remover responsável
```

### **Prioridades**
- 🔴 **High**: Localização de emergência, alertas críticos
- 🟡 **Medium**: Atualizações de perfil, mensagens
- 🟢 **Low**: Logs, analytics, configurações

---

## 📊 Indicadores Visuais

### **Componentes de Status**
- `NetworkStatusIndicator`: Ícone de conexão no header
- `OfflineStatusBanner`: Banner quando offline
- `SyncStatusIndicator`: Status de sincronização
- `SmartCacheIndicator`: Indicador de cache

### **Estados Visuais**
- 🟢 **Online**: Conexão estável
- 🟡 **Sincronizando**: Processando fila
- 🔴 **Offline**: Sem conexão
- ⚡ **Cache**: Dados em cache

---

## 🛠️ Hooks Disponíveis

### **useNetworkStatus**
```typescript
const { isOnline, connectionType } = useNetworkStatus();
```

### **useSmartCache**
```typescript
const { 
  lastKnownLocation, 
  userProfile, 
  syncWithServer,
  clearCache 
} = useSmartCache();
```

### **useSyncQueue**
```typescript
const { 
  enqueueAction, 
  forceSync, 
  queueStats,
  isSyncing 
} = useSyncQueue();
```

### **useServiceWorker**
```typescript
const { 
  isRegistered, 
  updateAvailable, 
  applyUpdate 
} = useServiceWorker();
```

---

## 🔧 Configuração e Manutenção

### **Cache Settings**
- **TTL padrão**: 24 horas
- **Limpeza automática**: A cada 6 horas
- **Tamanho máximo**: 50MB por store
- **Expiração**: Baseada em timestamp + TTL

### **Sync Settings**
- **Retry automático**: 3 tentativas
- **Backoff exponencial**: 1s, 2s, 4s
- **Timeout**: 30 segundos por ação
- **Batch size**: 10 ações por vez

### **Service Worker Updates**
- **Verificação**: A cada carregamento da página
- **Aplicação**: Manual (usuário confirma)
- **Rollback**: Automático em caso de erro

---

## 🚨 Limitações Conhecidas

### **Não Funciona Offline**
- ❌ Autenticação inicial (login/registro)
- ❌ Recuperação de senha
- ❌ Convites para novos usuários
- ❌ Pagamentos/transações
- ❌ Relatórios em tempo real
- ❌ Chamadas de vídeo/áudio

### **Limitações de Armazenamento**
- **IndexedDB**: ~250MB (varia por navegador)
- **Cache API**: ~2GB (compartilhado)
- **LocalStorage**: 5-10MB (configurações apenas)

### **Restrições de API**
- GPS funciona offline
- Mapas requerem tiles em cache
- Geocoding requer conexão
- Push notifications precisam de service worker ativo

---

## 📋 Troubleshooting

### **Cache Corrompido**
```typescript
import { clearAppCache } from '@/lib/utils/cache-manager';
clearAppCache(); // Limpa todo o cache
```

### **Sync Travado**
```typescript
const { forceSync, clearQueue } = useSyncQueue();
await clearQueue(); // Limpa fila
await forceSync();  // Força sincronização
```

### **Service Worker Inativo**
1. Abrir DevTools → Application → Service Workers
2. Clicar "Unregister"
3. Recarregar página

---

## 🔮 Roadmap de Melhorias

### **Próximas Funcionalidades**
- [ ] Background sync para localizações
- [ ] Offline maps com tiles pré-baixados
- [ ] Cache de rotas frequentes
- [ ] Modo de emergência offline
- [ ] Sincronização diferencial
- [ ] Compressão de dados em cache

### **Otimizações Planejadas**
- [ ] Cache predictivo baseado em uso
- [ ] Limpeza inteligente por frequência
- [ ] Sync incremental de grandes datasets
- [ ] Detecção de conexão limitada
- [ ] Priority lane para emergências

---

## 📚 Recursos Adicionais

- **Service Worker API**: [MDN Documentation](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- **IndexedDB**: [MDN Documentation](https://developer.mozilla.org/en-US/docs/Web/API/IndexedDB_API)
- **Cache API**: [MDN Documentation](https://developer.mozilla.org/en-US/docs/Web/API/Cache)
- **Network Information API**: [MDN Documentation](https://developer.mozilla.org/en-US/docs/Web/API/Network_Information_API)

---
*Última atualização: Julho 2025*