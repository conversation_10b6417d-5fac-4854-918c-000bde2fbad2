{"common": {"loading": "Carregando...", "processing": "Processando...", "error": "Erro", "success": "Sucesso", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "save": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir", "close": "<PERSON><PERSON><PERSON>", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "search": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Filtrar", "clear": "Limpar", "apply": "Aplicar", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "attention": "Atenção", "actions": "Ações", "logout": "<PERSON><PERSON>", "login": "Entrar", "register": "Cadastrar", "profile": "Perfil", "settings": "Configurações", "help": "<PERSON><PERSON><PERSON>", "dashboard": "Dashboard", "home": "Início", "today": "Hoje", "notProvided": "Não informado", "user": "<PERSON><PERSON><PERSON><PERSON>", "undefined": "<PERSON><PERSON> definido"}, "navigation": {"applicationMonitor": "Aplicativo Monitor", "monitoring": "Monitoring", "familyLocationSafety": "Localização Familiar Segura", "myMap": "<PERSON><PERSON>", "myGuardians": "<PERSON><PERSON>", "studentMap": "Mapa dos Estudantes", "apiDocs": "API Docs"}, "auth": {"login": {"title": "Entrar", "subtitle": "Acesse sua conta", "email": "Email", "password": "<PERSON><PERSON>", "forgotPassword": "Esque<PERSON>u a senha?", "signIn": "Entrar", "noAccount": "Não tem uma conta?", "createAccount": "C<PERSON><PERSON> conta"}, "forgotPassword": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "selectProfile": "Escolha seu <PERSON>l", "changeAccountType": "Alterar tipo de conta", "userTypes": {"student": "Estudante", "parent": "Responsável", "guardian": "Responsável", "admin": "Administrador", "developer": "<PERSON><PERSON><PERSON><PERSON>"}, "userTypeSelector": {"title": "Qual é o seu perfil?", "subtitle": "Escolha com cuidado - isso definir<PERSON> todas as funcionalidades disponíveis para você", "selected": "Selecionado", "featuresLabel": "Funcionalidades:", "examplesLabel": "Exemplos de uso:", "continueAs": "Continuar como {{type}}", "student": {"title": "Sou Estudante", "description": "Compartilho minha localização com meus responsáveis", "features": {"shareLocation": "Compartilha sua localização", "privacyControl": "Privacidade controlada por você", "receiveNotifications": "Recebe notificações dos responsáveis", "locationWhenNeeded": "Localização apenas quando necessário"}, "examples": {"school": "Você está na escola/universidade", "parents": "Seus pais querem saber se chegou bem", "control": "Você controla quando compartilhar", "age": "Você tem entre 6-25 anos"}, "warning": "Escolha esta opção apenas se você é o ESTUDANTE que vai compartilhar localização"}, "parent": {"title": "<PERSON>u Responsá<PERSON>", "description": "Monitoro a localização dos meus estudantes", "features": {"manageStudents": "Gerencia múltiplos estudantes", "viewLocation": "Visualiza localização dos estudantes", "sendNotifications": "Envia notificações e alertas", "responsibleSecurity": "Responsável pela segurança"}, "examples": {"parentRole": "Você é pai/mãe ou responsável", "schoolArrival": "Quer saber se filho chegou na escola", "mapView": "Visualiza no mapa onde estão", "inviteStudents": "Convida estudantes para se conectar"}, "warning": "Escolha esta opção apenas se você é PAI/MÃE ou RESPONSÁVEL"}, "dialog": {"title": "Confirmar <PERSON><PERSON><PERSON>", "creatingAs": "Você está criando uma conta como {{type}}.", "studentWillDo": "Como estudante, você vai:", "parentWillDo": "Como responsável, você vai:", "studentFeatures": {"shareLocation": "Compartilhar sua localização com responsáveis", "receiveFamilyRequests": "Receber solicitações de vínculos familiares", "controlSharing": "Controlar quando compartilhar sua localização"}, "parentFeatures": {"viewStudentLocation": "Visualizar localização dos estudantes vinculados", "requestLinks": "Solicitar vínculos com estudantes", "manageFamilyConnections": "Gerenciar múl<PERSON>las <PERSON> familiares"}, "warning": "ATENÇÃO: <PERSON>ssa escolha não pode ser alterada depois!", "confirmAs": "Sim, confirmar como {{type}}"}}, "tabs": {"login": "Entrar", "register": "Cadastrar", "forgotPassword": "Esque<PERSON>u a senha?"}, "password": {"show": "<PERSON><PERSON> senha", "hide": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>a"}, "register": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Crie sua nova conta", "fullName": "Nome <PERSON>to", "email": "Email", "password": "<PERSON><PERSON>", "confirmPassword": "Confirmar <PERSON>", "userType": "Tipo de Usuário", "student": "Estudante", "parent": "Responsável", "country": "País / Idioma", "countryBR": "Brasil / Português", "countryUK": "Reino Unido / Inglês", "nationalId": "Identidade Nacional", "phone": "Telefone", "cpf": "CPF", "createAccount": "<PERSON><PERSON><PERSON>", "haveAccount": "Já tem uma conta?", "signIn": "Fazer login", "cpfHint": "O CPF será usado para evitar contas duplicadas e facilitar a conexão entre responsáveis e estudantes.", "nationalIdHint": "A Identidade Nacional ajuda a prevenir contas duplicadas."}}, "dashboard": {"tabs": {"map": "Mapa", "history": "Hist<PERSON><PERSON><PERSON>"}, "parent": {"title": "Painel do Responsável", "addStudent": "<PERSON><PERSON><PERSON><PERSON>", "selectStudentPrompt": "Selecione um estudante", "selectStudentInstruction": "Clique em um estudante na lista para ver sua localização.", "viewAllLocations": "<PERSON><PERSON><PERSON> as localizações", "showing": "Exibindo", "studentLocationTitle": "Localização de {{name}}", "selectStudentPromptDetailed": "Selecione um estudante para ver sua localização"}, "student": {"title": "Dashboard do Estudante", "welcome": "<PERSON><PERSON>-vindo", "shareLocation": "Compartilhar Localização", "locationHistory": "Histórico de Localização", "guardians": "Responsáveis", "privacy": "Privacidade"}}, "student": {"manageStudentsTitle": "Gerenciar Estudantes", "addStudent": "Adicionar Nov<PERSON> Estudante", "cpfHint": "O CPF será usado para identificar o estudante no sistema.", "studentName": "Nome do Estudante", "studentEmail": "Email do Estudante", "studentCPF": "CPF do Estudante", "studentPhone": "Telefone do Estudante", "inviteStudent": "<PERSON><PERSON><PERSON>", "inviteButton": "Enviar Convite para Estudante", "sendingInvite": "Enviando Convite...", "inviteEmailHint": "Um convite será enviado para este email para que o estudante possa aceitar o vínculo.", "studentAdded": "Estudante adicionado com sucesso", "addStudentDescription": "Insira os dados do estudante que deseja monitorar.", "addError": "Erro ao adicionar estudante", "studentExists": "Estudante já existe", "invalidData": "<PERSON><PERSON>", "createAccount": "<PERSON><PERSON><PERSON>", "accountCreated": "Conta criada com sucesso"}, "location": {"shareLocation": "Compartilhar Localização", "locationShared": "Localização compartilhada", "locationHistory": "Histórico de Localização", "currentLocation": "Localização Atual", "lastUpdate": "Última Atualização", "map": "Mapa", "history": "Hist<PERSON><PERSON><PERSON>", "noLocations": "Nenhuma localização disponível", "accuracy": "Precisão", "timestamp": "Data/Hora"}, "profile": {"title": "Perfil", "greeting": "<PERSON><PERSON><PERSON>, {{name}}", "subtitle": "Gerencie suas informações pessoais, privacidade e configurações.", "personalInfo": "Informações do Perfil", "editProfile": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "saveChanges": "<PERSON><PERSON>", "saving": "Salvando...", "cancel": "<PERSON><PERSON><PERSON>", "fullName": "Nome <PERSON>to", "fullNamePlaceholder": "Seu nome completo", "email": "Email", "emailPlaceholder": "<EMAIL>", "phone": "Telefone", "phonePlaceholder": "+55 (11) 99999-9999", "cpf": "CPF", "cpfPlaceholder": "000.000.000-00", "userType": "Tipo de Usuário", "userTypePlaceholder": "Selecione o tipo de usuário", "student": "Estudante", "parent": "Responsável", "birthDateTitle": "Data de Nascimento", "birthDateLabel": "Data de Nascimento", "age": "Idade: {{count}} anos", "minor": "<PERSON><PERSON>", "saveBirthDate": "Salvar Data de Nascimento", "errorLoading": "Erro ao carregar perfil", "errorLoadingMessage": "Não foi possível carregar suas informações de perfil.", "tabs": {"profile": "Perfil", "privacy": "Privacidade", "relationships": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Configurações"}, "accountSettings": "Configurações da Conta", "changePassword": "<PERSON><PERSON><PERSON>", "twoFactorAuth": "Autenticação em Duas Etapas", "manageSessions": "Gerenciar Sessões", "accountInfo": "Informações da Conta", "lastProfileUpdate": "Última atualização do perfil", "sessionStartedAt": "Sessão iniciada em", "connectedDevices": "Dispositivos conectados", "updateSuccessTitle": "<PERSON><PERSON>l atualizado", "updateSuccessDescription": "Suas informações foram salvas com sucesso.", "updateErrorTitle": "Erro ao salvar", "updateErrorDescription": "Não foi poss<PERSON><PERSON>l<PERSON> as alterações", "notLoggedIn": "Você não está logado. Faça login para acessar seu perfil.", "name": "Nome"}, "messages": {"welcome": "Bem-vindo ao {{brandName}}", "accountCreated": "Conta criada com sucesso! Verifique seu email para ativar.", "locationShared": "Localização compartilhada com sucesso", "errorOccurred": "Ocorreu um erro. Tente novamente.", "sessionExpired": "Sessão expirada. Faça login novamente.", "unauthorized": "Acesso não autorizado", "dataNotFound": "Dados não encontrados", "invalidInput": "Dados inválidos fornecidos", "operationSuccess": "Operação realizada com sucesso", "operationFailed": "Operação falhou", "inDevelopment": "Funcionalidade em desenvolvimento"}, "errors": {"required": "Este campo é obrigatório", "invalidEmail": "<PERSON><PERSON>", "invalidCPF": "CPF inválido", "invalidPhone": "Telefone inválido", "passwordTooShort": "Senha muito curta", "passwordMismatch": "<PERSON><PERSON> n<PERSON>m", "networkError": "<PERSON>rro <PERSON>", "serverError": "Erro interno do servidor", "authenticationFailed": "Falha na autenticação", "accessDenied": "<PERSON><PERSON>"}, "guardianManager": {"title": "<PERSON><PERSON>", "addGuardian": "<PERSON><PERSON><PERSON><PERSON>", "addGuardianAria": "Abrir diálogo para adicionar um novo responsável", "noGuardians": "Nenhum responsá<PERSON> vinculado ainda.", "addFirstGuardian": "Adicione seu primeiro responsável para começar a compartilhar sua localização.", "email": "Email", "emailPlaceholder": "<EMAIL>", "name": "Nome", "fullNamePlaceholder": "Nome completo", "phone": "Telefone", "phonePlaceholder": "+XX (XX) XXXXX-XXXX", "cancel": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "active": "Ativo", "pending": "Pendente", "addedAt": "Adicionado em", "connected": "Conectado", "invite": "<PERSON><PERSON><PERSON>", "remove": "Remover"}, "studentsList": {"title": "Estudantes Vinculados", "none": "Nenhum estudante vinculado ainda.", "helpTitle": "Como adicionar estudantes", "helpBullet1": "Preencha o formulário acima com os dados do estudante", "helpBullet2": "As credenciais são enviadas automaticamente por e-mail", "helpBullet3": "Estudantes já existentes serão vinculados à sua conta", "helpBullet4": "Você receberá confirmação quando processado", "helpFooter": "Sistema totalmente operacional com múltiplos níveis de confiabilidade"}, "studentInvitationsStatus": {"title": "Status das Solicitações", "pending": "Aguardando", "accepted": "<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>", "unknown": "Desconhecido", "sent": "Enviado", "acceptedAt": "<PERSON><PERSON>", "expires": "Expira", "expiredAt": "<PERSON><PERSON><PERSON>", "error": "Erro ao carregar convites"}, "accountDeletion": {"title": "Excluir Conta", "intro": "Remove permanentemente todos os seus dados. Esta ação não pode ser desfeita.", "button": "Excluir <PERSON>", "confirmTitle": "Confirmar <PERSON><PERSON><PERSON> da Conta", "confirmList": ["Todos os seus dados pessoais", "Histórico de localizações", "Relacionamentos com responsáveis/estudantes", "Acesso à plataforma"], "confirmButton": "<PERSON>m, Excluir Permanentemente", "cancel": "<PERSON><PERSON><PERSON>", "processing": "Excluindo..."}, "landing": {"title": "Monitore a localização de quem você ama", "tagline": "Acompanhe a localização dos seus filhos em tempo real com consentimento, segurança e privacidade.", "features": {"liveMap": "Visualização ao vivo no mapa (Mapbox)", "quickInvite": "Convite rápido entre estudantes e responsáveis", "security": "Segurança com PKCE, RLS e LGPD", "noHistory": "Nenhum histórico é compartilhado sem permissão"}, "login": "Entrar", "createAccount": "<PERSON><PERSON><PERSON>", "studentHelp": "Ajuda do Estudante", "guardianHelp": "Ajuda do Responsável"}, "acceptInvitation": {"pageTitle": "Aceitar Convite Familiar", "cardTitle": "<PERSON><PERSON><PERSON>", "validating": "Validando convite...", "invalid": "<PERSON><PERSON><PERSON>", "valid": "<PERSON><PERSON><PERSON>", "inviteMessage": "Você recebeu um convite para se vincular a um estudante. Clique abaixo para aceitar.", "whatHappens": "O que acontece ao aceitar?", "benefit1": "Você será vinculado(a) ao estudante como responsável", "benefit2": "Poderá visualizar informações e localizações do estudante", "benefit3": "Receberá notificações sobre atividades do estudante", "benefit4": "Poderá gerenciar permissões e configurações", "acceptButton": "Aceitar Convite", "goToProfile": "<PERSON>r para <PERSON><PERSON>", "backToDashboard": "Voltar ao Dashboard", "tokenNotFound": "Token de convite não encontrado na URL", "invalidToken": "Token de convite inválido", "processingError": "Erro interno ao processar convite", "acceptedTitle": "Convite Aceito com Sucesso!", "acceptedMessage": "O vínculo familiar foi estabelecido. Você será redirecionado para seu perfil em alguns segundos.", "errorTitle": "Erro ao Aceitar Convite", "tryAgain": "Tentar Novamente", "needHelp": "Precisa de Ajuda?", "helpText": "Se você está tendo problemas para aceitar o convite, entre em contato com o estudante que enviou o convite ou com o suporte técnico."}, "studentDashboard": {"title": "Dashboard do Estudante", "profile": "Perfil", "personalInfo": "Informaçõ<PERSON>", "name": "Nome", "email": "E-mail", "phone": "Telefone", "actions": "Ações", "myGuardians": "<PERSON><PERSON>", "map": "Mapa", "history": "Hist<PERSON><PERSON><PERSON>", "quickActions": "Ações <PERSON>", "updateLocationAria": "Atualize sua localização atual", "updateLocation": "Atualizar Localização", "sendToGuardiansAria": "Compartilhar localização com seus responsáveis", "sendToGuardians": "ENVIAR PARA {{count}} RESPONSÁVEIS", "readyToShare": "Pronto para compartilhar com {{count}} responsável(is)", "addGuardian": "<PERSON><PERSON><PERSON><PERSON>", "active": "Ativo", "addedOn": "Adicionado em: {{date}}", "connected": "Conectado", "remove": "Remover", "guardianRequests": "Solicitações de Responsáveis", "guardianRequestsDesc": "Solicitações de responsáveis que querem se conectar à sua conta", "noPendingRequests": "Você não tem solicitações de responsáveis pendentes no momento.", "obtaining": "Obtendo...", "sending": "Enviando...", "locationSentSuccess": "Localização enviada com sucesso!", "locationSendError": "Erro ao enviar localização", "addGuardiansToShare": "Adicione responsáveis para compartilhar sua localização", "getLocationFirst": "Obtenha sua localização atual primeiro", "locationRecorded": "Localização registrada com sucesso!", "locationUpdated": "Localização atualizada!", "shareSuccess": "Localização compartilhada com sucesso!", "shareComplete": "Compartilhamento concluído!", "duplicateLocationTitle": "Localização já enviada", "duplicateLocationDesc": "Você já enviou esta localização recentemente. Aguarde antes de tentar novamente."}, "studentPendingRequests": {"title": "Solicitações de Responsáveis", "titleWithCount": "Solicitações de Responsáveis ({{count}})", "description": "Solicitações de responsáveis que desejam se conectar à sua conta", "loading": "Carregando solicitações...", "noPending": "Você não possui solicitações pendentes de responsáveis no momento.", "error": "❌ Erro", "loadError": "Não foi possível carregar as solicitações pendentes.", "requestAccepted": "✅ Solicitação aceita!", "guardianCanTrack": "O responsável agora pode acompanhar sua localização.", "acceptError": "❌ Erro ao aceitar", "acceptRetry": "Não foi possível aceitar a solicitação. Tente novamente.", "requestRejected": "🚫 Solicitação rejeitada", "guardianNotified": "O responsável foi notificado da recusa.", "rejectError": "❌ Erro ao rejeitar", "rejectFailed": "Não foi possível rejeitar a solicitação.", "receivedAt": "Solicitação recebida em: {{date}}", "expiresAt": "Expira em: {{date}}", "wantsToConnect": "Esta pessoa quer se conectar como seu responsável para acompanhar sua localização.", "accept": "Aceitar", "reject": "<PERSON><PERSON><PERSON><PERSON>", "howItWorks": "Como funciona?", "acceptDescription": "O responsável será vinculado à sua conta e poderá acompanhar sua localização", "rejectDescription": "A solicitação será removida e o responsável não terá acesso", "autoExpire": "As solicitações expiram automaticamente em 7 dias"}, "locale": "pt-BR", "validation": {"registrationError": "Erro ao realizar cadastro", "userAlreadyExists": "Este email já está cadastrado. Tente fazer login ou recuperar sua senha.", "userAlreadyRegistered": "Usuário já cadastrado", "redirectingToLogin": "Redirecionando para a página de login...", "databaseError": "Erro no servidor durante o cadastro. Entre em contato com o suporte e informe o código:", "serverError": "Erro interno no servidor", "serverErrorDescription": "Estamos com um problema técnico. Por favor, tente novamente mais tarde ou entre em contato com o suporte.", "passwordRequirements": "A senha não atende aos requisitos mínimos. Deve ter no mínimo 8 caracteres.", "invalidCpf": "CPF inválido ou já cadastrado no sistema.", "consentRequired": "Consentimento obrigatório", "consentDescription": "É necessário concordar com o tratamento de dados pessoais para prosseguir.", "passwordMismatchError": "Erro na confirmação de senha", "passwordsDoNotMatch": "As senhas não coincidem.", "invalidEmail": "<PERSON><PERSON>", "cpfRequired": "CPF é obrigatório", "cpfInvalid": "CPF inválido. Verifique o formato e os dígitos verificadores.", "missingFields": "Campos obrigatórios não informados.", "passwordGuidance": {"title": "💡 Dicas para criar uma senha legal e segura:", "tip1": "Use o nome do seu animal de estimação favorito", "tip2": "Adicione números especiais para você (como sua idade)", "tip3": "Coloque símbolos divertidos (!@#$)", "example": "Exemplo: \"Rex2024!@\""}, "accountCreated": "Cadastro realizado com sucesso!", "verifyEmail": "Verifique seu e-mail para confirmar a conta antes de fazer login.", "registrationComplete": "Cadastro concluído", "accountCreatedConfirmed": "Conta {{userType}} criada e confirmada com sucesso!", "studentAccount": "de estudante", "parentAccount": "de respons<PERSON><PERSON>"}, "communication": {"title": "Preferências de Comunicação", "email": {"label": "Email", "description": "Sempre ativo para notificações importantes"}, "sms": {"label": "SMS", "description": "Mensagens de texto para seu celular"}, "whatsapp": {"label": "WhatsApp", "description": "Mensagens via WhatsApp Business"}, "phone": {"label": "Número de Telefone", "placeholder": "+55 (11) 99999-9999", "formatHelp": "Formato: +55 (DDD) 9XXXX-XXXX"}, "preferredMethod": {"label": "Método <PERSON>", "description": "Canal usado primeiro quando você compartilhar localização"}, "phoneRequired": {"title": "Número de telefone obrigatório", "description": "Informe um número de telefone para habilitar SMS ou WhatsApp."}, "saveSuccess": {"title": "Preferências salvas", "description": "Suas preferências de comunicação foram atualizadas com sucesso."}, "saveError": {"title": "Erro ao salvar", "description": "Não foi possível salvar suas preferências. Tente novamente."}, "saving": "Salvando...", "saveButton": "<PERSON><PERSON>"}, "theme": {"title": "<PERSON><PERSON>face", "light": "<PERSON><PERSON><PERSON>", "dark": "Escuro", "system": "Automático (Sistema)", "systemDescription": "O tema automático segue as configurações do seu dispositivo."}, "map": {"style": {"label": "Estilo do Mapa", "streets-v12": "<PERSON><PERSON><PERSON>", "satellite-streets-v12": "Satélite", "light-v11": "<PERSON><PERSON><PERSON>", "dark-v11": "Escuro", "outdoors-v12": "<PERSON><PERSON>"}}, "notifications": {"title": "Notificações", "locationAlerts": {"label": "Alertas de Localização", "description": "Notificações sobre compartilhamento de localização"}, "networkStatus": {"label": "Status da Rede", "description": "Avisos sobre conexão offline/online"}, "batteryWarnings": {"label": "Avisos de Bateria", "description": "Alertas sobre bateria baixa dos dispositivos"}, "pushNotifications": {"label": "Push Notifications", "description": "Notificações no navegador"}, "privacy": {"title": "Configurações de Privacidade", "profileVisibility": {"label": "Visibilidade do Perfil", "description": "<PERSON><PERSON><PERSON> que outros usuários vejam seu perfil"}, "autoSharing": {"label": "Compartilhamento Automático", "description": "Compartilhar localização automaticamente"}, "dataRetention": {"label": "Retenção de Dados", "description": "Tempo para manter o histórico de localizações", "options": {"7days": "7 dias", "30days": "30 dias", "90days": "90 dias", "1year": "1 ano", "forever": "Sempre"}}}, "interface": {"title": "Interface e Acessibilidade", "fontSize": {"label": "<PERSON><PERSON><PERSON>", "options": {"small": "Pequena", "medium": "Média", "large": "Grande", "extraLarge": "Extra Grande"}}, "layoutStyle": {"label": "Estilo do <PERSON>", "options": {"default": "Padrão", "compact": "Compacto", "spacious": "Espaçoso"}}, "highContrast": {"label": "Alto Contraste", "description": "Aumentar contraste para melhor visibilidade"}, "reducedMotion": {"label": "<PERSON>uz<PERSON>", "description": "Minimizar animações e transições"}}}, "legal": {"purpose": "Finalidade", "purposeDescription": "Coletamos seus dados para {{purpose}}", "dataCollected": "Dados coletados", "legalBasis": "Base legal", "retention": "Retenção", "yourRights": "Seus direitos", "consentText": "Concordo com o tratamento dos meus dados pessoais conforme descrito e autorizo o compartilhamento de localização quando necessário.", "viewFullPolicy": "Ver política completa", "dataCollection": {"student": {"purpose": "permitir o compartilhamento seguro de sua localização com seus responsáveis"}, "parent": {"purpose": "permitir que você monitore a localização de estudantes sob sua responsabilidade"}, "data": {"fullName": "Nome completo", "email": "E-mail", "phone": "Telefone (opcional)", "gpsLocation": "Dados de localização GPS", "studentLinks": "Dados de vinculação com estudantes"}, "retention": "24 meses após a última atividade da conta"}, "rights": {"access": "Acessar dados", "export": "Exportar dados", "delete": "Excluir dados"}, "lgpd": {"title": "Proteção de Dados Pessoais (LGPD)", "legalBasis": "Consentimento (Art. 7º, I da LGPD)"}, "uk_gdpr": {"title": "Proteção de Dados Pessoais (UK GDPR)", "legalBasis": "Consentimento (Art. 6(1)(a) do UK GDPR)"}, "privacyPolicy": {"title": "Política de Privacidade - EduConnect", "description": "Informações detalhadas sobre o tratamento de dados pessoais", "controller": {"title": "1. Identificação do Controlador", "name": "EduConnect - Plataforma de Localização Familiar", "contact": "Contato para questões de privacidade: educate<PERSON><EMAIL>"}, "dataCollected": {"title": "2. <PERSON><PERSON>", "intro": "Coletamos apenas os dados necessários para o funcionamento da plataforma:", "identification": "Dados de identificação: nome, e-mail", "contact": "Dados de contato: telefone (opcional)", "location": "Dados de localização: coordenadas GPS quando compartilhadas", "navigation": "Dados de navegação: logs de acesso para segurança"}, "purposes": {"title": "3. Finalidades do Tratamento", "authentication": "Autenticação e controle de acesso", "locationSharing": "Compartilhamento seguro de localização", "communication": "Comunicação sobre segurança e atualizações", "improvement": "Melhoria da experiência do usuário"}, "sharing": {"title": "4. Compart<PERSON><PERSON><PERSON> de Dados", "intro": "Seus dados são compartilhados apenas:", "authorized": "Com responsáveis/estudantes autorizados", "legal": "Para cumprimento de obrigações legais", "noSale": "Nunca vendemos seus dados para terceiros"}, "rights": {"title": "5. <PERSON><PERSON>", "intro": "Você pode a qualquer momento:", "access": "Acessar seus dados pessoais", "correct": "Corrigir informações incorretas", "delete": "Solicitar exclusão da conta", "export": "Exportar seus dados", "revoke": "<PERSON><PERSON><PERSON>"}, "security": {"title": "6. <PERSON><PERSON><PERSON><PERSON>", "description": "Implementamos medidas técnicas e organizacionais para proteger seus dados contra acesso não autorizado, perda ou vazamento."}, "contact": {"title": "7. <PERSON><PERSON><PERSON>", "intro": "Para exercer seus direitos ou esclarecer dúvidas sobre privacidade:", "email": "E-mail: educate<PERSON><PERSON>@gmail.com", "response": "Resposta em até 15 dias úteis"}}}, "lgpdSettingsPage": {"mainTitle": "Configurações de Privacidade", "mainDescription": "Gerencie seus dados pessoais de acordo com o regulamento de proteção de dados aplicável.", "exportTitle": "Exportar <PERSON>", "exportDescription": "Baixe uma cópia de todos os seus dados pessoais armazenados em nosso sistema.", "exportButton": "Exportar Dados", "exportingButton": "Exportando...", "deleteAccountTitle": "Solicitar Exclusão da Conta", "deleteAccountDescription": "Esta ação solicitará a exclusão permanente de todos os seus dados. Seus responsáveis receberão uma notificação para aprovar esta ação.", "deleteReasonLabel": "Motivo da solicitação (opcional)", "deleteReasonPlaceholder": "Por favor, nos informe o motivo da solicitação de exclusão...", "deleteButton": "Solicitar Exclusão", "deletingButton": "Processando...", "yourRightsTitle": "<PERSON><PERSON> Di<PERSON>"}, "help": {"buttons": {"back": "Voltar", "proceed": "Prosseguir"}, "student": {"title": "Ajuda para Estudantes", "dashboard": {"title": "Dashboard do Estudante", "description": "Visão geral das principais funções disponíveis."}, "sharing": {"title": "Compartilhamento de Localização", "sendButton": "Use o botão \"Enviar\" para compartilhar sua localização.", "individual": "Compartilhamento individual também está disponível."}, "guardians": {"title": "Gerenciar Responsáveis", "description": "Adicione ou remova responsáveis confiáveis."}, "history": {"title": "Hist<PERSON><PERSON><PERSON>", "description": "Veja seus registros recentes de localização."}, "profile": {"title": "Perfil", "description": "Atualize suas informações pessoais."}, "faq": {"title": "Perguntas Frequentes", "q1": "Como parar de compartilhar?", "a1": "Você pode desativar o compartilhamento a qualquer momento no dashboard.", "q2": "<PERSON><PERSON><PERSON> <PERSON>ha senha", "a2": "Use a opção de recuperar senha na tela de login.", "q3": "Como excluir minha conta?", "a3": "Acesse as configurações de privacidade e solicite exclusão."}, "support": {"title": "Suporte", "description": "Em caso de dúvidas, contate o suporte em"}}, "parent": {"title": "Ajuda para Responsáveis", "dashboard": {"title": "Dashboard do Responsável", "description": "Ferramentas principais para monitorar estudantes."}, "managing": {"title": "Gerenciando Estudantes", "invite": "Convide novos estudantes usando o e-mail", "pending": "Aprove ou rejeite solicitações pendentes", "remove": "Remova um estudante da sua lista"}, "monitoring": {"title": "Monitoramento", "description": "Acompanhe a localização em tempo real quando autorizado."}, "profile": {"title": "Perfil", "description": "Atualize suas informações de responsável."}, "faq": {"title": "Perguntas Frequentes", "q1": "Como convidar um estudante?", "a1": "Use o formulário de convite no seu dashboard.", "q2": "Posso ter vários responsáveis?", "a2": "<PERSON><PERSON>, estudantes podem vincular múltiplos responsáveis.", "q3": "Como altero meu e-mail?", "a3": "Edite suas informações de perfil."}, "support": {"title": "Suporte", "description": "Para assistência entre em contato"}, "removeDuplicates": {"title": "Remover Localizações Duplicadas", "step1": "Selecione um estudante no painel do responsável.", "step2": "Clique no botão vermelho \"Remover duplicatas do banco\" no topo do histórico de localizações.", "step3": "Configure os critérios de remoção (raio, tempo, precisão) conforme desejado.", "step4": "Digite CONFIRMAR (ou CONFIRM) para habilitar a exclusão e clique em \"Remover duplicatas\".", "step5": "Aguarde a confirmação. As localizações duplicadas serão removidas do banco de dados de forma permanente.", "warning": "Atenção: Esta ação é irreversível. Certifique-se de revisar os critérios antes de confirmar."}}}, "locationHistory": {"loadingAddresses": "Carregando endereços...", "loadingHistory": "Carregando histórico...", "errorTitle": "Erro ao carregar histórico", "noneFound": "Nenhum histórico de localização encontrado", "noLocationsForStudent": "{{name}} ainda não compartilhou localizações", "noLocations": "Ainda não há localizações registradas", "requestShare": "Solicite que {{name}} compartilhe sua localização atual.", "title": "Histórico de Localizações", "oneLocation": "localização", "manyLocations": "localizações", "clickToView": "Clique em qualquer localização para visualizá-la no mapa", "student": "Estudante", "mostRecent": "<PERSON><PERSON>e", "selected": "Selecionada", "accuracy": "Precisão", "loadingAddress": "Carregando endereço...", "addressUnavailable": "Endereço não disponível", "sharedWithGuardians": "Compartilhada com responsáveis", "clickToSeeOnMap": "Clique para ver no mapa", "sendByEmail": "En<PERSON>r por Email", "importantInfo": "Informações Importantes", "automaticSharing": "As localizações são compartilhadas automaticamente quando {{name}} permite. As informações de endereço são obtidas automaticamente com base nas coordenadas GPS.", "clickToHighlight": "Clique em qualquer localização do histórico para visualizá-la destacada no mapa."}, "lgpd": {"dataSummary": {"title": "Resumo dos Seus Dados", "name": "Nome", "email": "E-mail", "userType": "Tipo de usuário", "locationTitle": "Dados de localização", "locationStatusActive": "Compartilhamento ativo"}, "export": {"title": "Exportar Dados", "description": "Baixe uma cópia de todos os seus dados pessoais em formato JSON.", "button": "<PERSON><PERSON><PERSON>", "exporting": "Exportando..."}, "legalInfo": {"title": "Informações sobre Tratamento de Dados", "legalBasis": "Base Legal:", "consent": "Consentimento (Art. 7º, I da LGPD) para coleta e tratamento de dados pessoais.", "purposesTitle": "Finalidades do Tratamento:", "purposes": {"auth": "Autenticação e controle de acesso", "location": "Compartilhamento de localização", "security": "Comunicação sobre segurança", "experience": "Melhoria da experiência"}, "rightsTitle": "Seus Direitos:", "rights": {"access": "Acessar seus dados", "correct": "Corrigir informações", "delete": "Solicitar exclusão", "portability": "Portabilidade dos dados", "revoke": "<PERSON><PERSON><PERSON>"}, "privacyQuestions": "Dúvidas sobre privacidade?", "contact": "Entre em contato: <PERSON><PERSON><PERSON>@gmail.com", "responseTime": "Prazo de resposta: até 15 dias úteis"}, "deletionRequests": {"title": "Solicitações de Exclusão de Conta", "actionRequired": "Ação necessária", "studentRequested": "Um estudante solicitou a exclusão de sua conta.", "studentsRequested": "{{count}} estudantes solicitaram a exclusão de suas contas.", "reviewPrompt": "Por favor, revise e tome uma decisão.", "pending": "Pendente", "requestedAt": "Solicitado em {{date}}", "reason": "Motivo da solicitação", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>"}}, "relationships": {"supervisedTitle": "Estudantes Supervisionados", "supervisedDescription": "Estudantes que você acompanha e pode monitorar.", "studentViewTitle": "<PERSON><PERSON>", "studentViewDescription": "Responsáveis que podem acompanhar sua localização e atividades.", "noGuardians": "Nenhum responsá<PERSON> vinculado ainda.", "contactGuardian": "Entre em contato com um responsável para criar o vínculo.", "loadingGuardians": "Carregando responsáveis...", "guardianActive": "Ativo", "guardianInactive": "Inativo", "noStudents": "Nenhum estudante vinculado ainda.", "inviteStudents": "Convide estudantes para estabelecer vínculos familiares.", "addStudentButton": "Vincular Novo Estudante"}}