
import { useState, useEffect } from 'react';
import { LocationData } from '@/types/database';
import { locationService } from '@/lib/services/location/LocationService';

export function useLocationData(selectedUserId: string | null, userEmail?: string, userType?: string) {
  const [locationData, setLocationData] = useState<LocationData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLocationData = async () => {
      if (!selectedUserId) {
        setLocationData([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        console.log('[useLocationData] Fetching locations for:', selectedUserId);
        
        const data = await locationService.getStudentLocations(selectedUserId);
        
        console.log('[useLocationData] Fetched locations:', data?.length || 0);
        
        if (data && data.length > 0) {
          // Sort by timestamp (newest first)
          const sortedData = [...data].sort((a, b) => {
            const dateA = new Date(a.timestamp).getTime();
            const dateB = new Date(b.timestamp).getTime();
            return dateB - dateA;
          });
          
          setLocationData(sortedData);
        } else {
          setLocationData([]);
        }
      } catch (error: any) {
        console.error('[useLocationData] Error:', error);
        setError(error.message || 'Erro ao carregar localizações');
        setLocationData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchLocationData();
  }, [selectedUserId, userEmail, userType]);

  return {
    locationData,
    loading,
    error
  };
}
