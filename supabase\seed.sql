-- Seed data for testing
insert into auth.users (id, email, email_confirmed_at, aud)
values (
    '11111111-1111-1111-1111-111111111111',
    '<EMAIL>',
    timezone('utc'::text, now()),
    'authenticated'
),
(
    '22222222-2222-2222-2222-222222222222',
    '<EMAIL>',
    timezone('utc'::text, now()),
    'authenticated'
);

-- COMENTADO: Insert test users (tabela users não existe)
-- insert into users (id, name, role, phone)
-- values (
--     '11111111-1111-1111-1111-111111111111',
--     '<PERSON>',
--     'student',
--     '+44 7900 123456'
-- ),
-- (
--     '22222222-2222-2222-2222-222222222222',
--     '<PERSON>',
--     'parent',
--     '+44 7800 123456'
-- );
