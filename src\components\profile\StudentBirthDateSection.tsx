
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar, Cake, Loader2 } from 'lucide-react';
import { useProfile } from '@/hooks/useProfile';
import { useTranslation } from 'react-i18next';

const StudentBirthDateSection: React.FC = () => {
  const { t } = useTranslation();
  const { profile, updateProfile } = useProfile();
  const [birthDate, setBirthDate] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Sincronizar o estado local com o profile quando ele mudar
  useEffect(() => {
    if (profile?.birth_date) {
      setBirthDate(profile.birth_date);
    }
  }, [profile?.birth_date]);

  // Calcular idade se a data de nascimento estiver disponível
  const calculateAge = (birthDateString: string): number | null => {
    if (!birthDateString) return null;
    
    const birth = new Date(birthDateString);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const age = profile?.birth_date ? calculateAge(profile.birth_date) : null;
  const isMinor = age !== null && age < 18;

  const handleSave = async () => {
    if (!profile) {
      console.error('[StudentBirthDateSection] No profile available');
      return;
    }

    if (!birthDate) {
      console.error('[StudentBirthDateSection] No birth date provided');
      return;
    }

    // Validar se a data é válida
    const dateValue = new Date(birthDate);
    if (isNaN(dateValue.getTime())) {
      console.error('[StudentBirthDateSection] Invalid date format:', birthDate);
      return;
    }

    setIsSaving(true);
    
    try {
      // Log para debugging
      console.log('[StudentBirthDateSection] Saving birth date:', birthDate);
      console.log('[StudentBirthDateSection] Profile data:', {
        full_name: profile.full_name,
        email: profile.email,
        phone: profile.phone,
        user_type: profile.user_type,
        cpf: profile.cpf
      });
      
      const success = await updateProfile({
        full_name: profile.full_name,
        email: profile.email,
        phone: profile.phone || '',
        user_type: profile.user_type,
        cpf: profile.cpf || '',
        birth_date: birthDate
      });

      if (success) {
        console.log('[StudentBirthDateSection] Birth date saved successfully');
      } else {
        console.error('[StudentBirthDateSection] Failed to save birth date');
      }
    } catch (error) {
      console.error('[StudentBirthDateSection] Error saving birth date:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className="bg-transparent border-0 shadow-none">
      <CardHeader className="bg-transparent border-0 shadow-none">
        <CardTitle className="flex items-center gap-2 bg-transparent">
          <Cake className="h-5 w-5" />
          {t('profile.birthDateTitle')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 bg-transparent border-0 shadow-none">
        <div className="space-y-2">
          <Label htmlFor="birth_date">{t('profile.birthDateLabel')}</Label>
          <Input
            id="birth_date"
            type="date"
            value={birthDate}
            onChange={(e) => setBirthDate(e.target.value)}
            max={new Date().toISOString().split('T')[0]} // Não permitir datas futuras
          />
        </div>

        {age !== null && (
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">
                {t('profile.age', { count: age })}
              </span>
              {isMinor && (
                <span className="px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded-full">
                  {t('profile.minor')}
                </span>
              )}
            </div>
          </div>
        )}

        <Button 
          onClick={handleSave} 
          disabled={isSaving || !birthDate}
          className="w-full"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {t('profile.saving')}
            </>
          ) : (
            t('profile.saveBirthDate')
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

export default StudentBirthDateSection;
