
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useCommunicationPreferences } from '@/hooks/useCommunicationPreferences';
import { Mail, MessageSquare, Phone } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface CommunicationPreferencesProps {
  className?: string;
}

export const CommunicationPreferences: React.FC<CommunicationPreferencesProps> = ({ className = '' }) => {
  const { toast } = useToast();
  const { t } = useTranslation();
  const { preferences, isLoading, updatePreferences } = useCommunicationPreferences();
  
  const [formData, setFormData] = useState({
    email_enabled: true,
    sms_enabled: false,
    whatsapp_enabled: false,
    phone_number: '',
    preferred_method: 'email' as 'email' | 'sms' | 'whatsapp'
  });

  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (preferences) {
      setFormData({
        email_enabled: preferences.email_enabled,
        sms_enabled: preferences.sms_enabled,
        whatsapp_enabled: preferences.whatsapp_enabled,
        phone_number: preferences.phone_number || '',
        preferred_method: preferences.preferred_method as 'email' | 'sms' | 'whatsapp'
      });
    }
  }, [preferences]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Validar número de telefone se SMS ou WhatsApp estiverem habilitados
      if ((formData.sms_enabled || formData.whatsapp_enabled) && !formData.phone_number.trim()) {
        toast({
          variant: "destructive",
          title: t('settings.communication.phoneRequired.title','Phone number required'),
          description: t('settings.communication.phoneRequired.description','Provide a phone number to enable SMS or WhatsApp.'),
        });
        return;
      }

      const success = await updatePreferences(formData);
      
      if (success) {
        toast({
          title: t('settings.communication.saveSuccess.title','Preferences saved'),
          description: t('settings.communication.saveSuccess.description','Your communication preferences have been updated.'),
        });
      } else {
        throw new Error('Falha ao salvar preferências');
      }
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
      toast({
        variant: "destructive",
        title: t('settings.communication.saveError.title','Save error'),
        description: t('settings.communication.saveError.description','Could not save your preferences. Please try again.'),
      });
    } finally {
      setIsSaving(false);
    }
  };

  const formatPhoneNumber = (value: string) => {
    // Remove tudo que não é número
    const numbers = value.replace(/\D/g, '');
    
    // Formatar número brasileiro
    if (numbers.length <= 11) {
      return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '+55 ($1) $2-$3');
    }
    
    return value;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setFormData(prev => ({ ...prev, phone_number: formatted }));
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {t('settings.communication.title','Communication Preferences')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">{t('common.loading','Loading...')}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          {t('settings.communication.title','Communication Preferences')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Email - sempre habilitado */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Mail className="h-5 w-5 text-blue-500" />
            <div>
              <Label>{t('settings.communication.email.label','Email')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.communication.email.description','Always on for important notifications')}
              </p>
            </div>
          </div>
          <Switch
            checked={true}
            disabled={true}
          />
        </div>

        {/* SMS */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Phone className="h-5 w-5 text-green-500" />
            <div>
              <Label>{t('settings.communication.sms.label','SMS')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.communication.sms.description','Text messages to your phone')}
              </p>
            </div>
          </div>
          <Switch
            checked={formData.sms_enabled}
            onCheckedChange={(checked) => 
              setFormData(prev => ({ ...prev, sms_enabled: checked }))
            }
          />
        </div>

        {/* WhatsApp */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MessageSquare className="h-5 w-5 text-green-600" />
            <div>
              <Label>{t('settings.communication.whatsapp.label','WhatsApp')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.communication.whatsapp.description','Messages via WhatsApp Business')}
              </p>
            </div>
          </div>
          <Switch
            checked={formData.whatsapp_enabled}
            onCheckedChange={(checked) => 
              setFormData(prev => ({ ...prev, whatsapp_enabled: checked }))
            }
          />
        </div>

        {/* Número de telefone */}
        {(formData.sms_enabled || formData.whatsapp_enabled) && (
          <div className="space-y-2">
            <Label htmlFor="phone">{t('settings.communication.phone.label','Phone Number')}</Label>
            <Input
              id="phone"
              type="tel"
              placeholder={t('settings.communication.phone.placeholder','+55 (11) 99999-9999')}
              value={formData.phone_number}
              onChange={handlePhoneChange}
              className="max-w-xs"
            />
            <p className="text-sm text-muted-foreground">
              {t('settings.communication.phone.formatHelp','Format: +55 (DDD) 9XXXX-XXXX')}
            </p>
          </div>
        )}

        {/* Método preferido */}
        <div className="space-y-2">
          <Label>{t('settings.communication.preferredMethod.label','Preferred Method')}</Label>
          <Select
            value={formData.preferred_method}
            onValueChange={(value: 'email' | 'sms' | 'whatsapp') =>
              setFormData(prev => ({ ...prev, preferred_method: value }))
            }
          >
            <SelectTrigger className="max-w-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="email">{t('settings.communication.email.label','Email')}</SelectItem>
              {formData.sms_enabled && (
                <SelectItem value="sms">{t('settings.communication.sms.label','SMS')}</SelectItem>
              )}
              {formData.whatsapp_enabled && (
                <SelectItem value="whatsapp">{t('settings.communication.whatsapp.label','WhatsApp')}</SelectItem>
              )}
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            {t('settings.communication.preferredMethod.description','Channel used first when you share location')}
          </p>
        </div>

        {/* Botão salvar */}
        <Button 
          onClick={handleSave} 
          disabled={isSaving}
          className="w-full"
        >
          {isSaving ? t('settings.communication.saving','Saving...') : t('settings.communication.saveButton','Save Preferences')}
        </Button>
      </CardContent>
    </Card>
  );
};
