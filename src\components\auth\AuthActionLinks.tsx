
import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

interface AuthActionLinksProps {
  onForgotPasswordClick: () => void;
  onRegisterClick: () => void;
  className?: string;
}

const AuthActionLinks: React.FC<AuthActionLinksProps> = ({
  onForgotPasswordClick,
  onRegisterClick,
  className = ''
}) => {
  const { t } = useTranslation();
  return (
    <div className={cn("flex flex-col items-center space-y-3 mt-4", className)}>
      <button
        type="button"
        onClick={onForgotPasswordClick}
        className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
        data-cy="forgot-password-link"
      >
        {t('auth.login.forgotPassword')}
      </button>
      
      <button
        type="button"
        onClick={onRegisterClick}
        className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
        data-cy="register-link"
      >
        {t('auth.login.noAccount')} {t('auth.login.createAccount')}
      </button>
    </div>
  );
};

export default AuthActionLinks;
