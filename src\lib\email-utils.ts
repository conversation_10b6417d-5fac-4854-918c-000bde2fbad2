import { env } from '@/env';
import axios from 'axios';
import { supabase } from '@/integrations/supabase/client';

/**
 * Verifica a validade da chave de API do Resend de forma segura
 * @returns Promise com objeto contendo status da chave e mensagens
 */
export async function verifyResendApiKey(): Promise<{ valid: boolean; message: string }> {
  try {
    console.log('[EMAIL] Verificando chave API Resend...');
    
    // Verificação simples da chave sem fazer chamadas CORS
    if (!env.RESEND_API_KEY || !env.RESEND_API_KEY.startsWith('re_')) {
      return { 
        valid: false, 
        message: 'Chave de API do Resend não configurada ou inválida' 
      };
    }
    
    console.log('[EMAIL] Chave API Resend encontrada');
    return { 
      valid: true, 
      message: 'Chave de API do Resend configurada!' 
    };
  } catch (error: any) {
    console.error('[EMAIL] Erro ao verificar chave do Resend:', error);
    return { 
      valid: false, 
      message: `Erro ao verificar chave: ${error.message || 'Erro desconhecido'}` 
    };
  }
}

/**
 * Envia um email de teste usando o Resend
 * @param email Email para envio do teste
 * @returns Promise com resultado do envio
 */
export async function sendTestEmail(email: string): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.log(`[EMAIL] Enviando email de teste para ${email}`);
    const response = await axios.post('https://api.resend.com/emails', {
      from: `EduConnect <<EMAIL>>`, // Usar endereço não verificado como solução temporária
      to: [email],
      subject: 'Teste de Email - EduConnect',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #4a6cf7;">EduConnect - Teste de Email</h2>
          <p style="font-size: 16px; color: #333;">
            Este é um email de teste do sistema EduConnect para verificar a configuração de envio de emails.
          </p>
          <p style="font-size: 16px; color: #333;">
            Se você está recebendo este email, significa que a configuração está funcionando corretamente.
          </p>
          <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
          <p style="font-size: 12px; color: #777;">
            Este é um email automático. Por favor, não responda esta mensagem.
          </p>
          <p style="font-size: 12px; color: #777;">
            Email enviado em: ${new Date().toLocaleString('pt-BR')}
          </p>
        </div>
      `
    }, {
      headers: {
        'Authorization': `Bearer ${env.RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('[EMAIL] Teste enviado com sucesso');
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error('[EMAIL] Erro ao enviar email de teste:', error);
    return { 
      success: false, 
      error: error.response?.data?.message || error.message || 'Erro desconhecido' 
    };
  }
}

/**
 * Envia um email de recuperação de senha usando o Resend
 * @param email Email para envio do link de recuperação
 * @param resetUrl URL para redefinição de senha
 * @returns Promise com resultado do envio
 */
export async function sendPasswordResetEmail(email: string, resetUrl: string): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.log(`[EMAIL] Enviando recuperação para ${email} via edge function`);

    const { data, error } = await supabase.functions.invoke('send-password-reset', {
      body: { email, resetUrl }
    });

    if (error) {
      throw error;
    }

    console.log('[EMAIL] Edge function response:', data);
    return { success: true, data };
  } catch (err: any) {
    console.error('[EMAIL] Erro ao enviar email de recuperação:', err);
    return {
      success: false,
      error: err.message || 'Erro desconhecido'
    };
  }
}
