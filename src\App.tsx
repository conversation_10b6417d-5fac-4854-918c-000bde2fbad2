
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import { Toaster } from '@/components/ui/toaster';
import { UnifiedAuthProvider } from '@/contexts/UnifiedAuthContext';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { NotificationProvider } from '@/components/providers/NotificationProvider';
import AuthLayout from '@/layouts/AuthLayout';
import ProtectedLayout from '@/layouts/ProtectedLayout';
// Lazy load all major components for better code splitting
const Dashboard = lazy(() => import('@/pages/Dashboard'));
const ParentDashboard = lazy(() => import('@/pages/ParentDashboard'));
const StudentDashboard = lazy(() => import('@/pages/StudentDashboard'));
const StudentDashboardDemo = lazy(() => import('@/pages/StudentDashboardDemo'));
const Login = lazy(() => import('@/pages/Login'));
const Register = lazy(() => import('@/pages/Register'));
const ResetPassword = lazy(() => import('@/pages/ResetPassword'));
const ProfilePage = lazy(() => import('@/pages/ProfilePage'));
const GuardiansPage = lazy(() => import('@/pages/GuardiansPage'));
const StudentMap = lazy(() => import('@/pages/StudentMap'));
const AddStudent = lazy(() => import('@/pages/AddStudent'));
const SystemTest = lazy(() => import('@/pages/SystemTest'));
const Landing = lazy(() => import('@/pages/Landing'));
const AcceptInvitation = lazy(() => import('@/pages/AcceptInvitation'));
const ActivateAccount = lazy(() => import('@/pages/ActivateAccount'));
const HelpParent = lazy(() => import('@/pages/HelpParent'));
const HelpStudent = lazy(() => import('@/pages/HelpStudent'));
import BlankScreenDetector from '@/components/common/BlankScreenDetector';
import RefreshHandler from '@/components/common/RefreshHandler';
import { MapboxBackground } from '@/components/map/MapboxBackground';

// Enhanced loading component for better UX
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-600 font-medium">Carregando...</p>
    </div>
  </div>
);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000,
      gcTime: 30 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: (failureCount: number, error: any) => {
        if (error?.status === 404) return false;
        return failureCount < 3;
      },
    },
  },
});

function App() {
  return (
    <>
      <MapboxBackground />
      <RefreshHandler>
        <BlankScreenDetector timeout={15000}>
          <QueryClientProvider client={queryClient}>
            <Router
              future={{
                v7_startTransition: true,
                v7_relativeSplatPath: true
              }}
            >
              <UnifiedAuthProvider>
                <NotificationProvider>
                <Suspense fallback={<LoadingFallback />}>
                <Routes>
                  {/* Auth routes & public pages */}
                  <Route element={<AuthLayout />}>
                    <Route path="/login" element={<Login />} />
                    <Route path="/register" element={<Register />} />
                    <Route path="/ajuda/responsavel" element={<HelpParent />} />
                    <Route path="/ajuda/estudante" element={<HelpStudent />} />
                    <Route path="/" element={<Landing />} />
                  </Route>

                  {/* Public routes for account management */}
                  <Route path="/reset-password" element={<ResetPassword />} />
                  <Route path="/accept-invitation" element={<AcceptInvitation />} />
                  <Route path="/activate-account" element={<ActivateAccount />} />

                  {/* Protected routes */}
                  <Route element={<ProtectedLayout />}>
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/parent-dashboard" element={<ParentDashboard />} />
                    <Route path="/student-dashboard" element={<StudentDashboard />} />
                    <Route path="/student-dashboard-demo" element={<StudentDashboardDemo />} />
                    <Route path="/profile" element={<ProfilePage />} />
                    <Route path="/guardians" element={<GuardiansPage />} />
                    <Route path="/student-dashboard/guardians" element={<GuardiansPage />} />
                    <Route path="/add-student" element={<AddStudent />} />
                    <Route path="/student-map/:id?" element={<StudentMap />} />
                    <Route path="/student-dashboard/location" element={<StudentMap />} />
                    <Route path="/system-test" element={<SystemTest />} />
                  </Route>

                  {/* End protected routes */}
                </Routes>
                </Suspense>
                <Toaster />
              </NotificationProvider>
            </UnifiedAuthProvider>
            </Router>
          </QueryClientProvider>
        </BlankScreenDetector>
      </RefreshHandler>
    </>
  );
}

export default App;
