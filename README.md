# Locate-Family-Connect - Sistema de Localização de Alunos

[![GitHub license](https://img.shields.io/github/license/FrankWebber33/educonnect-auth-system)](https://github.com/FrankWebber33/educonnect-auth-system/blob/main/LICENSE)
[![GitHub issues](https://img.shields.io/github/issues/FrankWebber33/educonnect-auth-system)](https://github.com/FrankWebber33/educonnect-auth-system/issues)
[![GitHub stars](https://img.shields.io/github/stars/FrankWebber33/educonnect-auth-system)](https://github.com/FrankWebber33/educonnect-auth-system/stargazers)

O Locate-Family-Connect é um sistema moderno e seguro de localização de alunos que permite que responsáveis acompanhem a localização de seus filhos/alunos em tempo real através de um mapa interativo.

Para uma introdução em inglês, consulte [README_en.md](README_en.md).

## 🎯 Objetivo

O objetivo principal do Locate-Family-Connect é:

- Permitir que responsáveis acompanhem a localização de seus filhos/alunos em tempo real
- Fornecer uma interface intuitiva e segura para visualização de localização
- Garantir a privacidade e segurança dos dados dos alunos
- Facilitar a comunicação entre responsáveis e instituições educacionais

## 🚀 Funcionalidades

- Autenticação e gerenciamento de usuários com Supabase
- Diferentes tipos de usuário (estudante, responsável, administrador)
- Monitoramento de localização em tempo real para estudantes
- Interface de mapa para visualizar localizações
- Sistema de alertas para responsáveis
- Painel administrativo para gerenciamento de usuários e permissões
- Sistema bidirecional de relacionamento responsável-estudante
  - Estudantes podem adicionar seus responsáveis
  - Responsáveis podem adicionar seus estudantes
  - Visualização de relações em ambos os dashboards
  - Pesquisa de estudantes por CPF no perfil para agilizar o vínculo
- Campo CPF para responsáveis brasileiros com validação automática
- Filtros avançados para histórico de localizações
  - Remoção de localizações duplicadas
  - Filtro por intervalo de tempo
  - Filtro por precisão de localização
  - Ordenação por data/hora

## 🌐 Internacionalização

A plataforma está disponível em Português e Inglês. Na página inicial, há um seletor de idioma no canto superior direito que permite alternar entre `pt-BR` e `en-GB`. A escolha é gravada em `localStorage` e permanece ativa durante toda a navegação.

## 🔗 Vínculos Familiares
O sistema permite que responsáveis pesquisem estudantes pelo email e opcionalmente CPF para confirmar se o cadastro existe antes de enviar o convite. Consulte o arquivo [FAMILY_LINKS_GUIDE.md](FAMILY_LINKS_GUIDE.md) para detalhes.

## 🛠️ Tecnologias Utilizadas

- React 18
- TypeScript
- Supabase (Backend)
- Drizzle ORM
- Tailwind CSS
- Postgres
- Docker

## 📋 Requisitos

- Node.js 18+
- npm ou yarn
- Docker (opcional, para ambiente de desenvolvimento)
- Conta no Supabase

## 🚀 Como Executar

1. Clone o repositório:
   ```bash
   git clone https://github.com/FrankWebber33/educonnect-auth-system.git
   cd educonnect-auth-system
   ```

2. Instale as dependências:
   ```bash
   npm install
   ```
   **Importante:** execute `npm install` sempre que for rodar `npm run lint` ou `npm run build` para garantir que todas as dependências estejam presentes.

3. Configure as variáveis de ambiente:
   ```bash
   cp .env.example .env
   ```
   Edite o arquivo `.env` com suas credenciais do Supabase.

4. Inicie o servidor de desenvolvimento:
   ```bash
   npm run dev
   ```

5. Acesse a aplicação em `http://localhost:5173`

6. *(opcional)* Gere a build de produção:
   ```bash
   npm run build
   ```
   Se algum erro aparecer, execute `npm install` para garantir que todas as dependências estejam instaladas e verifique as variáveis definidas em `.env`.

## 📁 Estrutura do Projeto

```
src/
├── components/         # Componentes React reutilizáveis
│   ├── RegisterForm/  # Componentes de registro
│   ├── LoginForm/     # Componentes de login
│   └── RegisterConfirmation/ # Componente de confirmação de registro
├── contexts/          # Contextos React
│   └── UserContext/   # Contexto de autenticação
├── lib/              # Configurações e utilitários
│   ├── db/          # Configuração do banco de dados
│   │   └── migrations/ # Migrações do banco de dados
│   └── supabase/    # Configuração do Supabase
└── types/           # Tipos TypeScript
```

## 🔐 Segurança

- Autenticação segura com Supabase
- Proteção de rotas
- Validação de dados
- Criptografia de senhas
- Sistema de sessões seguro

## 🔐 Fluxo de Autenticação

O sistema utiliza o Supabase Auth para gerenciar autenticação:

1. **Registro de Usuários**:
   - Cadastro com email/senha
   - Verificação por email
   - Opção de login social (Google, Facebook)

2. **Login**:
   - Autenticação por email/senha
   - Login com provedores sociais
   - Recuperação de senha

3. **Autorização**:
   - Sistema de roles (Admin, Responsável, Aluno)
   - Controle de acesso baseado em regras
   - Tokens JWT para sessões seguras

4. **Recuperação de Senha**:
   - Sistema automático de "Esqueci minha senha" via Supabase Auth
   - Fluxo seguro com links de uso único enviados por email
   - Implementação com as seguintes etapas:
     ```typescript
     // Solicitar redefinição de senha
     const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
       redirectTo: 'https://seuapp.com/reset-password'
     })
     
     // Na página de redefinição, atualizar a senha
     const { data, error } = await supabase.auth.updateUser({
       password: novaSenha
     })
     ```
   - Configuração de emails personalizados via painel do Supabase
   - Segurança com tokens de expiração automática
   - Proteção contra ataques de força bruta

## 📱 Responsividade e Aparência

O projeto oferece utilidades para adaptar o layout a celulares, tablets e desktop.

- O hook `useDevice` identifica tamanho de tela, orientação e sistema operacional. Os breakpoints podem ser ajustados em [`src/hooks/useDevice.ts`](src/hooks/useDevice.ts).
- O hook `useResponsiveLayout` converte essas informações em espaçamentos, tamanhos de texto e estilos de botão apropriados.
- Classes como `pt-safe-area-top` e `pb-safe-area-bottom` garantem compatibilidade com iOS e Android.

```tsx
import { useResponsiveLayout } from '@/hooks/useResponsiveLayout';

const { spacing, textSizes, buttonStyles } = useResponsiveLayout();

<div className={`flex ${spacing.gap}`}>
  <button className={`btn ${buttonStyles.padding}`}>Ação</button>
</div>
```

Essas práticas mantêm a experiência consistente em qualquer dispositivo.

## 📚 Análise do Projeto e Status Atual

O projeto Locate-Family-Connect apresenta uma estrutura bem organizada, com separação clara entre componentes, páginas e serviços. Utiliza componentes funcionais React com hooks, gerenciamento de estado via Context API, e integração robusta com Supabase para backend e autenticação.

### Pontos Fortes

- Estrutura consistente e nomenclatura semântica
- Fluxo completo de autenticação e autorização
- Interface responsiva e design moderno com Tailwind CSS
- Segurança reforçada com criptografia, validação e proteção contra ataques comuns
- Otimização de performance com lazy loading e cache
- Documentação e comentários no código

### Pontos a Melhorar

- Cobertura de testes limitada, com necessidade de testes unitários e e2e
- Documentação parcial, recomendando atualização e ampliação
- Simplificação e unificação de dashboards e menus para melhor manutenção
- Padronização de feedback visual e redução de elementos redundantes

## 🛠️ Diagnóstico e Plano de Melhoria

### Diagnóstico

- Separação clara de responsabilidades, mas com redundâncias em dashboards e menus
- Design moderno e responsivo, porém com botões e menus redundantes
- Documentação rica, mas com necessidade de atualização e remoção de arquivos obsoletos

### Recomendações

- Unificar dashboards em um único componente parametrizado
- Simplificar menus e sidebars, removendo itens não essenciais
- Padronizar mensagens de erro e sucesso
- Atualizar documentação e remover scripts obsoletos
- Implementar testes unitários, integração e e2e
- Configurar CI/CD para garantir qualidade contínua

### Próximos Passos

1. Reunião para validação das sugestões
2. Priorização das melhorias
3. Prototipação visual (wireframes)
4. Implementação incremental das melhorias aprovadas

## 📝 Documentação

- [Drizzle ORM](https://orm.drizzle.team/)
- [Supabase](https://supabase.com/docs)
- [React](https://react.dev/)
- [TypeScript](https://www.typescriptlang.org/docs/)
- [Edge Functions](docs/edge-functions.md)
- [Configuração do Resend](docs/configuracao-resend.md)
- [Roadmap Resumido](docs/ROADMAP_RESUMO.md)
- [Integração do ChatGPT](docs/CHATGPT_INTEGRATION_GUIDE.md)
- [Guia de Deploy Netlify](docs/NETLIFY_TROUBLESHOOTING.md)
- [Tour Guiado do Usuário](12_USER_TOUR_GUIDE.md)

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 👥 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 🙏 Agradecimentos

- [Supabase](https://supabase.com/) - Backend como serviço
- [Drizzle ORM](https://orm.drizzle.team/) - ORM leve e rápido
- [Tailwind CSS](https://tailwindcss.com/) - Framework CSS

## 📞 Contato

- Email: <EMAIL>
- GitHub: [FrankWebber33](https://github.com/FrankWebber33)

---

Este README foi atualizado em 23/04/2023

## 📚 Drizzle ORM Commands

### Instalação do Drizzle
Para instalar o Drizzle ORM, execute o seguinte comando:
```bash
npm install drizzle-orm
```

### Configuração
Certifique-se de que o arquivo `drizzle.config.ts` está configurado corretamente com as credenciais do banco de dados e o esquema.

### Comandos Comuns
- **Executar Migrações**:
```bash
npx drizzle-kit push
```
- **Gerar Migrações**:
```bash
npx drizzle-kit migrate
```
- **Verificar o Status das Migrações**:
```bash
npx drizzle-kit status
```
- **Reverter Migrações**:
```bash
npx drizzle-kit rollback
```

## Configuração do Banco de Dados

O projeto utiliza o Supabase como backend. Siga estas etapas para configurar o banco de dados:

1. Crie uma conta no [Supabase](https://supabase.io/) se ainda não tiver
2. Crie um novo projeto
3. Configure as variáveis de ambiente conforme descrito na seção "Variáveis de Ambiente"
4. Execute as migrações necessárias conforme detalhado no arquivo [MIGRATIONS.md](./MIGRATIONS.md)

### Migrações Importantes

Este projeto requer a execução de migrações SQL para funcionar corretamente. As migrações criam:

1. **Tabela de Guardians:** Armazena os relacionamentos entre responsáveis e estudantes
2. **Funções SQL:** Facilitam a gestão das relações entre responsáveis e estudantes

Para instruções detalhadas sobre como aplicar estas migrações, consulte o arquivo [MIGRATIONS.md](./MIGRATIONS.md).

# Environment Configuration
Crie seu próprio arquivo `.env` com base no modelo em [`.env.example`](./.env.example).
Nele você deverá definir chaves do Supabase, tokens do MapBox e demais variáveis
necessárias para a aplicação. Nunca compartilhe suas chaves públicas ou privadas
em repositórios versionados.

## 🎨 Personalização do Favicon

Para utilizar seu próprio ícone de página, substitua os arquivos `public/favicon.svg` e
`public/favicon.ico` pelos seus. Após a troca, lembre-se de atualizar as referências
nos seguintes arquivos:

- `index.html` – tag `<link rel="icon">`
- `public/manifest.json` – campo de ícones
- `public/sw.js` – lista `STATIC_ASSETS`



## 📖 Questionário Locate-Family-Connect
Para um resumo das respostas ao questionário de requisitos e dúvidas, consulte [docs/QUESTIONARIO_RESPONDIDO.md](docs/QUESTIONARIO_RESPONDIDO.md).

## 📊 Análises Técnicas
Relatórios completos de investigação e diagnóstico estão disponíveis no repositório.
Para detalhes do fluxo de criação de estudantes via dashboard dos responsáveis,
consulte [ANALISE_COMPLETA_FRONTEND_CRIACAO_ESTUDANTES.md](./ANALISE_COMPLETA_FRONTEND_CRIACAO_ESTUDANTES.md).

## 📱 Mobile Status
Atualmente o projeto é distribuído como PWA e **não possui** versão nativa pronta para publicação na Play Store. O plano completo para apps mobile encontra-se em [docs/PLANO_PUBLICACAO_MOBILE.md](docs/PLANO_PUBLICACAO_MOBILE.md). Para iniciar um projeto Expo usando a mesma API veja [docs/EXPO_SETUP_GUIDE.md](docs/EXPO_SETUP_GUIDE.md).
