
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import AddGuardianForm from './AddGuardianForm';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface AddGuardianDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onGuardianAdded: () => void;
}

const AddGuardianDialog: React.FC<AddGuardianDialogProps> = ({
  isOpen,
  onOpenChange,
  onGuardianAdded
}) => {
  const { toast } = useToast();

  const handleSubmit = async (values: { email: string; name?: string; phone?: string }) => {
    try {
      // Use the family invitation system instead of direct insertion
      const { error } = await supabase.rpc('send_family_invitation', {
        p_guardian_email: values.email
      });

      if (error) throw error;

      toast({
        title: "Sucesso",
        description: "Convite enviado para o responsável"
      });

      onGuardianAdded();
      onOpenChange(false);
    } catch (error: any) {
      console.error('Error sending invitation:', error);
      toast({
        title: "Erro",
        description: error?.message || "Não foi possível enviar o convite",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Adicionar Responsável</DialogTitle>
          <DialogDescription>
            Envie um convite para um responsável se conectar com você.
          </DialogDescription>
        </DialogHeader>
        <AddGuardianForm onSubmit={handleSubmit} />
      </DialogContent>
    </Dialog>
  );
};

export default AddGuardianDialog;

