import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Navigation, Loader2, MapPin, Share2, Zap, CheckCircle, AlertCircle } from "lucide-react";
import { useLocationSharingFixed } from '@/hooks/useLocationSharingFixed';

interface LocationActionFixedProps {
  onGetLocation: () => void;
  isGettingLocation: boolean;
  guardianEmails: string[];
  hasLocations: boolean;
  currentLocation?: { latitude: number; longitude: number } | null;
  senderName?: string;
}

const LocationActionFixed: React.FC<LocationActionFixedProps> = ({
  onGetLocation,
  isGettingLocation,
  guardianEmails,
  hasLocations,
  currentLocation,
  senderName
}) => {
  const { shareLocationWithAll, isSharing } = useLocationSharingFixed();
  const [shareStatus, setShareStatus] = React.useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [shareResults, setShareResults] = React.useState<{ success: number; total: number; errors: string[] } | null>(null);

  const handleShareClick = async () => {
    if (!currentLocation || !guardianEmails.length) {
      console.warn('Não é possível compartilhar: falta localização ou responsáveis');
      return;
    }

    console.log('🚀 [LocationActionFixed] Botão compartilhar clicado');
    setShareStatus('loading');
    
    try {
      const results = await shareLocationWithAll(
        currentLocation.latitude,
        currentLocation.longitude,
        guardianEmails,
        senderName || 'Estudante'
      );
      
      setShareResults(results);
      
      if (results.success > 0) {
        setShareStatus('success');
      } else {
        setShareStatus('error');
      }
    } catch (error) {
      console.error('Erro ao compartilhar com todos:', error);
      setShareStatus('error');
    }
  };

  const getStatusIcon = () => {
    switch (shareStatus) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusMessage = () => {
    if (!shareResults) return null;
    
    if (shareResults.success === shareResults.total) {
      return `✅ Enviado para ${shareResults.success} responsável(is)`;
    } else if (shareResults.success > 0) {
      return `⚠️ Enviado para ${shareResults.success}/${shareResults.total} responsável(is)`;
    } else {
      return `❌ Falha ao enviar para todos os responsáveis`;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-green-600" />
          <span>Ações Rápidas</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Botão Atualizar Localização */}
          <Button
            onClick={onGetLocation}
            disabled={isGettingLocation}
            variant="outline"
            size="lg"
            className="flex-1 h-12 text-base font-medium"
          >
            {isGettingLocation ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Obtendo...
              </>
            ) : (
              <>
                <Navigation className="mr-2 h-5 w-5" />
                Atualizar Localização
              </>
            )}
          </Button>

          {/* Botão Compartilhar - Só aparece se tiver responsáveis */}
          {guardianEmails.length > 0 && (
            <Button
              onClick={handleShareClick}
              disabled={isSharing || !hasLocations || !currentLocation}
              className="bg-green-600 text-white w-full text-sm truncate"
            >
              {isSharing ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Enviando...
                </>
              ) : (
                <>🚀 ENVIAR PARA {guardianEmails.length} RESPONSÁVEIS</>
              )}
            </Button>
          )}
        </div>

        {/* Feedback visual do status */}
        {shareStatus !== 'idle' && (
          <div className="flex items-center gap-2 text-sm">
            {getStatusIcon()}
            <span className={shareStatus === 'success' ? 'text-green-600' : shareStatus === 'error' ? 'text-red-600' : 'text-gray-600'}>
              {getStatusMessage()}
            </span>
          </div>
        )}

        {/* Informações adicionais */}
        <div className="text-sm text-muted-foreground">
          {guardianEmails.length === 0 ? (
            <span>⚠️ Adicione responsáveis para compartilhar sua localização</span>
          ) : !hasLocations || !currentLocation ? (
            <span>📍 Obtenha sua localização atual primeiro</span>
          ) : (
            <span>✅ Pronto para compartilhar com {guardianEmails.length} responsável(is)</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LocationActionFixed;