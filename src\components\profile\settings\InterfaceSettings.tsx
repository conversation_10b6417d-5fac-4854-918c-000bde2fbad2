
import React, { useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Type, Layout, Accessibility } from 'lucide-react';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useTranslation } from 'react-i18next';

export const InterfaceSettings: React.FC = () => {
  const { settings, updateSetting, isUpdating } = useUserSettings();
  const { t } = useTranslation();

  // Aplicar configurações de acessibilidade ao DOM
  useEffect(() => {
    const root = document.documentElement;
    const body = document.body;
    
    // Tamanho da fonte
    root.style.setProperty('--font-scale', 
      settings.font_size === 'small' ? '0.875' :
      settings.font_size === 'large' ? '1.125' :
      settings.font_size === 'extra-large' ? '1.25' : '1'
    );
    
    // Alto contraste
    root.classList.toggle('high-contrast', settings.high_contrast);
    
    // Movimento reduzido
    root.classList.toggle('reduce-motion', settings.reduced_motion);
    
    // Layout
    body.classList.remove('layout-compact', 'layout-spacious');
    if (settings.layout_style === 'compact') {
      body.classList.add('layout-compact');
    } else if (settings.layout_style === 'spacious') {
      body.classList.add('layout-spacious');
    }
    
  }, [settings.font_size, settings.high_contrast, settings.reduced_motion, settings.layout_style]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Layout className="h-5 w-5" />
          {t('settings.interface.title', 'Interface & Accessibility')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <Type className="h-4 w-4 text-blue-500" />
            <Label>{t('settings.interface.fontSize.label', 'Font Size')}</Label>
          </div>
          <Select
            value={settings.font_size}
            onValueChange={(value) => updateSetting('font_size', value as any)}
            disabled={isUpdating}
          >
            <SelectTrigger className="max-w-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="small">{t('settings.interface.fontSize.options.small', 'Small')}</SelectItem>
              <SelectItem value="medium">{t('settings.interface.fontSize.options.medium', 'Medium')}</SelectItem>
              <SelectItem value="large">{t('settings.interface.fontSize.options.large', 'Large')}</SelectItem>
              <SelectItem value="extra-large">{t('settings.interface.fontSize.options.extraLarge', 'Extra Large')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <Layout className="h-4 w-4 text-green-500" />
            <Label>{t('settings.interface.layoutStyle.label', 'Layout Style')}</Label>
          </div>
          <Select
            value={settings.layout_style}
            onValueChange={(value) => updateSetting('layout_style', value as any)}
            disabled={isUpdating}
          >
            <SelectTrigger className="max-w-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="default">{t('settings.interface.layoutStyle.options.default', 'Default')}</SelectItem>
              <SelectItem value="compact">{t('settings.interface.layoutStyle.options.compact', 'Compact')}</SelectItem>
              <SelectItem value="spacious">{t('settings.interface.layoutStyle.options.spacious', 'Spacious')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Accessibility className="h-4 w-4 text-purple-500" />
            <div>
              <Label>{t('settings.interface.highContrast.label', 'High Contrast')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.interface.highContrast.description', 'Increase contrast for better visibility')}
              </p>
            </div>
          </div>
          <Switch
            checked={settings.high_contrast}
            onCheckedChange={(value) => updateSetting('high_contrast', value)}
            disabled={isUpdating}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Accessibility className="h-4 w-4 text-orange-500" />
            <div>
              <Label>{t('settings.interface.reducedMotion.label', 'Reduce Motion')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('settings.interface.reducedMotion.description', 'Minimize animations and transitions')}
              </p>
            </div>
          </div>
          <Switch
            checked={settings.reduced_motion}
            onCheckedChange={(value) => updateSetting('reduced_motion', value)}
            disabled={isUpdating}
          />
        </div>
      </CardContent>
    </Card>
  );
};
