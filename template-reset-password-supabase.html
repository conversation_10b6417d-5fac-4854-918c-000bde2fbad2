<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Redefinir Senha - Sistema Monitore</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa;">
  <div style="max-width: 600px; margin: 0 auto; background-color: white;">
    <!-- Header -->
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
      <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 300;">
        🔐 Sistema Monitore
      </h1>
      <p style="color: rgba(255,255,255,0.9); margin: 5px 0 0 0; font-size: 16px;">
        Localização Familiar Segura
      </p>
    </div>
    
    <!-- Content -->
    <div style="padding: 40px 30px;">
      <h2 style="color: #2c3e50; margin: 0 0 20px 0; font-size: 24px; font-weight: 400;">
        Redefinir Sua Senha
      </h2>
      
      <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
        Olá! 👋
      </p>
      
      <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
        Recebemos uma solicitação para redefinir a senha da sua conta <strong>{{ .Email }}</strong> no Sistema Monitore.
      </p>
      
      <!-- Action Button -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="{{ .ConfirmationURL }}" 
           style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                  color: white; 
                  padding: 15px 30px; 
                  text-decoration: none; 
                  border-radius: 8px; 
                  font-size: 16px; 
                  font-weight: 600; 
                  display: inline-block;
                  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);">
          🔑 Redefinir Minha Senha
        </a>
      </div>
      
      <!-- Security Notice -->
      <div style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 20px; margin: 25px 0; border-radius: 8px;">
        <h3 style="margin: 0 0 10px 0; color: #856404; font-size: 16px;">
          🛡️ Importante - Segurança
        </h3>
        <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
          Este link expira em 1 hora por segurança. Se não foi você quem solicitou, pode ignorar este email.
        </p>
      </div>
      
      <!-- Alternative Text Link -->
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
        <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 14px; font-weight: 600;">
          Problemas com o botão? Use este link:
        </p>
        <p style="margin: 0; word-break: break-all;">
          <a href="{{ .ConfirmationURL }}" style="color: #667eea; font-size: 12px;">{{ .ConfirmationURL }}</a>
        </p>
      </div>
      
      <p style="color: #6c757d; font-size: 14px; line-height: 1.5; margin: 20px 0 0 0;">
        <strong>Não foi você?</strong> Pode ignorar este email com segurança. Sua senha permanecerá inalterada.
      </p>
    </div>
    
    <!-- Footer -->
    <div style="background-color: #f8f9fa; padding: 25px 30px; border-top: 1px solid #e9ecef;">
      <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 14px; font-weight: 600;">
        📱 Sistema Monitore - Localização Familiar
      </p>
      <p style="margin: 0; color: #868e96; font-size: 12px; line-height: 1.4;">
        Este é um email automático do sistema de recuperação de senha. Não responda a esta mensagem.<br>
        Para suporte, visite: <a href="https://sistema-monitore.com.br" style="color: #667eea;">sistema-monitore.com.br</a>
      </p>
    </div>
  </div>
</body>
</html> 