# 🧠 Cursor Rules & Development Guidelines

*Atualizado: 2025-01-06*

---

## 🚨 PROTOCOLO BREAK-SAFE 

**🔗 CONSULTE SEMPRE:** [Ultimate Break-Safe Plan](.cursor/rules/break-safe-protocol.md)

**RESUMO CRÍTICO:**
1. ✅ `npm run build && npm test && npm run dev` - Sistema funciona?
2. ✅ Backup estado atual (`git add -A && git commit && git push`)
3. ✅ Plano de rollback definido
4. ✅ Teste manual do fluxo principal
5. ✅ Verificação de funcionalidade similar existente

**MUDANÇAS CRÍTICAS = Auth, Edge Functions, RLS, Migrações**

> ⚠️ **OBRIGATÓRIO**: Siga o protocolo completo em `.cursor/rules/break-safe-protocol.md`

---

## ⚙️ Git Command Automation
**Comando padrão quando usuário digita apenas "git":**
```bash
git status
git add -A
git commit -m "update"
git push origin main
```

---

## 👨‍💻 Seu Papel
Você é um(a) engenheiro(a) sênior, focado em sistemas escaláveis, manuteníveis e colaboração estratégica. **NUNCA assume que algo está funcionando sem evidência concreta.** Oriente, revise e priorize qualidade e clareza.

---

## 🏗️ Arquitetura e Stack do Projeto
- **Frontend:** React 18 + TypeScript + Vite (porta 4000)
- **UI:** shadcn/ui, Radix UI, TailwindCSS
- **Backend:** Supabase (Auth, PostgreSQL, Edge Functions)
- **Integração:** MapBox (mapas), Resend (emails)
- **Arquitetura:** SPA com RLS (Row Level Security)

### Estrutura Crítica de Arquivos
- `/src/contexts/UnifiedAuthContext.tsx` – Auth principal
- `/src/integrations/supabase/client.ts` – Conexão Supabase
- `/src/App.tsx` – Rotas & guards
- `/supabase/functions/` – Edge Functions (invite-student, etc)
- `/src/components/student/hooks/useInviteStudent.ts` – Lógica de convites
- `/src/lib/services/family/FamilyInvitationService.ts` – Serviços de família
- `/src/types/` – Definições TypeScript

---

## 🧱 Estrutura e Organização
- **Tamanho de arquivo:** Máximo 300 linhas
- **Separação:** Organize por feature (student/, guardian/, map/)
- **Nomenclatura:** Sempre clara e consistente
- **TypeScript:** Use APENAS .ts e .tsx (não .js/.jsx)
- **Imports:** Sempre relativos ou com @/ prefix
- **Props:** Use interfaces TypeScript explícitas

### Padrões de Nomenclatura
```typescript
// ✅ Componentes
export const StudentDashboard: React.FC<Props> = () => {}

// ✅ Hooks
export const useStudentData = () => {}

// ✅ Services
export class StudentService extends BaseService {}

// ✅ Types
export interface StudentProfile {}
```

---

## 🔧 Edge Functions & APIs (CRÍTICO)
### Debugging de Edge Functions
1. **Verificar existência:** `mcp_supabase_list_edge_functions`
2. **Logs em tempo real:** `mcp_supabase_get_logs --service=edge-function`
3. **Estrutura da função:** Verificar arquivos em `/supabase/functions/`
4. **Headers obrigatórios:** Sempre incluir `x-site-url` para CORS
5. **Teste direto:** Usar Postman/curl antes de integrar frontend

### Estrutura Padrão Edge Function
```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

serve(async (req: Request) => {
  // CORS headers obrigatórios
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-site-url',
  }

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Lógica da função
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})
```

---

## 🛡️ Row Level Security (RLS)
### Padrões de Políticas RLS
```sql
-- ✅ Guardians criam convites
CREATE POLICY "Guardians can create invitations for students" 
ON family_invitations 
FOR INSERT 
WITH CHECK (guardian_id = auth.uid());

-- ✅ Estudantes aceitam convites
CREATE POLICY "Students can accept/reject invitations sent to them" 
ON family_invitations 
FOR UPDATE 
USING (student_email = auth.email() OR student_id = auth.uid());
```

### Debugging RLS
1. **Verificar se RLS está ativo:** `SELECT relname, rowsecurity FROM pg_class WHERE relname = 'tabela';`
2. **Listar políticas:** `SELECT * FROM pg_policies WHERE tablename = 'tabela';`
3. **Testar sem RLS:** Temporariamente desabilitar para debug
4. **Logs de auth:** Verificar `auth.uid()` e `auth.email()`

---

## 🌍 Ambientes e Configuração
### Variáveis Críticas
```bash
# Supabase
VITE_SUPABASE_URL=https://rsvjnndhbyyxktbczlnk.supabase.co
VITE_SUPABASE_ANON_KEY=eyJ...

# MapBox (96 caracteres)
VITE_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoidGVjaC1lZHUtbGFiIi...

# Resend (apenas em Edge Functions)
RESEND_API_KEY=re_...
```

### Validação de Config
```typescript
// ✅ Sempre validar tokens
const mapboxToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN;
if (!mapboxToken || mapboxToken.length !== 96) {
  console.error('Token MapBox inválido:', mapboxToken?.length);
}
```

---

## 🧠 Modo Planner (ATUALIZADO)
1. **Análise de impacto:** Quais sistemas serão afetados?
2. **Dependências:** Edge Functions, RLS, tipos TypeScript
3. **Perguntas específicas:**
   - Precisa de nova Edge Function?
   - RLS está configurado corretamente?
   - Tipos estão atualizados?
   - Headers CORS estão incluídos?
4. **Plano de teste:** Como validar a mudança?
5. **Rollback:** Como reverter se der problema?

---

## 🧪 Modo Debugger (EXPANDIDO)
### Categorias de Problemas
1. **500 Edge Function:** Verificar logs, estrutura, RLS
2. **406/404 API:** Verificar RLS, permissões, políticas
3. **CORS:** Headers x-site-url, preflight OPTIONS
4. **Auth:** Verificar tokens, redirecionamentos, contextos
5. **TypeScript:** Imports, tipos, builds

### Ferramentas de Debug
```bash
# Logs do Supabase
mcp_supabase_get_logs --service=edge-function
mcp_supabase_get_logs --service=api

# Verificar tabelas
mcp_supabase_execute_sql --query="SELECT * FROM table LIMIT 5"

# Testar build
npm run build && npm run dev
```

---

## 🔐 Segurança Avançada
### Validação de Entrada
```typescript
// ✅ Sempre validar CPF
const cpf = data.cpf?.replace(/\D/g, '');
if (!cpf || cpf.length !== 11) {
  throw new Error('CPF inválido');
}

// ✅ Sanitizar emails
const email = data.email?.trim().toLowerCase();
if (!email?.includes('@')) {
  throw new Error('Email inválido');
}
```

### Proteções Obrigatórias
- **RLS ativo** em todas as tabelas sensíveis
- **Validação de entrada** em Edge Functions
- **Rate limiting** implícito via Supabase
- **Headers CORS** explícitos em todas as APIs
- **Logs de erro** detalhados mas seguros

---

## 🧪 Testes e Validação
### Checklist de Teste
- [ ] Login funciona para todos os tipos de usuário
- [ ] Formulários validam entrada corretamente
- [ ] Edge Functions retornam status corretos
- [ ] RLS bloqueia acesso não autorizado
- [ ] Headers CORS estão presentes
- [ ] Emails são enviados (se configurado)

### Scripts de Teste
```bash
# Teste de conexão
npm run dev
curl http://localhost:4000/

# Teste de build
npm run build

# Teste de Edge Function
curl -X POST https://projeto.supabase.co/functions/v1/function \
  -H "Content-Type: application/json" \
  -H "x-site-url: http://localhost:4000"
```

---

## 🚨 Troubleshooting Rápido
### Problemas Comuns
1. **Edge Function 500:**
   - Verificar RLS da tabela target
   - Conferir estrutura da função
   - Validar headers CORS

2. **Token MapBox inválido:**
   - Verificar comprimento (96 chars)
   - Limpar cache do Vite
   - Restart do servidor

3. **TypeScript errors:**
   - Verificar imports (.ts vs .js)
   - Atualizar tipos do Supabase
   - Build limpo: `rm -rf dist && npm run build`

---

## ✅ Checklist Pré-Mudança (OBRIGATÓRIO)
**SEMPRE executar antes de mudanças críticas:**

### 🔍 Verificação Atual
- [ ] `npm run build` - sem erros
- [ ] `npm run dev` - servidor inicia
- [ ] Login manual funciona
- [ ] Dashboard carrega sem erro 500

### 🎯 Áreas Críticas
- [ ] **Auth Flow:** Login, logout, reset senha
- [ ] **Edge Functions:** Estrutura, logs, CORS
- [ ] **RLS:** Políticas ativas e corretas
- [ ] **Types:** Imports TypeScript válidos

### 📋 Pós-Mudança
- [ ] Teste manual completo
- [ ] Verificar logs de erro
- [ ] Confirmar funcionalidades críticas
- [ ] Commit + push das mudanças

---

## 📝 Logs e Documentação
### Padrão de Logs
```typescript
// ✅ Logs informativos
console.log('[ComponentName] Action started:', data);

// ✅ Logs de erro
console.error('[ComponentName] Error:', error.message);

// ❌ Nunca logar dados sensíveis
console.log('Password:', password); // NUNCA!
```

### Commits Padronizados
```bash
# ✅ Commits descritivos
git commit -m "feat(edge-functions): add invite-student function with RLS support"
git commit -m "fix(rls): correct family_invitations policies for guardian access"
git commit -m "refactor(types): update Supabase types for family invitations"
```

---

> **🚨 IMPORTANTE:** Este documento é OBRIGATÓRIO antes de mudanças críticas. Sempre consulte exemplos reais em `/docs/` e `/scripts/`. Em caso de dúvida, PARE e peça esclarecimentos antes de prosseguir. 