
#!/bin/bash

# 🛡️ Performance Baseline - Métricas de Performance
# Protocolo Anti-Quebra - Fase 3: Verificação de Performance

echo "📊 [PERFORMANCE-BASELINE] Coletando métricas de performance..."
echo "📅 Data: $(date)"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Arquivo de baseline
BASELINE_FILE="performance-baseline.json"
CURRENT_METRICS="current-metrics.json"

echo -e "${BLUE}🔍 Coletando métricas atuais...${NC}"

# 1. Build Size Analysis
echo ""
echo "📦 Analisando tamanho do build..."

if npm run build &> /dev/null; then
    # Tamanho total do bundle
    BUNDLE_SIZE_BYTES=$(du -sb dist 2>/dev/null | cut -f1)
    BUNDLE_SIZE_MB=$(echo "scale=2; $BUNDLE_SIZE_BYTES / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
    
    # Arquivos JavaScript
    JS_SIZE_BYTES=$(find dist -name "*.js" -exec du -cb {} + 2>/dev/null | tail -1 | cut -f1)
    JS_SIZE_KB=$(echo "scale=2; $JS_SIZE_BYTES / 1024" | bc -l 2>/dev/null || echo "0")
    
    # Arquivos CSS
    CSS_SIZE_BYTES=$(find dist -name "*.css" -exec du -cb {} + 2>/dev/null | tail -1 | cut -f1)
    CSS_SIZE_KB=$(echo "scale=2; $CSS_SIZE_BYTES / 1024" | bc -l 2>/dev/null || echo "0")
    
    # Número de arquivos
    JS_FILES_COUNT=$(find dist -name "*.js" | wc -l)
    CSS_FILES_COUNT=$(find dist -name "*.css" | wc -l)
    TOTAL_FILES=$(find dist -type f | wc -l)
    
    echo "  📊 Bundle total: ${BUNDLE_SIZE_MB}MB"
    echo "  📄 JavaScript: ${JS_SIZE_KB}KB ($JS_FILES_COUNT arquivos)"
    echo "  🎨 CSS: ${CSS_SIZE_KB}KB ($CSS_FILES_COUNT arquivos)"
    echo "  📁 Total de arquivos: $TOTAL_FILES"
else
    echo -e "${RED}❌ Falha no build - não foi possível coletar métricas${NC}"
    exit 1
fi

# 2. Build Time Analysis
echo ""
echo "⏱️  Medindo tempo de build..."

BUILD_START=$(date +%s%N)
npm run build &> /dev/null
BUILD_END=$(date +%s%N)
BUILD_TIME_MS=$(echo "scale=2; ($BUILD_END - $BUILD_START) / 1000000" | bc -l)
BUILD_TIME_S=$(echo "scale=2; $BUILD_TIME_MS / 1000" | bc -l)

echo "  ⚡ Tempo de build: ${BUILD_TIME_S}s"

# 3. Dependencies Analysis
echo ""
echo "📦 Analisando dependências..."

# Contar dependências
PROD_DEPS=$(jq '.dependencies | length' package.json 2>/dev/null || echo "0")
DEV_DEPS=$(jq '.devDependencies | length' package.json 2>/dev/null || echo "0")
TOTAL_DEPS=$((PROD_DEPS + DEV_DEPS))

# Tamanho do node_modules
if [ -d "node_modules" ]; then
    NODE_MODULES_SIZE_BYTES=$(du -sb node_modules 2>/dev/null | cut -f1)
    NODE_MODULES_SIZE_MB=$(echo "scale=2; $NODE_MODULES_SIZE_BYTES / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
else
    NODE_MODULES_SIZE_MB="0"
fi

echo "  📚 Dependências de produção: $PROD_DEPS"
echo "  🛠️  Dependências de desenvolvimento: $DEV_DEPS"
echo "  📊 Total de dependências: $TOTAL_DEPS"
echo "  💾 Tamanho node_modules: ${NODE_MODULES_SIZE_MB}MB"

# 4. Code Quality Metrics
echo ""
echo "📝 Analisando qualidade do código..."

# Contar linhas de código
TS_LINES=$(find src -name "*.ts" -o -name "*.tsx" | xargs wc -l 2>/dev/null | tail -1 | awk '{print $1}' || echo "0")
CSS_LINES=$(find src -name "*.css" | xargs wc -l 2>/dev/null | tail -1 | awk '{print $1}' || echo "0")
TOTAL_LINES=$((TS_LINES + CSS_LINES))

# Contar arquivos
TS_FILES=$(find src -name "*.ts" -o -name "*.tsx" | wc -l)
CSS_FILES=$(find src -name "*.css" | wc -l)
TOTAL_SOURCE_FILES=$((TS_FILES + CSS_FILES))

echo "  📄 Linhas TypeScript/React: $TS_LINES"
echo "  🎨 Linhas CSS: $CSS_LINES" 
echo "  📊 Total de linhas: $TOTAL_LINES"
echo "  📁 Arquivos de código: $TOTAL_SOURCE_FILES"

# 5. Memory Usage (node_modules)
echo ""
echo "💾 Análise de uso de memória..."

# Tamanho médio por dependência
if [ $TOTAL_DEPS -gt 0 ]; then
    AVG_DEP_SIZE=$(echo "scale=2; $NODE_MODULES_SIZE_MB / $TOTAL_DEPS" | bc -l)
else
    AVG_DEP_SIZE="0"
fi

echo "  📊 Tamanho médio por dependência: ${AVG_DEP_SIZE}MB"

# 6. Gerar JSON com métricas atuais
cat > "$CURRENT_METRICS" << EOF
{
  "timestamp": "$(date -Iseconds)",
  "build": {
    "total_size_mb": $BUNDLE_SIZE_MB,
    "js_size_kb": $JS_SIZE_KB,
    "css_size_kb": $CSS_SIZE_KB,
    "js_files_count": $JS_FILES_COUNT,
    "css_files_count": $CSS_FILES_COUNT,
    "total_files": $TOTAL_FILES,
    "build_time_seconds": $BUILD_TIME_S
  },
  "dependencies": {
    "production": $PROD_DEPS,
    "development": $DEV_DEPS,
    "total": $TOTAL_DEPS,
    "node_modules_size_mb": $NODE_MODULES_SIZE_MB,
    "avg_dependency_size_mb": $AVG_DEP_SIZE
  },
  "code": {
    "typescript_lines": $TS_LINES,
    "css_lines": $CSS_LINES,
    "total_lines": $TOTAL_LINES,
    "source_files": $TOTAL_SOURCE_FILES
  }
}
EOF

# 7. Comparação com baseline (se existir)
echo ""
echo -e "${BLUE}📈 Análise de tendências...${NC}"

if [ -f "$BASELINE_FILE" ]; then
    echo "📊 Comparando com baseline anterior:"
    
    # Extrair métricas do baseline
    BASELINE_SIZE=$(jq -r '.build.total_size_mb' "$BASELINE_FILE" 2>/dev/null || echo "0")
    BASELINE_BUILD_TIME=$(jq -r '.build.build_time_seconds' "$BASELINE_FILE" 2>/dev/null || echo "0")
    BASELINE_DEPS=$(jq -r '.dependencies.total' "$BASELINE_FILE" 2>/dev/null || echo "0")
    
    # Calcular diferenças
    SIZE_DIFF=$(echo "scale=2; $BUNDLE_SIZE_MB - $BASELINE_SIZE" | bc -l)
    TIME_DIFF=$(echo "scale=2; $BUILD_TIME_S - $BASELINE_BUILD_TIME" | bc -l)
    DEPS_DIFF=$((TOTAL_DEPS - BASELINE_DEPS))
    
    # Mostrar comparações
    if (( $(echo "$SIZE_DIFF > 0" | bc -l) )); then
        echo -e "  📈 Tamanho: ${RED}+${SIZE_DIFF}MB${NC} (⚠️  Aumento)"
    elif (( $(echo "$SIZE_DIFF < 0" | bc -l) )); then
        echo -e "  📉 Tamanho: ${GREEN}${SIZE_DIFF}MB${NC} (✅ Diminuição)"
    else
        echo -e "  📊 Tamanho: ${GREEN}Sem mudança${NC}"
    fi
    
    if (( $(echo "$TIME_DIFF > 0" | bc -l) )); then
        echo -e "  ⏱️  Build: ${YELLOW}+${TIME_DIFF}s${NC} (Mais lento)"
    elif (( $(echo "$TIME_DIFF < 0" | bc -l) )); then
        echo -e "  ⚡ Build: ${GREEN}${TIME_DIFF}s${NC} (Mais rápido)"
    else
        echo -e "  ⏱️  Build: ${GREEN}Sem mudança${NC}"
    fi
    
    if [ $DEPS_DIFF -gt 0 ]; then
        echo -e "  📦 Dependências: ${YELLOW}+${DEPS_DIFF}${NC} (Adicionadas)"
    elif [ $DEPS_DIFF -lt 0 ]; then
        echo -e "  📦 Dependências: ${GREEN}${DEPS_DIFF}${NC} (Removidas)"
    else
        echo -e "  📦 Dependências: ${GREEN}Sem mudança${NC}"
    fi
    
else
    echo "📝 Primeira execução - criando baseline..."
    cp "$CURRENT_METRICS" "$BASELINE_FILE"
    echo "✅ Baseline salvo em $BASELINE_FILE"
fi

# 8. Alertas de Performance
echo ""
echo -e "${BLUE}🚨 Verificações de performance...${NC}"

# Alertas por tamanho
if (( $(echo "$BUNDLE_SIZE_MB > 10" | bc -l) )); then
    echo -e "${RED}⚠️  ALERTA: Bundle muito grande (${BUNDLE_SIZE_MB}MB > 10MB)${NC}"
elif (( $(echo "$BUNDLE_SIZE_MB > 5" | bc -l) )); then
    echo -e "${YELLOW}⚠️  ATENÇÃO: Bundle grande (${BUNDLE_SIZE_MB}MB > 5MB)${NC}"
else
    echo -e "${GREEN}✅ Bundle em tamanho aceitável (${BUNDLE_SIZE_MB}MB)${NC}"
fi

# Alertas por tempo de build
if (( $(echo "$BUILD_TIME_S > 60" | bc -l) )); then
    echo -e "${RED}⚠️  ALERTA: Build muito lento (${BUILD_TIME_S}s > 60s)${NC}"
elif (( $(echo "$BUILD_TIME_S > 30" | bc -l) )); then
    echo -e "${YELLOW}⚠️  ATENÇÃO: Build lento (${BUILD_TIME_S}s > 30s)${NC}"
else
    echo -e "${GREEN}✅ Build em tempo aceitável (${BUILD_TIME_S}s)${NC}"
fi

# Alertas por número de dependências
if [ $TOTAL_DEPS -gt 100 ]; then
    echo -e "${RED}⚠️  ALERTA: Muitas dependências ($TOTAL_DEPS > 100)${NC}"
elif [ $TOTAL_DEPS -gt 50 ]; then
    echo -e "${YELLOW}⚠️  ATENÇÃO: Várias dependências ($TOTAL_DEPS > 50)${NC}"
else
    echo -e "${GREEN}✅ Número de dependências aceitável ($TOTAL_DEPS)${NC}"
fi

# 9. Atualizar baseline se tudo estiver OK
echo ""
echo "💾 Atualizando baseline..."
cp "$CURRENT_METRICS" "$BASELINE_FILE"
echo "✅ Baseline atualizado com métricas atuais"

# Cleanup
rm -f "$CURRENT_METRICS"

echo ""
echo -e "${GREEN}📊 Coleta de métricas de performance concluída!${NC}"
echo "📄 Métricas salvas em: $BASELINE_FILE"
