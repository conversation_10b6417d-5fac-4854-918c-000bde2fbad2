# API Availability Cache - Enhanced Debugging Guide

## Problem Summary

The `/api/resolve-location` endpoint was generating repeated 404 errors in production builds despite implementing an API availability cache. This guide documents the enhanced debugging system implemented to identify and fix the issue.

## Enhanced Cache Features

### 1. **Comprehensive Logging System**
- **Instance tracking**: Each cache instance has a unique ID for debugging
- **Detailed state logging**: Complete cache state with timestamps and status
- **Operation tracking**: All cache operations (hits, misses, failures) are logged
- **Global access**: Cache available as `window.apiCache` for browser debugging

### 2. **Concurrent Call Prevention**
- **Pending call tracking**: Prevents multiple simultaneous calls to the same endpoint
- **Race condition protection**: Ensures only one API call per endpoint at a time
- **Automatic cleanup**: Pending calls are cleared when API responses are received

### 3. **Production-Specific Optimizations**
- **Immediate 404 handling**: 404 errors immediately mark endpoints as unavailable
- **Singleton enforcement**: Ensures only one cache instance exists across the application
- **Cross-component persistence**: Cache state persists across React component re-renders

## Testing the Enhanced Cache

### 1. **Open Browser Console**
Navigate to: `http://localhost:4174/student-dashboard`

### 2. **Monitor Cache Behavior**
Look for these log patterns:

#### **First API Call (Expected)**
```
[ApiCache] 🆕 Creating singleton instance
[ApiCache] 🌐 Added to window.apiCache for debugging
[StudentDashboard] 🎯 resolveLocationViaServer called with lat=undefined, lon=undefined
[ApiCache] 📊 Cache state (before resolveLocationViaServer): { instanceId: "abc123", cacheSize: 0, ... }
[ApiCache] 🔍 shouldAttemptCall for /api/resolve-location (instance: abc123)
[ApiCache] ✅ Allowing call to /api/resolve-location: { shouldCall: true, reason: "API available or not yet tested" }
[ApiCache] 🔄 Marked /api/resolve-location as pending call
[StudentDashboard] ✅ Proceeding with API call: API available or not yet tested
```

#### **After 404 Response (Expected)**
```
[ApiCache] ❌ API failure for /api/resolve-location: { instanceId: "abc123", statusCode: 404, ... }
[ApiCache] 🚫 /api/resolve-location marked as UNAVAILABLE after 1 failures (status: 404)
[ApiCache] ✅ Cleared pending call for /api/resolve-location
```

#### **Subsequent Calls (Should Be Blocked)**
```
[ApiCache] ♻️ Returning existing instance: abc123
[StudentDashboard] 🎯 resolveLocationViaServer called with lat=undefined, lon=undefined
[ApiCache] 📊 Cache state (before resolveLocationViaServer): { instanceId: "abc123", cacheSize: 1, ... }
[ApiCache] 🔍 shouldAttemptCall for /api/resolve-location (instance: abc123)
[ApiCache] 🚫 Blocking call to /api/resolve-location: { shouldCall: false, reason: "API marked as unavailable..." }
[StudentDashboard] 🚫 Skipping API call: API marked as unavailable. Retry in 300s (failures: 1)
```

### 3. **Browser Console Commands**
Use these commands to inspect cache state:

```javascript
// Check cache instance info
window.apiCache.getInstanceInfo()

// View current cache state
window.apiCache.logCacheState('manual check')

// Check specific endpoint status
window.apiCache.getApiStatus('/api/resolve-location')

// Clear cache for testing
window.apiCache.clearEndpoint('/api/resolve-location')
```

## Debugging Scenarios

### **Scenario 1: Cache Working Correctly**
- ✅ First call allowed and marked as pending
- ✅ 404 response marks endpoint as unavailable
- ✅ Subsequent calls are blocked with clear reason
- ✅ No repeated 404 errors in console

### **Scenario 2: Multiple Cache Instances (Problem)**
- ❌ Multiple "Creating singleton instance" messages
- ❌ Different instance IDs in logs
- ❌ Cache state not persisting between calls

### **Scenario 3: Race Conditions (Problem)**
- ❌ Multiple "Allowing call" messages for same endpoint
- ❌ Concurrent API calls before first response
- ❌ Repeated 404 errors before cache is populated

### **Scenario 4: Cache Bypass (Problem)**
- ❌ API calls made without "shouldAttemptCall" logs
- ❌ Missing cache state logs
- ❌ Direct fetch calls bypassing cache system

## Expected Results

### **Success Indicators**
1. **Single 404 error**: Only one 404 error per endpoint
2. **Cache persistence**: Same instance ID across all operations
3. **Call blocking**: Subsequent calls blocked with clear reasons
4. **Fallback behavior**: Application uses default/GPS coordinates seamlessly
5. **User experience**: No repeated error messages, smooth operation

### **Performance Metrics**
- **Cache hit rate**: >90% after first API failure
- **Reduced network calls**: Significant decrease in failed requests
- **Faster fallback**: Immediate fallback without network delay
- **Memory efficiency**: Single cache instance, minimal memory usage

## Troubleshooting

### **If Repeated 404s Still Occur**
1. Check for multiple cache instances in logs
2. Verify singleton pattern is working
3. Look for direct API calls bypassing cache
4. Check for component re-mount issues

### **If Cache Not Persisting**
1. Verify instance IDs are consistent
2. Check for React strict mode double-mounting
3. Look for cache clearing in component lifecycle
4. Verify imports are using same cache instance

### **If Fallback Not Working**
1. Check fallback logic in StudentDashboard and useMapLocation
2. Verify coordinates are being returned correctly
3. Look for error handling in location services
4. Check toast notifications for user feedback

## Production Monitoring

The enhanced cache system provides comprehensive logging for production monitoring:
- All cache operations are logged with context
- Instance tracking helps identify multiple instance issues
- Performance metrics help optimize cache behavior
- Global access allows runtime debugging and monitoring

This system should eliminate repeated 404 errors and provide seamless fallback behavior in production builds.
