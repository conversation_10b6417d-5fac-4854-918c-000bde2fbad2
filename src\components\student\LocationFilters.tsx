import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { 
  Filter,
  RotateCcw,
  MapPin,
  Clock,
  Target,
  Copy
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

export interface LocationFilters {
  removeDuplicates: boolean;
  timeRange: 'all' | '1h' | '6h' | '24h' | '7d' | '30d';
  accuracyFilter: 'all' | 'high' | 'medium' | 'low';
  minDistance: number; // metros entre localizações para considerar duplicata
  sortBy: 'newest' | 'oldest' | 'accuracy';
}

interface LocationFiltersProps {
  filters: LocationFilters;
  onFiltersChange: (filters: LocationFilters) => void;
  totalLocations: number;
  filteredCount: number;
}

const LocationFiltersComponent: React.FC<LocationFiltersProps> = ({
  filters,
  onFiltersChange,
  totalLocations,
  filteredCount
}) => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  const resetFilters = () => {
    onFiltersChange({
      removeDuplicates: false,
      timeRange: 'all',
      accuracyFilter: 'all',
      minDistance: 50,
      sortBy: 'newest'
    });
  };

  const activeFiltersCount = [
    filters.removeDuplicates,
    filters.timeRange !== 'all',
    filters.accuracyFilter !== 'all',
    filters.sortBy !== 'newest'
  ].filter(Boolean).length;

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-blue-600" />
            <CardTitle className="text-sm font-medium">
              {t('locationFilters.title', 'Filtros de Localização')}
            </CardTitle>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFiltersCount} {activeFiltersCount === 1 ? 'filtro' : 'filtros'}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {filteredCount} de {totalLocations}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 px-2"
            >
              {isExpanded ? 'Ocultar' : 'Mostrar'}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Remover Duplicatas */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Copy className="h-3 w-3" />
                Duplicatas
              </Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="removeDuplicates"
                  checked={filters.removeDuplicates}
                  onCheckedChange={(checked) =>
                    onFiltersChange({ ...filters, removeDuplicates: !!checked })
                  }
                />
                <Label htmlFor="removeDuplicates" className="text-sm">
                  Remover duplicatas
                </Label>
              </div>
              {filters.removeDuplicates && (
                <p className="text-xs text-gray-500">
                  Raio: {filters.minDistance}m
                </p>
              )}
            </div>

            {/* Período */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Clock className="h-3 w-3" />
                Período
              </Label>
              <Select
                value={filters.timeRange}
                onValueChange={(value: any) =>
                  onFiltersChange({ ...filters, timeRange: value })
                }
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="1h">Última hora</SelectItem>
                  <SelectItem value="6h">Últimas 6 horas</SelectItem>
                  <SelectItem value="24h">Últimas 24 horas</SelectItem>
                  <SelectItem value="7d">Últimos 7 dias</SelectItem>
                  <SelectItem value="30d">Últimos 30 dias</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Precisão */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Target className="h-3 w-3" />
                Precisão
              </Label>
              <Select
                value={filters.accuracyFilter}
                onValueChange={(value: any) =>
                  onFiltersChange({ ...filters, accuracyFilter: value })
                }
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  <SelectItem value="high">Alta</SelectItem>
                  <SelectItem value="medium">Média</SelectItem>
                  <SelectItem value="low">Baixa</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Ordenação */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <MapPin className="h-3 w-3" />
                Ordenar por
              </Label>
              <Select
                value={filters.sortBy}
                onValueChange={(value: any) =>
                  onFiltersChange({ ...filters, sortBy: value })
                }
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Mais recente</SelectItem>
                  <SelectItem value="oldest">Mais antiga</SelectItem>
                  <SelectItem value="accuracy">Melhor precisão</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {filteredCount < totalLocations && (
                <span>
                  {totalLocations - filteredCount} localização(ões) filtrada(s)
                </span>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="h-8 px-3"
              disabled={activeFiltersCount === 0}
            >
              <RotateCcw className="h-3 w-3 mr-2" />
              Limpar filtros
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default LocationFiltersComponent;