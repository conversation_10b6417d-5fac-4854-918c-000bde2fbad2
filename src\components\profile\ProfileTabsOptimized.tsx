
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { User, Shield, Users, Settings } from 'lucide-react';
import ProfileEditFormOptimized from './ProfileEditFormOptimized';
import StudentBirthDateSection from './StudentBirthDateSection';
import PrivacySection from './PrivacySection';
import RelationshipsSection from './RelationshipsSection';
import SettingsSection from './SettingsSection';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useTranslation } from 'react-i18next';

const ProfileTabsOptimized: React.FC = () => {
  const { user } = useUser();
  const { t } = useTranslation();
  const userType = user?.user_metadata?.user_type;

  return (
    <Tabs defaultValue="profile" className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="profile" className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span className="hidden sm:inline">{t('profile.tabs.profile')}</span>
        </TabsTrigger>
        <TabsTrigger value="privacy" className="flex items-center gap-2">
          <Shield className="h-4 w-4" />
          <span className="hidden sm:inline">{t('profile.tabs.privacy')}</span>
        </TabsTrigger>
        <TabsTrigger value="relationships" className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          <span className="hidden sm:inline">{t('profile.tabs.relationships')}</span>
        </TabsTrigger>
        <TabsTrigger value="settings" className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          <span className="hidden sm:inline">{t('profile.tabs.settings')}</span>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="profile" className="space-y-6">
        <ProfileEditFormOptimized />
        {userType === 'student' && <StudentBirthDateSection />}
      </TabsContent>

      <TabsContent value="privacy">
        <PrivacySection />
      </TabsContent>

      <TabsContent value="relationships">
        <RelationshipsSection />
      </TabsContent>

      <TabsContent value="settings">
        <SettingsSection />
      </TabsContent>
    </Tabs>
  );
};

export default ProfileTabsOptimized;
