
import React from 'react';
import { Alert, AlertTitle, AlertDescription } from '../ui/alert';
import { AlertCircle } from 'lucide-react';

interface MapErrorOverlayProps {
  error: string | null;
}

const MapErrorOverlay: React.FC<MapErrorOverlayProps> = ({ error }) => {
  if (!error) return null;

  return (
    <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-20">
      <Alert variant="destructive" className="max-w-md space-y-2">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Erro no mapa</AlertTitle>
        <AlertDescription className="text-sm">{error}</AlertDescription>
      </Alert>
    </div>
  );
};

export default MapErrorOverlay;
