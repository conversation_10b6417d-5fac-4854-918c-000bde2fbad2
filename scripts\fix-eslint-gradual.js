#!/usr/bin/env node

/**
 * Script para correção gradual de erros ESLint
 * Segue protocolo break-safe: testa cada correção individualmente
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Categorias de correção por prioridade (menos arriscado primeiro)
const CORRECTION_PHASES = {
  phase1: {
    name: 'Correções Seguras',
    rules: [
      'prefer-const',
      'no-case-declarations',
      '@typescript-eslint/no-empty-object-type'
    ]
  },
  phase2: {
    name: 'Correções de Comentários',
    rules: [
      '@typescript-eslint/ban-ts-comment'
    ]
  },
  phase3: {
    name: 'Corre<PERSON><PERSON><PERSON> de Hooks (Dependências)',
    rules: [
      'react-hooks/exhaustive-deps'
    ]
  },
  phase4: {
    name: 'Correções Críticas (Any Types)',
    rules: [
      '@typescript-eslint/no-explicit-any'
    ]
  },
  phase5: {
    name: 'Correç<PERSON><PERSON> (Hooks Rules)',
    rules: [
      'react-hooks/rules-of-hooks'
    ]
  }
};

function runCommand(cmd, description) {
  console.log(`\n🔄 ${description}...`);
  try {
    const result = execSync(cmd, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description} - Sucesso`);
    return { success: true, output: result };
  } catch (error) {
    console.log(`❌ ${description} - Falhou`);
    console.log(error.stdout || error.message);
    return { success: false, error: error.stdout || error.message };
  }
}

function getCurrentLintErrors() {
  try {
    execSync('npm run lint', { encoding: 'utf8', stdio: 'pipe' });
    return { errors: 0, warnings: 0 };
  } catch (error) {
    const output = error.stdout || error.message;
    const errorMatch = output.match(/(\d+) problems \((\d+) errors, (\d+) warnings\)/);
    if (errorMatch) {
      return {
        total: parseInt(errorMatch[1]),
        errors: parseInt(errorMatch[2]),
        warnings: parseInt(errorMatch[3])
      };
    }
    return { errors: 999, warnings: 0, total: 999 };
  }
}

function testBuildAndFunctionality() {
  console.log('\n🧪 Testando build e funcionalidade...');
  
  // Testa build
  const buildResult = runCommand('npm run build', 'Build');
  if (!buildResult.success) {
    return false;
  }
  
  // Testa se o projeto ainda funciona
  // (adicionar mais testes conforme necessário)
  
  return true;
}

function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = `backup-eslint-fix-${timestamp}`;
  
  console.log(`\n📦 Criando backup em ${backupDir}...`);
  
  try {
    execSync(`git stash push -m "ESLint fix backup ${timestamp}"`, { stdio: 'inherit' });
    console.log('✅ Backup criado via git stash');
    return backupDir;
  } catch (error) {
    console.log('❌ Falha ao criar backup');
    return null;
  }
}

function restoreBackup() {
  console.log('\n🔄 Restaurando backup...');
  try {
    execSync('git stash pop', { stdio: 'inherit' });
    console.log('✅ Backup restaurado');
    return true;
  } catch (error) {
    console.log('❌ Falha ao restaurar backup');
    return false;
  }
}

function fixSpecificRule(rule) {
  console.log(`\n🔧 Tentando corrigir regra: ${rule}`);
  
  // Algumas correções automáticas básicas
  const fixCommands = {
    'prefer-const': 'npx eslint . --ext ts,tsx --fix --rule "prefer-const: error"',
    'no-case-declarations': 'echo "Correção manual necessária para no-case-declarations"',
    '@typescript-eslint/no-empty-object-type': 'echo "Correção manual necessária para interfaces vazias"'
  };
  
  const command = fixCommands[rule];
  if (command && command.startsWith('npx')) {
    return runCommand(command, `Corrigir ${rule}`);
  }
  
  console.log(`⚠️ Regra ${rule} requer correção manual`);
  return { success: false, manual: true };
}

async function main() {
  console.log('🚀 Iniciando correção gradual de erros ESLint\n');
  
  // Estado inicial
  const initialErrors = getCurrentLintErrors();
  console.log(`📊 Estado inicial: ${initialErrors.total} problemas (${initialErrors.errors} erros, ${initialErrors.warnings} warnings)`);
  
  // Backup
  const backupId = createBackup();
  if (!backupId) {
    console.log('❌ Não foi possível criar backup. Abortando.');
    return;
  }
  
  let totalFixed = 0;
  
  for (const [phaseId, phase] of Object.entries(CORRECTION_PHASES)) {
    console.log(`\n\n📋 FASE: ${phase.name}`);
    console.log('=' .repeat(50));
    
    for (const rule of phase.rules) {
      console.log(`\n🎯 Processando regra: ${rule}`);
      
      const beforeErrors = getCurrentLintErrors();
      console.log(`📊 Antes: ${beforeErrors.total} problemas`);
      
      // Tenta corrigir a regra
      const fixResult = fixSpecificRule(rule);
      
      if (fixResult.manual) {
        console.log(`⏭️ Pulando ${rule} - correção manual necessária`);
        continue;
      }
      
      if (!fixResult.success) {
        console.log(`❌ Falha ao corrigir ${rule}`);
        continue;
      }
      
      // Testa se ainda funciona
      if (!testBuildAndFunctionality()) {
        console.log(`💥 Correção de ${rule} quebrou o build! Revertendo...`);
        restoreBackup();
        continue;
      }
      
      const afterErrors = getCurrentLintErrors();
      const fixed = beforeErrors.total - afterErrors.total;
      
      if (fixed > 0) {
        console.log(`✅ ${rule}: ${fixed} problemas corrigidos`);
        totalFixed += fixed;
        
        // Commit da correção bem-sucedida
        try {
          execSync(`git add -A && git commit -m "fix(eslint): ${rule} - ${fixed} problemas corrigidos"`, { stdio: 'inherit' });
        } catch (error) {
          console.log('⚠️ Falha ao commitar, mas correção foi aplicada');
        }
      } else {
        console.log(`⚠️ ${rule}: Nenhum problema corrigido automaticamente`);
      }
    }
  }
  
  // Relatório final
  const finalErrors = getCurrentLintErrors();
  console.log('\n' + '='.repeat(60));
  console.log('📊 RELATÓRIO FINAL');
  console.log('='.repeat(60));
  console.log(`Inicial: ${initialErrors.total} problemas`);
  console.log(`Final: ${finalErrors.total} problemas`);
  console.log(`Corrigidos automaticamente: ${totalFixed} problemas`);
  console.log(`Restantes: ${finalErrors.total} problemas (necessitam correção manual)`);
  
  if (finalErrors.total > 0) {
    console.log('\n📋 PRÓXIMOS PASSOS:');
    console.log('1. Revisar problemas restantes manualmente');
    console.log('2. Priorizar correções por impacto na funcionalidade');
    console.log('3. Testar cada correção individualmente');
    console.log('4. Considerar usar @ts-expect-error com comentários explicativos');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { CORRECTION_PHASES, fixSpecificRule, testBuildAndFunctionality }; 