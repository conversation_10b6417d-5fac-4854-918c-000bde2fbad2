# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/
cypress/
.cypress-cache/
__tests__/
*.test.*

# Build outputs
dist/
build/
.next/
.declarations/

# Cache and logs
.cache/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and Editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Version control
.git/
.gitignore
.gitattributes
.github/
.husky/

# Config files not needed in container
*.config.js
*.config.ts
tsconfig*.json
.eslintrc*
.prettierrc*

# Documentation and assets not needed in runtime
*.md
docs/
README*
LICENSE*

# Environment and secrets
.env*
.env.local
.env.development
.env.test
.env.production

# Backup and temporary files
backups/
*.bak
*.tmp
*~

# Development tools
.npmrc
.yarnrc
.editorconfig
cypress/screenshots
cypress/videos
supabase/.temp
tasks
