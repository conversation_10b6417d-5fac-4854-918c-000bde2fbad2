-- Adição de data de nascimento aos perfis
-- Esta migração adiciona um campo de data de nascimento à tabela profiles
-- para permitir validação de idade dos usuários

-- Adicionar a coluna birth_date à tabela profiles
ALTER TABLE profiles ADD COLUMN birth_date DATE;

-- Adicionar campo last_activity para rastreamento de inatividade (permitindo NULL para usuários existentes)
ALTER TABLE profiles ADD COLUMN last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;

-- Índice para consultas de inatividade
CREATE INDEX idx_profiles_last_activity ON profiles(last_activity);

-- Adicionar campo para notificações enviadas sobre inatividade
ALTER TABLE profiles ADD COLUMN inactivity_notified BOOLEAN DEFAULT FALSE;

-- Função para atualizar o campo last_activity automaticamente no login
CREATE OR REPLACE FUNCTION public.update_last_activity()
RETURNS TRIGGER AS $$
BEGIN
  -- Se houver um login bem-sucedido, atualizar o campo last_activity
  UPDATE profiles 
  SET 
    last_activity = CURRENT_TIMESTAMP,
    inactivity_notified = FALSE  -- Reinicia o status de notificação 
  WHERE user_id = NEW.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para atualizar última atividade no login
DROP TRIGGER IF EXISTS update_activity_on_auth_login ON auth.users;
CREATE TRIGGER update_activity_on_auth_login
  AFTER INSERT ON auth.sessions
  FOR EACH ROW
  EXECUTE PROCEDURE public.update_last_activity();

-- Comentários para documentação
COMMENT ON COLUMN profiles.birth_date IS 'Data de nascimento do usuário para validação de idade';
COMMENT ON COLUMN profiles.last_activity IS 'Timestamp da última atividade do usuário';
COMMENT ON COLUMN profiles.inactivity_notified IS 'Indica se o usuário já foi notificado sobre inatividade';
