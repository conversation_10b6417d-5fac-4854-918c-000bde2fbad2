
import { useState, useEffect, useCallback } from 'react';

// Enhanced breakpoints for better device detection
export const BREAKPOINTS = {
  XXS: 320,     // Very small phones
  XS: 375,      // iPhone SE, small phones
  SM: 414,      // iPhone 6/7/8 Plus
  MOBILE: 640,  // Large phones
  TABLET_SM: 768, // Small tablets (iPad mini)
  TABLET: 834,  // Standard tablets (iPad)
  TABLET_LG: 1024, // Large tablets (iPad Pro)
  LAPTOP: 1280, // Small laptops
  DESKTOP: 1440, // Desktop
  XL: 1920      // Large desktop
} as const;

export type DeviceOrientation = 'portrait' | 'landscape';
export type DeviceType = 'mobile' | 'tablet' | 'laptop' | 'desktop';
export type DeviceSize = 'xxs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';

interface DeviceInfo {
  type: DeviceType;
  size: DeviceSize;
  orientation: DeviceOrientation;
  width: number;
  height: number;
  aspectRatio: number;
  isTouch: boolean;
  isHighDensity: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  hasNotch: boolean;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Detect device characteristics
const detectDeviceInfo = (): DeviceInfo => {
  const width = window.innerWidth;
  const height = window.innerHeight;
  const orientation = height > width ? 'portrait' : 'landscape';
  const aspectRatio = width / height;
  
  // Device type detection
  let type: DeviceType;
  if (width < BREAKPOINTS.MOBILE) {
    type = 'mobile';
  } else if (width < BREAKPOINTS.TABLET_LG) {
    type = 'tablet';
  } else if (width < BREAKPOINTS.DESKTOP) {
    type = 'laptop';
  } else {
    type = 'desktop';
  }
  
  // Device size detection
  let size: DeviceSize;
  if (width < BREAKPOINTS.XXS) {
    size = 'xxs';
  } else if (width < BREAKPOINTS.XS) {
    size = 'xs';
  } else if (width < BREAKPOINTS.MOBILE) {
    size = 'sm';
  } else if (width < BREAKPOINTS.TABLET) {
    size = 'md';
  } else if (width < BREAKPOINTS.LAPTOP) {
    size = 'lg';
  } else {
    size = 'xl';
  }
  
  // Platform detection
  const userAgent = navigator.userAgent.toLowerCase();
  const isIOS = /iphone|ipad|ipod/.test(userAgent);
  const isAndroid = /android/.test(userAgent);
  const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const isHighDensity = window.devicePixelRatio > 1.5;
  
  // Notch detection (approximate)
  const hasNotch = isIOS && (
    (width === 375 && height === 812) || // iPhone X/XS/11 Pro
    (width === 414 && height === 896) || // iPhone XR/11/XS Max/11 Pro Max
    (width === 390 && height === 844) || // iPhone 12/13 mini
    (width === 393 && height === 852) || // iPhone 14/15
    (width === 428 && height === 926)    // iPhone 12/13/14 Pro Max
  );
  
  // Safe area detection
  const safeAreaInsets = {
    top: hasNotch ? 44 : 20,
    bottom: hasNotch ? 34 : 0,
    left: 0,
    right: 0
  };
  
  return {
    type,
    size,
    orientation,
    width,
    height,
    aspectRatio,
    isTouch,
    isHighDensity,
    isIOS,
    isAndroid,
    hasNotch,
    safeAreaInsets
  };
};

export const useDevice = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(detectDeviceInfo);
  
  const updateDeviceInfo = useCallback(
    debounce(() => {
      setDeviceInfo(detectDeviceInfo());
    }, 150),
    []
  );
  
  useEffect(() => {
    window.addEventListener('resize', updateDeviceInfo);
    window.addEventListener('orientationchange', updateDeviceInfo);
    
    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      window.removeEventListener('orientationchange', updateDeviceInfo);
    };
  }, [updateDeviceInfo]);
  
  return deviceInfo;
};

// Utility hooks for backward compatibility
export const useIsMobile = () => {
  const { type } = useDevice();
  return type === 'mobile';
};

export const useIsTablet = () => {
  const { type } = useDevice();
  return type === 'tablet';
};

export const useOrientation = () => {
  const { orientation } = useDevice();
  return orientation;
};
