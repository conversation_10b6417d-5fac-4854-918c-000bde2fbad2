import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface UserContextType {
  user: any | null;
  session: any | null;
  loading: boolean;
  signIn: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  signUp: (email: string, password: string, additionalData?: any) => Promise<any>;
  updateUser: (data: any) => Promise<any>;
  createProfile: (user: any, additionalData?: any) => Promise<any>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<any | null>(null);
  const [session, setSession] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    };

    getSession();

    supabase.auth.onAuthStateChange(async (_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });
  }, []);

  const signIn = async (email: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithOtp({ email });
      if (error) throw error;
      alert('Check your email for the magic link!');
    } catch (error: any) {
      alert(error.error_description || error.message);
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      await supabase.auth.signOut();
    } catch (error: any) {
      alert(error.error_description || error.message);
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, additionalData: any = {}) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: additionalData.full_name,
            phone: additionalData.phone,
            user_type: additionalData.user_type || 'student',
          },
        },
      });

      if (error) throw error;

      // Create a user profile in the profiles table
      await createProfile(data.user, additionalData);

      alert('Check your email to confirm your registration!');
      return data.user;
    } catch (error: any) {
      alert(error.error_description || error.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateUser = async (data: any) => {
    try {
      setLoading(true);
      const { data: user, error } = await supabase.auth.updateUser({ data });
      if (error) throw error;
      setUser(user);
      return user;
    } catch (error: any) {
      alert(error.error_description || error.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const createProfile = async (user: any, additionalData: any = {}) => {
    try {
      const profileData = {
        user_id: user.id,
        full_name: additionalData.full_name || user.user_metadata?.full_name || '',
        phone: additionalData.phone || user.user_metadata?.phone || null,
        user_type: additionalData.user_type || user.user_metadata?.user_type || 'student',
        email: user.email || '',
        cpf: additionalData.cpf || '000.000.000-00' // Campo obrigatório com valor padrão
      };

      const { data, error } = await supabase
        .from('profiles')
        .insert([profileData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erro ao criar perfil:', error);
      throw error;
    }
  };

  const value: UserContextType = {
    user,
    session,
    loading,
    signIn,
    signOut,
    signUp,
    updateUser,
    createProfile,
  };

  return (
    <UserContext.Provider value={value}>
      {!loading && children}
    </UserContext.Provider>
  );
};

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  return context;
};

