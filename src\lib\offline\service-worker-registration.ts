
export interface ServiceWorkerAPI {
  register: () => Promise<ServiceWorkerRegistration | null>;
  unregister: () => Promise<boolean>;
  update: () => Promise<void>;
  getRegistration: () => Promise<ServiceWorkerRegistration | null>;
  isSupported: () => boolean;
}

class ServiceWorkerManager implements ServiceWorkerAPI {
  private registration: ServiceWorkerRegistration | null = null;
  private updateAvailable = false;

  isSupported(): boolean {
    return 'serviceWorker' in navigator;
  }

  async register(): Promise<ServiceWorkerRegistration | null> {
    if (!this.isSupported()) {
      console.warn('[SW] Service Worker não suportado neste navegador');
      return null;
    }

    try {
      console.log('[SW] Registrando service worker...');
      
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      });

      console.log('[SW] Service worker registrado com sucesso:', this.registration.scope);

      // Configurar event listeners
      this.setupEventListeners();

      // Verificar se já há uma atualização esperando
      if (this.registration.waiting) {
        console.log('[SW] Service worker esperando para ativar');
        this.handleWaiting();
      }

      // Verificar por atualizações automaticamente
      this.registration.update();

      return this.registration;
    } catch (error) {
      console.error('[SW] Falha ao registrar service worker:', error);
      return null;
    }
  }

  async unregister(): Promise<boolean> {
    if (!this.registration) {
      return false;
    }

    try {
      const result = await this.registration.unregister();
      console.log('[SW] Service worker desregistrado:', result);
      this.registration = null;
      return result;
    } catch (error) {
      console.error('[SW] Falha ao desregistrar service worker:', error);
      return false;
    }
  }

  async update(): Promise<void> {
    if (!this.registration) {
      throw new Error('Service worker não registrado');
    }

    try {
      await this.registration.update();
      console.log('[SW] Verificação de atualização concluída');
    } catch (error) {
      console.error('[SW] Falha ao verificar atualizações:', error);
      throw error;
    }
  }

  async getRegistration(): Promise<ServiceWorkerRegistration | null> {
    if (!this.isSupported()) {
      return null;
    }

    try {
      return await navigator.serviceWorker.getRegistration('/');
    } catch (error) {
      console.error('[SW] Falha ao obter registro:', error);
      return null;
    }
  }

  private setupEventListeners(): void {
    if (!this.registration) return;

    // Escutar atualizações
    this.registration.addEventListener('updatefound', () => {
      console.log('[SW] Nova versão do service worker encontrada');
      this.handleUpdateFound();
    });

    // Escutar mensagens do service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data?.type === 'SW_UPDATED') {
        console.log('[SW] Service worker atualizado para versão:', event.data.version);
        this.notifyUpdateApplied();
      }
    });

    // Escutar mudanças no controller
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('[SW] Controller alterado - recarregando página');
      // Aguardar um pouco antes de recarregar para evitar loops
      setTimeout(() => {
        if (!this.updateAvailable) {
          window.location.reload();
        }
      }, 100);
    });
  }

  private handleUpdateFound(): void {
    if (!this.registration?.installing) return;

    const newWorker = this.registration.installing;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed') {
        if (navigator.serviceWorker.controller) {
          console.log('[SW] Nova versão instalada e pronta');
          this.updateAvailable = true;
          this.notifyUpdateAvailable();
        } else {
          console.log('[SW] Service worker instalado pela primeira vez');
        }
      }
    });
  }

  private handleWaiting(): void {
    console.log('[SW] Service worker aguardando ativação');
    this.updateAvailable = true;
    this.notifyUpdateAvailable();
  }

  private notifyUpdateAvailable(): void {
    // Disparar evento customizado para a aplicação
    window.dispatchEvent(new CustomEvent('sw-update-available', {
      detail: { 
        registration: this.registration,
        canSkipWaiting: true
      }
    }));
  }

  private notifyUpdateApplied(): void {
    // Disparar evento quando atualização for aplicada
    window.dispatchEvent(new CustomEvent('sw-update-applied', {
      detail: { registration: this.registration }
    }));
  }

  // Força a ativação do service worker em espera
  skipWaiting(): void {
    if (this.registration?.waiting) {
      this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      this.updateAvailable = false;
    }
  }

  // Método para verificar a versão atual
  async getCurrentVersion(): Promise<string | null> {
    if (!navigator.serviceWorker.controller) {
      return null;
    }

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      messageChannel.port1.onmessage = (event) => {
        if (event.data?.type === 'VERSION_INFO') {
          resolve(event.data.version);
        } else {
          resolve(null);
        }
      };

      navigator.serviceWorker.controller.postMessage(
        { type: 'GET_VERSION' },
        [messageChannel.port2]
      );

      // Timeout após 5 segundos
      setTimeout(() => resolve(null), 5000);
    });
  }
}

// Singleton instance
export const serviceWorkerManager = new ServiceWorkerManager();

// Auto-register on load com melhor tratamento de erros
if (typeof window !== 'undefined') {
  window.addEventListener('load', async () => {
    try {
      await serviceWorkerManager.register();
      
      // Configurar listener para atualizações (sem auto-apply)
      window.addEventListener('sw-update-available', (event: any) => {
        console.log('[SW] Atualização disponível - aguardando ação do usuário');
        // Não aplicar automaticamente para evitar loops
      });

    } catch (error) {
      console.warn('[SW] Falha no auto-register:', error);
    }
  });
}
