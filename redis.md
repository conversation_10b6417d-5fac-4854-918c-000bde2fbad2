# Redis Cache & Sistema de Notificações

## Visão Geral

Redis serve duas funções críticas neste sistema:
1. **Camada de Cache** - Otimiza a performance de consultas ao banco de dados
2. **Notificações em Tempo Real** - Alimenta atualizações no dashboard de responsáveis

## 🛡️ Circuit Breaker (NOVO)

Impedindo falhas em cascata e garantindo resiliência do sistema:

```javascript
// Uso típico com circuit breaker (já implementado no serviço Redis)
try {
  const data = await redisService.getCachedData(key, fetchFallbackData);
  // Processar dados normalmente
} catch (error) {
  // Circuit breaker irá tentar usar fallback antes de chegar aqui
  console.error('Falha crítica com circuit breaker aberto:', error);
}
```

### Estados do Circuit Breaker
- **CLOSED**: Operação normal, Redis disponível
- **OPEN**: Redis inacessível, operações redirecionadas para fallback
- **HALF-OPEN**: Tentando recuperação após período de timeout

## 🔄 Workflow de Gerenciamento de Cache

### Inicialização do Cache
```bash
# Verificar se o serviço Redis está rodando
redis-cli ping

# Verificar tamanho atual do cache
redis-cli info | grep used_memory_human

# Limpar cache se necessário (usar com cautela)
redis-cli flushdb
```

### Monitoramento do Cache
```bash
# Monitorar taxa de acertos
redis-cli info stats | grep hit_rate

# Verificar chaves de cache para dados de localização de estudantes
redis-cli keys "student:location:*"

# Examinar expiração de chaves
redis-cli ttl "student:location:{id}"
```

## 📱 Workflow de Notificações em Tempo Real

### Testando o Pipeline de Notificações
```bash
# Publicar notificação de teste
redis-cli publish "guardian:notifications" "{\"type\":\"location_update\",\"studentId\":\"test-id\",\"timestamp\":\"$(date -u +\"%Y-%m-%dT%H:%M:%SZ\")\"}"  

# Monitorar canal de notificações
redis-cli psubscribe "guardian:*"
```

### Baseline de Performance
```bash
# Capturar métricas de latência de notificações
npm run performance-baseline -- --component=redis-notifications

# Validar entrega de notificações
npm run critical-test -- --test=notification-delivery
```

## 🛠️ Troubleshooting

### Problemas de Conexão
```bash
# Verificar conexão Redis a partir da aplicação
node scripts/test-redis-connection.js

# Verificar configuração de conexão Redis
grep REDIS_ .env
```

### Problemas de Memória
```bash
# Verificar uso de memória
redis-cli info memory

# Identificar chaves grandes
redis-cli --bigkeys
```

## 📊 Sistema de Monitoramento (NOVO)

### Health Check Automatizado

```bash
# Verificar saúde completa do Redis (conexão, operações, pub/sub)
node scripts/redis-health-check.mjs

# Verificação com saída detalhada
node scripts/redis-health-check.mjs --verbose

# Gerar relatório JSON para análise
node scripts/redis-health-check.mjs --report

# Incluir métricas detalhadas de performance
node scripts/redis-health-check.mjs --performance

# Modo de monitoramento contínuo
node scripts/redis-health-check.mjs --monitor
```

### Validação do Circuit Breaker

```bash
# Testar comportamento do circuit breaker
node scripts/test-circuit-breaker.mjs
```

### Métricas Coletadas
- Taxa de acertos do cache (hit rate)
- Tempo de resposta para operações críticas
- Estado do circuit breaker
- Uso de memória e fragmentação
- Taxa de falhas e recuperação

## 🔐 Protocolos de Segurança

1. **Antes de qualquer alteração na configuração Redis:**
   ```bash
   # Executar health check completo
   node scripts/redis-health-check.mjs --report
   
   # Verificar estado do circuit breaker
   node scripts/test-circuit-breaker.mjs
   
   # Tirar snapshot de memória
   redis-cli bgsave
   ```

2. **Após alterações:**
   ```bash
   # Verificar funcionalidade crítica
   npm run critical-test -- --component=redis
   
   # Comparar métricas de performance
   npm run performance-baseline -- --compare
   
   # Validar resiliência com circuit breaker
   node scripts/test-circuit-breaker.mjs
   ```

## 📊 Padrões de Implementação

### Cache-Aside (Implementado Atualmente)
1. Verificar cache primeiro (Cache Hit?)
2. Se miss, consultar banco de dados
3. Atualizar cache com resultados da consulta
4. Retornar dados para o cliente

### Write-Through Cache
Para implementação futura para garantir consistência do cache:
```javascript
// Exemplo de implementação
async function updateStudentLocation(studentId, location) {
  // Atualizar DB primeiro
  await supabaseClient.from('locations').upsert({
    student_id: studentId,
    latitude: location.latitude,
    longitude: location.longitude,
    timestamp: new Date()
  });
  
  // Então atualizar cache
  await redisClient.set(
    `student:location:${studentId}`,
    JSON.stringify(location),
    'EX',
    60 * 15 // 15 minutos de expiração
  );
  
  // Publicar notificação
  await redisClient.publish(
    'guardian:notifications', 
    JSON.stringify({
      type: 'location_update',
      studentId,
      timestamp: new Date()
    })
  );
}
```

## 📝 Comandos de Verificação de Saúde

```bash
# Verificar saúde completa do Redis (novo script)
node scripts/redis-health-check.mjs

# Testar funcionalidade de cache com circuit breaker
node scripts/test-circuit-breaker.mjs

# Testar funcionalidade de notificações
npm run critical-test -- --test=redis-notifications

# Executar verificação completa do Redis (pre-deploy)
npm run pre-deploy -- --focus=redis
```

## 💪 Boas Práticas de Resiliência (NOVO)

### Implementação com Circuit Breaker

```typescript
// 1. Sempre fornecer um fallback para operações Redis
async function getStudentLocation(studentId: string) {
  return await redisService.getCachedData(
    `student:location:${studentId}`,
    // Fallback que busca direto do banco em caso de falha Redis
    async () => await supabaseClient
      .from('locations')
      .select('*')
      .eq('student_id', studentId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()
      .then(({ data }) => data)
  );
}

// 2. Usar blocos try/catch para operações críticas
async function sendLocationNotification(guardianId: string, location: any) {
  try {
    await redisService.publishNotification(
      `guardian:${guardianId}:notifications`,
      { type: 'location_update', data: location }
    );
    return true;
  } catch (error) {
    // Logar o erro e usar canal alternativo
    console.error('Erro ao enviar notificação Redis:', error);
    // Fallback para notificação via email ou outro meio
    return false;
  }
}

// 3. Verificar saúde do Redis antes de operações em massa
async function processMultipleLocations(locations: any[]) {
  const { status } = await import('@/lib/monitoring/health-check')
    .then(module => module.checkRedisHealth());
    
  if (status !== 'healthy') {
    console.warn(`Redis não está saudável (${status}), usando modo degradado`);
    // Usar processamento degradado que prioriza banco de dados
  }
  
  // Processar localizações com método apropriado
}
```

### Monitoração em Produção

- Configure alertas para estado OPEN do circuit breaker
- Use métricas do Redis para tuning de performance
- Documente todas as estratégias de fallback implementadas
- Verifique logs de erro do circuit breaker regularmente

Lembre-se: "TODA mudança deve ser PROVADAMENTE SEGURA antes de ser aplicada"
