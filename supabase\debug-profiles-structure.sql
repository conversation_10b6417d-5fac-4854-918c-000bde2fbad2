-- ==========================================
-- DEBUG: ESTRUTURA DA TABELA PROFILES
-- Data: 24/06/2025
-- Objetivo: Entender estrutura real
-- ==========================================

-- 1. VERIFICAR ESTRUTURA DA TABELA PROFILES
SELECT 
    'PROFILES_STRUCTURE' as info,
    column_name, 
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 2. SAMPLE DE DADOS PROFILES
SELECT 
    'PROFILES_SAMPLE' as info,
    id, full_name, email, phone, user_type, created_at
FROM profiles
LIMIT 5;

-- 3. BUSCAR DADOS ESPECÍFICOS DO MAURÍCIO
SELECT 
    'MAURICIO_PROFILE' as info,
    *
FROM profiles
WHERE email = '<EMAIL>';

-- 4. <PERSON><PERSON><PERSON><PERSON><PERSON> ESTRUTURA DA TABELA STUDENTS
SELECT 
    'STUDENTS_STRUCTURE' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'students'
ORDER BY ordinal_position; 