
import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useToast } from '@/components/ui/use-toast';
import { LocationData } from '@/types/database';
import { Student } from '@/hooks/useGuardianStudents';
import { useStudentLocations } from '@/hooks/queries/useStudentLocations';
import { useGuardianStudents } from '@/hooks/useGuardianStudents';
import { useQueryClient } from '@tanstack/react-query';

export const useParentDashboard = () => {
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [activeTab, setActiveTab] = useState('map');
  
  const { user, signOut } = useUser();
  const { toast } = useToast();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Use the new hook for fetching students
  const { 
    students, 
    isLoading: isLoadingStudents, 
    error: studentsError,
    refetch: refetchStudents
  } = useGuardianStudents();

  // Use the hook for fetching student locations
  const { 
    data: locations = [], 
    isLoading: isLoadingLocations, 
    error: locationError,
    refetch: refetchLocations
  } = useStudentLocations(selectedStudent?.id || '');

  // Invalidate cache when student changes
  useEffect(() => {
    if (selectedStudent?.id) {
      queryClient.invalidateQueries({ 
        queryKey: ['studentLocations', selectedStudent.id] 
      });
    }
  }, [selectedStudent?.id, queryClient]);

  const handleGoToProfile = useCallback(() => {
    navigate('/profile');
  }, [navigate]);

  const handleSelectStudent = useCallback((student: Student) => {
    console.log('[ParentDashboard] Student selected:', student);
    setSelectedStudent(student);
    setSelectedLocation(null);
    
    toast({
      description: `Carregando localização de ${student.name}...`,
    });

    // Force refetch of locations
    setTimeout(() => {
      refetchLocations();
    }, 100);
  }, [toast, refetchLocations]);

  const handleLocationSelect = useCallback((location: LocationData) => {
    setSelectedLocation(location);
    setActiveTab('map');
    
    toast({
      description: `Mostrando localização de ${new Date(location.timestamp).toLocaleString()}`,
    });
  }, [toast]);

  const handleClearSelection = useCallback(() => {
    setSelectedLocation(null);
    toast({
      description: "Voltando à visualização geral",
    });
  }, [toast]);

  const handleStudentAdded = useCallback(() => {
    // Refresh the students list when a new student is added
    refetchStudents();
    toast({
      description: "Lista de estudantes atualizada",
    });
  }, [refetchStudents, toast]);

  // Show success toast when locations are loaded
  useEffect(() => {
    if (selectedStudent && locations.length > 0 && !isLoadingLocations) {
      toast({
        description: `${locations.length} localização${locations.length > 1 ? 'ões' : ''} carregada${locations.length > 1 ? 's' : ''} para ${selectedStudent.name}`,
      });
    }
  }, [selectedStudent, locations.length, isLoadingLocations, toast]);

  return {
    // Student data
    students,
    isLoadingStudents,
    studentsError: studentsError || null,
    
    // Selected student and locations
    selectedStudent,
    locations,
    locationError: locationError?.message || null,
    isLoadingLocations,
    selectedLocation,
    activeTab,
    
    // User data
    user,
    signOut,
    
    // Actions
    handleGoToProfile,
    handleSelectStudent,
    handleLocationSelect,
    handleClearSelection,
    handleStudentAdded,
    setActiveTab,
    refetchLocations,
    refetchStudents
  };
};
