# Guia de Correções Manuais ESLint

## 🎯 Protocolo Break-Safe para Correções

**ANTES DE QUALQUER CORREÇÃO:**
1. ✅ Verificar se o build funciona: `npm run build`
2. ✅ Fazer backup: `git stash push -m "before eslint fix"`
3. ✅ Corrigir um arquivo por vez
4. ✅ Testar após cada correção: `npm run build && npm run lint`
5. ✅ Commitar correções funcionais: `git commit -m "fix(eslint): descrição"`

---

## 🔧 Correções por Categoria

### 1. @typescript-eslint/no-explicit-any (PRIORIDADE ALTA)

**Problema:** Uso de `any` type que remove a segurança de tipos.

**Estratégias de Correção:**

#### A. Substituir por tipos específicos
```typescript
// ❌ Antes
const handleError = (error: any) => {
  console.log(error);
};

// ✅ Depois
const handleError = (error: Error | string | { message: string }) => {
  console.log(error);
};
```

#### B. Usar tipos genéricos
```typescript
// ❌ Antes
const apiCall = (data: any): Promise<any> => {
  return fetch('/api', { body: JSON.stringify(data) });
};

// ✅ Depois
const apiCall = <T, R>(data: T): Promise<R> => {
  return fetch('/api', { body: JSON.stringify(data) });
};
```

#### C. Usar unknown para casos gerais
```typescript
// ❌ Antes
const parseData = (data: any) => {
  return JSON.parse(data);
};

// ✅ Depois
const parseData = (data: unknown) => {
  if (typeof data === 'string') {
    return JSON.parse(data);
  }
  throw new Error('Data must be string');
};
```

#### D. Usar @ts-expect-error como último recurso
```typescript
// ✅ Último recurso com comentário explicativo
// @ts-expect-error - Biblioteca externa não tipada adequadamente
const result = thirdPartyLib.undocumentedMethod(data);
```

### 2. react-hooks/exhaustive-deps (PRIORIDADE MÉDIA)

**Problema:** useEffect/useCallback com dependências faltando.

**Correções:**

#### A. Adicionar dependências faltando
```typescript
// ❌ Antes
useEffect(() => {
  fetchData(userId);
}, []); // Falta userId

// ✅ Depois
useEffect(() => {
  fetchData(userId);
}, [userId]);
```

#### B. Usar useCallback para funções
```typescript
// ❌ Antes
const fetchData = (id: string) => {
  // lógica
};

useEffect(() => {
  fetchData(userId);
}, [userId]); // fetchData muda a cada render

// ✅ Depois
const fetchData = useCallback((id: string) => {
  // lógica
}, []);

useEffect(() => {
  fetchData(userId);
}, [userId, fetchData]);
```

#### C. Mover função para dentro do useEffect
```typescript
// ❌ Antes
const processData = () => {
  // usa outras variáveis do component
};

useEffect(() => {
  processData();
}, []);

// ✅ Depois
useEffect(() => {
  const processData = () => {
    // usa outras variáveis do component
  };
  processData();
}, []);
```

### 3. @typescript-eslint/ban-ts-comment (PRIORIDADE BAIXA)

**Problema:** Uso de @ts-ignore ao invés de @ts-expect-error.

**Correção:**
```typescript
// ❌ Antes
// @ts-ignore
const result = problematicCode();

// ✅ Depois
// @ts-expect-error - Razão específica do problema
const result = problematicCode();
```

### 4. react-hooks/rules-of-hooks (PRIORIDADE CRÍTICA)

**Problema:** Hooks chamados condicionalmente.

**Correção:**
```typescript
// ❌ Antes - DebugNav.tsx
function DebugNav() {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  const navigate = useNavigate(); // Hook chamado condicionalmente
  const user = useUser();
  // ...
}

// ✅ Depois
function DebugNav() {
  const navigate = useNavigate(); // Hooks sempre no topo
  const user = useUser();
  
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  // ...
}
```

### 5. no-case-declarations (PRIORIDADE BAIXA)

**Problema:** Declarações lexicais em case blocks.

**Correção:**
```typescript
// ❌ Antes
switch (type) {
  case 'A':
    const result = processA();
    break;
  case 'B':
    const result = processB(); // Conflito de nome
    break;
}

// ✅ Depois
switch (type) {
  case 'A': {
    const result = processA();
    break;
  }
  case 'B': {
    const result = processB();
    break;
  }
}
```

### 6. @typescript-eslint/no-empty-object-type (PRIORIDADE BAIXA)

**Problema:** Interfaces vazias equivalentes ao supertipo.

**Correção:**
```typescript
// ❌ Antes
interface EmptyInterface extends SomeType {}

// ✅ Depois - opção 1: usar type alias
type EmptyType = SomeType;

// ✅ Depois - opção 2: adicionar propriedades específicas
interface ExtendedInterface extends SomeType {
  // Propriedades específicas serão adicionadas no futuro
  [key: string]: unknown;
}
```

---

## 📋 Plano de Execução Recomendado

### Fase 1: Correções Automáticas (Executar script)
```bash
node scripts/fix-eslint-gradual.js
```

### Fase 2: Correções Críticas Manuais
1. **react-hooks/rules-of-hooks** (DebugNav.tsx)
2. **Casos de no-case-declarations** (RegisterForm.tsx)

### Fase 3: Correções de Any Types (Arquivo por arquivo)
**Ordem sugerida por impacto:**
1. `src/contexts/` - Contextos críticos
2. `src/lib/services/` - Serviços principais
3. `src/hooks/` - Hooks customizados
4. `src/components/` - Componentes UI
5. `src/pages/` - Páginas
6. Arquivos de configuração

### Fase 4: Correções de Dependências (react-hooks/exhaustive-deps)
**Estratégia:** Um hook por vez, testar funcionalidade após cada correção.

---

## 🚨 Arquivos de Alta Prioridade para Correção

### Críticos (Afetar funcionalidade core)
- `src/contexts/UnifiedAuthContext.tsx` - 17 erros
- `src/contexts/AuthContext.tsx` - 5 erros
- `src/lib/services/location/LocationService.ts` - 2 erros
- `src/hooks/useParentDashboard.ts` - 1 erro crítico

### Importantes (Componentes principais)
- `src/pages/StudentDashboard.tsx` - 6 erros
- `src/components/RegisterForm.tsx` - 5 erros
- `src/hooks/useGuardianData.tsx` - 6 erros

### Moderados (Podem ser tratados depois)
- Arquivos em `cypress/` - Principalmente testes
- Arquivos em `src/components/ui/` - Componentes básicos
- Edge functions em `supabase/functions/`

---

## 🛠 Comandos Úteis

```bash
# Verificar erros específicos de um arquivo
npx eslint src/contexts/UnifiedAuthContext.tsx

# Tentar correção automática de um arquivo
npx eslint src/contexts/UnifiedAuthContext.tsx --fix

# Verificar apenas erros (ignorar warnings)
npm run lint 2>&1 | grep "error"

# Contar erros por tipo
npm run lint 2>&1 | grep -o "@typescript-eslint/no-explicit-any" | wc -l

# Testar build após correções
npm run build && echo "✅ Build OK" || echo "❌ Build FALHOU"
```

---

## 📊 Progresso de Correção

**Meta:** Reduzir de 268 problemas para <20 problemas

- [ ] Fase 1: Automáticas (target: -10 problemas)
- [ ] Fase 2: Críticas (target: -5 problemas)  
- [ ] Fase 3: Any Types (target: -200 problemas)
- [ ] Fase 4: Dependencies (target: -30 problemas)
- [ ] Fase 5: Limpeza final (target: -23 problemas)

**Tracking:**
```bash
# Estado inicial: 268 problemas (233 erros, 35 warnings)
# Estado atual: [ATUALIZAR APÓS CADA FASE]
# Meta final: <20 problemas
``` 