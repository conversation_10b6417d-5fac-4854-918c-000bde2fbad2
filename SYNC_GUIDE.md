# GUIA DE SINCRONIZAÇÃO LOCAL ↔ REMOTO

## SIM, VOCÊ PODE SINCRONIZAR EM AMBAS DIREÇÕES!

### ENVIANDO LOCAL → REMOTO
- Novas migrações: npx supabase db push
- Functions: npx supabase functions deploy
- Mudanças de schema: npx supabase db push

### PUXANDO REMOTO → LOCAL  
- Esquema completo: npx supabase db pull
- Reset e sincronizar: npx supabase db reset

### SITUAÇÃO ATUAL
- REMOTO: 75 migrações, 21 tabelas, sistema completo
- LOCAL: 1 migração básica, 1 tabela teste

### OPÇÕES PARA VOCÊ

#### OPÇÃO A: IGUALAR LOCAL AO REMOTO
npx supabase db pull
# Resultado: Local = Remoto (completo)

#### OPÇÃO B: MANTER SEPARADOS
# Desenvolver localmente e enviar mudanças específicas
npx supabase migration new minha_feature
# Editar migração
npx supabase db push

### COMANDOS SEGUROS
# Sempre fazer backup antes:
npx supabase db dump --linked --file=backup-remoto.sql
npx supabase db dump --local --file=backup-local.sql

### RESPOSTA DIRETA
✅ Mudanças locais PODEM ser enviadas para online
✅ Mudanças online PODEM ser baixadas para local  
✅ É SEGURO com backups
✅ Migrações são ADITIVAS (não deletam dados)

Quer sincronizar completamente agora?
