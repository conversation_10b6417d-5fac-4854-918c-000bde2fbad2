# Relatório de Correção de Problemas Docker - Supabase

**Data:** 2025-06-02 (Atualizado em 16:30)
**Problemas:** 
1. Erro "Network unreachable (os error 101)" no container Vector do Supabase
2. Falha no build do Docker por arquivos de configuração TypeScript ausentes

## Diagnóstico

O problema principal identificado foi uma incompatibilidade de configuração de rede no Docker Desktop para Windows executando com o backend WSL2. O container Vector do Supabase não conseguia se comunicar corretamente devido a:

1. Configuração IPv6 inadequada (habilitada quando deveria estar desabilitada)
2. Falta de especificação de servidores DNS confiáveis
3. Ausência de definição de pools de endereços IP fixos
4. Falha na resolução do nome `docker.internal`

## Solução Implementada

### 1. Modificação do arquivo daemon.json

Atualizamos o arquivo `C:\Users\<USER>\.docker\daemon.json` com as seguintes configurações:

```json
{
  "builder": {
    "gc": {
      "defaultKeepStorage": "20GB",
      "enabled": true
    }
  },
  "experimental": false,
  "dns": ["*******", "*******"],
  "ipv6": false,
  "fixed-cidr": "**********/16",
  "default-address-pools": [
    {"base": "**********/16", "size": 24}
  ]
}
```

### 2. Script de suporte WSL2

Criamos o script `.wsl-fix.sh` que pode ser executado dentro do WSL2 para garantir a correta resolução do nome `docker.internal`:

```bash
#!/bin/bash
# Script para corrigir problemas de rede no WSL2 para o Supabase
# Este script ajusta as configurações de rede para permitir que containers Docker se comuniquem

# Obtém o IP do host WSL2
WSL_HOST_IP=$(ip route | grep default | awk '{print $3}')
echo "IP do host WSL2: $WSL_HOST_IP"

# Adiciona uma entrada para docker.internal
echo "$WSL_HOST_IP docker.internal" | sudo tee -a /etc/hosts

# Verifica se consegue fazer ping no host
echo "Testando conexão com docker.internal:"
ping -c 3 docker.internal

echo "Configuração de rede WSL2 ajustada para funcionar com Supabase"
```

### 3. Limpeza do ambiente Docker

Foi realizada uma limpeza completa do ambiente Docker:
- Parada de todos os containers do Supabase (`npx supabase stop`)
- Remoção de containers residuais que podiam estar causando conflitos
- Remoção de redes Docker não utilizadas ou conflitantes
- Criação de uma rede dedicada para o Supabase

## Solução para Problema de Build

### 1. Diagnóstico do problema com arquivos TypeScript

O build do Docker estava falhando com o seguinte erro:
```
ERROR [app builder 5/8] COPY tsconfig.json tsconfig.app.json ./
failed to solve: failed to compute cache key: failed to calculate checksum of ref: "/tsconfig.json": not found
```

Analisamos os arquivos de configuração TypeScript e descobrimos que o Dockerfile tentava copiar os arquivos de configuração antes de copiar o resto do projeto, mas na ordem incorreta.

### 2. Modificação do Dockerfile

Modificamos o Dockerfile da seguinte forma:

```dockerfile
# Antes (problemático)
COPY tsconfig.json tsconfig.app.json ./
COPY . .

# Depois (corrigido)
COPY . .
RUN ls -la && echo "Checking for TypeScript config files:" && ls -la tsconfig*.json
```

Esta mudança garante que todos os arquivos sejam copiados para o container antes de tentar acessá-los, resolvendo o problema de build.

### 3. Criação do docker-compose.override.yml

Adicionamos um arquivo `docker-compose.override.yml` para complementar as configurações de rede:

```yaml
version: '3'

services:
  # Configurações para melhorar a resolução de DNS para o Vector
  vector:
    networks:
      - database-network
    dns:
      - *******
      - *******
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "docker.internal:host-gateway"

  # Configurações para outros serviços Supabase
  db:
    networks:
      - database-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "docker.internal:host-gateway"

  app:
    networks:
      - database-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "docker.internal:host-gateway"

networks:
  database-network:
    driver: bridge
```

## Benefícios da Solução

1. **Estabilidade:** Configuração corrigida elimina erros de conectividade entre containers
2. **Desempenho:** Remoção de overhead desnecessário do IPv6 quando não está em uso
3. **Confiabilidade:** Servidores DNS específicos garantem resolução de nomes correta
4. **Compatibilidade:** Melhor integração entre Docker Desktop e o backend WSL2
5. **Reprodutibilidade:** Build consistente graças à ordem correta de cópia de arquivos
6. **Manutenibilidade:** Separação clara entre configurações básicas e específicas do ambiente
