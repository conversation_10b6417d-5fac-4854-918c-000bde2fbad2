# Plano Completo: Filtro de Remover Duplicatas no Histórico de Localizações (Parent Dashboard)

## 1. Definição de "Duplicado"
- <PERSON><PERSON> registros são considerados duplicados se tiverem:
  - Mesmo `student_id`
  - Mesma latitude e longitude (arredondar para 5 casas decimais)
  - Mesmo timestamp (ou diferença < 10s)
  - Mesma precisão (`accuracy`)

## 2. Fluxo de Dados e Pontos de Intervenção
- **Frontend:** Toggle "Remover duplicatas" ativa query especial.
- **Backend (Supabase):** View ou RPC retorna localizações únicas.

## 3. Implementação SQL (Supabase/Postgres)
```sql
SELECT DISTINCT ON (
    student_id,
    ROUND(latitude::numeric, 5),
    ROUND(longitude::numeric, 5),
    accuracy,
    date_trunc('minute', timestamp)
  )
  id,
  student_id,
  latitude,
  longitude,
  accuracy,
  timestamp,
  address
FROM locations
WHERE student_id = $1
ORDER BY
  student_id,
  ROUND(latitude::numeric, 5),
  ROUND(longitude::numeric, 5),
  accuracy,
  date_trunc('minute', timestamp),
  timestamp DESC
```

## 4. Função RPC no Supabase
```sql
CREATE OR REPLACE FUNCTION public.get_student_locations_deduped(
  p_student_id uuid,
  p_start timestamptz DEFAULT NULL,
  p_end timestamptz DEFAULT NULL
)
RETURNS TABLE (
  id uuid,
  student_id uuid,
  latitude double precision,
  longitude double precision,
  accuracy double precision,
  timestamp timestamptz,
  address text
) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (
      ROUND(latitude::numeric, 5),
      ROUND(longitude::numeric, 5),
      accuracy,
      date_trunc('minute', timestamp)
    )
    id, student_id, latitude, longitude, accuracy, timestamp, address
  FROM locations
  WHERE student_id = p_student_id
    AND (p_start IS NULL OR timestamp >= p_start)
    AND (p_end IS NULL OR timestamp <= p_end)
  ORDER BY
    ROUND(latitude::numeric, 5),
    ROUND(longitude::numeric, 5),
    accuracy,
    date_trunc('minute', timestamp),
    timestamp DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 5. Integração com o Frontend
- Toggle chama a função RPC se ativo.
- Service aceita parâmetro `dedupe`.
- Exemplo React Query:
```typescript
const { data, isLoading } = useQuery(
  ['locations', studentId, { dedupe: removeDuplicates, ...outrosFiltros }],
  () => fetchLocations({ studentId, dedupe: removeDuplicates, ...outrosFiltros })
);
```

## 6. Checklist de Implementação
- [ ] Definir critério de duplicidade
- [ ] Criar função SQL (RPC)
- [ ] Testar função SQL
- [ ] Adaptar service frontend
- [ ] Ligar toggle do filtro
- [ ] Testar na interface
- [ ] Garantir performance (índices)
- [ ] Documentar

## 7. Considerações
- **Performance:** Índices em `student_id`, `latitude`, `longitude`, `timestamp`.
- **Segurança:** Respeitar RLS.

## 8. UX
- Mensagem: "Exibindo apenas localizações únicas. Duplicatas ocultadas para facilitar a visualização."

## 9. Testes
- [ ] Testar com dados reais
- [ ] Testar com diferentes estudantes
- [ ] Testar performance
- [ ] Testar fallback

## 10. Próximos Passos
1. Criar função SQL no Supabase
2. Adaptar service frontend
3. Ligar filtro na UI
4. Testar e validar 