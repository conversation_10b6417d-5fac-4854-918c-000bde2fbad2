import React from 'react';
import StudentLocationTabs from '@/components/student/StudentLocationTabs';
import { LocationData } from '@/types/database';
import { cn } from '@/lib/utils';

interface StudentLocationSectionProps {
  userId?: string;
  userName: string;
  userEmail: string;
  locations: LocationData[];
  loading: boolean;
  error: string | null;
  className?: string;
}

const StudentLocationSection: React.FC<StudentLocationSectionProps> = ({
  userId,
  userName,
  userEmail,
  locations,
  loading,
  error,
  className
}) => {
  return (
    <StudentLocationTabs
      className={cn(className)}
      userId={userId}
      userName={userName}
      userEmail={userEmail}
      locations={locations}
      loading={loading}
      error={error}
    />
  );
};

export default StudentLocationSection;
