-- Corrigir função de deduplicação de localizações
DROP FUNCTION IF EXISTS delete_student_locations_duplicates(uuid, integer, integer, text);

-- Recriar função com lógica corrigida
CREATE OR REPLACE FUNCTION delete_student_locations_duplicates(
  p_user_id uuid,
  p_radius_meters integer DEFAULT 50,
  p_time_window_min integer DEFAULT 10,
  p_accuracy text DEFAULT NULL
)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count integer := 0;
BEGIN
  -- Validar parâmetros
  IF p_user_id IS NULL THEN
    RAISE EXCEPTION 'User ID é obrigatório';
  END IF;

  -- Log da operação
  INSERT INTO public.auth_logs (
    event_type,
    user_id,
    metadata,
    occurred_at
  ) VALUES (
    'location_deduplication_start',
    auth.uid(),
    jsonb_build_object(
      'target_user_id', p_user_id,
      'radius_meters', p_radius_meters,
      'time_window_min', p_time_window_min,
      'accuracy_filter', p_accuracy
    ),
    NOW()
  );

  -- Deletar localizações duplicadas mantendo apenas a mais recente por grupo
  WITH grouped_locations AS (
    SELECT
      id,
      user_id,
      latitude,
      longitude,
      timestamp,
      accuracy,
      ROW_NUMBER() OVER (
        PARTITION BY
          user_id,
          ROUND(latitude::numeric, 4), -- Agrupar por coordenadas arredondadas
          ROUND(longitude::numeric, 4),
          EXTRACT(EPOCH FROM timestamp)::integer / (p_time_window_min * 60), -- Janela de tempo
          CASE 
            WHEN p_accuracy IS NULL OR p_accuracy = 'all' THEN 'all'
            ELSE COALESCE(accuracy::text, 'unknown')
          END
        ORDER BY timestamp DESC, id DESC -- Manter o mais recente
      ) AS rn
    FROM locations
    WHERE user_id = p_user_id
      AND (
        p_accuracy IS NULL 
        OR p_accuracy = 'all' 
        OR accuracy::text = p_accuracy
        OR (p_accuracy = 'unknown' AND accuracy IS NULL)
      )
  ),
  duplicates_to_delete AS (
    SELECT id
    FROM grouped_locations
    WHERE rn > 1
  )
  -- Delete related records from location_history first
  DELETE FROM location_history
  WHERE location_id IN (SELECT id FROM duplicates_to_delete);

  -- Now delete the duplicate locations
  DELETE FROM locations
  WHERE id IN (SELECT id FROM duplicates_to_delete);

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Log do resultado
  INSERT INTO public.auth_logs (
    event_type,
    user_id,
    metadata,
    occurred_at
  ) VALUES (
    'location_deduplication_complete',
    auth.uid(),
    jsonb_build_object(
      'target_user_id', p_user_id,
      'deleted_count', deleted_count,
      'parameters', jsonb_build_object(
        'radius_meters', p_radius_meters,
        'time_window_min', p_time_window_min,
        'accuracy_filter', p_accuracy
      )
    ),
    NOW()
  );
  
  RETURN deleted_count;
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION delete_student_locations_duplicates(uuid, integer, integer, text) TO authenticated;

-- Comentário atualizado
COMMENT ON FUNCTION delete_student_locations_duplicates(uuid, integer, integer, text) IS
'Remove duplicate location records for a student based on spatial and temporal proximity.
Handles cascade deletion of related location_history records.
Parameters:
- p_user_id: Student UUID (required)
- p_radius_meters: Spatial grouping radius (uses coordinate rounding)
- p_time_window_min: Time window for grouping in minutes
- p_accuracy: Filter by accuracy level (high/medium/low/all/null for all)
Returns: Number of deleted duplicate records';