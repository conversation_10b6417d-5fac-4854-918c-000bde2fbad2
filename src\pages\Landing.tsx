import React, { useEffect, useRef } from 'react'
import { useNavigate, useLocation, Link } from 'react-router-dom'
import PublicPageContainer from '@/components/PublicPageContainer'
import { useToast } from '@/components/ui/use-toast'
import { useUser } from '@/contexts/UnifiedAuthContext'
import { Check, Shield, FileText } from 'lucide-react'
import { getUserTypeFromMetadata, getDefaultRouteForUserType } from '@/lib/types/user-types'
import { useTranslation } from 'react-i18next'

const Landing: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { user, isLoading } = useUser();
  const { t } = useTranslation();
  const redirectHandledRef = useRef(false);

  const queryParams = new URLSearchParams(location.search);
  const redirectMessage = queryParams.get('message');

  useEffect(() => {
    if (redirectMessage && !redirectHandledRef.current) {
      console.log(`[LANDING] Redirect message present: ${redirectMessage}`);
      toast({
        title: 'Atenção',
        description: redirectMessage,
        variant: 'default'
      });
      redirectHandledRef.current = true;
    }
  }, [redirectMessage, toast]);

  useEffect(() => {
    if (isLoading || redirectHandledRef.current) return;

    if (user) {
      console.log('[LANDING] User already authenticated, redirecting:', user);
      redirectHandledRef.current = true;

      const userType = getUserTypeFromMetadata(user.user_metadata);
      const redirectPath = getDefaultRouteForUserType(userType);

      setTimeout(() => {
        navigate(redirectPath, { replace: true });
      }, 100);
    }
  }, [user, isLoading, navigate]);


  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-blue-600 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (user && redirectHandledRef.current) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-blue-600 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  return (
    <PublicPageContainer className="text-center" data-cy="landing-page" withPattern={false}>
        <h1 className="text-4xl md:text-5xl font-bold text-blue-800 drop-shadow-lg text-center">
          {t('landing.title')}
        </h1>
        <p className="mt-4 text-lg text-gray-800 text-center">{t('landing.tagline')}</p>
        <ul className="mt-6 text-gray-700 text-base text-center space-y-1">
          <li className="flex items-center gap-2 justify-center">
            <Check size={18} className="text-blue-600" /> {t('landing.features.liveMap')}
          </li>
          <li className="flex items-center gap-2 justify-center">
            <Check size={18} className="text-blue-600" /> {t('landing.features.quickInvite')}
          </li>
          <li className="flex items-center gap-2 justify-center">
            <Shield size={18} className="text-blue-600" /> {t('landing.features.security')}
          </li>
          <li className="flex items-center gap-2 justify-center">
            <FileText size={18} className="text-blue-600" /> {t('landing.features.noHistory')}
          </li>
        </ul>
        <div className="mt-8 flex gap-4 justify-center">
          <Link
            to="/login"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg text-lg shadow-md transition"
            data-cy="login-button"
          >
            {t('landing.login')}
          </Link>
          <Link
            to="/register"
            className="bg-white border border-blue-600 text-blue-600 px-6 py-3 rounded-lg text-lg hover:bg-blue-50 transition"
            data-cy="register-button"
          >
            {t('landing.createAccount')}
          </Link>
        </div>
        <div className="mt-6 text-sm text-center">
          <Link to="/ajuda/estudante" className="text-blue-600 hover:underline">
            {t('landing.studentHelp')}
          </Link>
          <span className="mx-2 text-gray-400">|</span>
          <Link to="/ajuda/responsavel" className="text-blue-600 hover:underline">
            {t('landing.guardianHelp')}
          </Link>
        </div>
    </PublicPageContainer>
  );
};

export default Landing;
