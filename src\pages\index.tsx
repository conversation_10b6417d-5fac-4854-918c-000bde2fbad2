import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Select, SelectItem } from '@/components/ui/select'
import { supportedLocales } from '@/i18n/config'
import i18n from '@/lib/i18n'

const IndexPage = () => {
  const navigate = useNavigate()
  const [locale, setLocale] = useState(() => localStorage.getItem('i18nextLng') || i18n.language)

  useEffect(() => {
    i18n.changeLanguage(locale)
  }, [locale])

  const handleChange = (value: string) => {
    setLocale(value)
    localStorage.setItem('i18nextLng', value)
    document.cookie = `NEXT_LOCALE=${value};path=/;max-age=31536000`
    navigate('/')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-blue-50 to-white">
      <div className="text-center">
        <Select onValueChange={handleChange} defaultValue={locale}>
          <SelectItem value="pt-BR">🇧🇷 Português (Brasil)</SelectItem>
          <SelectItem value="en-GB">🇬🇧 English (UK)</SelectItem>
        </Select>
      </div>
    </div>
  )
}

export default IndexPage
