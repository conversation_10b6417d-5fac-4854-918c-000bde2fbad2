-- ==========================================
-- DEBUG: TESTAR RPC get_student_guardians_secure
-- Data: 24/06/2025
-- Objetivo: Entender por que a RPC falha
-- ==========================================

-- 1. TESTAR RPC DIRETAMENTE PARA MAURÍCIO
SELECT 
    'RPC_TEST_MAURICIO' as info,
    *
FROM get_student_guardians_secure('864a6c0b-4b17-4df7-8709-0c3f7cf0be91');

-- 2. TESTAR RPC SEM PARÂMETROS (usa auth.uid())
SELECT 
    'RPC_TEST_NO_PARAMS' as info,
    *
FROM get_student_guardians_secure();

-- 3. VERIFICAR DEFINIÇÃO DA FUNÇÃO
SELECT 
    'RPC_DEFINITION' as info,
    routine_name,
    routine_definition
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name = 'get_student_guardians_secure';

-- 4. VERIFICAR PARÂMETROS DA FUNÇÃO
SELECT 
    'RPC_PARAMETERS' as info,
    parameter_name,
    data_type,
    parameter_default
FROM information_schema.parameters
WHERE specific_schema = 'public'
  AND specific_name IN (
    SELECT specific_name 
    FROM information_schema.routines 
    WHERE routine_name = 'get_student_guardians_secure'
  )
ORDER BY ordinal_position;

-- 5. VERIFICAR TABELA GUARDIANS (SE EXISTE)
SELECT 
    'GUARDIANS_TABLE_CHECK' as info,
    COUNT(*) as total_records
FROM guardians;

-- 6. MOSTRAR ESTRUTURA DA TABELA GUARDIANS  
SELECT 
    'GUARDIANS_STRUCTURE' as info,
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'guardians'
ORDER BY ordinal_position; 