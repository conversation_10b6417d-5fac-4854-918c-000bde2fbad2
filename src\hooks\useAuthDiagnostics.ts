
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface AuthDiagnostics {
  sessionStatus: 'loading' | 'authenticated' | 'unauthenticated' | 'error';
  lastError: string | null;
  sessionDetails: any;
  clientStatus: 'healthy' | 'error';
}

export const useAuthDiagnostics = () => {
  const [diagnostics, setDiagnostics] = useState<AuthDiagnostics>({
    sessionStatus: 'loading',
    lastError: null,
    sessionDetails: null,
    clientStatus: 'healthy'
  });

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Test basic client connectivity
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          setDiagnostics({
            sessionStatus: 'error',
            lastError: error.message,
            sessionDetails: null,
            clientStatus: 'error'
          });
          return;
        }

        setDiagnostics({
          sessionStatus: session ? 'authenticated' : 'unauthenticated',
          lastError: null,
          sessionDetails: session ? {
            userId: session.user.id,
            email: session.user.email,
            expiresAt: new Date(session.expires_at! * 1000).toISOString(),
            tokenType: session.token_type,
            providerToken: !!session.provider_token,
            accessToken: !!session.access_token
          } : null,
          clientStatus: 'healthy'
        });

      } catch (error: any) {
        setDiagnostics({
          sessionStatus: 'error',
          lastError: error.message,
          sessionDetails: null,
          clientStatus: 'error'
        });
      }
    };

    checkAuthStatus();

    // Set up auth state listener for real-time updates
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('[AUTH_DIAGNOSTICS] Auth state changed:', event, session?.user?.email);
        
        setDiagnostics(prev => ({
          ...prev,
          sessionStatus: session ? 'authenticated' : 'unauthenticated',
          sessionDetails: session ? {
            userId: session.user.id,
            email: session.user.email,
            expiresAt: new Date(session.expires_at! * 1000).toISOString(),
            tokenType: session.token_type,
            providerToken: !!session.provider_token,
            accessToken: !!session.access_token
          } : null
        }));
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const runDiagnostics = async () => {
    console.log('[AUTH_DIAGNOSTICS] Running full diagnostics...');
    
    try {
      // Test database connectivity
      const { data, error } = await supabase.from('auth_logs').select('count').limit(1);
      
      if (error) {
        console.error('[AUTH_DIAGNOSTICS] Database test failed:', error);
        return { databaseConnectivity: false, error: error.message };
      }

      console.log('[AUTH_DIAGNOSTICS] Database connectivity: OK');
      return { databaseConnectivity: true, error: null };

    } catch (error: any) {
      console.error('[AUTH_DIAGNOSTICS] Diagnostics failed:', error);
      return { databaseConnectivity: false, error: error.message };
    }
  };

  return {
    diagnostics,
    runDiagnostics
  };
};

