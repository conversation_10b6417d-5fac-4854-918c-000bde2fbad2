#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Custom styles for location sharing button */
.pulse-on-hover:hover {
  animation: pulse-glow 1s infinite;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px #22c55e, 0 0 10px #22c55e, 0 0 15px #22c55e;
  }
  50% {
    box-shadow: 0 0 10px #22c55e, 0 0 20px #22c55e, 0 0 30px #22c55e;
  }
  100% {
    box-shadow: 0 0 5px #22c55e, 0 0 10px #22c55e, 0 0 15px #22c55e;
  }
}

/* Make location controls more prominent */
.location-controls-container {
  z-index: 1000;
}

.location-share-button {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border: 2px solid #15803d;
  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.4);
}

.location-share-button:hover {
  background: linear-gradient(135deg, #16a34a, #15803d);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 25px rgba(34, 197, 94, 0.6);
}
