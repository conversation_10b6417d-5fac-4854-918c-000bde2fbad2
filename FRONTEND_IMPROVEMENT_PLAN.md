# Plano de Melhoria do Frontend

Este documento propõe melhorias de organização e experiência de usuário para o frontend do Locate-Family-Connect. A análise considera as rotas declaradas em `src/App.tsx` e a estrutura de componentes existente.

## 1. <PERSON><PERSON>s e Páginas

As principais rotas estão configuradas em `App.tsx` entre as linhas 42‑78, agrupando rotas públicas, de autenticação e protegidas:

```
<Route path="/login" element={<Login />} />
<Route path="/register" element={<Register />} />
<Route path="/reset-password" element={<ResetPassword />} />
<Route element={<ProtectedLayout />}>
  <Route path="/dashboard" element={<Dashboard />} />
  <Route path="/parent-dashboard" element={<ParentDashboard />} />
  <Route path="/student-dashboard" element={<StudentDashboard />} />
  <Route path="/profile" element={<ProfilePage />} />
  <Route path="/guardians" element={<GuardiansPage />} />
  <Route path="/add-student" element={<AddStudent />} />
  <Route path="/student-map/:id?" element={<StudentMap />} />
  <Route path="/system-test" element={<SystemTest />} />
</Route>
<Route path="/" element={<Landing />} />
```

Fonte: `src/App.tsx`【F:src/App.tsx†L42-L79】.

## 2. Organização de Componentes

Os componentes estão em `src/components` organizados por categoria (`common`, `guardian`, `student`, `location`, etc.) e a pasta `ui` contém os elementos de interface derivados do Shadcn/UI, como `button.tsx`, `card.tsx` e `sidebar.tsx`【F:src/components/ui/button.tsx†L1-L2】. Essa estrutura facilita a reutilização.

## 3. Uso do Shadcn/UI

A biblioteca Shadcn/UI já está instalada e provê diversos componentes reutilizáveis em `src/components/ui`. Exemplos:

- `card.tsx` e `button.tsx` para cartões e botões padronizados.
- `sidebar.tsx` usado em `AppSidebar.tsx` para a navegação principal do dashboard【F:src/components/AppSidebar.tsx†L14-L21】.
- `form.tsx` e `input.tsx` para formulários.

## 4. Recomendações de Melhoria por Página

### 4.1 Landing (`/`)
- Utilizar componentes `Hero`, `Button` e `Card` do Shadcn para um layout inicial mais moderno.
- Criar seções de destaque (benefícios, screenshots) aproveitando `Tabs` para mostrar passos de uso.

### 4.2 Auth Pages (`/login`, `/register`, `/reset-password`)
- Consolidar em um único layout `AuthLayout` com `Card` centralizado para os formulários.
- Usar o componente de `form` do Shadcn, garantindo validação visual consistente.
- Incluir feedback via `toast` em caso de erro ou sucesso.

### 4.3 Dashboards
- **ParentDashboard** e **StudentDashboard** compartilham a navegação lateral `AppSidebar`. Padronizar o cabeçalho com `DashboardHeader`.
- Organizar cartões de informação usando `Grid` e `Card` do Shadcn para responsividade melhor.
- Avaliar criar `DashboardLayout` com `Sidebar` e `Header` para reutilização entre perfis.

### 4.4 Mapas (`/student-map` e dashboards)
- Componentizar o mapa em `StudentMapSection` e `StudentLocationMap` mantendo o controle de localizações via hooks (`useMapInitialization`, `useLocationData`).
- Utilizar o componente `drawer.tsx` para exibir detalhes de localização no mobile.
- Garantir que botões de ação (ex: “🚀 Enviar Localização”) utilizem `Button` padrão e estados visuais (loading, sucesso) via `use-toast`.

### 4.5 Perfil (`/profile`)
- Unificar exibição e edição em um componente `ProfileForm` usando campos do Shadcn.
- Mostrar avatar (`avatar.tsx`) e informações adicionais (último login, status) se aplicável.

### 4.6 Administração (`/admin/UsersManagement`)
- Aplicar `table.tsx` e `pagination.tsx` do Shadcn para listagem de usuários.
- Incluir filtros com `select.tsx` e `input.tsx` para pesquisa.

## 5. Organização de Código

- Garantir que cada componente tenha no máximo 300 linhas, conforme guia de frontend.
- Separar lógica em hooks específicos (`/hooks`) e manter UI limpa.
- Centralizar traduções no diretório `i18n` e evitar strings soltas em componentes.

## 6. Próximos Passos

1. **Criar um layout base** utilizando componentes Shadcn (`sidebar`, `header`, `card`).
2. **Padronizar formulários** com o componente `form.tsx` e validações integradas.
3. **Revisar cada página** para substituir elementos customizados por equivalentes do Shadcn, mantendo responsividade.
4. **Documentar** no README as novas dependências e exemplos de uso.

Seguindo estas etapas, o frontend ficará mais coeso e fácil de manter, aproveitando o conjunto de componentes oferecido pelo Shadcn/UI.
