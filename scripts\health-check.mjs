#!/usr/bin/env node

import http from 'http';

async function checkRedis() {
  try {
    const { default: Redis } = await import('ioredis');
    const redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 1,
    });
    
    await redis.ping();
    await redis.quit();
    return true;
  } catch (error) {
    return false;
  }
}

async function checkApp() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:4000', (res) => {
      resolve(res.statusCode === 200);
    });
    
    req.on('error', () => resolve(false));
    req.setTimeout(5000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

async function performHealthCheck() {
  console.log('🏥 Verificando saúde do sistema...\n');
  
  const checks = {
    app: await checkApp(),
    redis: await checkRedis(),
    sentry: !!process.env.VITE_SENTRY_DSN,
    posthog: !!process.env.VITE_POSTHOG_KEY,
  };
  
  console.log('📊 Resultados:');
  console.log(`App (localhost:4000): ${checks.app ? '✅' : '❌'}`);
  console.log(`Redis: ${checks.redis ? '✅' : '❌'}`);
  console.log(`Sentry: ${checks.sentry ? '✅' : '❌'}`);
  console.log(`PostHog: ${checks.posthog ? '✅' : '❌'}`);
  
  const allHealthy = Object.values(checks).every(Boolean);
  console.log(`\n🎯 Status geral: ${allHealthy ? '✅ SAUDÁVEL' : '⚠️ PROBLEMAS DETECTADOS'}`);
  
  if (!allHealthy) {
    console.log('\n💡 Dicas:');
    if (!checks.app) console.log('- Execute: npm run dev');
    if (!checks.redis) console.log('- Execute: npm run redis:start');
    if (!checks.sentry) console.log('- Configure VITE_SENTRY_DSN no .env');
    if (!checks.posthog) console.log('- Configure VITE_POSTHOG_KEY no .env');
  }
  
  process.exit(allHealthy ? 0 : 1);
}

performHealthCheck().catch(console.error); 